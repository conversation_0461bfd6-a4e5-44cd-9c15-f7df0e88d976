<template>
  <el-form :model="formData" label-width="100px">
    <el-form-item label="标题">
      <el-input v-model="formData.title" />
    </el-form-item>
    <el-form-item label="类型">
      <el-select v-model="formData.type">
        <el-option
          v-for="(label, key) in MessageTypeLabels"
          :key="key"
          :label="label"
          :value="key"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="内容">
      <el-input v-model="formData.content" type="textarea" :rows="5" />
    </el-form-item>
    <el-form-item label="Word文档">
      <el-upload
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        accept=".docx"
      >
        <el-button type="primary">选择文件</el-button>
      </el-upload>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Notice } from '@/enums/MessageTypeEnum';
import { MessageTypeLabels, MessageTypeEnum } from '@/enums/MessageTypeEnum';

const props = defineProps<{
  notice?: Notice;
}>();

const emit = defineEmits<{
  (e: 'submit', notice: Notice): void;
  (e: 'uploadWord', file: File): void;
}>();

const formData = ref<Notice>({
  title: '',
  type: MessageTypeEnum.NOTICE,
  content: '',
  level: 0
});

watch(() => props.notice, (newValue) => {
  if (newValue) {
    formData.value = { ...newValue };
  }
}, { immediate: true });

const submitForm = () => {
  emit('submit', formData.value);
};

const handleFileChange = (file: File) => {
  emit('uploadWord', file);
};
</script>
