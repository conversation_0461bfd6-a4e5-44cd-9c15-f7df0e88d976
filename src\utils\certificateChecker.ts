import AuthAPI from "@/api/auth";
import { isExpired } from "@/utils/dateUtils";

// 证书缓存
interface CertificateCache {
  isValid: boolean;
  timestamp: number;
  data: any;
}

let certificateCache: CertificateCache | null = null;
const CACHE_DURATION = 300 * 60 * 1000; // 300分钟缓存

/**
 * 检查证书是否有效
 * @param forceCheck 是否强制检查（忽略缓存）
 * @returns Promise<{isValid, data}>
 */
export async function checkCertificate(forceCheck = false): Promise<{ isValid: boolean; data: any }> {
  // 使用缓存避免频繁请求
  console.log('检查证书有效性...');
  if (
    !forceCheck && 
    certificateCache && 
    Date.now() - certificateCache.timestamp < CACHE_DURATION
  ) {
    return {
      isValid: certificateCache.isValid,
      data: certificateCache.data
    };
  }

  try {
    const response = await AuthAPI.getCertificate();
    
    // 使用您的isExpired函数检查证书是否过期
    const isValid = !!(response && response.validTo && !isExpired(response.validTo));
    
    // 更新缓存
    certificateCache = {
      isValid,
      timestamp: Date.now(),
      data: response
    };
    
    return {
      isValid,
      data: response
    };
  } catch (error) {
    console.error('证书检查失败:', error);
    
    // 标记为无效状态
    certificateCache = {
      isValid: false,
      timestamp: Date.now(),
      data: null
    };
    
    return {
      isValid: false,
      data: null
    };
  }
}

/**
 * 清除证书缓存
 */
export function clearCertificateCache(): void {
  certificateCache = null;
}
