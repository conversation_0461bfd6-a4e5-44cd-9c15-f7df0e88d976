<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item v-if="showAdvancedFilters" label="下线事件名" prop="name">
          <el-input v-model="queryParams.name" placeholder="下线事件名" clearable @keyup.enter="handleQuery()"
            class="!max-w-[130px]" />
        </el-form-item>
        <el-form-item v-if="showAdvancedFilters" label="下线步骤" prop="step">
          <el-input v-model="queryParams.step" placeholder="下线步骤" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="申请人" prop="applicantName">
          <el-input v-model="queryParams.applicantName" placeholder="申请人姓名" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="处理人" prop="handler">
          <el-input v-model="queryParams.handler" placeholder="处理人姓名" clearable @keyup.enter="handleQuery()"
            class="!max-w-[130px]" />
        </el-form-item>
        <el-form-item label="申请时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()"><i-ep-search />搜索</el-button>
          <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
        </el-form-item>
      </el-form>
      <el-button @click="toggleAdvancedFilters" class="mb-2">
        {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
      </el-button>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button v-hasPerm="['system:offline:add']" type="success" @click="handleOpenDialog()">
              <i-ep-plus />发起申请
            </el-button>
            <el-button v-hasPerm="['system:offline:delete']" type="danger" :disabled="ids.length === 0"
              @click="handleDelete()">
              <i-ep-delete />删除
            </el-button>
          </div>
          <div>
            <el-button class="ml-3" @click="handleExport">
              <template #icon><i-ep-download /></template>
              导出
            </el-button>
          </div>
        </div>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="pageData" highlight-current-row border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column key="id" label="id" prop="id" min-width="100" /> -->
        <el-table-column key="status" label="工单状态" prop="status" min-width="100">
          <template #default="{ row }">
            <el-tag :type="row.status == '3' ? 'success' : 'warning'">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column key="name" label="任务名称" prop="name" min-width="100" />
        <el-table-column key="applicantName" label="申请人" prop="applicantName" min-width="100" />
        <el-table-column key="step" label="当前步骤" prop="step" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getStepTagType(row.step)">
              {{ getStepName(row.step) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column key="handler" label="处理人" prop="handler" min-width="100" />
        <el-table-column key="reason" label="下线原因" prop="reason" min-width="100" >
          <template #default="scope">
                <div class="flex items-center">
                  <span class="truncate max-w-[80px]" :title="scope.row.notes">
                    {{ scope.row.reason ? (scope.row.reason.length > 0 ? '' :
                      scope.row.reason) : '-' }}
                  </span>
                  <ViewNotes v-if="scope.row.reason" :content="scope.row.reason"
                    :title="`${scope.row.name || ''}描述`" />
                </div>
              </template>
        </el-table-column>
        <el-table-column key="createTime" label="申请时间" prop="createTime" min-width="100" />
        <el-table-column key="updateTime" label="更新时间" prop="updateTime" min-width="100" />

        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-button type="info" size="small" link @click="handleViewTicket(scope.row.id)">
              <i-ep-view />查看详情
            </el-button>
            <el-button v-if="(scope.row.step != 4 && scope.row.reviewDeptType.includes(userInfo.deptType))"
              v-hasPerm="['system:offline:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />处理下线
            </el-button>
            <el-button v-if="scope.row.step != 4"
              v-hasPerm="['system:offline:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
    </el-card>

    <!-- 查看工单详情组件 -->
    <ViewTicket v-model:visible="viewTicketDialog.visible" :ticket-id="viewTicketDialog.ticketId"
      @navigateToProcess="handleOpenDialog" />
  </div>
</template>

<script setup lang="ts">
import UserAPI, {UserInfo} from "@/api/user";

defineOptions({
  name: "offlineDetails",
  inheritAttrs: false,
});

import { reactive, ref } from "vue";
import offlineAPI, { offlinePageVO, offlineForm, offlinePageQuery } from "@/api/assets_management/details/offline";
import ViewTicket from './offlineViewTicket.vue';

const props = defineProps({
  id: Number,
});

const emits = defineEmits(["navigateToProcess"]);

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const userInfo = ref<UserInfo>({perms: [], roles: []});

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<offlinePageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 下线管理表格数据
const pageData = ref<offlinePageVO[]>([]);

// 高级筛选控制
const showAdvancedFilters = ref(false);

// 工单详情查看对话框
const viewTicketDialog = reactive({
  visible: false,
  ticketId: undefined as number | undefined
});

// 切换高级筛选
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}

/** 查询下线管理 */
function handleQuery() {
  loading.value = true;
  offlineAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置下线管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

// 获取用户选项
const loadUserInfo = async () => {
  try {
    const data = await UserAPI.getInfo()
    userInfo.value = data
  } catch (error) {
  }
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

// 获取步骤名称
function getStepName(step) {
  const stepMap = {
    '1': '下线申请',
    '2': '下线审核',
    '3': '下线评价',
    '4': '下线完成'
  };
  return stepMap[step] || `步骤${step}`;
}

// 获取步骤标签类型
function getStepTagType(step) {
  const typeMap = {
    '1': 'warning',
    '2': 'success',
    '3': 'success',
    '4': 'info'  
  };
  return typeMap[step] || 'info';
}

// 获取状态名称  0待处理 1进行中 2未通过 3已完成
function getStatusName(status) {
  const statusMap = {
    '0': '待处理',
    '1': '进行中',
    '2': '未通过',
    '3': '已完成'
  };
  return statusMap[status] || '未知状态';
}


/** 处理打开工单详情 */
function handleViewTicket(id: number) {
  if (!id) {
    ElMessage.warning('工单ID不存在');
    return;
  }

  viewTicketDialog.ticketId = id;
  viewTicketDialog.visible = true;
}

/** 处理打开工单流程 */
function handleOpenDialog(id?: number) {
  emits("navigateToProcess", id);
}

/** 导出数据 */
function handleExport() {
  // 实现导出逻辑
}

/** 删除下线管理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      offlineAPI.deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
  loadUserInfo();

  // 如果有ID参数，打开处理对话框
  if (props.id) {
    handleOpenDialog(props.id);
  }
});
</script>

<style scoped>
.search-container {
  margin-bottom: 20px;
}

.table-container {
  margin-top: 20px;
}

.flex-x-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ml-3 {
  margin-left: 12px;
}

.mb-2 {
  margin-bottom: 8px;
}
</style>
