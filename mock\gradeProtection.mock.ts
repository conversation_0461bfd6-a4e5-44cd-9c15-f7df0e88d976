import { defineMock } from "./base";

export default defineMock([
  // 等保定级表单数据
  {
    url: "grade_protection/grading/index",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: "1",
          name: "企业资源管理系统",
          systemNumber: "SYS-2023-001",
          department: "信息技术部",
          level: "三级",
          assessmentStatus: "已测评",
          responsiblePerson: "张明",
          avatar: "https://randomuser.me/api/portraits/men/1.jpg",
        },
        {
          id: "2",
          name: "人力资源管理系统",
          systemNumber: "SYS-2023-002",
          department: "人力资源部",
          level: "二级",
          assessmentStatus: "测评中",
          responsiblePerson: "李华",
          avatar: "https://randomuser.me/api/portraits/women/2.jpg",
        },
        {
          id: "3",
          name: "财务管理系统",
          systemNumber: "SYS-2023-003",
          department: "财务部",
          level: "三级",
          assessmentStatus: "已测评",
          responsiblePerson: "王强",
          avatar: "https://randomuser.me/api/portraits/men/3.jpg",
        },
        {
          id: "4",
          name: "客户关系管理系统",
          systemNumber: "SYS-2023-004",
          department: "销售部",
          level: "五级",
          assessmentStatus: "未测评",
          responsiblePerson: "赵燕",
          avatar: "https://randomuser.me/api/portraits/women/4.jpg",
        },
        {
          id: "5",
          name: "供应链管理系统",
          systemNumber: "SYS-2023-005",
          department: "运营部",
          level: "三级",
          assessmentStatus: "已测评",
          responsiblePerson: "刘伟",
          avatar: "https://randomuser.me/api/portraits/men/5.jpg",
        },
        {
          id: "6",
          name: "办公自动化系统",
          systemNumber: "SYS-2023-006",
          department: "行政部",
          level: "一级",
          assessmentStatus: "未测评",
          responsiblePerson: "陈静",
          avatar: "https://randomuser.me/api/portraits/women/6.jpg",
        },
        {
          id: "7",
          name: "数据分析平台",
          systemNumber: "SYS-2023-007",
          department: "数据分析部",
          level: "四级",
          assessmentStatus: "测评中",
          responsiblePerson: "杨光",
          avatar: "https://randomuser.me/api/portraits/men/7.jpg",
        },
        {
          id: "8",
          name: "电子邮件系统",
          systemNumber: "SYS-2023-008",
          department: "信息技术部",
          level: "二级",
          assessmentStatus: "已测评",
          responsiblePerson: "周梅",
          avatar: "https://randomuser.me/api/portraits/women/8.jpg",
        },
        {
          id: "9",
          name: "网络安全监控系统",
          systemNumber: "SYS-2023-009",
          department: "安全部",
          level: "四级",
          assessmentStatus: "已测评",
          responsiblePerson: "孙宇",
          avatar: "https://randomuser.me/api/portraits/men/9.jpg",
        },
        {
          id: "10",
          name: "项目管理系统",
          systemNumber: "SYS-2023-010",
          department: "项目管理部",
          level: "二级",
          assessmentStatus: "测评中",
          responsiblePerson: "郭丽",
          avatar: "https://randomuser.me/api/portraits/women/10.jpg",
        },
      ],
      msg: "一切ok",
    },
  },

  // 定级备案表单数据
  {
    url: "grade_protection/record/index",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: "1",
          name: "企业资源管理系统",
          systemNumber: "SYS-2023-001",
          level: "三级",
          status: "已备案",
          date: "2023-01-15",
          department: "信息技术部",
        },
        {
          id: "2",
          name: "人力资源管理系统",
          systemNumber: "SYS-2023-002",
          level: "二级",
          status: "待备案",
          date: "2023-02-20",
          department: "人力资源部",
        },
        {
          id: "3",
          department: "财务部",
          systemNumber: "SYS-2023-003",
          level: "三级",
          status: "已备案",
          date: "2023-03-10",
          name: "财务管理系统",
        },
        {
          id: "4",
          name: "客户关系管理系统",
          systemNumber: "SYS-2023-004",
          level: "五级",
          status: "待备案",
          date: "2023-04-05",
          department: "销售部",
        },
        {
          id: "5",
          name: "供应链管理系统",
          systemNumber: "SYS-2023-005",
          level: "三级",
          status: "已备案",
          date: "2023-05-12",
          department: "运营部",
        },
      ],
      msg: "一切ok",
    },
  },

  // 测评报告数据
  {
    url: "grade_protection/report/index",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: "001",
          reportName: "信息系统等级保护第三级测评报告",
          systemName: "财务管理系统",
          level: "三级",
          agency: "XX测评中心",
          date: "2024-04-15",
          status: "已完成",
        },
        {
          id: "002",
          reportName: "信息系统等级保护二级测评报告",
          systemName: "人力资源管理系统",
          level: "二级",
          agency: "YY信息安全测评有限公司",
          date: "2024-05-10",
          status: "待整改",
        },
        {
          id: "003",
          reportName: "信息系统等级保护四级测评报告",
          systemName: "客户关系管理系统",
          level: "四级",
          agency: "ZZ安全技术服务有限公司",
          date: "2024-03-20",
          status: "已完成",
        },
        {
          id: "004",
          reportName: "信息系统等级保护五级测评报告",
          systemName: "供应链管理系统",
          level: "五级",
          agency: "XX测评中心",
          date: "2024-02-05",
          status: "测评中",
        },
        {
          id: "005",
          reportName: "信息系统等级保护一级测评报告",
          systemName: "办公自动化系统",
          level: "一级",
          agency: "YY信息安全测评有限公司",
          date: "2024-06-01",
          status: "未开始",
        },
        {
          id: "006",
          reportName: "信息系统等级保护三级测评报告",
          systemName: "网络安全监控系统",
          level: "三级",
          agency: "ZZ安全技术服务有限公司",
          date: "2024-05-25",
          status: "待整改",
        },
        {
          id: "007",
          reportName: "信息系统等级保护四级测评报告",
          systemName: "数据分析平台",
          level: "四级",
          agency: "XX测评中心",
          date: "2024-04-30",
          status: "已完成",
        },
        {
          id: "008",
          reportName: "信息系统等级保护二级测评报告",
          systemName: "电子邮件系统",
          level: "二级",
          agency: "YY信息安全测评有限公司",
          date: "2024-03-18",
          status: "已完成",
        },
        {
          id: "009",
          reportName: "信息系统等级保护五级测评报告",
          systemName: "项目管理系统",
          level: "五级",
          agency: "ZZ安全技术服务有限公司",
          date: "2024-02-28",
          status: "测评中",
        },
        {
          id: "010",
          reportName: "信息系统等级保护三级测评报告",
          systemName: "企业资源管理系统",
          level: "三级",
          agency: "XX测评中心",
          date: "2024-01-15",
          status: "已完成",
        },
      ],
      msg: "一切ok",
    },
  },
]);
