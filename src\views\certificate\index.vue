<template>
    <div class="certificate-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h2>系统授权证书</h2>
        </div>

        <!-- 证书信息卡片 -->
        <el-card class="certificate-card" v-loading="loading">
            <!-- 状态横幅 -->
            <div class="status-banner" :class="certificateStatus.type">
                <div class="banner-icon">
                    <el-icon v-if="certificateStatus.type === 'danger'"><i-ep-warning /></el-icon>
                    <el-icon v-else-if="certificateStatus.type === 'warning'"><i-ep-warning /></el-icon>
                    <el-icon v-else><i-ep-warning /></el-icon>
                </div>
                <div class="banner-content">
                    <h3>证书状态: {{ certificateStatus.text }}</h3>
                    <p v-if="remainingDays >= 0">当前证书有效期还剩 <span class="highlight">{{ remainingDays }}</span> 天</p>
                    <p v-else>证书已过期 <span class="highlight">{{ Math.abs(remainingDays) }}</span> 天，请尽快更新</p>
                </div>
                <!-- <div class="banner-tag">
                    <el-tag size="large" :type="certificateStatus.type">{{ certificateStatus.text }}</el-tag>
                </div> -->
            </div>

            <!-- 证书基本信息 -->
            <div class="certificate-content">
                <div class="content-section">
                    <h3 class="section-title">基本信息</h3>
                    <el-descriptions :column="2" border size="large" class="info-descriptions">
                        <el-descriptions-item label="产品名称" label-align="right" label-width="140px">
                            <span class="info-value">{{ certificateInfo.productName }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="证书类型" label-align="right" label-width="140px">
                            <el-tag size="default" :type="certificateInfo.certType === 'official' ? 'success' : 'warning'">
                                {{ certificateInfo.certType === 'official' ? '正式证书' : '临时证书' }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="产品序列号" label-align="right">
                            <span class="info-value">{{ certificateInfo.serialNumber }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="用户名称" label-align="right">
                            <span class="info-value">{{ certificateInfo.userName }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="授权有效期" :span="2" label-align="right">
                            <div class="validity-info">
                                <span class="date-range">{{ formatDate(certificateInfo.validFrom) }} 至 {{ formatDate(certificateInfo.validTo) }}</span>
                                <span class="remaining-tag" :class="remainingDays < 0 ? 'expired' : (remainingDays <= 30 ? 'warning' : '')">
                                    {{ remainingDays < 0 ? '已过期' : `剩余 ${remainingDays} 天` }}
                                </span>
                            </div>
                        </el-descriptions-item>
                    </el-descriptions>
                </div>

                <!-- 授权模块 -->
                <div class="content-section modules-section">
                    <h3 class="section-title">授权模块</h3>
                    <div class="modules-list">
                        <el-tag v-for="(module, index) in certificateInfo.modules" :key="index" 
                            class="module-item" effect="light" size="large">
                            <el-icon class="module-icon"><i-ep-check /></el-icon>
                            {{ module }}
                        </el-tag>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="certificate-actions">
                    <el-button size="large" type="primary" @click="openUploadDialog">
                        <el-icon><i-ep-upload /></el-icon>
                        上传新证书
                    </el-button>
                </div>
            </div>
        </el-card>
        <CertificateUpload v-model:visible="uploadDialogVisible" @upload-success="handleUploadSuccess" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { format, differenceInDays, isExpiring, getCertificateStatus } from '@/utils/dateUtils';
import CertificateUpload from './components/CertificateUpload.vue';
import AuthAPI from "@/api/auth";

// 状态
const loading = ref(false);
const uploadDialogVisible = ref(false);

// 证书信息
const certificateInfo = reactive({
    productName: '安全运营管理平台',
    serialNumber: 'AQYX-2025-04290001',
    userName: 'XX安全科技有限公司',
    certType: 'official', // 'official' 或 'temporary'
    validFrom: '2025-01-01',
    validTo: '2025-12-31',
    modules: ['全部模块']
});

// 格式化日期
const formatDate = (dateString: string) => {
    if (!dateString) return '';
    try {
        return format(new Date(dateString), 'yyyy-MM-dd');
    } catch (error) {
        return dateString;
    }
};

// 打开上传对话框
const openUploadDialog = () => {
    uploadDialogVisible.value = true;
};

// 处理上传成功
const handleUploadSuccess = () => {
    ElMessage.success('证书上传成功');
    refreshCertificate();
};

// 计算剩余天数
const remainingDays = computed(() => {
    const today = new Date();
    const endDate = new Date(certificateInfo.validTo);
    return differenceInDays(endDate, today);
});

// 计算证书状态
const certificateStatus = computed(() => {
    if (remainingDays.value < 0) {
        return { text: '已过期', type: 'danger' };
    } else if (remainingDays.value <= 30) {
        return { text: '即将过期', type: 'warning' };
    } else {
        return { text: '有效', type: 'success' };
    }
});

// 计算进度条百分比 (保留代码但不再使用)
const progressPercentage = computed(() => {
    const startDate = new Date(certificateInfo.validFrom);
    const endDate = new Date(certificateInfo.validTo);
    const today = new Date();

    const totalDays = differenceInDays(endDate, startDate) + 1;
    const passedDays = differenceInDays(today, startDate) + 1;

    const percentage = Math.round(((totalDays - passedDays) / totalDays) * 100);
    return Math.max(0, Math.min(100, percentage));
});

// 计算进度条状态 (保留代码但不再使用)
const progressStatus = computed(() => {
    if (progressPercentage.value > 50) {
        return 'success';
    } else if (progressPercentage.value > 20) {
        return 'warning';
    } else {
        return 'exception';
    }
});

// 刷新证书信息
const refreshCertificate = () => {
    loading.value = true;

  AuthAPI.getCertificate()
        .then(response => {
            // 更新证书信息
            Object.assign(certificateInfo, response);
            ElMessage.success('证书信息已刷新');
        })
        .catch(() => {
            ElMessage.error('刷新证书信息失败');
        })
        .finally(() => {
            loading.value = false;
        });
};


// 页面加载时获取证书信息
onMounted(() => {
    loading.value = true;

    AuthAPI.getCertificate() // 修改API调用，添加signal参数
        .then(response => {
            // 更新证书信息
            Object.assign(certificateInfo, response);
        })
        .catch((error) => {
            // 忽略取消的请求错误
            if (error.name !== 'AbortError') {
                ElMessage.warning('获取证书信息失败');
            }
        })
        .finally(() => {
            loading.value = false;
        });
});
</script>

<style scoped>
.certificate-container {
    padding: 32px;
    margin: 0 auto;
    max-width: 1100px;
    min-height: calc(100vh - 120px);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 28px;
}

.page-header h2 {
    margin: 0;
    color: var(--el-text-color-primary);
    font-size: 24px;
    font-weight: 600;
    position: relative;
    padding-left: 14px;
}

.page-header h2::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background-color: var(--el-color-primary);
    border-radius: 2px;
}

.certificate-card {
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

/* 状态横幅 */
.status-banner {
    display: flex;
    align-items: center;
    padding: 20px 24px;
    margin-bottom: 24px;
    border-radius: 8px;
}

.status-banner.danger {
    background-color: rgba(var(--el-color-danger-rgb), 0.08);
}

.status-banner.warning {
    background-color: rgba(var(--el-color-warning-rgb), 0.08);
}

.status-banner.success {
    background-color: rgba(var(--el-color-success-rgb), 0.08);
}

.banner-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 20px;
    flex-shrink: 0;
}

.banner-icon i {
    font-size: 24px;
}

.status-banner.danger .banner-icon {
    background-color: rgba(var(--el-color-danger-rgb), 0.1);
    color: var(--el-color-danger);
}

.status-banner.warning .banner-icon {
    background-color: rgba(var(--el-color-warning-rgb), 0.1);
    color: var(--el-color-warning);
}

.status-banner.success .banner-icon {
    background-color: rgba(var(--el-color-success-rgb), 0.1);
    color: var(--el-color-success);
}

.banner-content {
    flex: 1;
}

.banner-content h3 {
    margin: 0 0 8px;
    font-size: 18px;
    font-weight: 600;
}

.banner-content p {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
}

.banner-content .highlight {
    font-weight: 700;
    font-size: 16px;
    color: inherit;
}

.status-banner.danger .highlight {
    color: var(--el-color-danger);
}

.status-banner.warning .highlight {
    color: var(--el-color-warning);
}

.status-banner.success .highlight {
    color: var(--el-color-success);
}

.banner-tag {
    flex-shrink: 0;
    padding-left: 20px;
}

/* 内容区域 */
.certificate-content {
    padding: 0 8px;
}

.content-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0 0 20px;
    padding-bottom: 12px;
    border-bottom: 1px dashed var(--el-border-color);
    position: relative;
    padding-left: 16px;
}

.section-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: var(--el-color-primary);
    border-radius: 2px;
}

/* 证书信息描述列表 */
.info-descriptions :deep(.el-descriptions__label) {
    color: var(--el-text-color-secondary);
    font-weight: normal;
}

.info-descriptions :deep(.el-descriptions__content) {
    padding: 16px 20px;
}

.info-value {
    font-size: 15px;
    font-weight: 500;
}

.validity-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
}

.date-range {
    font-weight: 500;
}

.remaining-tag {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 13px;
    background-color: rgba(var(--el-color-success-rgb), 0.1);
    color: var(--el-color-success);
}

.remaining-tag.warning {
    background-color: rgba(var(--el-color-warning-rgb), 0.1);
    color: var(--el-color-warning);
}

.remaining-tag.expired {
    background-color: rgba(var(--el-color-danger-rgb), 0.1);
    color: var(--el-color-danger);
}

/* 模块列表 */
.modules-section {
    margin-top: 32px;
}

.modules-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.module-item {
    padding: 10px 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.module-icon {
    margin-right: 8px;
    font-size: 16px;
}

/* 操作按钮 */
.certificate-actions {
    display: flex;
    gap: 16px;
    margin-top: 40px;
}

.certificate-actions .el-button {
    padding: 12px 24px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s;
}

.certificate-actions .el-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式布局 */
@media (max-width: 768px) {
    .certificate-container {
        padding: 20px;
        max-width: 100%;
    }
    
    .info-descriptions :deep(.el-descriptions__cell) {
        padding: 12px;
    }
    
    .validity-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .certificate-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .status-banner {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .banner-icon {
        margin-bottom: 16px;
    }
    
    .banner-tag {
        padding-left: 0;
        margin-top: 16px;
    }
}
</style>
