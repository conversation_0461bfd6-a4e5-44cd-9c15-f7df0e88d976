<template>
  <el-dialog v-model="dialogVisible" :title="props.title" width="70%" :close-on-click-modal="false"
    :before-close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" class="inventory-form"
      v-loading="loading">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-form-item label="任务名称" prop="inventoryName">
          <el-input v-model="formData.inventoryName" placeholder="请输入盘点任务名称" />
        </el-form-item>

        <!-- 添加开始时间和截止时间 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="formData.startTime" type="datetime" placeholder="选择开始时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="截止时间" prop="deadline">
              <el-date-picker v-model="formData.deadline" type="date" placeholder="选择截止时间" value-format="YYYY-MM-DD"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker v-model="formData.createTime" type="datetime" placeholder="创建时间"
                value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 资产选择部分 -->
        <div class="assets-section">
          <el-button type="primary" @click="openSelectAssets" class="mb-4">
            <el-icon>
              <Plus />
            </el-icon>
            选择资产
          </el-button>
          <el-table v-if="selectedAssetsDetails?.length" :data="selectedAssetsDetails" border style="width: 100%"
            max-height="400">
            <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
            <el-table-column prop="type" label="资产类型" min-width="100" align="center">
              <template #default="scope">
                <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="资产名称" min-width="120" align="center" />
            <el-table-column label="资产地址" min-width="200" align="center">
              <template #default="scope">
                <div v-if="scope.row.ip">{{ scope.row.ip }}</div>
                <div v-else="scope.row.url">{{ scope.row.url }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="deptName" label="管理部门" min-width="120" align="center">
            </el-table-column>
            <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" />
            <el-table-column fixed="right" label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" link @click="removeAsset(scope.row.id)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-assets">
            <el-empty :image-size="60" description="暂无选择资产" />
          </div>
        </div>
      </div>

      <!-- 通知策略 -->
      <div class="form-section">
        <div class="section-title">通知策略</div>
        <el-form-item label="启用短信通知" prop="notificationStatus">
          <el-radio-group v-model="formData.notificationStatus">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'0'">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>

    <!-- 资产选择对话框 -->
    <select-assets v-model:visible="transferDialog.visible" :title="'选择资产'"
      :selected-assets="transferDialog.selectedAssets" @selected="handleAssetsSelected" />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import SelectAssets from "@/components/AssetsManage/SelectAssets.vue"
import type { assetsPageVO } from '@/api/assets_management/details/assets'
import InventoryAPI, { AssetInventoryForm } from "@/api/assets_management/assets_inventory/index"
import { useUserStore } from '@/store/modules/user';
import { formatLocalDateTime } from '@/utils/dateUtils'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '新增资产盘点'
  },
  id: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'submitted'])
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})
// 表单相关
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitLoading = ref(false)
const formData = reactive<any>({
  inventoryName: '',
  assetIds: [],
  status: '0', // 默认待进行
  type: '1', // 默认即时盘点
  inventoryCycle: '',
  deadline: '',
  startTime: formatLocalDateTime(new Date(),'datetime'), // 开始时间
  notificationStatus: '0', // 默认不启用短信通知
  createTime: formatLocalDateTime(new Date(),'datetime'), // 默认当前时间，格式为 YYYY-MM-DD HH:mm:ss
})

const rules = reactive({
  inventoryName: [{ required: true, message: '请输入盘点任务名称', trigger: 'blur' },
    { max: 10, message: '任务名称不能超过10个字符', trigger: 'blur' }],
  startTime: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  updateTime: [{ required: true, message: '请选择截止时间', trigger: 'change' }],
})

// 资产选择相关
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

const selectedAssetsDetails = ref<assetsPageVO[]>([])

const getAssetTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '服务器',
    '2': '网络设备',
    '3': '安全设备',
    '10': '信息系统'
  }
  return typeMap[type] || '未知类型'
}

const openSelectAssets = () => {
  transferDialog.visible = true
}

const handleAssetsSelected = (data) => {
  console.log('选择资产回调数据：', data);

  // 正确解构参数命名
  const { selectedIds, selectedAssets } = data;

  // 确保更新到表单数据中
  formData.assetIds = selectedIds || [];
  transferDialog.selectedAssets = selectedIds || [];
  selectedAssetsDetails.value = selectedAssets || [];

  console.log('更新后的资产列表：', selectedAssetsDetails.value);
}

const removeAsset = (assetId: number) => {
  formData.assetIds = formData.assetIds.filter(id => id !== assetId)
  transferDialog.selectedAssets = transferDialog.selectedAssets.filter(id => id !== assetId)
  selectedAssetsDetails.value = selectedAssetsDetails.value.filter(asset => asset.id !== assetId)
}

const getFormData = async () => {
  console.log('获取表单数据', props.id)
  if (!props.id) return

  loading.value = true
  try {
    const response = await InventoryAPI.getFormData(props.id)
    const data = response
    // 填充表单数据
    formData.inventoryName = data.inventoryName || ''
    formData.deadline = data.deadline || ''
    formData.status = data.status || '0'
    formData.type = data.type || '1'
    formData.inventoryCycle = data.inventoryCycle || ''
    formData.notificationStatus = data.notificationStatus || '0'
    formData.startTime = data.startTime || '' // 使用开始时间
    formData.createTime = data.createTime || formatLocalDateTime(new Date(),'datetime')
    formData.assetIds = data.assetIds || []
    formData.updateTime = data.updateTime || ''
    // 加载已选择的资产信息
    if (data.assetsList && data.assetsList.length > 0) {
      // 提取资产ID列表并过滤掉undefined值
      const extractedAssetIds = data.assetsList.map(asset => asset.assetsId).filter((id): id is number => id !== undefined)
      console.log('从assetsList中提取的资产ID:', extractedAssetIds)

      formData.assetIds = extractedAssetIds
      transferDialog.selectedAssets = extractedAssetIds

      // 转换资产列表为前端需要的格式
      selectedAssetsDetails.value = data.assetsList.map(asset => ({
        id: asset.assetsId,
        name: asset.name || '',
        type: typeof asset.type === 'number' ? asset.type : Number(asset.type) || 0,
        ip: asset.ip || '',
        url: asset.url || '',
        deptId: typeof asset.deptId === 'number' ? asset.deptId : asset.deptId ? Number(asset.deptId) : undefined,
        deptName: asset.deptName || '',
        managerName: asset.managerName || '',
        ownerName: asset.ownerName || '',
        mobile: asset.mobile || '',
        port: asset.port || '',
        status: asset.status || '',
        os: asset.os || '',
        notes: asset.notes || ''
      }))

      console.log('处理后的选择资产详情:', selectedAssetsDetails.value)
    } else {
      formData.assetIds = []
      transferDialog.selectedAssets = []
      selectedAssetsDetails.value = []
    }

  } catch (error) {
    console.error('获取盘点数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      // 验证开始时间和截止时间
      if (new Date(formData.startTime) >= new Date(formData.deadline)) {
        ElMessage.warning('开始时间必须早于截止时间')
        return
      }

      if (formData.assetIds.length === 0) {
        ElMessage.warning('请选择至少一个资产')
        return
      }

      submitLoading.value = true

      try {
        const userStore = useUserStore();
        const userInfo = await userStore.getUserInfo();
        const currentAssetIds = formData.assetIds;

        // 准备提交数据
        const submitData: AssetInventoryForm = {
          ...formData,
          createUserId: userInfo.userId,
          createDeptId: userInfo.deptId,
          assetIds: currentAssetIds,
          createTime: props.id ? formData.createTime : formatLocalDateTime(new Date(),'datetime'), 
        }

        if (props.id) {
          // 更新
          submitData.id = props.id
          await InventoryAPI.update(props.id, submitData)
          ElMessage.success('更新成功')
        } else {
          // 新增
          await InventoryAPI.add(submitData)
          ElMessage.success('添加成功')
        }

        emit('submitted')
        handleClose()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
  selectedAssetsDetails.value = []
  formData.assetIds = []
  transferDialog.selectedAssets = []
}

watch(
  () => props.visible,
  async (visible) => {
    if (visible) {
      // 弹窗打开时
      if (props.id) {
        // 如果是编辑模式，则加载数据
        console.log('准备加载数据', props.id)
        await getFormData()
        console.log('加载数据完成')
      } else {
        // 如果是新增模式，则初始化表单
        console.log('初始化新增表单')
        formRef.value?.resetFields()
        selectedAssetsDetails.value = []
        formData.assetIds = []
        transferDialog.selectedAssets = []
      }
    }
  },
  { immediate: true }
)


onMounted(async () => {
  console.log('props.id', props.id)
  if (props.id !== undefined) {
    await getFormData()
  }
})
</script>

<style scoped>
.inventory-form {
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.assets-section {
  margin-top: 16px;
}

.empty-assets {
  margin-top: 12px;
  padding: 12px;
  min-height: 120px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.el-empty) {
  padding: 12px;
}

:deep(.el-empty__description) {
  margin-top: 8px;
  font-size: 13px;
}

.mb-4 {
  margin-bottom: 16px;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

.w-full {
  width: 100%;
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex;
  gap: 30px;
}

:deep(.el-table) {
  margin-top: 12px;
}

:deep(.el-tag) {
  margin: 0 4px;
}
</style>
