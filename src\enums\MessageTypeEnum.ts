import request from "@/utils/request";

const MESSAGES_BASE_URL = "/api/v1/messagess";

class messagesAPI {
  /** 获取安全公告管理分页数据 */
  static getPage(queryParams?: messagesPageQuery) {
      return request<any, PageResult<messagesPageVO[]>>({
          url: `${MESSAGES_BASE_URL}/page`,
          method: "get",
          params: queryParams,
      });
  }

  /**
   * 获取个人
   */
  static getUserInfo(queryParams?: messagesPageQuery) {
      return request<any, PageResult<messagesPageVO[]>>({
          url: `${MESSAGES_BASE_URL}/pageNotice`,
          method: "get",
          params: queryParams,
      });
  }
  
  
  /**
   * 获取安全公告管理表单数据
   *
   * @param id messagesID
   * @returns messages表单数据
   */
  static getFormData(id: number) {
      return request<any, messagesForm>({
          url: `${MESSAGES_BASE_URL}/${id}/form`,
          method: "get",
      });
  }

  /** 添加安全公告管理*/
  static add(data: messagesForm) {
      return request({
          url: `${MESSAGES_BASE_URL}`,
          method: "post",
          data: data,
      });
  }

  /**
   * 新增安全公告管理（new）
   *
   * @param id messagesID
   */
  static addMessage(data: messagesForm) {
      return request({
          url: `${MESSAGES_BASE_URL}/send`,
          method: "post",
          data: data,
      });
  }

  /**
   * 更新安全公告管理
   *
   * @param id messagesID
   * @param data messages表单数据
   */
  static update(id: number, data: messagesForm) {
      return request({
          url: `${MESSAGES_BASE_URL}/${id}`,
          method: "put",
          data: data,
      });
  }

  /**
   * 批量删除安全公告管理，多个以英文逗号(,)分割
   *
   * @param ids 安全公告管理ID字符串，多个以英文逗号(,)分割
   */
  static deleteByIds(ids: string) {
      return request({
          url: `${MESSAGES_BASE_URL}/${ids}`,
          method: "delete",
      });
  }

  /**
   * 获取消息通知列表
   *
   * @returns 消息通知列表
   */
  static getMessageList(queryParams: MessageQuery) {
    return request<any, Message[]>({
      url: `${MESSAGES_BASE_URL}/message`,
      method: "get",
      data: queryParams,
    });
  }

  /** 
   * 待办状态更新
  */
  static updateMessageStatus(id: number) {
    return request({
      url: `${MESSAGES_BASE_URL}/isRead/${id}`,
      method: "get",
    });
  }

  /**
   * 消息已读（new）
   */
  static updateMessageRead(id: number) {
    return request({
      url: `${MESSAGES_BASE_URL}/isRead_new/${id}`,
      method: "get",
    });
  }

  /**
   * 个人公告
   */
  static getPersonMessageList() {
    return request<any, []>({
      url: `${MESSAGES_BASE_URL}/me`,
      method: "get",
    });
  }
}

export default messagesAPI;

/** 安全公告管理分页查询参数 */
export interface messagesPageQuery extends PageQuery {
  /** 公告id */
  id?: number;
  /** 公告类型 */
  type?: string;
  /** 公告标题 */
  title?: string;
  /** 公告内容 */
  content?: string;
  /** 公告发布时间 */
  createTime?: [string,string];
  /** 公告更新时间 */
  updateTime?: string;
  /** 已读未读 */
  isRead?: number;
  /** 用户id */
  userId?: number;
  /** 发送者id */
  sendId?: number;
  /** 通知类型 */
  noticeType?: string;
  /** 优先级 */
  level?: number;
}

/** 安全公告管理表单对象 */
export interface messagesForm {
  id?: number;
  /** 公告类型 */
  type?:  string;
  /** 公告标题 */
  title?:  string;
  /** 公告内容 */
  content?:  string;
  /** 公告发布时间 */
  createTime?:  string;
  /** 公告更新时间 */
  updateTime?:  string;
  /** 已读未读 */
  isRead?:  number;
  /** 接收类型 */
  receiverType?:  string;
  /** 用户id */
  userId?:  number;
  /** 发送者id */
  sendId?:  number;
  /** 通知类型 */
  noticeType?: string;
  /** 优先级 */
  level?: number;
  /** 用户组Id */
  groupId?: number;
  /** 公告文件Url */
  fileUrl?: string;
}

/** 安全公告管理分页对象 */
export interface messagesPageVO {
  /** 公告id */
  id?: number;
  /** 公告类型 */
  type?: number;
  /** 公告标题 */
  title?: string;
  /** 公告内容 */
  content?: string;
  /** 公告发布时间 */
  createTime?: string;
  /** 公告更新时间 */
  updateTime?: string;
  /** 已读未读 */
  isRead?: number;
  /** 用户id */
  userId?: number;
  /** 发送者id */
  sendId?: number;
    /** 通知类型 */
    noticeType?: string;
    /** 优先级 */
    level?: number;
}

/* 消息类型枚举 */
export const enum MessageTypeEnum {
  /* 消息 */
  MESSAGE = "MESSAGE",
  /* 通知 */
  NOTICE = "NOTICE",
  /* 待办 */
  TODO = "TODO",
}

export const MessageTypeLabels = {
  [MessageTypeEnum.MESSAGE]: "消息",
  [MessageTypeEnum.NOTICE]: "通知",
  [MessageTypeEnum.TODO]: "待办",
};

export interface Message {
  id?: number;
  type: MessageTypeEnum;
  level: number;
  title: string;
  content: string;
  createdAt: string;
  updatedAt: string;
  isRead: boolean;
  noticeType: string;
}

export interface MessageQuery {
  type?: MessageTypeEnum;
  isRead?: boolean;
  pageNum: number;
  pageSize: number;
}
