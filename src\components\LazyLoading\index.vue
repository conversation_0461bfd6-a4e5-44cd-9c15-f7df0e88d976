<template>
  <el-autocomplete
    v-model="query"
    :fetch-suggestions="fetchSuggestions"
    :placeholder="placeholder"
    :disabled="disabled"
    clearable
    @select="handleSelect"
  >
    <template #default="{ item }">
      <div>{{ item.username }} ({{ item.mobile }})({{ item.deptName }})</div>
    </template>
    <template #append>
      <slot name="append"></slot>
    </template>
  </el-autocomplete>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import UserAPI, { UserForm, UserPageQuery, UserPageVO } from "@/api/user";
import { useWatchModel } from "@/plugins/useWatchModel";

const props = defineProps({
  code: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [String, Number],
  },
  placeholder: {
    type: String,
    default: "请输入用户名、手机号或者部门来筛选",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  onModelChange: {
    type: Function,
    default: null,
  },
});

const emits = defineEmits(["update:modelValue"]);

const query = ref('');
const suggestions = ref<UserForm[]>([]);
const selectedValue = ref<string | number | undefined>();
const queryParams = reactive<UserPageQuery>({
  pageNum: 1,
  pageSize: 10,
  keywords: ''
});

// 使用自定义 hook 监听 selectedValue 的变化
useWatchModel(selectedValue, (newValue, oldValue) => {
  if (typeof props.onModelChange === 'function') {
    props.onModelChange(newValue, oldValue); // 调用父组件传递的函数
  }
});

watch(() => props.modelValue, (newModelValue) => {
  if (newModelValue == undefined) {
    selectedValue.value = undefined;
  } else {
    selectedValue.value = newModelValue;
  }
});

const fetchSuggestions = async (queryString: string, cb: any) => {
  if (queryString) {
    try {
      if (props.code === 'user') {
        queryParams.keywords = queryString;
        const data = await UserAPI.getPage(queryParams);
        suggestions.value = data.list;
        cb(suggestions.value);
      }else if (props.code === 'manager') {
        ElMessage.error('暂未开放');
      }
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  } else {
    suggestions.value = [];
    cb(suggestions.value);
  }
};

const handleSelect = (item: UserForm) => {
  query.value = `${item.nickname} (${item.mobile})`;
  selectedValue.value = item.id;
  emits("update:modelValue", item.id);
};

const fetchUserDetail = async (id: any) => {
  try {
    if (props.code === 'user') {
      const user = await UserAPI.getFormData(id);

      query.value = `${user.nickname} (${user.mobile})`;
    } else if (props.code === 'manager') {
      ElMessage.error('暂未开放');
    }
  }
  catch (error) {
    console.log('加载用户详情失败', error);
  }
};


onBeforeMount(() => {
  if (props.code === 'user') {
    if (props.modelValue !== undefined) {
      selectedValue.value = props.modelValue;
      const userId = Number(props.modelValue);
        if (!isNaN(userId)) {
          fetchUserDetail(userId).catch(error => {
            console.error('加载用户详情失败', error);
            ElMessage.error('加载用户详情失败');
          });
        }
    }
  } else if (props.code === 'manager') {
    ElMessage.error('暂未开放');
  }
});
</script>

<style scoped>
/* 添加一些样式以美化组件 */
.el-input-group__append .el-button {
  margin: 0;
  padding: 0 10px;
  height: 100%;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
</style>
