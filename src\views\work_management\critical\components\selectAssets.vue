<template>
    <el-dialog 
      v-model="dialogVisible" 
      :title="title"
      width="1200px"
      :close-on-click-modal="false"
      destroy-on-close
      class="select-assets-dialog"
    >
      <div class="dialog-content">
        <!-- 左侧资产列表 -->
        <div class="assets-container">
          <!-- 搜索区域 -->
          <div class="search-bar">
            <el-form :inline="true" :model="queryParams">
              <!-- 添加地址搜索 -->
              <el-form-item>
                <el-input
                  v-model="queryParams.url"
                  placeholder="请输入资产地址搜索"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <i-ep-location />
                  </template>
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-input
                  v-model="queryParams.name"
                  placeholder="请输入资产名称搜索"
                  clearable
                  @keyup.enter="handleSearch"
                >
                  <template #prefix>
                    <i-ep-search />
                  </template>
                </el-input>
              </el-form-item>
  
              <el-form-item>
                <el-tree-select
                  v-model="queryParams.deptId"
                  placeholder="请选择管理部门"
                  :data="deptOptions"
                  clearable
                  filterable
                  check-strictly
                  :render-after-expand="false"
                  @change="handleSearch"
                />
              </el-form-item>
  
              <el-form-item>
                <el-input
                  v-model="queryParams.ownerName"
                  placeholder="请输入管理员"
                  clearable
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
  
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
  
          <!-- 表格区域 -->
          <div class="table-wrapper">
            <el-table
              ref="tableRef"
              :data="assetsList"
              @selection-change="handleSelectionChange"
              v-loading="loading"
              :row-key="(row) => row.id"
              height="450px"
            >
              <el-table-column type="selection" width="55" />
             
              <el-table-column prop="type" label="资产类型" width="120">
                <template #default="{ row }">
                  <el-tag>{{ getAssetTypeName(row.type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="资产名称" min-width="200">
                <template #default="{ row }">
                  <div class="asset-name-cell">
                    {{ row.name }}
                    <el-tag 
                      size="small" 
                      :type="row.status === '1' ? 'success' : (row.status === '0' ? 'danger' : 'info')"
                    >
                      {{ row.status === '1' ? '正常' : (row.status === '0' ? '异常' : '废弃') }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="address" label="地址" min-width="180">
                <template #default="{ row }">
                  <el-tooltip 
                    :content="row.address" 
                    placement="top" 
                    :show-after="500"
                    :hide-after="0"
                  >
                    <span class="address-cell">{{ row.address }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="管理员" prop="ownerName" width="120" />
              <el-table-column label="管理部门" width="180">
                <template #default="{ row }">
                  <!-- <Dictmap v-model="row.deptId" code="dept0x0" /> -->
                  <span>{{ getDeptName(row.deptId) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
  
          <!-- 分页区域 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 30, 50]"
              :total="total"
              layout="total, sizes, prev, pager, next"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
  
        <!-- 右侧已选择列表 -->
        <div class="selected-container">
          <div class="selected-header">
            <h3>已选择资产 ({{ persistentSelectedIds.length }})</h3>
            <el-button 
              type="text" 
              @click="clearSelected"
              :disabled="!persistentSelectedIds.length"
            >
              清空全部
            </el-button>
          </div>
  
          <div class="selected-wrapper">
            <el-scrollbar height="510px" class="selected-scrollbar">
              <div class="selected-list">
                <el-empty v-if="!persistentSelectedIds.length" description="暂无选择资产" />
                <template v-else>
                  <div 
                    v-for="id in persistentSelectedIds" 
                    :key="id" 
                    class="selected-item"
                  >
                    <div class="selected-info">
                      <div class="selected-name">
                        <span class="truncate-text">{{ getAssetName(id) }}</span>
                        <el-tag 
                          size="small" 
                          :type="getAssetStatus(id) === '1' ? 'success' : (getAssetStatus(id) === '0' ? 'danger' : 'info')"
                        >
                          {{ getAssetStatus(id) === '1' ? '正常' : (getAssetStatus(id) === '0' ? '异常' : '废弃') }}
                        </el-tag>
                      </div>
                      <div class="selected-detail">
                        <div class="detail-row">
                          <span class="detail-label">类型:</span>
                          <el-tag size="small" class="asset-type-tag">
                            {{ getAssetTypeName(Number(getAssetType(id))) }}
                          </el-tag>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">管理员:</span>
                          <span class="detail-value truncate-text">{{ getAssetOwner(id) || '-' }}</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">地址:</span>
                          <span class="detail-value address-text">{{ getAssetAddress(id) || '-' }}</span>
                        </div>
                      </div>
                    </div>
                    <el-button 
                      type="danger" 
                      link
                      class="delete-btn"
                      @click="removeSelected(id)"
                    >
                      <i-ep-delete />
                    </el-button>
                  </div>
                </template>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { ref, reactive, watch, onMounted, nextTick, PropType } from 'vue'
  import { ElMessage } from 'element-plus'
  import assetsAPI from "@/api/assets_management/details/assets"
  import DeptAPI from "@/api/dept"
  import { useDictStore } from '@/store/modules/dictStore' // 导入dictStore
  const dictStore = useDictStore()
  
  interface Asset {
    id: number
    name: string
    status: string
    ownerName: string
    deptId: string | number
    type: number
    url?: string
    address?: string
  }
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择资产'
    },
    assetsType: {
      type: String,
      default: 'assetsAll'
    },
    selectedAssets: {
      type: Array as PropType<number[]>,
      default: () => []
    },
    selectedAssetsData: {
      type: Array as PropType<Asset[]>,
      default: () => []
    }
  })
  
  const emit = defineEmits(['update:visible', 'selected'])
  
  const getAssetTypeName = (type: number) => {
    const typeMap: Record<number, string> = {
      '-1': '未知类型',
      1: '服务器',
      3: '安全设备',
      2: '网络设备',
      4: '物联网设备',
      10: '信息系统'
    }
    return typeMap[type] || '未知类型'
  }
  
  // 表格引用和基础数据
  const tableRef = ref()
  const dialogVisible = ref(false)
  // 持久化的选择结果，不会因为翻页而丢失
  const persistentSelectedIds = ref<number[]>([])
  const assetsList = ref<Asset[]>([])
  const loading = ref(false)
  const total = ref(0)
  const deptOptions = ref<any[]>([])
  
  // 禁用选择变化处理标志，避免重复触发
  const disableSelectionChange = ref(false)
  
  // 缓存所有看到过的资产数据，键为ID，值为资产对象
  const assetsCache = ref<Map<number, Asset>>(new Map())
  
  // 页面缓存键生成
  const getPageCacheKey = (params: any) => {
    return `${params.pageNum}_${params.pageSize}_${params.name || ''}_${params.url || ''}_${params.type || ''}_${params.deptId || ''}_${params.ownerName || ''}`
  }
  
  // 部门数据缓存
  const deptMap = ref<Record<string | number, string>>({})
  const deptLoading = ref(false)
  
  // 获取部门名称的函数
  const getDeptName = (deptId: string | number): string => {
    if (!deptId) return '-'
    return deptMap.value[deptId] || `部门${deptId}`
  }
  
  // 加载部门映射数据
  const loadDeptMappings = async () => {
    if (deptLoading.value || Object.keys(deptMap.value).length > 0) return
    
    try {
      deptLoading.value = true
      console.log('开始加载部门映射数据')
      
      // 使用dictStore加载部门数据
      const options = await dictStore.fetchOptions('dept0x0')
      
      // 递归处理部门树，提取ID和名称
      const processDeptTree = (depts: any[]) => {
        depts.forEach(dept => {
          if (dept.value !== undefined && dept.label) {
            deptMap.value[dept.value] = dept.label
          }
          
          if (dept.children && dept.children.length > 0) {
            processDeptTree(dept.children)
          }
        })
      }
      
      if (Array.isArray(options)) {
        processDeptTree(options)
      }
      
      console.log('部门映射数据加载完成，共', Object.keys(deptMap.value).length, '条记录')
    } catch (error) {
      console.error('加载部门映射数据失败:', error)
    } finally {
      deptLoading.value = false
    }
  }
  
  // 获取部门选项 - 现有函数保持不变
  const loadDeptOptions = async () => {
    try {
      const data = await DeptAPI.getOptions()
      deptOptions.value = data
      
      // 同时预加载部门映射数据
      await loadDeptMappings()
    } catch (error) {
      console.error('加载部门列表失败:', error)
      ElMessage.error('加载部门列表失败')
    }
  }
  
  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    status: 1,
    name: '',
    url: '',
    type: 10,
    deptId: undefined,
    ownerName: '',
    assetsType: props.assetsType
  })
  
  
  // 初始化资产缓存 - 从传入的已选数据
  const initAssetsCache = () => {
    try {
      // 从传入的数据中获取
      if (props.selectedAssetsData && props.selectedAssetsData.length > 0) {
        props.selectedAssetsData.forEach(asset => {
          if (asset && asset.id) {
            assetsCache.value.set(asset.id, {
              id: asset.id,
              name: asset.name || `资产${asset.id}`,
              status: asset.status || '',
              ownerName: asset.ownerName || '',
              deptId: asset.deptId || '',
              type: typeof asset.type === 'number' ? asset.type : -1,
              address: asset.address || asset.url || ''
            })
          }
        })
      }
      
      // 对于未获取到详情的资产ID，使用默认值
      persistentSelectedIds.value.forEach(id => {
        if (!assetsCache.value.has(id)) {
          assetsCache.value.set(id, {
            id,
            name: `资产${id}`,
            status: '',
            ownerName: '',
            deptId: '',
            type: -1,
            address: ''
          })
        }
      })
      
      console.log('已初始化资产缓存，当前缓存数量:', assetsCache.value.size)
    } catch (error) {
      console.error('初始化资产缓存失败:', error)
    }
  }
  
  // 搜索处理
  const handleSearch = () => {
    queryParams.pageNum = 1
    loadAssetsList()
  }
  
  // 分页大小变化
  const handleSizeChange = (val: number) => {
    queryParams.pageSize = val
    queryParams.pageNum = 1
    loadAssetsList()
  }
  
  // 页码变化
  const handleCurrentChange = (val: number) => {
    queryParams.pageNum = val
    loadAssetsList()
  }
  
  // 设置表格选中状态
  const setTableSelection = () => {
    nextTick(() => {
      if (!tableRef.value) return
      
      try {
        // 禁用选择变化处理，避免循环触发
        disableSelectionChange.value = true
        
        // 先清除当前表格的选择状态
        tableRef.value.clearSelection()
        
        // 在当前页中查找已选资产并选中
        assetsList.value.forEach(asset => {
          if (persistentSelectedIds.value.includes(asset.id)) {
            tableRef.value.toggleRowSelection(asset, true)
          }
        })
      } finally {
        // 恢复选择变化处理
        disableSelectionChange.value = false
      }
    })
  }
  
  // 处理表格选择变化 - 简化版，只关注当前页面的变更
  const handleSelectionChange = (selection: Asset[]) => {
    // 如果禁用了选择变化处理，直接返回
    if (disableSelectionChange.value) return
    
    // 当前页面上的所有资产ID
    const currentPageIds = assetsList.value.map(asset => asset.id)
    
    // 选中的资产ID
    const selectedIds = selection.map(item => item.id)
    
    // 当前页面上取消选择的资产ID
    const deselectedIds = currentPageIds.filter(id => 
      persistentSelectedIds.value.includes(id) && !selectedIds.includes(id)
    )
    
    // 当前页面上新选择的资产ID
    const newSelectedIds = selectedIds.filter(id => 
      !persistentSelectedIds.value.includes(id)
    )
    
    // 更新持久化选择
    if (deselectedIds.length > 0) {
      persistentSelectedIds.value = persistentSelectedIds.value.filter(id => 
        !deselectedIds.includes(id)
      )
    }
    
    if (newSelectedIds.length > 0) {
      // 添加新选择的ID
      persistentSelectedIds.value = [...persistentSelectedIds.value, ...newSelectedIds]
      
      // 更新缓存
      selection
        .filter(asset => newSelectedIds.includes(asset.id))
        .forEach(asset => {
          assetsCache.value.set(asset.id, { ...asset })
        })
    }
  }
  
  // 清空选择
  const clearSelected = () => {
    persistentSelectedIds.value = []
    
    // 清除表格选择
    if (tableRef.value) {
      tableRef.value.clearSelection()
    }
  }
  
  // 移除单个选择
  const removeSelected = (id: number) => {
    // 从持久化选择中移除
    persistentSelectedIds.value = persistentSelectedIds.value.filter(item => item !== id)
    
    // 如果当前页面有这个资产，取消选择
    const row = assetsList.value.find(asset => asset.id === id)
    if (row && tableRef.value) {
      disableSelectionChange.value = true
      try {
        tableRef.value.toggleRowSelection(row, false)
      } finally {
        disableSelectionChange.value = false
      }
    }
  }
  
  // 获取资产信息的方法 - 优先从缓存获取
  const getAssetName = (id: number) => {
    if (assetsCache.value.has(id)) {
      return assetsCache.value.get(id)!.name
    }
    return `资产${id}`
  }
  
  const getAssetStatus = (id: number) => {
    if (assetsCache.value.has(id)) {
      return assetsCache.value.get(id)!.status
    }
    return ''
  }
  
  const getAssetType = (id: number) => {
    if (assetsCache.value.has(id)) {
      return assetsCache.value.get(id)!.type
    }
    return -1
  }
  
  const getAssetAddress = (id: number) => {
    if (assetsCache.value.has(id)) {
      const cached = assetsCache.value.get(id)!
      return cached.address || cached.url || ''
    }
    return ''
  }
  
  const getAssetOwner = (id: number) => {
    if (assetsCache.value.has(id)) {
      return assetsCache.value.get(id)!.ownerName
    }
    return ''
  }
  
  const getAssetDept = (id: number) => {
    if (assetsCache.value.has(id)) {
      return assetsCache.value.get(id)!.deptId
    }
    return ''
  }
  
  // 加载资产列表，不使用本地缓存以确保数据最新
  const loadAssetsList = async () => {
    loading.value = true;
    try {
      // 从API获取数据
      console.log('加载资产列表:', queryParams);
      const { list, total: totalCount } = await assetsAPI.getPageAll(queryParams);
      
      const processedAssets = list.map(asset => {
        const processedAsset = {
          id: asset.id!,
          name: asset.name!,
          status: asset.status || '',
          ownerName: asset.ownerName || '',
          deptId: asset.deptId || '',
          type: typeof asset.type === 'number' ? asset.type : 0,
          address: asset.url || ''
        };
        
        // 更新资产缓存
        assetsCache.value.set(processedAsset.id, { ...processedAsset });
        
        return processedAsset;
      });
      
      assetsList.value = processedAssets;
      total.value = totalCount;
      
      // 设置表格选中状态
      setTableSelection();
    } catch (error) {
      console.error('加载资产列表失败:', error);
      ElMessage.error('加载资产列表失败');
    } finally {
      loading.value = false;
    }
  }
  
  // 获取选中资产的详细信息
  const fetchSelectedAssetDetails = async () => {
    // 仅获取缓存中没有的资产详情
    const idsToFetch = persistentSelectedIds.value.filter(id => 
      !assetsCache.value.has(id) || 
      !assetsCache.value.get(id)?.name || 
      assetsCache.value.get(id)?.name === `资产${id}`
    );
    
    if (idsToFetch.length === 0) return;
    
    try {
      // 批量获取资产详情
      const details = await Promise.all(idsToFetch.map(id => assetsAPI.getDetail(id)));
      console.log('获取资产详情:', details);
      details.forEach((detail, index) => {
        const id = idsToFetch[index];
        if (detail) {
          assetsCache.value.set(id, {
            id,
            name: detail.name || `资产${id}`,
            status: detail.status || '',
            ownerName: detail.ownerName || '',
            deptId: detail.deptId || '',
            type: typeof detail.type === 'number' ? detail.type : -1,
            address: detail.url || detail.address || ''
          });
        }
      });
      
      console.log('已更新资产详情:', idsToFetch);
    } catch (error) {
      console.error('获取资产详情失败:', error);
    }
  }
  
  // 重置查询
  const resetQuery = () => {
    queryParams.name = ''
    queryParams.url = ''
    queryParams.type = undefined
    queryParams.deptId = undefined
    queryParams.ownerName = ''
    handleSearch()
  }
  
  // 监听对话框显示状态
  watch(() => props.visible, async (val) => {
    dialogVisible.value = val
    if (val) {
      console.log('对话框打开，初始化选择:', props.selectedAssets)
          // 确保部门映射数据已加载
      if (Object.keys(deptMap.value).length === 0) {
        await loadDeptMappings()
      }
      
      // 初始化选择状态和缓存
      persistentSelectedIds.value = [...props.selectedAssets]
      queryParams.pageNum = 1
      queryParams.pageSize = 10
      
      // 初始化资产缓存
      initAssetsCache()
      
      // 加载资产列表
      await loadAssetsList()
      
      // 获取已选择资产的详情
      await fetchSelectedAssetDetails()
    }
  })
  
  // 监听持久化选择变化，当有新选择时获取详情
  watch(() => persistentSelectedIds.value.length, async (newCount, oldCount) => {
    if (newCount > oldCount) {
      await fetchSelectedAssetDetails()
    }
  })
  
  // 监听对话框关闭
  watch(() => dialogVisible.value, (val) => {
    if (!val) {
      emit('update:visible', false)
    }
  })
  
  // 取消选择
  const handleCancel = () => {
    dialogVisible.value = false
  }
  
  // 确认选择
  const handleConfirm = () => {
    // 收集已选资产的详情数据
    const selectedAssetsData = persistentSelectedIds.value.map(id => {
      if (assetsCache.value.has(id)) {
        return assetsCache.value.get(id)
      }
      
      return { 
        id, 
        name: getAssetName(id),
        status: getAssetStatus(id),
        ownerName: getAssetOwner(id),
        deptId: getAssetDept(id),
        type: getAssetType(id),
        address: getAssetAddress(id)
      }
    }).filter(Boolean) as Asset[]
    
    emit('selected', {
      selectedIds: persistentSelectedIds.value,
      selectedAssets: selectedAssetsData
    })
    
    console.log('确认选择:', persistentSelectedIds.value.length, '个资产')
    ElMessage.success(`已选择${persistentSelectedIds.value.length}个资产`)
    dialogVisible.value = false
  }
  
  onMounted(async () => {
    await loadDeptOptions() // 这会同时调用loadDeptMappings
  })
  </script>
  
  <style scoped>
  .select-assets-dialog :deep(.el-dialog__body) {
    height: 600px;
    padding: 20px;
  }
  
  .dialog-content {
    display: flex;
    gap: 24px;
    height: 100%;
  }
  
  /* 左侧容器 */
  .assets-container {
    flex: 2;
    display: flex;
    flex-direction: column;
    min-width: 0;
  }
  
  .search-bar {
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--el-fill-color-blank);
    border-radius: 4px;
  }
  
  .search-bar :deep(.el-form--inline) {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-right: 0;
  }
  
  .search-bar :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 0;
  }
  
  .search-bar :deep(.el-form-item__content) {
    margin-left: 0 !important;
  }
  
  .search-bar :deep(.el-input),
  .search-bar :deep(.el-select),
  .search-bar :deep(.el-tree-select) {
    width: 200px;
  }
  
  .table-wrapper {
    flex: 1;
    overflow: auto;
  }
  
  .pagination-container {
    margin-top: 16px;
    padding: 10px 0;
    background-color: var(--el-bg-color);
    border-top: 1px solid var(--el-border-color-lighter);
  }
  
  /* 右侧容器 */
  .selected-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-left: 1px solid var(--el-border-color-light);
    padding-left: 24px;
    min-width: 0;
  }
  
  .selected-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .selected-header h3 {
    margin: 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
  }
  
  .selected-wrapper {
    flex: 1;
    overflow: hidden;
  }
  
  .selected-list {
    padding: 0 6px 6px 0;
  }
  
  /* 优化后的已选列表项样式 */
  .selected-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 6px 10px;
    border-radius: 4px;
    background-color: var(--el-fill-color-light);
    margin-bottom: 4px;
  }
  
  .selected-info {
    flex: 1;
    margin-right: 8px;
    min-width: 0;
  }
  
  .selected-name {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
    font-weight: 500;
  }
  
  .truncate-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
  }
  
  .selected-detail {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  .detail-row {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-bottom: 2px;
    flex-wrap: nowrap;
  }
  
  .detail-label {
    color: var(--el-text-color-secondary);
    font-weight: normal;
    min-width: 45px;
  }
  
  .detail-value {
    color: var(--el-text-color-regular);
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .address-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: calc(100% - 45px);
  }
  
  .ml-auto {
    margin-left: auto;
  }
  
  .delete-btn {
    padding: 4px;
    margin-right: -4px;
    margin-top: -4px;
  }
  
  .asset-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .asset-type-tag {
    height: 20px;
    line-height: 20px;
  }
  
  :deep(.el-dialog__footer) {
    padding-top: 10px;
    margin-top: 0;
    border-top: 1px solid var(--el-border-color-light);
  }
  
  :deep(.el-table) {
    height: 100% !important;
  }
  
  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }
  
  :deep(.el-tag) {
    border-radius: 4px;
  }
  
  .address-cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
  </style>
  