import request from "@/utils/request";

const COMMENTS_BASE_URL = "/api/v1/commentss";

class commentsAPI {
    /** 获取评论分页数据 */
    static getPage(queryParams?: commentsPageQuery) {
        return request<any, PageResult<commentsPageVO[]>>({
            url: `${COMMENTS_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    static getPage_step5(queryParams?: commentsPageQuery) {
      return request<any, PageResult<commentsPageVO[]>>({
          url: `${COMMENTS_BASE_URL}/page_step5`,
          method: "get",
          params: queryParams,
      });
  }
    /**
     * 获取评论表单数据
     *
     * @param id commentsID
     * @returns comments表单数据
     */
    static getFormData(id: number) {
        return request<any, commentsForm>({
            url: `${COMMENTS_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加评论*/
    static add(data: commentsForm) {
        return request({
            url: `${COMMENTS_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新评论
     *
     * @param id commentsID
     * @param data comments表单数据
     */
    static update(id: number, data: commentsForm) {
        return request({
            url: `${COMMENTS_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除评论，多个以英文逗号(,)分割
     *
     * @param ids 评论ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${COMMENTS_BASE_URL}/${ids}`,
            method: "delete",
        });
    }
}

export default commentsAPI;

/** 评论分页查询参数 */
export interface commentsPageQuery extends PageQuery {
    /** 评价人id */
    commentBy?: number;
    /** 评价内容 */
    commentContent?: string;
    /** 评价类型 */
    commentType?: number;
    /** 创建时间 */
    createTime?: [string, string];
    /** 评价id */
    id?: number;
    /** 评价目标id */
    parentCode?: string;
    /** 更新时间 */
    updateTime?: [string, string];

    step?: number;
}

/** 评论表单对象 */
export interface commentsForm {
    /** 评价人id */
    commentBy?:  number;
    /** 评价内容 */
    commentContent?:  string;
    /** 评价类型 */
    commentType?:  number;
    /** 创建时间 */
    createTime?:  Date;
    /** 评价id */
    id?:  number;
    /** 评价目标id */
    parentCode?:  string;
    /** 更新时间 */
    updateTime?:  Date;
}

/** 评论分页对象 */
export interface commentsPageVO {
    /** 评价人id */
    commentBy?: number;
    /** 评价内容 */
    commentContent?: string;
    /** 评价类型 */
    commentType?: number;
    /** 创建时间 */
    createTime?: Date;
    /** 评价id */
    id?: number;
    /** 评价目标id */
    parentCode?: string;
    /** 更新时间 */
    updateTime?: Date;
}
