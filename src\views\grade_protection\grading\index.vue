<template>
  <div class="app-container">
    <div class="content">
      <!-- 页面头部标题 -->
      <div class="header">
        <h1 class="title">等级保护定级</h1>
        <p class="subtitle">管理和查看组织内信息系统的等保定级情况</p>
      </div>

      <!-- 等保级别统计卡片区域 -->
      <div class="stats-grid">
        <!-- 遍历等级选项，展示每个等级的系统数量 -->
        <div v-for="level in levelOptions" :key="level" class="stat-card">
          <div class="stat-content">
            <div class="stat-title">{{ level }}系统</div> <!-- 等级名称 -->
            <div class="stat-value">{{ levelCounts[level] || 0 }}</div> <!-- 对应等级数量 -->
          </div>
          <div class="stat-icon-container" :style="{ backgroundColor: getLevelBarColor(level) }">
            <svg-icon icon-class="captcha" /> <!-- 图标 -->
          </div>
        </div>
      </div>

      <!-- 等保级别分布柱状图 -->
      <div class="chart-section">
        <div class="section-title">等保级别分布</div>
        <div class="bar-chart">
          <div class="baseline"></div> <!-- 基线 -->
          <!-- 遍历等级生成柱状条 -->
          <div v-for="level in levelOptions" :key="level" class="bar-container">
            <div class="bar" :style="{
              height: `${getBarHeight(level)}%`,
              backgroundColor: getLevelBarColor(level)
            }">
              <span class="bar-value">{{ levelCounts[level] || 0 }}</span> <!-- 数量显示 -->
            </div>
            <div class="bar-label">{{ level }}</div> <!-- 等级标签 -->
          </div>
        </div>
      </div>

      <!-- 筛选条件区域 -->
      <div class="filter-section">
        <h3 class="filter-heading">筛选条件</h3>
        <div class="filter-grid">
          <!-- 系统名称输入 -->
          <div class="filter-item">
            <label class="filter-label">系统名称</label>
            <div class="input-wrapper">
              <i class="input-icon">🔍</i>
              <input v-model="filters.name" type="text" class="filter-input" placeholder="请输入系统名称" />
            </div>
          </div>
          <!-- 部门选择 -->
          <div class="filter-item">
            <label class="filter-label">所属部门</label>
            <div class="select-wrapper">
              <select v-model="filters.department" class="filter-input">
                <option value="">全部部门</option>
                <option v-for="dept in departmentOptions" :key="dept" :value="dept">
                  {{ dept }}
                </option>
              </select>
            </div>
          </div>
          <!-- 等级选择 -->
          <div class="filter-item">
            <label class="filter-label">等保级别</label>
            <div class="select-wrapper">
              <select v-model="filters.level" class="filter-input">
                <option value="">全部级别</option>
                <option v-for="level in levelOptions" :key="level" :value="level">
                  {{ level }}
                </option>
              </select>
            </div>
          </div>
          <!-- 刷新按钮 -->
          <div class="filter-actions">
            <button class="refresh-btn" @click="handleRefresh" :disabled="loading">
              <i class="refresh-icon">{{ loading ? '⏳' : '🔄' }}</i>
              <span>{{ loading ? '加载中...' : '刷新数据' }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 信息系统数据表 -->
      <div class="table-section">
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>系统名称</th>
                <th>等保级别</th>
                <th>是否对公网开放</th>
                <th>所属部门</th>
                <th>责任人</th>
                <th>联系方式</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <!-- 遍历系统数据，生成表格行 -->
              <tr v-for="system in systems" :key="system.id">
                <td>{{ system.systemId }}</td>
                <td>
                  <div class="system-info">
                    <!-- 系统图标，背景色根据等级 -->
                    <div class="system-icon" :class="getSystemIconBg(system.level)">
                      <svg-icon icon-class="client" />
                    </div>
                    <div class="system-name">{{ system.name }}</div>
                  </div>
                </td>
                <td>
                  <!-- 显示等级徽章 -->
                  <span class="badge" :class="getLevelBadgeClass(system.level)">
                    {{ levelMap[system.level] || system.level }}
                  </span>
                </td>
                <td>
                  <!-- 是否对公网开放状态标签 -->
                  <span class="open-status-badge" :class="system.isOpen === '1' ? 'is-open' : 'not-open'">
                    <i class="status-icon">{{ system.isOpen === '1' ? '🌐' : '🔒' }}</i>
                    {{ isOpenMap[system.isOpen] || "否" }}
                  </span>
                </td>
                <td>{{ system.department }}</td>
                <td>{{ system.responsiblePerson }}</td>
                <td>{{ system.respPersonPhone }}</td>
                <td>
                  <!-- 操作按钮：查看详情 -->
                  <button class="table-action-btn view-btn" @click="viewSystem(system)">
                    <i class="action-icon">📄</i>
                    <span>查看详情</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination">
          <div class="pagination-info">
            <template v-if="totalItems === 0">
              显示 0 条结果
            </template>
            <template v-else>
              显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalItems) }} 条，共 {{
                totalItems }} 条
            </template>
          </div>
          <div class="pagination-controls">
            <!-- 上一页按钮 -->
            <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">
              <svg-icon icon-class="close_left" />
            </button>
            <!-- 动态页码按钮 -->
            <button v-for="page in visiblePages" :key="page" class="page-btn" :class="{ active: currentPage === page }"
              @click="goToPage(page)">
              {{ page }}
            </button>
            <!-- 下一页按钮 -->
            <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">
              <svg-icon icon-class="close_right" />
            </button>
          </div>
        </div>
      </div>

      <!-- 等级保护定级标准介绍 -->
      <div class="level-intro-box">
        <h2 class="intro-title">等级保护定级标准</h2>
        <div class="level-intro-list">
          <!-- 遍历等级描述，显示详细介绍 -->
          <div class="level-intro-item" v-for="level in levelDescriptions" :key="level.level">
            <div class="level-badge" :style="{ backgroundColor: level.color }">
              {{ level.level }}
            </div>
            <div class="level-content">
              <div class="level-name">{{ level.name }}</div>
              <div class="level-desc">{{ level.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 弹窗表单组件，用于查看/编辑 -->
    <GradingFormDialog :visible="showDialog" :record="editingRecord" :isEdit="isEditMode" :isView="isViewMode"
      :level-options="levelOptions" :isOpen-options="isOpenOptions" @close="closeDialog" @submit="handleSubmitRecord" />
  </div>
</template>

<script setup lang="ts">
// 引入 Vue 相关 API
import { ref, reactive, computed, onMounted, watch } from "vue";
// 引入 API 方法与组件
import GradeProtectionAPI from "@/api/grade_protection";
import GradingFormDialog from "./components/GradingFormDialog.vue";
import type { GradingVO } from "@/api/grade_protection";

// ---------------------- 常量与选项 ---------------------- //

// 等保级别选项
const levelOptions = ["未定级", "一级", "二级", "三级", "四级", "五级"];

// 是否对公网开放选项
const isOpenOptions = ["否", "是"];

// 等级映射
const levelMap = {
  0: "未定级",
  1: "一级",
  2: "二级",
  3: "三级",
  4: "四级",
  5: "五级",
};

// 是否对公网开放映射
const isOpenMap = {
  0: "否",
  1: "是",
};

// 等级保护定级标准描述数据，用于介绍展示
const levelDescriptions = [
  // { level: "未定级", name: "无保护等级", description: "信息系统不涉及安全保护需求。", color: "#A0AEC0" },
  { level: "一级", name: "自主保护级", description: "信息系统对安全有基本的保护需求。", color: "#4299E1" },
  { level: "二级", name: "指导保护级", description: "信息系统需要国家指导保护的等级。", color: "#48BB78" },
  { level: "三级", name: "监督保护级", description: "信息系统需接受安全监督检查的等级。", color: "#ED8936" },
  { level: "四级", name: "强制保护级", description: "信息系统需强制保护，确保国家安全。", color: "#F56565" },
  { level: "五级", name: "特别保护级", description: "信息系统需特别严格保护，涉及核心机密。", color: "#D53F8C" },
];

// ---------------------- 数据状态 ---------------------- //

// 筛选条件，绑定输入框和下拉选择
const filters = ref({ name: "", department: "", level: "" });

// 信息系统列表数据
const systems = ref<GradingVO[]>([]);

// 部门选项列表
const departmentList = ref<string[]>([]);

// 加载状态
const loading = ref(false);

// 弹窗控制相关
const showDialog = ref(false);
const editingRecord = ref<GradingVO | null>(null);
const isEditMode = ref(false);
const isViewMode = ref(false);

// 等级统计数量
const levelCounts = reactive<Record<string, number>>({
  未定级: 0,
  一级: 0,
  二级: 0,
  三级: 0,
  四级: 0,
  五级: 0,
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// ---------------------- 计算属性 ---------------------- //

// 总页数
const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

// 部门下拉选项（直接返回部门列表）
const departmentOptions = computed(() => departmentList.value);

// 可见页码范围
const visiblePages = computed(() => {
  const pages: number[] = [];
  const maxVisible = 5;
  let start = Math.max(1, currentPage.value - 2);
  let end = start + maxVisible - 1;
  if (end > totalPages.value) {
    end = totalPages.value;
    start = Math.max(1, end - maxVisible + 1);
  }
  for (let i = start; i <= end; i++) pages.push(i);
  return pages;
});

// ---------------------- 方法函数 ---------------------- //

// 获取等级数量统计数据
const fetchLevelCounts = async () => {
  try {
    const res = await GradeProtectionAPI.getLevelCounts();
    if (Array.isArray(res)) {
      levelOptions.forEach((lvl, i) => {
        levelCounts[lvl] = res[i] || 0;
      });
    }
  } catch (e) {
    console.error(e);
  }
};

// 将中文级别转换为数字
const getLevelNumber = (levelText: string): string | null => {
  const levelNumberMap: Record<string, string> = {
    "未定级": "0",
    "一级": "1",
    "二级": "2",
    "三级": "3",
    "四级": "4",
    "五级": "5",
  };
  return levelText ? levelNumberMap[levelText] || levelText : null;
};

// 获取分页总条数（带筛选条件）
const fetchTotalItems = async () => {
  const params: any = {};
  if (filters.value.name) params.name = filters.value.name.trim();
  if (filters.value.department) params.department = filters.value.department.trim();
  if (filters.value.level) {
    const levelNumber = getLevelNumber(filters.value.level.trim());
    if (levelNumber !== null) params.level = levelNumber;
  }
  const res = await GradeProtectionAPI.getTotalCount(params);
  totalItems.value = res || 0;
};

// 获取当前页信息系统列表
const fetchSystems = async () => {
  loading.value = true;
  try {
    const params: any = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };
    if (filters.value.name) params.name = filters.value.name.trim();
    if (filters.value.department) params.department = filters.value.department.trim();
    if (filters.value.level) {
      const levelNumber = getLevelNumber(filters.value.level.trim());
      if (levelNumber !== null) params.level = levelNumber;
    }
    const res = await GradeProtectionAPI.getGradingList(params);
    systems.value = Array.isArray(res) ? res : res.records || [];
  } finally {
    loading.value = false;
  }
};

// 刷新所有数据（含分页和图表）
const refreshData = async () => {
  await fetchLevelCounts();
  await fetchTotalItems();
  if (currentPage.value > totalPages.value && totalPages.value > 0) {
    currentPage.value = totalPages.value;
  }
  if (totalPages.value === 0) {
    currentPage.value = 1;
  }
  await fetchSystems();
};

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await GradeProtectionAPI.getDepartmentList();
    if (Array.isArray(res)) departmentList.value = res;
  } catch (e) {
    console.error("获取部门列表失败：", e);
  }
};

// 获取等级柱状图高度（百分比）
const getBarHeight = (level: string) => {
  const max = Math.max(...Object.values(levelCounts));
  return max > 0 ? ((levelCounts[level] || 0) / max) * 100 : 0;
};

// 获取等级柱状图颜色
const getLevelBarColor = (level: string) => {
  switch (level) {
    case "未定级": return "#A0AEC0";
    case "一级": return "#4299E1";
    case "二级": return "#48BB78";
    case "三级": return "#ED8936";
    case "四级": return "#F56565";
    case "五级": return "#D53F8C";
    default: return "#CBD5E0";
  }
};

// 获取图标背景色
const getSystemIconBg = (level: number | string) => {
  const levelStr = typeof level === 'number' ? levelMap[level] : level;
  switch (levelStr) {
    case "未定级": return "#A0AEC0";
    case "一级": return "#4299E1";
    case "二级": return "#48BB78";
    case "三级": return "#ED8936";
    case "四级": return "#F56565";
    case "五级": return "#D53F8C";
    default: return "#CBD5E0";
  }
};

// 获取等级徽章类名
const getLevelBadgeClass = (level: number | string) => {
  const levelStr = typeof level === 'number' ? levelMap[level] : level;
  return `level-badge-${levelStr}`;
};

// ---------------------- 页面交互逻辑 ---------------------- //

// 分页控制
const prevPage = () => { if (currentPage.value > 1) currentPage.value--; };
const nextPage = () => { if (currentPage.value < totalPages.value) currentPage.value++; };
const goToPage = (p: number) => { currentPage.value = p; };

// 查看系统详情（只读弹窗）
const viewSystem = (system: GradingVO) => {
  editingRecord.value = system;
  isViewMode.value = true;
  isEditMode.value = false;
  showDialog.value = true;
};

// 编辑系统（可编辑弹窗）
const editSystem = (id: string | number) => {
  const record = systems.value.find(s => s.id === id);
  if (record) {
    editingRecord.value = record;
    isEditMode.value = true;
    isViewMode.value = false;
    showDialog.value = true;
  }
};

// 弹窗关闭，清理状态
const closeDialog = () => {
  showDialog.value = false;
  editingRecord.value = null;
  isEditMode.value = false;
  isViewMode.value = false;
};

// 删除系统（确认后）
const deleteSystem = async (id: string | number) => {
  if (confirm("确认要删除该系统吗？")) {
    await GradeProtectionAPI.deleteGradingSystem(id);
    await refreshData();
  }
};

// 提交编辑数据（调用更新接口）
const handleSubmitRecord = async (record: GradingVO) => {
  if (isEditMode.value && record) {
    const payload = {
      id: Number(record.id),
      name: record.name,
      systemNumber: record.systemNumber,
      department: record.department,
      level: record.level,
      assessmentStatus: record.assessmentStatus,
      responsiblePerson: record.responsiblePerson,
    };
    await GradeProtectionAPI.updateGradingSystem(payload.id, payload);
    await refreshData();
    closeDialog();
  }
};

// 刷新按钮点击
const handleRefresh = () => {
  if (!loading.value) refreshData();
};

// ---------------------- 生命周期与监听 ---------------------- //

// 初始化加载
onMounted(() => {
  fetchDepartments();
  refreshData();
});

// 筛选条件变更刷新数据
watch(filters, () => {
  currentPage.value = 1;
  refreshData();
}, { deep: true });

// 页码变更时刷新系统列表
watch(currentPage, fetchSystems);
</script>

<style scoped lang="scss">
.app-container {
  padding: 15px;
  background-color: var(--el-bg-color); // 使用主题背景色
  color: var(--el-text-color-regular);
}

.content {
  background-color: var(--el-bg-color);
  padding: 16px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

.header {
  margin-bottom: 24px;

  .title {
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .subtitle {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;

  .stat-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    padding: 12px 16px;
    box-shadow: var(--el-box-shadow-light);

    .stat-content {
      .stat-title {
        font-size: 16px;
        color: var(--el-text-color-primary);
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: var(--el-color-primary);
      }
    }

    .stat-icon-container {
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }
  }
}

.chart-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 18px;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }

  .bar-chart {
    display: flex;
    align-items: flex-end;
    gap: 11%;
    height: 360px;
    border-bottom: 1px solid var(--el-border-color);
    position: relative;

    /* 添加基线 */
    &::before {
      content: "";
      position: absolute;
      bottom: 30px;
      /* 让基线与padding-bottom对齐 */
      left: 0;
      right: 0;
      height: 2px;
      background-color: var(--el-border-color);
    }

    .bar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      width: 64px;
      /* 固定宽度，保证一致 */
      /* 清除多余的 margin/padding */
      margin: 0;
      padding: 0;

      .bar {
        width: 64px;
        border-radius: 4px 4px 0 0;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        transition: height 0.3s ease;
        background-color: #409EFF;
        /* 主题色示例 */

        /* 关键：确保height来自JS动态传入 */
        /* height: 例如 60% 或者具体像素 */

        .bar-value {
          font-size: 12px;
          color: #fff;
          margin-bottom: 4px;
        }
      }

      .bar-label {
        margin-top: 6px;
        font-size: 14px;
        color: var(--el-text-color-secondary);
        height: 30px;
        /* 预留标签空间 */
        line-height: 30px;
      }
    }
  }

}

.filter-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;

  .filter-heading {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    color: var(--el-text-color-primary);
  }

  .filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: end;
  }

  .filter-item {
    display: flex;
    flex-direction: column;

    .filter-label {
      font-size: 14px;
      margin-bottom: 4px;
      color: var(--el-text-color-regular);
    }

    .filter-input {
      height: 32px;
      width: 100%;
      padding: 4px 8px;
      border-radius: 4px;
      border: 1px solid var(--el-border-color);
      background-color: var(--el-fill-color-blank);
      color: var(--el-text-color-regular);
    }

    .input-wrapper {
      position: relative;

      .input-icon {
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
        color: var(--el-text-color-placeholder);
      }

      input {
        padding-left: 24px;
      }
    }

    .select-wrapper {
      position: relative;

      .select-arrow {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .filter-actions {
    .refresh-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 12px;
      background-color: var(--el-color-primary);
      color: #fff;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      width: 100%;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

.table-section {
  background-color: var(--el-bg-color-overlay);
  border-radius: 6px;
  box-shadow: var(--el-box-shadow-light);

  .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;

    thead {
      background-color: var(--el-fill-color-light);
      color: var(--el-text-color-primary);
    }

    th,
    td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    .system-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .system-icon {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--el-color-primary-light-9);
        color: var(--el-color-primary);
      }

      .system-name {
        color: var(--el-text-color-primary);
      }
    }

    .badge {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }

    .open-status-badge {
      font-size: 12px;
      border-radius: 4px;
      padding: 2px 6px;
      display: inline-flex;
      align-items: center;
      gap: 4px;

      &.is-open {
        background-color: var(--el-color-success-light-9);
        color: var(--el-color-success);
      }

      &.not-open {
        background-color: var(--el-color-danger-light-9);
        color: var(--el-color-danger);
      }
    }

    .table-action-btn {
      padding: 4px 8px;
      background-color: var(--el-color-primary);
      color: #fff;
      border-radius: 4px;
      font-size: 12px;
      border: none;
      cursor: pointer;

      .action-icon {
        margin-right: 4px;
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;

    .pagination-controls {
      display: flex;
      gap: 6px;

      .page-btn {
        padding: 4px 8px;
        border: 1px solid var(--el-border-color-light);
        background-color: var(--el-fill-color-blank);
        border-radius: 4px;
        cursor: pointer;
        color: var(--el-text-color-regular);

        &.active {
          background-color: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }
}

.level-intro-box {
  margin-top: 32px;

  .intro-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
    color: var(--el-text-color-primary);
  }

  .level-intro-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 16px;

    .level-intro-item {
      display: flex;
      align-items: flex-start;
      background-color: var(--el-bg-color-overlay);
      border-radius: 6px;
      padding: 12px;
      box-shadow: var(--el-box-shadow-light);

      .level-badge {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
      }

      .level-content {
        .level-name {
          font-size: 14px;
          color: var(--el-text-color-primary);
          margin-bottom: 4px;
        }

        .level-desc {
          font-size: 12px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }
}
</style>