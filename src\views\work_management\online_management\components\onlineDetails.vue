<template>
    <div class="app-container">
        <div class="top_view">
          <div v-for="(item,index) in querybox" :key="index" @click="handletab(index,item.title)" class="top_view_box " :class="item.active==true?'active_view':''">
              <div>
                <p>{{item.title}}</p>
                 <p>{{item.num}}</p>
               </div>
               <div>
                <img :src="item.img" />
              </div>
         </div>
    </div>
        <div class="search-container">
            <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="当前步骤" prop="step">
                    <el-select v-model="queryParams.step" filterable clearable placeholder="请选择当前步骤">
                        <el-option label="工单审核" :value="2" />
                        <el-option label="安全评估" :value="3" />
                        <el-option label="工单整改" :value="4" />
                        <el-option label="工单复核" :value="5" />
                        <el-option label="工单评价" :value="6" />
                        <el-option label="完成工单" :value="7" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申请人" prop="applicantId">
                    <el-select v-model="queryParams.applicantId" filterable clearable placeholder="请选择申请人">
                        <el-option v-for="user in userList" :key="user.userId" :label="user.nickname"
                            :value="user.userId" />
                    </el-select>
                </el-form-item>
                <el-form-item label="申请部门" prop="deptId">
                    <el-tree-select v-model="queryParams.deptId" placeholder="请选择申请部门" :data="deptOptions" clearable
                        filterable check-strictly :render-after-expand="false" />
                </el-form-item>
                <el-form-item label="工单名称" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入工单名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="创建时间" prop="createTime">
                    <el-date-picker class="!w-[240px]" v-model="queryParams.createTime" type="daterange"
                        range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleQuery">
                        <el-icon>
                            <Search />
                        </el-icon>
                        搜索
                    </el-button>
                    <el-button @click="handleResetQuery">
                        <el-icon>
                            <Refresh />
                        </el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-card shadow="never" class="table-container">
            <template #header>
                <div class="flex-x-between">
                    <div>
                        <el-button v-hasPerm="['system:business:add']" type="success"
                            @click="handleOpenDialog(undefined)">
                            <el-icon>
                                <Plus />
                            </el-icon>
                            发起业务上线工单
                        </el-button>
                        <el-button v-hasPerm="['system:business:delete']" type="danger" :disabled="ids.length === 0"
                            @click="handleDelete()">
                            <el-icon>
                                <Delete />
                            </el-icon>
                            批量删除
                        </el-button>
                    </div>
                    <div>
                        <el-button class="ml-3" @click="handleExport">
                            <template #icon><i-ep-download /></template>
                            导出
                        </el-button>
                    </div>
                </div>
            </template>
<!-- 状态、截止日期、评估对象、评估描述、评估报告、责任单位、责任人、联系方式、任务创建时间（原工单提交时间） -->
            <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading"
                :data="pageData" highlight-current-row border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column key="businessStatus" label="工单状态" prop="businessStatus" min-width="120" align="center"
                    show-overflow-tooltip >
                     <template #default="scope">
                     <el-tag :type="scope.row.businessStatus == 0 || scope.row.businessStatus == 11 || scope.row.businessStatus == 1 || scope.row.businessStatus == 2 ||scope.row.safetyStatus == 12
                            ? 'warning'
                            : scope.row.businessStatus == 4 || scope.row.businessStatus == 14
                                ? 'success'
                                : 'info'
                            ">
                            {{statusbox[scope.row.businessStatus]}}
                            </el-tag>
                    </template>
                    </el-table-column>
                <el-table-column key="deadline" label="截止日期" prop="deadline" min-width="150" align="center"
                    show-overflow-tooltip>
                    </el-table-column>
                <el-table-column key="sysname" label="评估对象" prop="sysname" min-width="120" align="center"
                    show-overflow-tooltip />
                <el-table-column key="reason" label="评估描述" prop="reason" min-width="120" align="center"
                    show-overflow-tooltip />
               <el-table-column key="newFileUrl" label="评估报告" prop="newFileName" min-width="150" align="center">
                    <template #default="scope">
                        <el-button v-v-show="scope.row.evaluationObjectFileUrl" size="small" @click="downloadFile2(scope.row.evaluationObjectFileUrl,scope.row.evaluationObjectFileName)" >
                        下载报告
                        </el-button>
                    </template>
                 </el-table-column>
                <el-table-column key="businessDeptName" label="责任单位" prop="businessDeptName" min-width="150" align="center"
                    show-overflow-tooltip />
                <el-table-column key="businessUserName" label="责任人" prop="businessUserName" min-width="120" align="center"
                    show-overflow-tooltip />
                <el-table-column key="businessUserMobile" label="联系方式" prop="businessUserMobile" min-width="120" align="center"
                    show-overflow-tooltip />
                <el-table-column key="createTime" label="任务创建时间" prop="createTime" min-width="150" align="center"
                    show-overflow-tooltip />
                <el-table-column fixed="right" label="操作" width="220">
                    <template #default="scope">
                        <el-button type="info" link @click="handleViewTicket(scope.row.id)">
                            <el-icon>
                                <View />
                            </el-icon>
                            查看工单
                        </el-button>
                        <el-button
                            v-show="(scope.row.step != 7 && scope.row.reviewDeptType == userInfo.deptType) || userInfo.roles.includes('ROOT')"
                            type="primary" link @click="handleOpenDialog(scope.row.id)">
                            <el-icon>
                                <Edit />
                            </el-icon>
                            处理工单
                        </el-button>
                        <el-button  v-show="scope.row.step != 7" v-hasPerm="['system:business:delete']" type="danger" link
                            @click="handleDelete(scope.row.id)">
                            <el-icon>
                                <Delete />
                            </el-icon>
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination  v-show="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="handleQuery" />
        </el-card>

        <!-- 查看工单组件 -->
        <onlineViewTicket v-model:visible="viewTicketDialog.visible" :ticket-id="viewTicketDialog.ticketId"
            @navigateToProcess="handleOpenDialog" />

        <!-- 文件列表对话框 -->
        <el-dialog v-model="fileListDialog.visible" title="附件列表" width="50%">
            <el-table :data="fileListDialog.files" style="width: 100%">
                <el-table-column prop="linkName" label="环节名称" />
                <el-table-column prop="name" label="文件名" />
                <el-table-column prop="createByName" label="创建人名称" />
                <el-table-column prop="createTime" label="上传时间" />
                <el-table-column label="操作" width="120" align="center">
                    <template #default="scope">
                        <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                            <el-icon>
                                <Download />
                            </el-icon>下载
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div  v-show="fileListDialog.files.length === 0" class="no-files-tip">
                <el-empty description="暂无附件" />
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Refresh, Plus, Delete, Edit, View, Download, Document } from "@element-plus/icons-vue";
import businessAPI, {
    businessPageVO,
    businessForm,
    businessPageQuery,
} from "@/api/work_management/online_service/index";
import Process from "./onlineAssprocess.vue";
import onlineViewTicket from './onlineViewTicket.vue';
import { useUserStore } from '@/store/modules/user';
import DeptAPI from "@/api/dept";
import UserAPI, { UserInfo, UserQuery } from "@/api/user";
import img1 from '@/assets/p_icon/cl.png'
import img2 from '@/assets/p_icon/sh.png'
import img3 from '@/assets/p_icon/wc.png'

defineOptions({
    name: "BusinessOnlineManagement",
    inheritAttrs: false,
});
const emits = defineEmits(["navigateToProcess"]);
const queryFormRef = ref();
const dataTableRef = ref();

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const ticketId = ref(0);
// 获取用户信息
const userStore = useUserStore();
const deptOptions = ref<any[]>([])
const userList = ref<any[]>([])
const userInfo = ref<UserInfo>({
    perms: [],
    roles: []
}
);
// 状态列表
const statusbox=ref({
    1:'待处理',11:'逾期待处理',
    2:'待审核',12:'逾期待审核',
    3:'流转中',13:'逾期流转中',
    4:'已完成', 14:'逾期完成',
}) 
const queryParams = reactive<businessPageQuery>({
    pageNum: 1,
    pageSize: 10,
    step: undefined,
    ticketType: "business", // 固定为业务上线类型
    name: undefined,
    applicantId: undefined,
    deptId: undefined,
    createTime: undefined
});
//  搜索查询状态
const statusquery:any=ref(null)
const querybox=ref([
        {title:"待处理",num:0,img:img1,active:false,status:1},
        {title:"待审核",num:0,img:img2,active:false,status:2},
         {title:"未完成",num:0,img:img1,active:false,status:4},
        {title:"已完成",num:0,img:img3,active:false,status:3},

 ])
 // 点击tab
const handletab=(index:number,title:string)=>{
  querybox.value[index].active=!querybox.value[index].active;
  querybox.value.forEach((item,indexc)=>{
    if(title!=item.title){
      item.active=false;
    }
    if(querybox.value[index].active==true) statusquery.value=querybox.value[index].status;
    else statusquery.value=null
  })
  queryParams.pageNum = 1;
  handleQuery();
}
// 获取安全问题管理列表页面数据统计
const  getpageCount=()=>{
  businessAPI.getpageCount("business",{}).then((res) => {
    querybox.value[0].num=res?.pendingProcessingCount//待处理
    querybox.value[1].num=res?.pendingApprovalCount//待审核
    querybox.value[3].num=res?.completedCount//已完成
    querybox.value[2].num=res?.incompleteCount//已完成
  });
}
const userParams = reactive<UserQuery>({});

const pageData = ref<businessPageVO[]>([]);

const dialog = reactive({
    title: "",
    visible: false,
});

const formData = reactive<businessForm>({});

const viewTicketDialog = reactive({
    visible: false,
    ticketId: undefined as number | undefined
});

// 文件列表对话框
const fileListDialog = reactive({
    visible: false,
    files: [] as any[],
});

// 添加查看工单方法
function handleViewTicket(id: number) {
    console.log('查看工单详情:', id);
    if (!id) {
        ElMessage.warning('工单ID不存在');
        return;
    }

    // 设置对话框数据
    viewTicketDialog.ticketId = id;
    viewTicketDialog.visible = true;
}

function handleQuery() {
    loading.value = true;
    businessAPI
        .getPage({...queryParams,status:statusquery.value})
        .then((data) => {
            pageData.value = data.list;
            total.value = data.total;
        })
        .catch(error => {
            console.error('获取数据失败:', error);
            ElMessage.error('获取数据失败，请稍后重试');
        })
        .finally(() => {
            loading.value = false;
        });
}

function handleResetQuery() {
    queryFormRef.value?.resetFields();
    queryParams.pageNum = 1;
    queryParams.ticketType = "business"; // 重置时保持工单类型
    handleQuery();
}

// 获取部门选项
const loadDeptOptions = async () => {
    try {
        const data = await DeptAPI.getOptions()
        deptOptions.value = data

    } catch (error) {
        console.error('加载部门列表失败:', error)
        ElMessage.error('加载部门列表失败')
    }
}

// 获取用户选项
const loadUserList = async () => {
    try {
        const data = await UserAPI.getList(userParams)
        userList.value = data
    } catch (error) {
    }
}

// 获取用户选项
const loadUserInfo = async () => {
    try {
        const data = await UserAPI.getInfo()
        userInfo.value = data
    } catch (error) {
    }
}

function handleSelectionChange(selection: any) {
    ids.value = selection.map((item: any) => item.id);
}

function handleOpenDialog(id?: number) {
  console.log("处理工单ID:", id);
  emits("navigateToProcess", id);
}

function nextStep() {
    handleQuery();
}

function handleCloseDialog() {

}

function handleDelete(id?: number) {
    const deleteIds = id ? [id] : ids.value;
    const removeId = deleteIds.join(",");

    if (!removeId) {
        ElMessage.warning("请勾选删除项");
        return;
    }

    ElMessageBox.confirm("确认删除已选中的业务上线工单?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
    })
        .then(() => {
            loading.value = true;
            businessAPI
                .deleteByIds(removeId)
                .then(() => {
                    ElMessage.success("删除成功");
                    handleQuery();
                })
                .finally(() => (loading.value = false));
        })
        .catch(() => {
            ElMessage.info("已取消删除");
        });
}

// 获取步骤名称
function getStepName(step: number | string) {
    const stepMap = {
        1: '发起工单',
        2: '工单审核',
        3: '安全评估',
        4: '工单整改',
        5: '工单复核',
        6: '工单评价',
        7: '完成工单'
    };
    return stepMap[step as keyof typeof stepMap] || `步骤${step}`;
}

// 获取步骤标签类型
function getStepTagType(step: number | string) {
    const stepMap = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info',
        5: 'warning',
        6: 'danger',
        7: 'success'
    };
    return stepMap[step as keyof typeof stepMap] || 'info';
}

// 判断是否有文件
function hasFiles(fileIds: string) {
    if (!fileIds) return false;
    try {
        const fileIdList = fileIds.split(",").map(fileId => fileId.trim());
        // 过滤掉空值
        const filteredFileIds = fileIdList.filter(fileId => fileId !== "");
        return filteredFileIds.length > 0;
    } catch (e) {
        return false;
    }
}

// 获取资产数量
function getAssetCount(assetIds: string) {
    if (!assetIds) return 0;
    try {
        const assets = assetIds.split(",").map(asset => asset.trim());
        // 过滤掉空值
        const filteredAssets = assets.filter(asset => asset !== "");
        return filteredAssets.length;
    } catch (e) {
        return 0;
    }
}

// 显示文件列表
function showFileList(id: number) {
    // 如果解析失败或没有文件，则尝试从API获取
    businessAPI.getFileList(id).then(fileList => {
        fileListDialog.files = fileList || [];
        fileListDialog.visible = true;
    }).catch(err => {
        console.error('获取文件列表失败:', err);
        ElMessage.error('获取文件列表失败');
    });
}
function downloadFile2(file,newFileName) {
  if (file) {
  fetch(file)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download =newFileName; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  }); 
  } 
}


// 下载文件
function downloadFile(file: { name: string; url?: string }) {
    if (!file.url) {
        ElMessage.warning('文件地址不存在');
        return;
    }

    try {
        const link = document.createElement('a');
        link.href = file.url;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    } catch (error) {
        console.error('文件下载失败:', error);
        ElMessage.error('文件下载失败');
    }
}

// 导出
function handleExport() {
    businessAPI.export(queryParams).then((response: any) => {
        const fileData = response.data;
        const fileName = decodeURI(
            response.headers["content-disposition"].split(";")[1].split("=")[1]
        );
        const fileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

        const blob = new Blob([fileData], { type: fileType });
        const downloadUrl = window.URL.createObjectURL(blob);

        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;

        document.body.appendChild(downloadLink);
        downloadLink.click();

        document.body.removeChild(downloadLink);
        window.URL.revokeObjectURL(downloadUrl);
    });
}

onMounted(() => {
    getpageCount();
    handleQuery();
    loadDeptOptions();
    loadUserList();
    loadUserInfo();
});
</script>

<style scoped>
.app-container {
    padding: 20px;
}

.search-container {
    margin-bottom: 20px;
    padding: 18px;
    border-radius: 4px;
}

.table-container {
    margin-top: 20px;
}

.flex-x-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.no-files-tip {
    padding: 20px 0;
    text-align: center;
}
.top_view{
    display: flex;
    align-items: center;
    margin-bottom:20px;
}
p{
    margin: 0;
    padding: 0;
}
.top_view_box{
    min-width: 220px;
    box-shadow: 0 0  10px 2px rgba(0,0,0,.1);
    margin-right: 20px;
    padding:  20px;
    background: white;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    
}
.top_view_box div:nth-of-type(1) p:nth-of-type(1){
  color: #333;
}
.top_view_box div:nth-of-type(1) p:nth-of-type(2){
    font-weight: bold;
    color: #333;
    font-size: 22px;
    height: 30px;
    line-height: 50px;
}
.top_view_box img{
    width: 46px;
    filter: grayscale(100%);
}

.active_view{
    background: #edf4fc;
}
.active_view img{
     filter: grayscale(0);
}
</style>
