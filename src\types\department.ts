/** 部门类型 */
export interface Department {
  /** 部门ID */
  id: number;
  /** 部门名称 */
  name: string;
  /** 部门类型 */
  type?: string;
  /** 执行配置 */
  execution?: {
    /** 选中的部门ID */
    selectedDeptId: number;
    /** 选中的部门名称 */
    selectedDeptName: string;
    /** 人员列表 */
    persons: UserInfo[];
    /** 是否启用短信 */
    enableSms: boolean;
    /** 短信模板ID */
    smsTemplateId?: number;
    /** 短信内容 */
    smsContent?: string;
    /** 通知类型 */
    notifyType?: string;
    /** 通知周期 */
    notifyPeriod?: string;
  };
  /** 通知配置 */
  notification?: {
    /** 选中的部门ID */
    selectedDeptId: number;
    /** 选中的部门名称 */
    selectedDeptName: string;
    /** 人员列表 */
    persons: UserInfo[];
    /** 是否启用短信 */
    enableSms: boolean;
    /** 短信模板ID */
    smsTemplateId?: number;
    /** 短信内容 */
    smsContent?: string;
    /** 通知类型 */
    notifyType?: string;
    /** 通知周期 */
    notifyPeriod?: string;
  };
} 