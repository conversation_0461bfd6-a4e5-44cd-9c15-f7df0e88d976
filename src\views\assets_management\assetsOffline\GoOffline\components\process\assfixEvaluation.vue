<template>
  <div class="initiate-ticket">
    <h3 class="page-title">下线评价</h3>
    <el-form :model="form" :rules="rules" ref="dataFormRef" label-width="120px" class="offline-form">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下线业务id" prop="id">
              <el-input v-model="form.id" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicantName">
              <el-input v-model="form.applicantName" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="concat">
              <el-input v-model="form.concat" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下线业务名称" prop="name">
              <el-input v-model="form.name" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下线原因" prop="reason">
              <el-input 
                v-model="form.reason" 
                type="textarea" 
                :rows="4"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker 
                v-model="form.createTime" 
                type="datetime" 
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 资产关联信息 -->
        <el-form-item label="关联资产">
          <el-button type="primary" plain @click="openTransferDialog">
            <i-ep-view />查看关联资产
          </el-button>
        </el-form-item>
      </div>

      <!-- 评价信息区域 -->
      <div class="form-section">
        <h4 class="section-title">评价信息</h4>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="评分" prop="rate">
              <el-rate 
                v-model="evaluate.rate" 
                :disabled="!showStep"
                show-score 
                text-color="#ff9900"
                score-template="{value}分"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="评价意见" prop="commentContent" :rules="[{ required: true, message: '请输入评价意见', trigger: 'blur' }]">
              <el-input 
                v-model="evaluate.commentContent" 
                type="textarea" 
                :rows="4"
                :disabled="!showStep"
                placeholder="请输入评价意见..."
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部按钮区 -->
    <div class="form-actions" v-if="showStep">
      <el-button type="primary" @click="submitForm">
        <i-ep-check />提交评价
      </el-button>
      <el-button @click="resetForm">
        <i-ep-refresh />重置
      </el-button>
    </div>

    <!-- 关联资产弹窗 -->
    <el-dialog v-model="transferDialog.visible" title="关联资产" width="800px">
      <el-table :data="transferDialog.allAssets" border stripe>
        <el-table-column prop="name" label="资产名称" show-overflow-tooltip/>
        <el-table-column prop="ip" label="资产IP" show-overflow-tooltip/>
        <el-table-column prop="url" label="资产链接" show-overflow-tooltip/>
        <el-table-column align="center" label="资产状态" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
              {{ scope.row.status == '1' ? '正常' : (scope.row.status == '0' ? '异常' : '废弃') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="管理员" prop="ownerName"/>
        <el-table-column label="管理部门" prop="deptId">
          <template #default="scope">
            <Dictmap v-model="scope.row.deptId" code="dept0x0"/>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import offlineAPI from "@/api/assets_management/details/offline"
import UserAPI from "@/api/user"
import {formatLocalDateTime} from "@/utils/dateUtils";

// 表单引用
const dataFormRef = ref<FormInstance | null>(null)
const emit = defineEmits(['next'])

// 定义接口
interface TicketData {
  id: string
  currentStep: string
  isClick: boolean
}

// props定义
const props = defineProps<{
  ticketdata: TicketData
}>()

// 表单验证规则
const rules = reactive({
  rate: [{ required: true, message: '请选择评分', trigger: 'change' }],
  commentContent: [{ required: true, message: '请输入评价意见', trigger: 'blur' }]
})

// 表单数据
const form = reactive<any>({
  id: '',
  name: '',
  createTime: '',
  updateTime: '',
  reason: '',
  applicantId: '',
  applicantName: '',
  concat: '',
  assetsList: [], //关联资产列表
  remark: ''
})

// 审核历史
const auditHistory = ref<any[]>([])

// 评价表单
const evaluate = reactive({
  rate: 5,
  commentContent: '',
  createTime: formatLocalDateTime(new Date()),
  reviews: [],
  comments: ''
})

// 弹窗状态
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[],
})

// 步骤控制
const currentStep = ref(props.ticketdata.currentStep)
const showStep = ref(true)
const nowStep = ref('')
const stepStatus = ref<any | null>(null)

// 是否为当前步骤
const isCurrentStep = () => {
  if (currentStep.value == nowStep.value) {
    showStep.value = true
  } else {
    showStep.value = false
  }
}

// 打开关联资产弹窗
const openTransferDialog = () => {
  transferDialog.visible = true
  transferDialog.allAssets = form.assetsList;
}

// 表单提交
const submitForm = async () => {
  if (!evaluate.rate) {
    ElMessage.warning('请选择评分')
    return
  }
  
  if (!evaluate.commentContent.trim()) {
    ElMessage.warning('请输入评价意见')
    return
  }

  try {
    await ElMessageBox.confirm('确定要提交评价吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await offlineAPI.evaluate(props.ticketdata.id, evaluate)
    ElMessage.success('评价已提交')

    emit('next')
  } catch (error) {
    console.error('Error submitting evaluation:', error)
    ElMessage.error('提交失败，请重试')
  }
}

// 重置表单
const resetForm = () => {
  evaluate.rate = 5
  evaluate.commentContent = ''
  if (dataFormRef.value) {
    dataFormRef.value.resetFields()
  }
}

// 数据初始化
const handleQuery = async () => {
  if (props.ticketdata.id) {
    // 获取工单状态
    const statusRes: any = await offlineAPI.status(props.ticketdata.id)
    stepStatus.value = statusRes
    for (const step in stepStatus.value) {
      if (stepStatus.value[step as keyof any] == 'process') {
        nowStep.value = step as string
        break
      }
    }

    // 获取表单数据
    const data = await offlineAPI.getFormData(props.ticketdata.id)
    Object.assign(form, data)

    // 获取审核历史
    if (data.comments) {
      auditHistory.value = data.comments
        .filter((item: any) => item.step === 2)
        .map((item: any, index: number) => ({
          ...item,
          level: index + 1
        }))
    }

    isCurrentStep()
    
    // 如果是查看历史评价
    if (!showStep.value && data.comments) {
      const evaluationComments = data.comments.filter((item: any) => item.step === 3)
      if (evaluationComments.length > 0) {
        Object.assign(evaluate, evaluationComments[evaluationComments.length - 1])
      }
    }
  }
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.initiate-ticket {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  margin-bottom: 30px;
  color: var(--el-color-primary);
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  color: var(--el-text-color-primary);
  font-size: 18px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.audit-record {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
}

.audit-record h5 {
  margin: 0 0 15px;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}
</style>
