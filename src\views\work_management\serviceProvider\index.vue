<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <!-- 常用搜索项 -->
        <el-form-item label="服务商名称" prop="providerName">
          <el-input v-model="queryParams.providerName" placeholder="服务商名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="项目负责人" prop="projectManager">
          <el-input v-model="queryParams.projectManager" placeholder="项目负责人" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="技术负责人" prop="techLeader">
          <el-input v-model="queryParams.techLeader" placeholder="技术负责人" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="全部" clearable class="!w-[120px]">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- 高级搜索项 -->
        <template v-if="showAdvanced">
          <el-form-item label="ID" prop="id">
            <el-input v-model="queryParams.id" placeholder="ID" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="负责人手机号" prop="managerMobile">
            <el-input v-model="queryParams.managerMobile" placeholder="项目负责人手机号" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="负责人邮箱" prop="managerEmail">
            <el-input v-model="queryParams.managerEmail" placeholder="项目负责人邮箱" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="技术负责人" prop="techLeader">
            <el-input v-model="queryParams.techLeader" placeholder="技术负责人" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="技术负责人手机" prop="techMobile">
            <el-input v-model="queryParams.techMobile" placeholder="技术负责人手机号" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="技术负责人邮箱" prop="techEmail">
            <el-input v-model="queryParams.techEmail" placeholder="技术负责人邮箱" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="服务商描述" prop="description">
            <el-input v-model="queryParams.description" placeholder="服务商描述" clearable @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="合同时间" prop="contractTime">
            <el-date-picker v-model="contractTimeRange" type="daterange" range-separator="~" start-placeholder="开始时间"
              end-placeholder="结束时间" value-format="YYYY-MM-DD" @change="handleContractTimeChange" />
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" @click="handleQuery"><i-ep-search />搜索</el-button>
          <el-button @click="handleResetQuery"><i-ep-refresh />重置</el-button>
          <el-button @click="toggleAdvanced">
            {{ showAdvanced ? '隐藏高级筛选' : '显示高级筛选' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button v-hasPerm="['system:provider:add']" type="success" @click="handleOpenDialog()">
              <i-ep-plus />
              新增
            </el-button>
            <el-button v-hasPerm="['system:provider:delete']" type="danger" :disabled="ids.length === 0"
              @click="handleDelete()"><i-ep-delete />
              删除
            </el-button>
          </div>
          <div>
            <el-button class="ml-3" @click="handleOpenImportDialog">
              <template #icon><i-ep-upload /></template>
              导入
            </el-button>

            <el-button class="ml-3" @click="handleExport">
              <template #icon><i-ep-download /></template>
              {{ ids.length > 0 ? '导出选中' : '导出全部' }}
            </el-button>
          </div>
        </div>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="pageData" highlight-current-row border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column key="id" label="ID" prop="id" min-width="40" align="center" />
        <el-table-column key="providerName" label="服务商名称" prop="providerName" min-width="100" align="center" />
        <el-table-column key="projectManager" label="项目负责人" prop="projectManager" min-width="100" align="center" />
        <el-table-column key="managerMobile" label="项目负责人手机号" prop="managerMobile" min-width="160" align="center" />
        <el-table-column key="managerEmail" label="项目负责人邮箱" prop="managerEmail" min-width="160" align="center" />
        <el-table-column key="techLeader" label="技术负责人" prop="techLeader" min-width="100" align="center" />
        <el-table-column key="techMobile" label="技术负责人手机号" prop="techMobile" min-width="160" align="center" />
        <el-table-column key="techEmail" label="技术负责人邮箱" prop="techEmail" min-width="160" align="center" />
        <el-table-column key="description" label="服务商描述" prop="description" min-width="100" align="center">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="showDescriptionDialog(scope.row.description)">
              <i-ep-view />
              查看描述
            </el-button>
          </template>
        </el-table-column>
        <el-table-column key="status" label="状态" prop="status" min-width="80" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status == 1" type="success">启用</el-tag>
            <el-tag v-else type="info">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column key="contractStartTime" label="合同开始时间" prop="contractStartTime" min-width="120"
          align="center" />
        <el-table-column key="contractEndTime" label="合同结束时间" prop="contractEndTime" min-width="120" align="center" />
        <el-table-column key="contractFile" label="合同文件" prop="contractFile" min-width="120" align="center">
          <template #default="scope">
            <el-link v-if="scope.row.contractFile" type="primary" :href="scope.row.contractFile" target="_blank">
              查看合同文件
            </el-link>
          </template>
        </el-table-column>
        <el-table-column key="createTime" label="创建时间" prop="createTime" min-width="180" align="center" />
        <el-table-column key="updateTime" label="更新时间" prop="updateTime" min-width="180" align="center" />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button v-hasPerm="['system:provider:edit']" type="primary" size="small" link
              @click="handleViewDetail(scope.row.id)">
              <i-ep-view />
              查看详情
            </el-button>
            <el-button v-hasPerm="['system:provider:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />
              编辑
            </el-button>
            <el-button v-hasPerm="['system:provider:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
      <provider-detail-dialog v-model:visible="detailDialog.visible" :title="detailDialog.title" :data="detailData" />
    </el-card>

    <!-- 服务商管理表单弹窗 -->
    <service-provider-dialog v-model:visible="dialog.visible" :title="dialog.title" :data="formData"
      @submit="handleSubmit" />
    <service-provider-import v-model:visible="importDialogVisible" @import-success="handleOpenImportDialogSuccess" />

    <!-- 服务商描述弹窗 -->
    <el-dialog v-model="descriptionDialog.visible" :title="descriptionDialog.title" width="60%">
      <div style="height: 600px; overflow: auto;">
        <p><span style="color: rgb(96, 98, 102); background-color: rgb(255, 255, 255); font-size: 14px;">{{
          descriptionDialog.content }}</span></p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Provider",
  inheritAttrs: false,
});

import ProviderAPI, { ProviderPageVO, ProviderForm, ProviderPageQuery } from "@/api/work_management/serviceProvider/index";
import ServiceProviderDialog from './components/serviceProviderDialog.vue'
import ProviderDetailDialog from './components/serviceProviderDetail.vue'
import { ref } from "vue";
import ServiceProviderImport from "@/views/work_management/serviceProvider/components/serviceProviderImport.vue";
import ViewNotes from "@/components/GeneralModel/ViewNotes.vue";

const queryFormRef = ref(ElForm);

const importDialogVisible = ref(false);

const loading = ref(false);
const ids = ref<bigint[]>([]);
const total = ref(0);
const detailData = ref({})

const queryParams = reactive<ProviderPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 服务商管理表格数据
const pageData = ref<ProviderPageVO[]>([]);
// 详情弹窗状态
const detailDialog = reactive({
  title: '服务商详情',
  visible: false
})
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 描述弹窗状态
const descriptionDialog = reactive({
  title: "服务商描述",
  visible: false,
  content: ""
})

// 显示描述弹窗
function showDescriptionDialog(description: string) {
  descriptionDialog.content = description || "暂无描述";
  descriptionDialog.visible = true;
}

// 服务商管理表单数据
const formData = reactive<ProviderForm>({});

// 服务商管理表单校验规则
const rules = reactive({
  providerName: [{ required: true, message: "请输入服务商名称", trigger: "blur" }],
});

const showAdvanced = ref(false)
// 合同时间范围
const contractTimeRange = ref<[string, string]>(['', ''])
function toggleAdvanced() {
  showAdvanced.value = !showAdvanced.value
}
// 处理合同时间变化
const handleContractTimeChange = (val: string) => {
  if (val) {
    queryParams.contractStartTime = val[0]
    queryParams.contractEndTime = val[1]
  } else {
    queryParams.contractStartTime = undefined
    queryParams.contractEndTime = undefined
  }
}

// 修改查看详情按钮的点击事件
const handleViewDetail = (id: bigint) => {
  detailDialog.visible = true
  loading.value = true
  ProviderAPI.getFormData(id)
    .then((data) => {
      detailData.value = data
    })
    .finally(() => {
      loading.value = false
    })
}

/** 查询服务商管理 */
function handleQuery() {
  loading.value = true;
  ProviderAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置服务商管理查询 */
const handleResetQuery = () => {
  queryFormRef.value!.resetFields()
  contractTimeRange.value = ['', '']
  queryParams.contractStartTime = undefined
  queryParams.contractEndTime = undefined
  queryParams.status = undefined // 确保状态值被重置
  queryParams.pageNum = 1
  handleQuery()
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开服务商管理弹窗 */
function handleOpenDialog(id?: bigint) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改服务商管理";
    ProviderAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增服务商管理";
  }
}

/** 提交服务商管理表单 */
function handleSubmit() {
  const id = formData.id;
  if (id) {
    ProviderAPI.update(id, formData)
      .then(() => {
        ElMessage.success("修改成功");
        handleCloseDialog();
        handleResetQuery();
      })
      .finally();
  } else {
    ProviderAPI.add(formData)
      .then(() => {
        ElMessage.success("新增成功");
        handleCloseDialog();
        handleResetQuery();
      })
      .finally();
  }
}

/** 关闭服务商管理弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  formData.id = undefined;
}

/** 删除服务商管理 */
function handleDelete(id?: bigint) {
  const providerIds = [id || ids.value].join(",");
  if (!providerIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      ProviderAPI.deleteByIds(providerIds)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}


function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  // 复制查询参数，避免修改原始查询参数
  const exportParams = { ...queryParams };
  
  // 如果有选中的记录，添加ids参数
  if (ids.value && ids.value.length > 0) {
    exportParams.ids = ids.value.join(',');
  }
  
  ProviderAPI.export(exportParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}

// 添加状态选项定义
const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
]

onMounted(() => {
  handleQuery();
});
</script>


<style lang="scss" scoped>
.search-container {
  background-color: var(--el-fill-color-blank);
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .el-form {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
    margin-right: 0;
  }

  :deep(.el-form--inline .el-form-item__content) {
    margin-right: 0;
  }

  // 调整日期选择器的宽度
  :deep(.el-date-editor--daterange) {
    width: 240px;
  }
}

// 调整输入框的统一宽度
:deep(.el-input) {
  width: 180px;
}

// 状态选择器宽度
:deep(.dictionary) {
  width: 120px;
}
</style>
