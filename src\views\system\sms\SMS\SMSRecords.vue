<template>
  <el-card class="sms-records">
    <template #header>
      <div class="card-header">
        <span>短信发送记录</span>
        <el-input
          v-model="search"
          placeholder="搜索手机号或内容"
          style="width: 300px"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </template>
    <el-table :data="filteredRecords" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="phoneNumber" label="手机号" width="140" />
      <el-table-column prop="content" label="内容" show-overflow-tooltip />
      <el-table-column prop="sendTime" label="发送时间" width="180" />
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="scope.row.status === '发送成功' ? 'success' : 'danger'">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="currentPage"
        :page-size="pageSize"
        :total="filteredRecords.length"
        layout="total, prev, pager, next"
      />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Search } from '@element-plus/icons-vue';

const records = ref([
  {
    id: 1,
    phoneNumber: "13800138000",
    content: "这是一条测试短信",
    sendTime: "2023-05-20 10:00:00",
    status: "发送成功",
  },
  // 添加更多记录
]);

const search = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

const filteredRecords = computed(() => {
  return records.value.filter(record => 
    record.phoneNumber.includes(search.value) || 
    record.content.includes(search.value)
  );
});
</script>

<style scoped>
.sms-records {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>
