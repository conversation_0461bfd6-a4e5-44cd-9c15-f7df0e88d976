import {
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteRecordRaw,
} from "vue-router";

import NProgress from "@/utils/nprogress";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import router from "@/router";
import { usePermissionStore, useUserStore } from "@/store";
import { checkCertificate } from "@/utils/certificateChecker";  // 导入证书检查器

export function setupPermission() {
  // 白名单路由
  const whiteList = ["/login", "/certificate", "/404", "/401", "/auth/callback"];
  // 证书检查白名单 - 这些路径不需要检查证书
  const certificateWhiteList = ["/login", "/certificate", "/404", "/401", "/auth/callback"];

  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    const hasToken = localStorage.getItem(TOKEN_KEY);

    // 打印当前导航信息，帮助调试
    console.log(`路由导航: ${from.path} -> ${to.path}, 是否有Token: ${Boolean(hasToken)}`);

    if (hasToken) {
      // 首先处理白名单路由
      if (certificateWhiteList.some(path => to.path.startsWith(path))) {
        console.log('目标路径在证书白名单中，无需检查证书');
        
        // 如果是登录页且已登录，重定向到首页
        if (to.path === "/login") {
          next({ path: "/" });
          NProgress.done();
          return;
        }
        
        // 其他白名单页面直接通过
        next();
        NProgress.done();
        return;
      }
      
      // 非白名单路由，检查证书
      try {
        console.log('开始检查证书有效性...');
        const { isValid } = await checkCertificate();
        
        // 证书无效，中断所有后续逻辑，直接跳转到证书页面
        if (!isValid) {
          console.log('证书已过期或无效，跳转到证书页面');
          ElMessage.error('证书过期或者未上传，请联系管理员');
          next({ path: '/certificate', replace: true });
          NProgress.done();
          return; // 确保后续代码不再执行
        }
        
        console.log('证书有效，继续导航流程');
      } catch (error) {
        console.error('证书检查出错:', error);
        ElMessage.error('证书过期或者未上传，请联系管理员');
        next({ path: '/certificate', replace: true });
        NProgress.done();
        return; // 确保后续代码不再执行
      }

      // 证书验证通过，继续原有逻辑
      if (to.path === "/login") {
        // 如果已登录，跳转到首页
        next({ path: "/" });
        NProgress.done();
      } else {
        const userStore = useUserStore();
        const hasRoles =
          userStore.user.roles && userStore.user.roles.length > 0;

        if (hasRoles) {
          // 如果未匹配到任何路由，跳转到404页面
          if (to.matched.length === 0) {
            console.log('未匹配到任何路由，重定向到404页面或原页面');
            next(from.name ? { name: from.name } : "/404");
          } else {
            // 如果路由参数中有 title，覆盖路由元信息中的 title
            const title =
              (to.params.title as string) || (to.query.title as string);
            if (title) {
              to.meta.title = title;
            }
            next();
          }
        } else {
          console.log('用户没有角色信息，正在获取用户信息...');
          const permissionStore = usePermissionStore();
          try {
            await userStore.getUserInfo();
            console.log('用户信息获取成功，生成动态路由');
            const dynamicRoutes = await permissionStore.generateRoutes();
            dynamicRoutes.forEach((route: RouteRecordRaw) =>
              router.addRoute(route)
            );
            next({ ...to, replace: true });
          } catch (error) {
            console.error('获取用户信息失败，重定向到登录页:', error);
            // 移除 token 并重定向到登录页，携带当前页面路由作为跳转参数
            await userStore.resetToken();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        console.log('未登录，但路径在白名单中，直接进入');
        next(); // 在白名单，直接进入
      } else {
        // 不在白名单，重定向到登录页
        console.log('未登录且路径不在白名单中，重定向到登录页');
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  router.afterEach((to) => {
    console.log(`路由完成: ${to.path}`);
    NProgress.done();
  });
}

/** 重定向到登录页 */
function redirectToLogin(
  to: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  // 检查是否有无效重定向参数
  const validRedirect = (path: string): boolean => {
    return Boolean(path) && path !== '/' && !path.startsWith('//') && !path.includes('undefined');
  };
  
  // 构建重定向参数
  let redirect = '';
  
  if (validRedirect(to.path)) {
    const params = new URLSearchParams(to.query as Record<string, string>);
    const queryString = params.toString();
    redirect = queryString ? `${to.path}?${queryString}` : to.path;
  }
  
  // 如果有有效的重定向参数，则添加到登录页URL
  const loginPath = redirect 
    ? `/login?redirect=${encodeURIComponent(redirect)}`
    : '/login';
    
  console.log(`重定向到登录页: ${loginPath}`);
  next(loginPath);
}

/** 判断是否有权限 */
export function hasAuth(
  value: string | string[],
  type: "button" | "role" = "button"
) {
  const { roles, perms } = useUserStore().user;
  // 超级管理员 拥有所有权限
  if (type === "button" && (roles.includes("ROOT"))) {
    return true;
  }

  const auths = type === "button" ? perms : roles;
  return typeof value === "string"
    ? auths.includes(value)
    : value.some((perm) => auths.includes(perm));
}
