<template>
  <div class="initiate-ticket">
    <div class="page-header">
      <h3>发起工单</h3>
    </div>

    <el-form 
      :model="form" 
      :rules="rules" 
      ref="dataFormRef" 
      label-width="120px"
      class="form-container"
    >
      <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="id" prop="id">
              <el-input v-model="form.id" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="form.name" :disabled="!showStep"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单类型" prop="ticketType">
              <el-select v-model="form.ticketType" placeholder="请选择工单类型" disabled style="width: 100%">
                <el-option label="安全处置事件" value="emergency" />
                <el-option label="业务上线管理" value="business" />
                <el-option label="安全评估管理" value="security" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="资产列表" class="asset-buttons">
              <el-button type="primary" @click="openTransferDialog" :disabled="!showStep">
                <el-icon><plus /></el-icon>选择关联资产
              </el-button>
              <!-- <el-button type="success" @click="handleOpenDialog()" :disabled="!showStep">
                <el-icon><circle-plus /></el-icon>新增资产
              </el-button> -->
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 选择的关联资产展示 -->
        <el-form-item label="已选择的关联资产">
          <el-table :data="assetsData" style="width: 100%" border>
            <!-- 序号列 -->
            <el-table-column type="index" label="序号" width="60" align="center" />
            
            <!-- 资产类型 -->
            <el-table-column 
              prop="type"
              label="资产类型" 
              show-overflow-tooltip
              align="center"
              width="120"
            >
              <template #default="scope">
                <el-tag size="small" class="asset-type-tag">
                  {{ getAssetTypeName(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 资产名称 -->
            <el-table-column 
              prop="name" 
              align="center"
              label="资产名称" 
              show-overflow-tooltip 
            />
            
            <!-- 资产地址 -->
            <el-table-column 
              prop="address" 
              label="资产地址" 
              align="center"
              show-overflow-tooltip
              min-width="150"
            />
            
            <!-- 管理部门 -->
            <el-table-column 
              prop="deptId" 
              label="管理部门" 
              show-overflow-tooltip 
              align="center"
              width="180"
            >
              <template #default="scope">
                <Dictmap v-model="scope.row.deptId" code="dept0x0" />
              </template>
            </el-table-column>
            
            <!-- 管理人员 -->
            <el-table-column 
              prop="ownerName" 
              label="管理人员" 
              show-overflow-tooltip 
              align="center"
              width="120" 
            />
          </el-table>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="任务描述" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="3" :disabled="!showStep" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker 
                v-model="form.createTime" 
                type="datetime" 
                placeholder="选择时间"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        

        <!-- <el-col :span="12">
          <el-form-item label="截止日期" prop="deadline">
            <el-date-picker 
              :disabled="!showStep" 
              v-model="form.deadline" 
              type="datetime" 
              placeholder="选择截止时间"
              style="width: 100%"
              :min-date="form.deadline"
            />
          </el-form-item>
        </el-col> -->
      </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="上传附件">
              <file-upload 
                :upload-max-size="20 * 1024 * 1024" 
                v-model="fileList"
                :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'" 
                :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'"
              >
              </file-upload>
              <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 添加已上传文件展示 -->
        <el-row :gutter="20" v-if="fileList && fileList.length">
          <el-col :span="24">
            <el-form-item label="已上传文件">
              <div class="file-list">
                <el-tag 
                  v-for="file in fileList"
                  :key="file.id"
                  class="file-item"
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 工作流程卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工作流程</span>
          </div>
        </template>
        
          <process-group ref="processGroupRef" @validation-change="handleValidationChange" />
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions" v-if="showStep">
        <el-button type="primary" @click="submitForm" size="large">
          <el-icon><check /></el-icon>提交工单
        </el-button>
        <el-button @click="resetForm" size="large">
          <el-icon><refresh /></el-icon>重置
        </el-button>
      </div>
    </el-form>

    <!-- 弹窗组件 -->
    <assetsDialog
      v-model:visible="dialog.visible" 
      :title="dialog.title" 
      :id="dialog.id"
      @submitted="handleQuery"
    />

    <select-assets
      v-model:visible="transferDialog.visible"  
      :title="'选择关联资产'"
      :selected-assets="transferDialog.selectedAssets"
      @selected="handleAssetsSelected"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import businessAPI from '@/api/work_management/online_service/index'
import assetsAPI from "@/api/assets_management/details/assets"
import assetsDialog from '@/views/assets_management/DetailOfAssets/details/components/assetsDialog.vue'
import SelectAssets from '@/components/AssetsManage/SelectAssets.vue'
import ProcessGroup from '@/components/ProcessGroup/OnlineProcessGroup.vue'
import {formatLocalDateTime} from "@/utils/dateUtils";
import { fileUtils } from '@/utils/fileUtils';
import { useRoute } from 'vue-router' 


const dataFormRef = ref<FormInstance | null>(null)
const emit = defineEmits(['next'])
const route = useRoute() 

interface TicketData {
  id: number
  currentStep: string
  isClick: boolean
}

const props = defineProps<{
  ticketdata: TicketData
  ticketType?: string
}>()

const getTicketType = () => {
  
  if (props.ticketType) {
    return props.ticketType
  }
  
  // 其次使用路由参数
  const routeType = route.query.type as string
  if (routeType) {
    switch (routeType) {
      case 'safetyDetails':
        return 'emergency'
      case 'evaluateDetails':
        return 'security'
      case 'onlineDetails':
        return 'business'
      default:
        return 'business'
    }
  }
  
  return 'business' // 默认值
}

// 表单数据
const form = reactive({
  createTime: formatLocalDateTime(new Date()),
  deadline: "",
  name: "",
  ticketType: getTicketType(),
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  id: 0,
  assetIds: [] as number[],
  fileIds: [] as number[],
  fileList: [] as any[],
})

// 新增资产弹窗
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined,
})

// 选择资产弹窗状态
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

// 已选资产数据
const sleectedAssetsData = ref<any[]>([])

// 文件列表
const fileList = ref([] as any[]);

// 计算已选择的关联资产数据
const assetsData = computed(() => {
  return sleectedAssetsData.value.filter(asset => 
    transferDialog.selectedAssets.includes(asset.id)
  )
})

// 打开资产选择弹窗
const openTransferDialog = () => {
  transferDialog.visible = true
}

// 处理资产选择结果
const handleAssetsSelected = ({ selectedIds, selectedAssets }: any) => {
  transferDialog.selectedAssets = selectedIds
  sleectedAssetsData.value = selectedAssets
}

// 其他原有代码保持不变...
const currentStep = ref(props.ticketdata.currentStep)
const showStep = ref(true)
const nowStep = ref('')
const stepStatus = ref<any | null>(null)
// 添加流程组件引用
const processGroupRef = ref()

// 流程验证状态
const processValidationStatus = ref<{valid: boolean; missingSteps: any[]} | null>(null);

// 是否为当前步骤
function isCurrentStep() {
  if (currentStep.value == nowStep.value) {
    showStep.value = true
  } else {
    showStep.value = false
  }
}

// 表单验证规则
const rules = {
  reason: [
    { required: false, message: '请填写上线原因', trigger: 'blur' }
  ],
  createTime: [
    { required: true, message: '请选择创建时间', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入上线业务名称', trigger: 'change' }
  ],
}

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 处理验证状态变化
const handleValidationChange = (status: any) => {
  processValidationStatus.value = status;
};


// 检查流程配置
const checkProcessConfiguration = () => {
  if (processGroupRef.value) {
    processValidationStatus.value = processGroupRef.value.getValidationStatus();
    return processValidationStatus.value?.valid;
  }
  return false;
};

// 生成工单编号
const generateTimeBasedId = () => {
  const now = new Date()
  return `${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`
}

// 提交表单
const submitForm = async () => {
  // 检查流程配置
  if (!checkProcessConfiguration()) {
    const missingStepNames = processValidationStatus.value?.missingSteps
      .map(step => step.name)
      .join('、');
    
    ElMessage.error(`请配置流程步骤: ${missingStepNames}`);
    return;
  }
  
  const processData = processGroupRef.value?.getProcessData();
  
  await ElMessageBox.confirm(
    '确定要提交申请工单吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  
  try {
    // 准备表单数据
    const formData = {
      ...form,
      reviewProcessForms: convertToReviewProcessForms(processData),
      fileIds: fileUtils.extractFileIds(fileList.value),
      assetIds: transferDialog.selectedAssets,
    };
    await businessAPI.add(formData);
    ElMessage.success('已提交');
    emit('next');
  } catch (error) {
    console.error('Error submitting ticket:', error);
    ElMessage.error('提交失败，请重试');
  }
}

// 将前端流程数据转换为后端需要的格式
const convertToReviewProcessForms = (processData) => {
  const result = [];
  const now = formatLocalDateTime(new Date());
  
  try {
    // 处理执行列表
    if (processData.executionList && processData.executionList.length > 0) {
      processData.executionList.forEach(item => {
        const departmentId = item.departmentId; // 流程步骤 ID

        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5, 5->6)
        const deptTypeMap = {1: '2', 2: '3', 3: '4', 4: '5', 5: '6'};
        const deptType = deptTypeMap[departmentId] || departmentId.toString(); 
        
        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.executionPersons?.[departmentId] || [];
        
        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map(person => person.id).join(',');
          
          result.push({
            id: null, // 使用原有ID或0
            businessType: "business", // 业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-6
            executeType: "1", // 执行类型: 1执行 
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }
    
    // 处理通知列表
    if (processData.notificationList && processData.notificationList.length > 0) {
      processData.notificationList.forEach(item => {
        const departmentId = item.departmentId; // 流程步骤 ID
        
        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5, 5->6)
        const deptTypeMap = {1: '2', 2: '3', 3: '4', 4: '5', 5: '6'};
        const deptType = deptTypeMap[departmentId] || departmentId.toString(); 
        
        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.notificationPersons?.[departmentId] || [];
        
        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map(person => person.id).join(',');
          
          result.push({
            id: null, 
            businessType: "business", // 业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-6
            executeType: "2", // 执行类型: 2通知
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }
    return result;
  } catch (error) {
    console.error('转换流程数据出错:', error);
    return [];
  }
}

const downloadFile = (row: any) => {
  const fileUrl = row.url ? row.url : null;
  if (fileUrl) {
    window.open(fileUrl, '_blank');
  } else {
    ElMessage.error('附件不存在');
  }
}

// 重置表单
const resetForm = () => {
  if (dataFormRef.value) {
    dataFormRef.value.resetFields()
  }
  transferDialog.selectedAssets = []
  sleectedAssetsData.value = []
  
  // 重置流程组件
  processGroupRef.value?.reset()
}

  // 加载流程数据到组件
  const loadProcessData = (flowData) => {
  try {
    // 准备执行和通知列表数据
    const execList = [];
    const notifyList = [];
    
    // 准备人员数据的映射
    const execPersonsMap = {};
    const notifyPersonsMap = {};
    
    // 遍历流程数据
    flowData.forEach(process => {
      // 确定是执行部门还是通知部门
      const isExecution = process.executeType === '1';
      
      // 映射逆向转换 (2->1, 3->2, 4->3, 5->4)
      const deptTypeReverseMap = {'2': 1, '3': 2, '4': 3, '5': 4};
      const deptTypeNum = deptTypeReverseMap[process.executeDeptType] || Number(process.executeDeptType);
      
      // 创建基础项目
      const baseItem = {
        id: process.id || 0,
        departmentId: deptTypeNum,
        selectedDeptId: process.deptId || 0,
        deptName: '',  // 在组件中处理
        personName: '', // 在组件中处理
        enableSms: process.enableSms === '1',
        smsTemplateId: process.smsTemplateId ? Number(process.smsTemplateId) : undefined,
        smsContent: '',
        notifyType: process.notifyType || 'once',
        notifyPeriod: process.notifyPeriod || 'daily',
        userIds: []
      };
      
      // 解析逗号分隔的用户ID列表
      const userIdList = process.userId ? process.userId.split(',').map(id => id.trim()) : [];
      
      // 根据类型添加到不同列表
      if (isExecution) {
        // 检查执行列表是否已有相同部门类型的项
        const existingIndex = execList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          execList.push(baseItem);
        }
        
        // 添加用户到执行人员映射
        if (userIdList.length > 0) {
          if (!execPersonsMap[deptTypeNum]) {
            execPersonsMap[deptTypeNum] = [];
          }
          
          // 处理每个用户ID
          userIdList.forEach(userId => {
            // 基本用户信息，其他信息将由组件负责加载
            const userInfo = {
              id: userId,
              username: '', // 组件中处理
              nickname: '', // 组件中处理
              deptName: '', // 组件中处理
              mobile: ''    // 组件中处理
            };
            
            // 确保不重复添加用户
            const exists = execPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              execPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      } else {
        // 通知列表处理
        const existingIndex = notifyList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          notifyList.push(baseItem);
        }
        
        if (userIdList.length > 0) {
          if (!notifyPersonsMap[deptTypeNum]) {
            notifyPersonsMap[deptTypeNum] = [];
          }
          
          userIdList.forEach(userId => {
            const userInfo = {
              id: userId,
              username: '',
              nickname: '',
              deptName: '',
              mobile: ''
            };
            
            const exists = notifyPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              notifyPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      }
    });
    
    // 初始化流程组件数据
    nextTick(() => {
      if (processGroupRef.value) {
        processGroupRef.value.initFromExistingData({
          executionList: execList,
          notificationList: notifyList,
          executionPersons: execPersonsMap,
          notificationPersons: notifyPersonsMap
        });
      }
    });
  } catch (error) {
    console.error('流程数据映射出错:', error);
  }
}

// 加载数据
const handleQuery = async () => {
  // 获取资产列表
  const { list } = await assetsAPI.getPage({ pageNum: 1, pageSize: 999 })
  sleectedAssetsData.value = list

  if (props.ticketdata.id) {
    // 获取步骤状态
    const statusRes: any = await businessAPI.getStepStatus(props.ticketdata.id)
    stepStatus.value = statusRes
    for (const step in stepStatus.value) {
      if (stepStatus.value[step] == 'process') {
        nowStep.value = step
        break
      }
    }

    isCurrentStep()
    
    if (!showStep.value) {
      const data = await businessAPI.getFormData(props.ticketdata.id)
      Object.assign(form, data)
      
      if (form.fileList && Array.isArray(form.fileList)) {
            fileList.value = form.fileList;
          }
          
          // 加载已选资产
          if (form.assetIds && Array.isArray(form.assetIds)) {
            transferDialog.selectedAssets = form.assetIds;
          }

      // 加载流程数据
      try {
        const flowData = await businessAPI.getFlow(props.ticketdata.id);
        if (flowData && Array.isArray(flowData)) {
          loadProcessData(flowData);
        }
      } catch (error) {
        console.error('加载流程数据失败:', error);
      }
    }
  } else {
    // 新建模式：应用默认配置
    await nextTick();
    if (processGroupRef.value) {
      console.log('新建工单，应用默认配置');
      try {
        // 调用流程组件的应用默认配置方法
        await processGroupRef.value.applyDefaultConfig();
        console.log('默认配置应用成功');
      } catch (error) {
        console.error('应用默认配置失败:', error);
      }
    }
  }

  if (!form.id) {
    form.id = Number(generateTimeBasedId().slice(0, -1))
  }
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>

/* 在 style 部分添加 */
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.initiate-ticket {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h3 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin: 0;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

.asset-buttons {
  display: flex;
  gap: 12px;
}

.asset-buttons .el-button {
  min-width: 120px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}

/* 输入框样式统一 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 卡片内容区域padding */
:deep(.el-card__body) {
  padding: 20px;
}
</style>
