<template>
  <div class="app-container">
<!--    <div class="page-header">-->
<!--      <h2>盘点任务管理</h2>-->
<!--      <div class="header-actions">-->
<!--        <el-button type="primary" @click="refreshTasks">-->
<!--          <el-icon>-->
<!--            <Refresh />-->
<!--          </el-icon>-->
<!--          刷新任务-->
<!--        </el-button>-->
<!--      </div>-->
<!--    </div>-->
<!--    <div class="mb-6">-->
<!--      <h2 class="text-3xl font-extrabold mb-1 flex items-center text-blue-700">-->
<!--        <el-icon class="mr-3 text-3xl"><DataAnalysis /></el-icon>-->
<!--        盘点任务管理-->
<!--      </h2>-->
<!--      &lt;!&ndash;      <p class="text-gray-600">管理和维护信息安全考核的各项对象和指标</p>&ndash;&gt;-->
<!--    </div>-->

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">任务数量</div>
            <div class="stat-value text-2xl font-bold text-blue-600">
              {{ totalCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-blue-500"><Calendar /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">进行中</div>
            <div class="stat-value text-2xl font-bold text-orange-600">
              {{ inProgressCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-orange-500"><Clock /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">逾期进行中</div>
            <div class="stat-value text-2xl font-bold text-red-600">
              {{ completedCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-green-500"><Clock /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">已完成</div>
            <div class="stat-value text-2xl font-bold text-green-600">
              {{ overdueCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-red-500"><Check /></el-icon>
        </div>
      </el-card>
    </div>

    <el-card class="mb-6">
<!--      <template #header>-->
<!--        <div class="flex justify-between items-center">-->
<!--          <span class="text-lg font-semibold">筛选条件</span>-->
<!--        </div>-->
<!--      </template>-->

      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex flex-1 gap-2">
          <!-- 搜索输入框 -->
          <el-input
            v-model="queryParams.inventoryName"
            placeholder="搜索任务名称、发起部门、发起人员..."
            class="flex-1"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <!-- 搜索按钮 -->
          <el-button
            type="primary"
            class="flex items-center gap-2"
            @click="handleQuery"
          >
            <el-icon><Search /></el-icon>
            <span>搜索</span>
          </el-button>

          <!-- 重置按钮 -->
          <el-button class="flex items-center gap-2" @click="handleResetQuery">
            <el-icon><Refresh /></el-icon>
            <span>重置</span>
          </el-button>

          <!-- 筛选按钮 -->
          <el-button
            class="flex items-center gap-2 ml-auto"
            @click="toggleAdvancedFilters"
          >
            <el-icon><Filter /></el-icon>
            <span>筛选</span>
            <el-icon
              class="transition-transform duration-300"
              :class="{ 'rotate-180': showAdvancedFilters }"
            >
              <ArrowDown />
            </el-icon>
          </el-button>
        </div>
      </div>
    </el-card>
    <!--    <div class="search-container">-->
    <!--      <el-form ref="queryFormRef" :model="queryParams" :inline="true">-->
    <!--        <el-form-item label="ID" prop="id">-->
    <!--          <el-input-->
    <!--            v-model="queryParams.id"-->
    <!--            placeholder="ID"-->
    <!--            clearable-->
    <!--            class="!max-w-[130px]"-->
    <!--            @keyup.enter="handleQuery()"-->
    <!--          />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="任务名称" prop="inventoryName">-->
    <!--          <el-input-->
    <!--            v-model="queryParams.inventoryName"-->
    <!--            placeholder="任务名称"-->
    <!--            clearable-->
    <!--            class="!max-w-[130px]"-->
    <!--            @keyup.enter="handleQuery()"-->
    <!--          />-->
    <!--        </el-form-item>-->
    <!--        &lt;!&ndash; <el-form-item label="盘点状态" prop="status">-->
    <!--            <el-select v-model="queryParams.status" placeholder="请选择" clearable class="!w-[120px]">-->
    <!--              <el-option label="待进行" value="0" />-->
    <!--              <el-option label="进行中" value="1" />-->
    <!--              <el-option label="已完成" value="2" />-->
    <!--              <el-option label="逾期完成" value="3" />-->
    <!--              <el-option label="逾期未完成" value="4" />-->
    <!--            </el-select>-->
    <!--          </el-form-item> &ndash;&gt;-->
    <!--        &lt;!&ndash; <el-form-item label="盘点类型" prop="type">-->
    <!--            <el-select v-model="queryParams.type" placeholder="请选择" clearable class="!w-[120px]">-->
    <!--              <el-option label="即时盘点" value="1" />-->
    <!--              <el-option label="定期盘点" value="2" />-->
    <!--            </el-select>-->
    <!--          </el-form-item> &ndash;&gt;-->

    <!--        &lt;!&ndash; 高级筛选项 &ndash;&gt;-->
    <!--        &lt;!&ndash; <el-form-item v-if="showAdvancedFilters" label="盘点周期" prop="inventoryCycle">-->
    <!--            <el-select v-model="queryParams.inventoryCycle" placeholder="请选择" clearable class="!w-[120px]">-->
    <!--              <el-option label="每天" value="daily" />-->
    <!--              <el-option label="每周" value="weekly" />-->
    <!--              <el-option label="每季度" value="quarterly" />-->
    <!--              <el-option label="每年" value="yearly" />-->
    <!--              <el-option label="无" value="none" />-->
    <!--            </el-select>-->
    <!--          </el-form-item> &ndash;&gt;-->
    <!--        <el-form-item-->
    <!--          v-if="showAdvancedFilters"-->
    <!--          label="创建时间"-->
    <!--          prop="createTime"-->
    <!--        >-->
    <!--          <el-date-picker-->
    <!--            v-model="queryParams.createTime"-->
    <!--            type="datetimerange"-->
    <!--            range-separator="~"-->
    <!--            start-placeholder="开始时间"-->
    <!--            end-placeholder="结束时间"-->
    <!--            value-format="YYYY-MM-DD HH:mm:ss"-->
    <!--          />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item-->
    <!--          v-if="showAdvancedFilters"-->
    <!--          label="创建部门"-->
    <!--          prop="createDeptName"-->
    <!--        >-->
    <!--          <el-input-->
    <!--            v-model="queryParams.createDeptName"-->
    <!--            placeholder="创建部门"-->
    <!--            clearable-->
    <!--            class="!max-w-[130px]"-->
    <!--            @keyup.enter="handleQuery()"-->
    <!--          />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item-->
    <!--          v-if="showAdvancedFilters"-->
    <!--          label="创建人员"-->
    <!--          prop="createUserName"-->
    <!--        >-->
    <!--          <el-input-->
    <!--            v-model="queryParams.createUserName"-->
    <!--            placeholder="创建人员"-->
    <!--            clearable-->
    <!--            class="!max-w-[130px]"-->
    <!--            @keyup.enter="handleQuery()"-->
    <!--          />-->
    <!--        </el-form-item>-->
    <!--        <el-form-item>-->
    <!--          <el-button type="primary" @click="handleQuery()">-->
    <!--            <i-ep-search />搜索-->
    <!--          </el-button>-->
    <!--          <el-button @click="handleResetQuery()">-->
    <!--            <i-ep-refresh />重置-->
    <!--          </el-button>-->
    <!--        </el-form-item>-->
    <!--      </el-form>-->
    <!--      <el-button @click="toggleAdvancedFilters" class="mb-2">-->
    <!--        {{ showAdvancedFilters ? "隐藏高级筛选" : "显示高级筛选" }}-->
    <!--      </el-button>-->
    <!--    </div>-->
    <el-card v-if="showAdvancedFilters" class="mt-4" shadow="never">
      <div class="advanced-filters">
        <el-form :model="queryParams" ref="queryRef" :inline="true">

          <el-form-item label="发起人员" prop="createUserName">
            <el-input
              v-model="queryParams.createUserName"
              placeholder="请输入发起人员姓名"
              clearable
            />
          </el-form-item>

          <el-form-item label="发起部门" prop="createDeptName">
            <el-input
              v-model="queryParams.createDeptName"
              placeholder="请输入发起部门名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="queryParams.startDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <!-- 应用筛选按钮 -->
          <el-form-item class="apply-filters">
            <el-button
              type="primary"
              @click="applyAdvancedFilters"
              :icon="Search"
            >
              应用筛选
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button type="success" @click="handleOpenDialog()">
              <i-ep-plus />
              新增任务
            </el-button>
            <el-button
              type="danger"
              :disabled="ids.length === 0"
              @click="handleDelete()"
            >
              <i-ep-delete />
              删除
            </el-button>
          </div>
          <div></div>

          <div>
            <el-button type="primary" @click="handleQuery()">
              <i-ep-refresh />
              刷新列表
            </el-button>
            <!-- <el-button @click="handleImport">
                <template #icon><i-ep-upload /></template>
  导入
  </el-button>
  <el-button class="ml-3" @click="handleExport">
    <template #icon><i-ep-download /></template>
    导出
  </el-button> -->
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          key="id"
          label="序号"
          prop="id"
          min-width="80"
          align="center"
        />
        <el-table-column
          key="status"
          label="任务状态"
          prop="status"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" class="status-tag">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          key="inventoryName"
          label="任务名称"
          prop="inventoryName"
          min-width="250"
          align="center"
          show-overflow-tooltip
        />
        <!-- 修改盘点进度列 -->
        <el-table-column
          key="inventoryProgress"
          label="盘点结果"
          min-width="300"
          align="center"
        >
          <template #default="scope">
            <div class="text-sm text-gray-500">
              <!-- 资产数量统计 -->
              <div class="mb-1">
                资产数量: {{ scope.row.sumInventoryNum || 0 }}个, 已盘点:
                {{ scope.row.stopInventoryNum || 0 }}个, 未盘点:
                {{ scope.row.treatInventoryNum || 0 }}个
              </div>

              <!-- 进度条 -->
              <div class="flex items-center gap-2">
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    class="h-2.5 rounded-full transition-all duration-500 ease-out"
                    :class="{
                      'bg-red-500': scope.row.inventoryProgress < 30,
                      'bg-yellow-500':
                        scope.row.inventoryProgress >= 30 &&
                        scope.row.inventoryProgress < 70,
                      'bg-green-500': scope.row.inventoryProgress >= 70,
                    }"
                    :style="{ width: `${scope.row.inventoryProgress}%` }"
                  ></div>
                </div>
                <span class="text-xs font-medium">
                  {{ scope.row.inventoryProgress }}%
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          key="treatInventoryNum"
          label="盘点策略"
          prop="treatInventoryNum"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <span>{{ scope.row.treatInventoryNum || 0 }} 个</span>
          </template>
        </el-table-column>
        <!-- <el-table-column key="type" label="盘点类型" prop="type" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.type === '2' ? 'success' : 'warning'">
                {{ scope.row.type === '2' ? '定期盘点' : '即时盘点' }}
              </el-tag>
            </template>
          </el-table-column> -->
        <!-- <el-table-column key="inventoryCycle" label="盘点周期" prop="inventoryCycle" min-width="100" align="center" /> -->
        <el-table-column
          prop="startTime"
          label="开始日期"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{ formatDateOnly(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column
          key="deadline"
          label="截止日期"
          prop="deadline"
          min-width="100"
          align="center"
        />
        <el-table-column
          prop="createTime"
          label="创建日期"
          align="center"
          width="100"
        >
          <template #default="{ row }">
            {{ formatDateOnly(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          key="createDeptName"
          label="发起部门"
          prop="createDeptName"
          min-width="140"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          key="createUserName"
          label="发起人员"
          prop="createUserName"
          min-width="200"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.createUserName }}({{row.createUserPhone || 153652222555}})
          </template>
        </el-table-column>

        <!-- 优化操作按钮 -->
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <div class="flex items-center justify-center gap-1">
              <el-button
                v-hasPerm="['system:inventory:query']"
                type="success"
                :size="'small'"
                class="!px-2 !h-[28px] !text-xs"
                @click="handleViewDetail(scope.row.id)"
              >
                <i-ep-view class="mr-1" />
                查看
              </el-button>
              <el-button
                v-hasPerm="['system:inventory:edit']"
                type="primary"
                :size="'small'"
                class="!px-2 !h-[28px] !text-xs"
                @click="handleOpenDialog(scope.row.id)"
              >
                <i-ep-edit class="mr-1" />
                编辑
              </el-button>
              <!--            <el-button-->
              <!--              v-hasPerm="['system:inventory:delete']"-->
              <!--              type="danger"-->
              <!--              size="small"-->
              <!--              @click="handleDelete(scope.row.id)"-->
              <!--            >-->
              <!--              <i-ep-delete />-->
              <!--              删除-->
              <!--            </el-button>-->
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页器保持不变 -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 资产盘点表单弹窗 -->
    <inventory-dialog
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :id="dialog.id"
      @submitted="handleQuery"
    />

    <!-- 资产盘点详情弹窗 -->
    <inventory-detail-dialog
      v-model:visible="detailDialog.visible"
      :title="detailDialog.title"
      :id="detailDialog.id"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Inventory",
  inheritAttrs: false,
});
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, ElForm } from "element-plus";
import {
  Search,
  Refresh,
  Filter,
  ArrowDown,
  Calendar,
  Clock,
  Warning,
  Check,
} from "@element-plus/icons-vue";
import InventoryAPI, {
  InventoryPageQuery,
  AssetInventoryVO,
} from "@/api/assets_management/assets_inventory/index";
import InventoryDialog from "./components/InventoryAddDialog.vue";
import InventoryDetailDialog from "./components/InventoryDetailDialog.vue";
import { formatDateOnly } from "@/utils/dateUtils";

const queryFormRef = ref(ElForm);
const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const allData = ref<AssetInventoryVO[]>([]);

// 查询参数
const queryParams = reactive<InventoryPageQuery>({
  pageNum: 1,
  pageSize: 10,
  id: "",
  inventoryName: "",
  status: "",
  type: "",
  inventoryCycle: "",
  createTime: undefined,
  createDeptName: "",
  createUserName: "",
});
// 资产盘点表格数据
const pageData = ref<AssetInventoryVO[]>([]);

// =======================统计卡片===========================
// 添加统计数据的计算属性
const totalCount = computed(() => {
  return total;
});

const inProgressCount = computed(() => {
  return allData.value.filter((item) => item.status === "1").length;
});

const overdueCount = computed(() => {
  return allData.value.filter((item) => {
    const now = new Date();
    const deadline = item.deadline ? new Date(item.deadline) : null;
    // 状态为进行中且已超过截止日期
    return item.status === "2" && deadline && now > deadline;
  }).length;
});

const completedCount = computed(() => {
  return allData.value.filter((item) => item.status === "4").length;
});
// 弹窗控制
const dialog = reactive({
  visible: false,
  title: "",
  id: undefined as number | undefined,
});

const strategyDialog = reactive({
  visible: false,
  title: "",
  id: undefined as number | undefined,
});

// 详情弹窗控制
const detailDialog = reactive({
  visible: false,
  title: "盘点任务详情",
  id: undefined as number | undefined,
});

// 状态处理方法
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    "0": "info", // 待进行
    "1": "primary", // 进行中
    "2": "success", // 已完成
    "3": "warning", // 逾期完成
    "4": "danger", // 逾期未完成
  };
  return statusMap[status] || "info";
};

// 盘点状态标签
const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    "0": "待进行",
    "1": "进行中",
    "2": "已完成",
    "3": "逾期完成",
    "4": "逾期未完成",
  };
  return statusMap[status] || "未知状态";
};
const showAdvancedFilters = ref(false);
// 切换高级筛选显示
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value;
};

// 进度格式化
const calculatePercentage = (completed: number, total: number): number => {
  if (!total || total === 0) return 0;
  const percentage = (completed / total) * 100;
  return Math.round(percentage); // 四舍五入到整数
};

// 查询方法
function handleQuery() {
  loading.value = true;
  InventoryAPI.getPage(queryParams)
    .then((response) => {
      pageData.value = response.list;
      total.value = response.total;
    })
    .catch((error) => {
      console.error("获取盘点数据失败:", error);
      ElMessage.error("获取盘点列表数据失败");
      pageData.value = [];
      total.value = 0;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleQueryNoPage() {
  loading.value = true;
  Promise.all([
    // 获取分页数据
    InventoryAPI.getPage(queryParams),
    // 获取所有数据（设置较大的pageSize）
    InventoryAPI.getPage({
      pageNum: 1,
      pageSize: 99999, // 设置一个足够大的数以获取所有数据
    }),
  ])
    .then(([pageResponse, allResponse]) => {
      // 更新分页数据
      pageData.value = pageResponse.list;
      total.value = pageResponse.total;
      // 更新所有数据用于统计
      allData.value = allResponse.list;
    })
    .catch((error) => {
      console.error("获取数据失败:", error);
      ElMessage.error("获取数据失败");
      pageData.value = [];
      total.value = 0;
      allData.value = [];
    })
    .finally(() => {
      loading.value = false;
    });
}

// 重置查询
function handleResetQuery() {
  // 重置查询参数到初始状态
  Object.assign(queryParams, {
    pageNum: 1,
    pageSize: 10,
    id: "",
    inventoryName: "",
    status: "",
    type: "",
    inventoryCycle: "",
    createTime: undefined,
    createDeptName: "",
    createUserName: "",
    startTimeRange: undefined
  });
  handleQuery();
  handleQueryNoPage();
}

// 选择处理
function handleSelectionChange(selection: AssetInventoryVO[]) {
  ids.value = selection.map((item) => item.id);
}

// 弹窗操作
function handleOpenDialog(id?: number) {
  console.log("打开弹窗", id);
  dialog.id = id;
  dialog.visible = true;
  dialog.title = id ? "修改资产盘点" : "新增资产盘点";
}

// 查看详情
function handleViewDetail(id: number) {
  console.log("查看详情", id);
  detailDialog.id = id;
  detailDialog.visible = true;
}

// 判断是否已经过了开始时间
const isPastStartTime = (row: any) => {
  // 如果状态不是待进行(0)，则表示已经开始
  if (row.status !== "0") return true;

  // 如果有开始时间且已经过了开始时间
  if (row.startTime) {
    const startTime = new Date(row.startTime);
    const now = new Date();
    return now >= startTime;
  }

  return false;
};

// 删除操作
function handleDelete(id?: number) {
  const deleteIds = id ? String(id) : ids.value.join(",");
  if (!deleteIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      InventoryAPI.deleteByIds(deleteIds)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
          handleQueryNoPage();
        })
        .catch((error) => {
          console.error("删除失败:", error);
          ElMessage.error("删除失败");
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

// 导入导出方法
const handleImport = () => {
  // ElMessage.info('导入功能待实现')
};

const handleExport = () => {
  // loading.value = true
  // InventoryAPI.exportInventory(queryParams)
  //   .then(response => {
  //     const blob = new Blob([response])
  //     const link = document.createElement('a')
  //     link.href = window.URL.createObjectURL(blob)
  //     link.download = `资产盘点数据_${new Date().getTime()}.xlsx`
  //     link.click()
  //     window.URL.revokeObjectURL(link.href)
  //     ElMessage.success('导出成功')
  //   })
  //   .catch(error => {
  //     console.error('导出失败:', error)
  //     ElMessage.error('导出失败')
  //   })
  //   .finally(() => {
  //     loading.value = false
  //   })
};

onMounted(() => {
  console.log("所有盘点任务数据：", pageData.value);
  handleQuery();
  handleQueryNoPage();
});
</script>

<style scoped>
/* 搜索框相关样式 */
.el-input {
  --el-input-height: 40px;
}

.el-button {
  height: 40px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 动画过渡效果 */
.transition-transform {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}
/* 添加状态标签样式 */
:deep(.status-tag) {
  border-radius: 12px;
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
}
</style>
