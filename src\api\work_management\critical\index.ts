import request from "@/utils/request";

const EVENTS_BASE_URL = "/api/v1/eventss";

class eventsAPI {
    /** 获取重保清单管理分页数据 */
    static getPage(queryParams?: eventsPageQuery) {
        return request<any, PageResult<eventsPageVO[]>>({
            url: `${EVENTS_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /** 获取重保清单管理分页数据 */
    static getList() {
      return request<any, eventsForm[]>({
        url: `${EVENTS_BASE_URL}/list`,
        method: "get",
      });
    }
    /**
     * 获取重保清单管理表单数据
     *
     * @param id eventsID
     * @returns events表单数据
     */
    static getFormData(id: number) {
        return request<any, eventsForm>({
            url: `${EVENTS_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加重保清单管理*/
    static add(data: eventsForm) {
        return request({
            url: `${EVENTS_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新重保清单管理
     *
     * @param id eventsID
     * @param data events表单数据
     */
    static update(id: number, data: eventsForm) {
        return request({
            url: `${EVENTS_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除重保清单管理，多个以英文逗号(,)分割
     *
     * @param ids 重保清单管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${EVENTS_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /** 
     * 新增重保关联资产
     */
    static addEventsSystems(id: any, data: number[]) {
        return request({
            url: `${EVENTS_BASE_URL}/${id}/addEventsSystems`,
            method: "post",
            data: data,
        });
    }

    /**
     * 重保事件关联资产
     */
    static getAssets(id: any) {
        return request<any, any>({
            url: `${EVENTS_BASE_URL}/${id}/selectAssets`,
            method: "get",
        });
      }
}

export default eventsAPI;

/** 重保清单管理分页查询参数 */
export interface eventsPageQuery extends PageQuery {
    /** 重保id */
    id?: number;
    /** 重保事件名称 */
    eventName?: string;
    /** 关联资产(每个资产id,以“,”分割) */
    assets?: string;
    /** 重保创建人 */
    createdBy?: string;
    /** 重保创建时间 */
    createTime?: [string, string];
    /** 结束时间 */
    endTime?: [string, string];
    /** 优先级 */
    priority?: number;
    /** 重保事件的备注信息 */
    remarks?: string;
    /** 开始时间 */
    startTime?: [string, string];
    /** 重保更新时间 */
    updateTime?: [string, string];
    /** 重保状态 */
    status?: string;
}

/** 重保清单管理表单对象 */
export interface eventsForm {
    /** 重保id */
    id?:  number;
    /** 重保事件名称 */
    eventName?:  string;
    /** 重保创建人 */
    createdBy?:  string;
    /** 重保创建时间 */
    createTime?:  string;
    /** 结束时间 */
    endTime?:  string;
    /** 优先级 */
    priority?:  number;
    /** 重保事件的备注信息 */
    remarks?:  string;
    /** 开始时间 */
    startTime?:  string;
    /** 重保更新时间 */
    updateTime?:  string;
    /** 重保状态 */
    status?:  string;
    /** 关联重保Id */
    associationEventsId?:  number;
    /** 关联信息系统id */
    systemsIds?:  [];
}

/** 重保清单管理分页对象 */
export interface eventsPageVO {
    /** 重保id */
    id?: number;
    /** 重保事件名称 */
    eventName?: string;
    /** 关联资产(每个资产id,以“,”分割) */
    assets?: string;
    /** 重保创建人 */
    createdBy?: string;
    /** 重保创建时间 */
    createTime?: Date;
    /** 结束时间 */
    endTime?: Date;
    /** 优先级 */
    priority?: number;
    /** 重保事件的备注信息 */
    remarks?: string;
    /** 开始时间 */
    startTime?: Date;
    /** 重保更新时间 */
    updateTime?: Date;
    /** 重保状态 */
    status?: string;
}
