<template>
  <el-dialog v-model="dialogVisible" :title="'盘点任务详情: ' + (taskData?.taskName || '')" width="80%" destroy-on-close>
    <div v-loading="loading" class="inventory-info-container">
      <!-- 任务基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-descriptions v-if="taskData" :column="3" border size="medium">
          <el-descriptions-item label="任务编号">{{ taskData.taskId }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskData.taskName }}</el-descriptions-item>
          <el-descriptions-item label="发起部门">{{ taskData.deptName }}</el-descriptions-item>
          <el-descriptions-item label="发起人员">{{ taskData.username }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ taskData.createTime }}</el-descriptions-item>
          <el-descriptions-item label="截止时间">{{ taskData.deadline }}</el-descriptions-item>
          <el-descriptions-item label="资产数量">{{ taskData.assetCount || 0 }} 个</el-descriptions-item>
          <el-descriptions-item label="盘点状态">
            <el-tag :type="getStatusType(taskData.inventoryStatus)">
              {{ getStatusText(taskData.inventoryStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="完成进度">
            <div style="display: flex; align-items: center;">
              <el-progress :percentage="displayCompletionRate" :status="getProgressStatus(displayCompletionRate)"
                :show-text="false" style="flex-grow: 1;" />
              <span style="margin-left: 10px; white-space: nowrap; min-width: 40px;">
                {{ taskData.inventoryAssetsNum || 0 }}/{{ taskData.assetsNum || 0 }}
              </span>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 资产列表与过滤 -->
      <el-card>
        <template #header>
          <div class="card-header">
            <span>盘点资产列表</span>
            <div class="filter-actions">
              <el-select v-model="filterStatus" placeholder="盘点状态" clearable style="width: 120px"
                @change="filterAssets">
                <el-option label="未盘点" value="0" />
                <el-option label="已盘点" value="1" />
              </el-select>
              <el-select v-model="filterType" placeholder="资产类型" clearable style="width: 120px; margin-left: 10px"
                @change="filterAssets">
                <el-option label="服务器" value="1" />
                <el-option label="网络设备" value="2" />
                <el-option label="安全设备" value="3" />
                <el-option label="信息系统" value="10" />
                <el-option label="其他类型" value="other" />
              </el-select>
              <el-input v-model="searchKeyword" placeholder="搜索资产" clearable style="width: 180px; margin-left: 10px"
                @input="filterAssets">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
        </template>

        <!-- 资产表格 -->
        <el-table :data="filteredAssets" border style="width: 100%" :row-class-name="getRowClassName">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="inventoryStatus" label="盘点状态" width="90" align="center">
            <template #default="scope">
              <el-tag :type="getInventoryStatusType(scope.row.inventoryStatus)">
                {{ getInventoryStatusText(scope.row.inventoryStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="type" label="资产类型" width="90" align="center">
            <template #default="scope">
              <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="资产名称" min-width="120" show-overflow-tooltip align="center" />
          <el-table-column label="IP/域名" min-width="120" show-overflow-tooltip align="center">
            <template #default="scope">
              <span>{{ scope.row.ip || scope.row.url || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="管理部门" min-width="100" show-overflow-tooltip align="center" />
          <el-table-column prop="managerName" label="管理人员" width="100" align="center" show-overflow-tooltip />
          <el-table-column label="变动状况" width="120" align="center">
            <template #default="scope">
              <el-popover placement="top" :width="300" trigger="hover" v-if="scope.row.changeStatus === '2'">
                <template #reference>
                  <el-tag type="warning" class="cursor-pointer">
                    已变更 <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tag>
                </template>
                <div>
                  <div class="popover-title">变更详情</div>
                  <el-divider class="my-2" />
                  <div class="popover-row">
                    <span class="label">变更时间：</span>
                    <span>{{ scope.row.inventoryTime || '-' }}</span>
                  </div>
                  <div class="popover-row">
                    <span class="label">变更状态：</span>
                    <span>{{ getChangeStatusText(scope.row.changeStatus) }}</span>
                  </div>
                  <div class="popover-row">
                    <span class="label">操作人员：</span>
                    <span>{{ scope.row.managerName || '当前用户' }}</span>
                  </div>
                </div>
              </el-popover>
              <span v-else class="text-muted">{{ getChangeStatusText(scope.row.changeStatus) || '无变动' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="viewAssetDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空数据显示 -->
        <el-empty v-if="filteredAssets.length === 0" description="无匹配资产" />

        <!-- 分页组件 -->
        <div class="pagination-container" v-if="filteredAssets.length > 0">
          <el-pagination background layout="prev, pager, next" :total="assetsList.length" :page-size="10"
            @current-change="handlePageChange" />
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

    <!-- 资产详情弹窗 -->
    <!-- 服务器详情 -->
    <asset-detail-view
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '1'"
      v-model:visible="assetDetailDialog.visible"
      :title="`查看服务器详情: ${assetDetailDialog.asset?.name || ''}`"
      :asset-id="assetDetailDialog.assetId"
    />

    <!-- 网络设备详情 -->
    <network-detail-view
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '2'"
      v-model:visible="assetDetailDialog.visible"
      :title="`查看网络设备详情: ${assetDetailDialog.asset?.name || ''}`"
      :asset-id="assetDetailDialog.assetId"
    />

    <!-- 安全设备详情 -->
    <safety-detail-view
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '3'"
      v-model:visible="assetDetailDialog.visible"
      :title="`查看安全设备详情: ${assetDetailDialog.asset?.name || ''}`"
      :asset-id="assetDetailDialog.assetId"
    />

    <!-- 信息系统详情 - 修改为正确的组件引用和属性 -->
    <view-system-assets
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '10'"
      v-model:visible="assetDetailDialog.visible"
      :system-id="assetDetailDialog.assetId"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { Search, InfoFilled } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import InventoryAPI, { AssetInventoryAListVO } from "@/api/assets_management/assets_inventory/index";

// 引入资产详情组件 - 修改引用路径
import AssetDetailView from '../../../DetailOfAssets/details/components/AssetDetailView.vue';
import NetworkDetailView from '../../../DetailOfAssets/networkFacility/components/NetworkDetailView.vue';
import SafetyDetailView from '../../../DetailOfAssets/safetyFacility/components/SafetyDetailView.vue';
import ViewSystemAssets from '../../../DetailOfAssets/system/components/ViewSystemAssets.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: [Number, String],
    default: ''
  },
  taskData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible']);

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

// 资产详情弹窗配置
const assetDetailDialog = reactive({
  visible: false,
  title: '资产详情',
  assetId: 0,
  asset: null as any,
  assetType: ''
});

// 查看资产详情 - 修改处理逻辑，参考台账的实现
const viewAssetDetail = (asset) => {
  console.log('查看资产详情:', asset); // 添加调试日志

  assetDetailDialog.assetId = asset.id;
  assetDetailDialog.title = `资产详情: ${asset.name}`;
  assetDetailDialog.asset = asset;
  assetDetailDialog.assetType = String(asset.type);
  assetDetailDialog.visible = true;

  console.log('弹窗配置:', assetDetailDialog); // 添加调试日志

  // 特别为信息系统添加调试信息
  if (asset.type === 10 || asset.type === '10') {
    console.log('这是信息系统，应该显示 ViewSystemAssets 组件');
    console.log('systemId:', asset.id);
  }
};

// 定义资产对象接口
interface Asset {
  id: number;
  inventoryAssetsId: number;
  name: string;
  type: string | number;
  ip?: string;
  url?: string;
  deptId?: number;
  deptName?: string;
  managerName?: string;
  inventoryStatus: string;
  inventoryTime?: string | null;
  changeStatus?: string;  // 资产变动状态（1无变动 2已变动 3已下线）
}

// 状态管理
const loading = ref(false);
const filterStatus = ref('');
const filterType = ref('');
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const assetsList = ref<Asset[]>([]);
const filteredAssets = computed(() => {
  let filtered = [...assetsList.value];

  // 按状态筛选
  if (filterStatus.value) {
    filtered = filtered.filter(asset => asset.inventoryStatus === filterStatus.value);
  }

  // 按资产类型筛选
  if (filterType.value) {
    if (filterType.value === 'other') {
      // 选择"其他类型"时，显示不属于1,2,3,10的类型
      filtered = filtered.filter(asset => !['1', '2', '3', '10'].includes(String(asset.type)));
    } else {
      // 正常类型筛选
      filtered = filtered.filter(asset => String(asset.type) === filterType.value);
    }
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(asset =>
      (asset.name || '').toLowerCase().includes(keyword) ||
      (asset.ip || '').toLowerCase().includes(keyword) ||
      (asset.url || '').toLowerCase().includes(keyword) ||
      (asset.deptName || '').toLowerCase().includes(keyword) ||
      (asset.managerName || '').toLowerCase().includes(keyword)
    );
  }

  return filtered;
});

// 获取盘点任务状态类型颜色
const getStatusType = (status: string | number) => {
  const statusMap: Record<string, string> = {
    '0': 'info',    // 待进行
    '1': 'primary',  // 进行中
    '2': 'success', // 已完成
    '3': 'warning',  // 逾期完成
    '4': 'danger'   // 逾期未完成
  };
  return statusMap[String(status)] || 'info';
};

// 获取盘点任务状态文本
const getStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    '0': '待进行',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期完成',
    '4': '逾期未完成'
  };
  return statusMap[String(status)] || '未知状态';
};

// 获取进度条状态
const getProgressStatus = (percentage: number) => {
  if (percentage >= 100) return 'success';
  if (percentage > 0) return '';
  return 'exception';
};

// 获取资产类型名称
const getAssetTypeName = (type: string | number) => {
  const typeMap: Record<string, string> = {
    '1': '服务器',
    '2': '网络设备',
    '3': '安全设备',
    '10': '信息系统'
  };
  return typeMap[String(type)] || '其他类型';
};

// 获取资产盘点状态类型
const getInventoryStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': 'info',     // 未盘点
    '1': 'success',  // 已盘点
    '2': 'danger',    // 逾期未盘点
    '3': 'warning'   // 逾期已盘点
  };
  return statusMap[status] || 'info';
};

// 获取资产盘点状态文本
const getInventoryStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '0': '未盘点',
    '1': '已盘点',
    '2': '逾期未盘点',
    '3': '逾期已盘点'
  };
  return statusMap[status] || '未知状态';
};

// 获取变更状态文本
const getChangeStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    '1': '无变动',
    '2': '已变动',
    '3': '已下线'
  };
  return statusMap[status] || '无变动';
};

// 获取行样式类名
const getRowClassName = ({ row }) => {
  if (row.inventoryStatus === '0') return 'unverified-row';
  if (row.changeStatus === '3') return 'offline-row';
  if (row.changeStatus === '2') return 'changed-row';
  return '';
};

// 添加到script部分的computed区域
const displayCompletionRate = computed(() => {
  // 如果资产总数为0，返回100%
  if (assetsList.value.length === 0) {
    return 100;
  }
  // 否则使用传入的完成率
  return props.taskData.completionRate || 0;
});

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 过滤资产
const filterAssets = () => {
  // 重置到第一页
  currentPage.value = 1;
};

// 加载任务资产数据
const loadTaskAssets = async () => {
  if (!props.taskId) return;

  loading.value = true;

  try {
    console.log('正在获取盘点任务数据，ID:', props.taskId);
    const response = await InventoryAPI.getAssetInventory(Number(props.taskId));
    console.log('获取到的盘点任务数据:', response);

    if (response) {
      const data = response;

      // 将API返回的资产数据转换为组件需要的格式
      if (data.assetsList && Array.isArray(data.assetsList)) {
        assetsList.value = data.assetsList.map((item: AssetInventoryAListVO) => ({
          id: item.assetsId,
          inventoryAssetsId: item.inventoryAssetsId,
          name: item.name || '',
          type: item.type,
          ip: item.ip || '',
          url: item.url || '',
          deptName: item.deptName || '',
          managerName: item.managerName || '',
          inventoryStatus: item.inventoryStatus || '0',
          inventoryTime: item.inventoryTime || item.createTime,
          changeStatus: item.changeStatus // 资产变动状态
        }));

        console.log('转换后的资产列表:', assetsList.value.length, '条数据');
      } else {
        console.warn('API返回的数据中没有资产列表或格式不正确');
        assetsList.value = [];
      }
    } else {
      assetsList.value = [];
    }
  } catch (error) {
    console.error('获取任务资产失败:', error);
    ElMessage.error('获取任务资产数据失败');
    assetsList.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听对话框显示状态变化
watch(dialogVisible, (visible) => {
  if (visible && props.taskId) {
    loadTaskAssets();
  } else {
    // 重置过滤条件
    filterStatus.value = '';
    filterType.value = '';
    searchKeyword.value = '';
    currentPage.value = 1;
  }
});

// 组件挂载
onMounted(() => {
  if (dialogVisible.value && props.taskId) {
    loadTaskAssets();
  }
});
</script>

<style scoped>
.inventory-info-container {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-actions {
  display: flex;
  align-items: center;
}

.mb-4 {
  margin-bottom: 20px;
}

/* 表格行样式 */
:deep(.unverified-row) {
  --el-table-tr-bg-color: rgba(247, 247, 247, 0.15);
  color: var(--el-text-color-secondary);
}

:deep(.offline-row) {
  --el-table-tr-bg-color: rgba(230, 230, 230, 0.3);
  color: var(--el-text-color-disabled);
}

:deep(.changed-row) {
  --el-table-tr-bg-color: rgba(253, 246, 236, 0.3);
  color: var(--el-color-warning-dark-2);
}

/* 分页容器 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.cursor-pointer {
  cursor: pointer;
}

.ml-1 {
  margin-left: 4px;
}

.text-muted {
  color: #909399;
  font-size: 13px;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.popover-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.popover-row {
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.4;
  color: #606266;
}

.popover-row .label {
  color: #909399;
  margin-right: 4px;
}
</style>
