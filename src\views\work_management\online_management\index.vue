<template>
  <div :key="componentKey" class="assessment-management">
    <el-tabs v-model="activeTab" :class="{ 'hide-tabs-header': activeTab === 'list' }">
      <!-- 流程标签 -->
      <el-tab-pane label="流程" name="process">
        <template v-if="activeTab === 'process'">
          <online-process :ticketId="processParams" @init="init" />
        </template>
      </el-tab-pane>
      <!-- 详情标签 -->
      <el-tab-pane label="返回列表" name="list">
        <!-- 根据type参数动态渲染对应组件 -->
        <component 
          :is="currentComponent" 
          @navigateToProcess="navigateToProcess" 
          :id="getId" 
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

import OnlineProcess from "./components/onlineAssprocess.vue";
import OnlineDetails from "./components/onlineDetails.vue";
import evaluateDetails from "./components/evaluateDetails.vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const activeTab = ref("list");
const processParams = ref<number | undefined>();

const componentKey = ref(0);
// 从get请求获取id
const getId = Number(route.query.id);

// 根据路由参数type动态选择组件
const currentComponent = computed(() => {
  const type = route.query.type as string;
  
  switch (type) {
    case 'safetyDetails':
      return SafetyDetails;
    case 'evaluateDetails':
      return evaluateDetails;
    case 'onlineDetails':
      return OnlineDetails;
    default:
      // 默认展示业务上线详情
      return OnlineDetails;
  }
});

const navigateToProcess = (ticketId: number | undefined) => {
  console.log("ticketId", ticketId);
  processParams.value = ticketId;
  activeTab.value = "process";
};

const init = async (id: number) => {
  console.log("123123123123123", id);
  activeTab.value = "list";
  // 强制重新渲染
  componentKey.value++;
  // 重新获取数据
  // await getData(id);
};
</script>

<style scoped>
.assessment-management {
  padding: 20px ;
}

/* 隐藏标签栏 */
.hide-tabs-header :deep(.el-tabs__header) {
  display: none;
}

/* 美化标签栏 */
:deep(.el-tabs__header) {
  margin-bottom: 20px;
  border-bottom: 2px solid var(--el-border-color-light);
  padding: 0 20px; /* 添加内边距避免贴边 */
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: bold;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 2px;
}

/* 将标签设置为flex布局 */
:deep(.el-tabs__nav) {
  display: flex;
  width: 100%;
}

:deep(.el-tabs__item[aria-controls="pane-list"]) {
  margin-left: auto;

}
:deep(.el-tabs__item[aria-controls="pane-list"]::before) {
}
</style>
