<template>
  <el-dialog 
    v-model="props.visible" 
    :title="title" 
    width="600px" 
    @close="handleClose"
  >
    <el-alert
      type="info"
      :closable="false"
      show-icon
    >
      已选择 {{ selectedIds.length }} 个任务
    </el-alert>
    
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="160px" class="mt-4">
      <el-form-item label="资产是否有继续使用" prop="isContinueUse">
        <el-radio-group v-model="formData.isContinueUse">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item 
        v-if="formData.isContinueUse"
        label="资产信息是否有变动" 
        prop="hasInfoChanged"
      >
        <el-radio-group v-model="formData.hasInfoChanged">
          <!-- <el-radio :label="true">是</el-radio> -->
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import InventoryAPI, { AssetInventoryBatchForm } from "@/api/assets_management/assets_inventory/index";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '批量编辑盘点策略'
  },
  selectedIds: {
    type: Array as PropType<number[]>,
    default: () => []
  },
  taskId: {
    type: [Number, String],
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'submitted'])

const formRef = ref<FormInstance>()
const submitting = ref(false)
const formData = reactive({
  isContinueUse: undefined as boolean | undefined,
  hasInfoChanged: undefined as boolean | undefined
})

const rules = reactive({
  isContinueUse: [
    { required: true, message: "请选择资产是否继续使用", trigger: "change" }
  ],
  hasInfoChanged: [
    { required: true, message: "请选择资产信息是否有变动", trigger: "change" }
  ]
})

const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitting.value = true;
    
    // 构建API所需的请求对象 - 调整为接口定义的格式
    const requestData: AssetInventoryBatchForm = {
      assetIds: props.selectedIds, // 使用正确的字段名 assetIds
      // 将布尔值转换为字符串 (0否 1是)
      isContinueUse: formData.isContinueUse ? '1' : '0',
      // 如果继续使用且有变动，则添加相应字段
      isChange: formData.isContinueUse && formData.hasInfoChanged ? '1' : '0'
    };
    
    try {
      // 调用正确的API方法
      await InventoryAPI.batchAssetInventory(Number(props.taskId), requestData);
      
      ElMessage.success('批量盘点提交成功');
      emit('submitted');
      handleClose();
    } catch (error) {
      console.error('批量提交盘点结果失败:', error);
      ElMessage.error('批量提交盘点结果失败，请重试');
    } finally {
      submitting.value = false;
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请完善表单信息')
  }
}

const handleClose = () => {
  emit('update:visible', false)
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex;
  gap: 30px;
}

.mt-4 {
  margin-top: 1rem;
}
</style>
