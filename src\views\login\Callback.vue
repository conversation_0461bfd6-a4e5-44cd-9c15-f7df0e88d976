<template>
  <div class="auth-callback">
    <div class="loading-container">
      <!-- 修改为正确的Element Plus加载组件 -->
      <el-icon class="loading-icon is-loading" :size="40">
        <i-ep-loading />
      </el-icon>
      <p>正在处理统一认证登录，请稍候...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter, useRoute, LocationQuery } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import { ElMessage } from 'element-plus';
import { SsoLoginData } from "@/api/auth";

const router = useRouter();
const route = useRoute();

const ssoLoginData = ref<SsoLoginData>({
  code: "",
  redirectUri: "",
} as SsoLoginData);


/** 解析 redirect 字符串 为 path 和  queryParams */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

onMounted(async () => {
  const userStore = useUserStore();
  try {
    // 获取URL中的code参数
    const code = route.query.code as string;
    console.log('code:', code);
    if (!code) {
      throw new Error('未获取到授权码');
    }
    // 获取当前地址作为回调地址
    const redirectUri = encodeURIComponent(window.location.origin + '/#/auth/callback');

    ssoLoginData.value.code = code;
    ssoLoginData.value.redirectUri = redirectUri;

    // 调用后端接口完成SSO登录
      await userStore.loginWithSso(ssoLoginData.value);
      const { path, queryParams } = parseRedirect();
      router.push({ path: path, query: queryParams });

  } catch (error) {
    console.error('统一认证登录失败:', error);
    router.replace('/login');
  }
});
</script>

<style scoped>
.auth-callback {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: var(--el-bg-color);
}

.loading-container {
  text-align: center;
  padding: 30px 50px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-lighter);
}

.loading-icon {
  font-size: 40px;
  color: var(--el-color-primary);
}

.loading-container p {
  margin-top: 20px;
  color: var(--el-text-color-primary);
  font-size: 16px;
}
</style>
