<template>
  <el-dialog v-model="dialogVisible" title="下线工单详情" width="90%" :close-on-click-modal="false" destroy-on-close
    class="ticket-detail-dialog">
    <div class="ticket-detail-container">
      <!-- 基本信息卡片 -->
      <el-card class="info-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单基本信息</span>
            <div class="header-actions">
              <el-tag :type="ticketData.status == '2' ? 'success' : 'danger'">
                {{ ticketData.status == '2' ? '已完成' : '处理中' }}
              </el-tag>
            </div>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="工单编号">{{ ticketData.id }}</el-descriptions-item>
          <el-descriptions-item label="下线事件名称">{{ ticketData.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="ticketData.status == '2' ? 'success' : 'danger'">
              {{ ticketData.status == '2' ? '已完成' : '处理中' }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="申请人">{{ ticketData.applicantName }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ ticketData.contact }}</el-descriptions-item>
          <el-descriptions-item label="当前步骤">
            <el-tag>{{ ticketData.step }}</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="提交时间">{{ formatDateTime(ticketData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(ticketData.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="计划下线时间">{{ formatDateTime(ticketData.plannedOfflineTime)
            }}</el-descriptions-item>

          <el-descriptions-item label="下线原因" :span="3">
            <div class="content-block">{{ ticketData.reason || '暂无描述' }}</div>
          </el-descriptions-item>

          <el-descriptions-item label="备注" :span="3">
            <div class="remarks-block">{{ ticketData.remark || '暂无备注' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 关联资产列表 -->
      <el-card class="asset-card mb-4" v-if="ticketData.assetsList && ticketData.assetsList.length > 0">
        <template #header>
          <div class="card-header">
            <span class="header-title">关联资产</span>
            <div class="header-actions">
              <el-tag>共 {{ ticketData.assetsList.length }} 个资产</el-tag>
            </div>
          </div>
        </template>
        <el-table :data="ticketData.assetsList" style="width: 100%" border>
          <el-table-column type="index" label="序号" width="80" align="center" />
          <el-table-column prop="type" label="资产类型" width="120" align="center">
            <template #default="scope">
              <el-tag size="small">{{ getAssetTypeName(scope.row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="资产名称" min-width="150" />
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
                {{ scope.row.status == '1' ? '正常' : (scope.row.status == '0' ? '异常' : '废弃') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="地址" min-width="180" show-overflow-tooltip>
            <template #default="scope">
                <!-- 如果是信息系统(type=10)则显示url，否则显示ip -->
                <span>{{ scope.row.type === 10 ? scope.row.url : scope.row.ip }}</span>
              </template>
          </el-table-column>
          <el-table-column prop="deptId" label="管理部门" width="160" align="center">
            <template #default="scope">
              <Dictmap v-model="scope.row.deptId" code="dept0x0" />
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="管理员" width="120" align="center" />
        </el-table>
      </el-card>

      <!-- 流转记录 -->
      <el-card class="flow-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单流转记录</span>
            <div class="header-actions">
              <el-switch v-model="flowViewMode" active-text="时间线" inactive-text="表格" inline-prompt />
            </div>
          </div>
        </template>

        <!-- 表格视图 -->
        <el-table v-if="!flowViewMode" :data="flowRecords" style="width: 100%" stripe border v-loading="flowLoading">
          <el-table-column prop="step" label="环节" width="120">
            <template #default="{ row }">
              <el-tag :type="getFlowTimelineType(row.step)" effect="plain">{{ row.step }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="处理人" width="180">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="24" :icon="UserFilled" class="user-avatar" />
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column label="执行时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.executeTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="result" label="处理结果" width="180">
            <template #default="{ row }">
              <el-tag v-if="row.result" :type="getResultTagType(row.result)" size="small">
                {{ row.result }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="处理意见" min-width="200" show-overflow-tooltip />
        </el-table>

        <!-- 时间线视图 -->
        <div v-else class="timeline-container" v-loading="flowLoading">
          <el-empty v-if="flowRecords.length === 0 && !flowLoading" description="暂无流转记录" />
          <el-timeline v-else>
            <el-timeline-item v-for="(flow, index) in flowRecords" :key="index" :type="getFlowTimelineType(flow.step)"
              :timestamp="formatDateTime(flow.executeTime || flow.startTime)" :hollow="index !== 0">
              <h4>{{ flow.step }}</h4>
              <p class="timeline-content">
                <span class="timeline-user">处理人：{{ flow.name }}</span>
                <span v-if="flow.result" class="timeline-result">
                  处理结果：
                  <el-tag :type="getResultTagType(flow.result)" size="small">{{ flow.result }}</el-tag>
                </span>
                <span v-if="flow.comment" class="timeline-msg">
                  处理意见：{{ flow.comment }}
                </span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>

      <!-- 附件列表 -->
      <el-card class="attachment-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">附件列表</span>
            <div class="header-actions">
              <el-tag>共 {{ attachments.length }} 个附件</el-tag>
            </div>
          </div>
        </template>
        <div v-if="attachments.length === 0" class="attachment-empty">
          <el-empty description="暂无附件" />
        </div>
        <div v-else class="attachment-list">
          <el-table :data="attachments" style="width: 100%">
            <el-table-column prop="linkName" label="环节名称" />
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="createByName" label="创建人名称" />
            <el-table-column prop="createTime" label="上传时间" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleProcess">处理工单</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import offlineAPI from "@/api/assets_management/details/offline";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  ticketId: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'navigateToProcess'])

// 对话框控制
const dialogVisible = ref(false)
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
  if (val && props.ticketId) {
    loadTicketData()
  }
})

// 数据状态
const ticketData = reactive({
  id: undefined,
  name: '',
  status: '',
  applicantName: '',
  contact: '',
  step: '',
  createTime: '',
  updateTime: '',
  plannedOfflineTime: '',
  reason: '',
  remark: '',
  assetsList: []
})

// 定义流转记录类型
interface FlowRecord {
  step: string;
  name: string;
  startTime: string | null;
  executeTime: string | null;
  result: string;
  comment: string;
  index: number;
}

const flowRecords = ref<FlowRecord[]>([])
const attachments = ref([])

// 加载状态
const loading = ref(false)
const flowLoading = ref(false)

// 视图控制
const flowViewMode = ref(false) // true=时间线, false=表格

// 加载工单详情
const loadTicketData = async () => {
  if (!props.ticketId) return

  loading.value = true
  try {
    // 获取工单基本信息
    const data = await offlineAPI.getFormData(props.ticketId)
    Object.assign(ticketData, data)

    // 加载附件列表(假设实现类似安全漏洞工单的附件API)
    try {
      attachments.value = await offlineAPI.getFileList(props.ticketId) || []
    } catch (err) {
      console.warn('获取附件列表失败:', err)
      attachments.value = []
    }

    // 加载流转信息
    await loadFlowRecords()

  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}



// 加载流转记录
const loadFlowRecords = async () => {
  if (!props.ticketId) return

  flowLoading.value = true
  try {
    const res = await offlineAPI.getFlowInfo(props.ticketId)
    console.log('流转记录:', res)
    if (res) {
      // 处理流转记录数据
      flowRecords.value = res.map((item: any, index: number) => {
        return {
          step: item.name || '-',
          name: item.userName || '-',
          startTime: item.executeStartTime || null,
          executeTime: item.executeTime || null,
          result: item.processingResults || '',
          comment: item.commentContent || item.msg || '',
          index
        } as FlowRecord
      })
    }
  } catch (error) {
    console.error('加载流转记录失败:', error)
  } finally {
    flowLoading.value = false
  }
}

// 处理工单 - 跳转到处理流程
const handleProcess = () => {
  emit('navigateToProcess', props.ticketId)
  dialogVisible.value = false
}

// 下载文件
const downloadFile = (file) => {
  if (!file.url) {
    ElMessage.warning('文件地址不存在')
    return
  }

  try {
    window.open(file.url, '_blank')
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 格式化时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'

  try {
    return dateTimeStr.replace('T', ' ').substring(0, 19)
  } catch (error) {
    return dateTimeStr || '-'
  }
}

// 获取资产类型名称
const getAssetTypeName = (type: number | string) => {
  const typeMap: Record<string | number, string> = {
    '-1': '未知类型',
    1: '服务器',
    2: '网络设备',
    3: '安全设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 获取流转记录时间线类型
const getFlowTimelineType = (step) => {
  const typeMap = {
    '申请下线': 'primary',
    '下线审核': 'success',
    '工单评价': 'warning',
    '关闭工单': 'info'
  }

  return typeMap[step] || 'info'
}

// 获取处理结果标签类型
const getResultTagType = (result) => {
  const resultTagMap = {
    '通过': 'success',
    '不通过': 'danger',
    '已修复': 'success',
    '未修复': 'warning',
    '已关闭': 'info',
    '满意': 'success',
    '不满意': 'danger'
  }

  return resultTagMap[result] || 'info'
}

// 组件挂载时加载数据
onMounted(() => {
  if (props.visible && props.ticketId) {
    loadTicketData()
  }
})
</script>

<style scoped>
.ticket-detail-container {
  padding: 0 10px;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.content-block,
.remarks-block {
  padding: 10px;
  /* 使用Element Plus的变量替代固定颜色 */
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-primary);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  white-space: pre-wrap;
  min-height: 60px;
}

/* 其他样式保持不变 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.timeline-container {
  padding: 10px 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
}

.timeline-result {
  margin: 5px 0;
}

.timeline-msg {
  color: var(--el-text-color-secondary);
  white-space: pre-wrap;
}

.attachment-empty {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
