import request from "@/utils/request";

const ASSETS_BASE_URL = "/api/v1/assetss";

class assetsAPI {
  /** 获取资产管理分页数据 */
  static getPage(queryParams?: assetsPageQuery) {
    return request<any, PageResult<assetsPageVO[]>>({
      url: `${ASSETS_BASE_URL}/page`,
      method: "get",
      params: queryParams,
    });
  }

  /** 获取资产管理分页数据(所有：包括信息系统) */
  static getPageAll(queryParams?: assetsPageQuery) {
    return request<any, PageResult<assetsPageVO[]>>({
      url: `${ASSETS_BASE_URL}/pageAll`,
      method: "get",
      params: queryParams,
    });
  }
  /**
   * 获取资产管理表单数据
   *
   * @param id assetsID
   * @returns assets表单数据
   */
  static getFormData(id: number) {
    return request<any, assetsForm>({
      url: `${ASSETS_BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 获取资产管理表单数据
   * /api/v1/assetss/{id}/serverDetail
   */
  static getServerDetail(id: number) {
    return request<any, AssetsForm>({
      url: `${ASSETS_BASE_URL}/${id}/serverDetail`,
      method: "get",
    });
  }

  /** 添加资产管理*/
  static add(data: assetsForm) {
    return request({
      url: `${ASSETS_BASE_URL}`,
      method: "post",
      data: data,
    });
  }

  /**
   * 更新资产管理
   *
   * @param id assetsID
   * @param data assets表单数据
   */
  static update(id: number, data: assetsForm) {
    return request({
      url: `${ASSETS_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  }

  /**
   * 批量删除资产管理，多个以英文逗号(,)分割
   *
   * @param ids 资产管理ID字符串，多个以英文逗号(,)分割
   */
  static deleteByIds(ids: string) {
    return request({
      url: `${ASSETS_BASE_URL}/${ids}`,
      method: "delete",
    });
  }

  /**
   * 下载资产模板
   *  @param type 资产类型(1服务器  2网络设备 3安全设备)
   * @returns {Promise} - 请求的Promise对象
   */
  static downloadTemplate(type: string) {
    return request({
      url: `${ASSETS_BASE_URL}/template/${type}`,
      method: "get",
      responseType: "arraybuffer",
    });
  }

  /**
   * 资产启用
   * @param {number} ids - 资产ID
   */
  static batchUpdateStatus(ids: string) {
    return request({
      url: `${ASSETS_BASE_URL}/enable/${ids}`,
      method: "put",
    });
  }

  /**
   * 导入资产-服务器
   * @param {FormData} data - 资产表单数据
   * @param file 导入文件
   */
  static importServer(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, ExcelResult>({
      url: `${ASSETS_BASE_URL}/importServer`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 导入资产-网络设备
   * @param {FormData} data - 资产表单数据
   * @param file 导入文件
   */
  static importNetwork(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, ExcelResult>({
      url: `${ASSETS_BASE_URL}/importNetwork`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 导入资产-安全设备
   * @param {FormData} data - 资产表单数据
   * @param file 导入文件
   */
  static importSafety(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, ExcelResult>({
      url: `${ASSETS_BASE_URL}/importSafety`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 资产-服务器导出
   */
  static exportServer(data: assetsPageQuery) {
    return request({
      url: `${ASSETS_BASE_URL}/exportServer`,
      method: "post",
      data: data,
      responseType: "blob",
    });
  }

  /**
   * 资产-网络设备导出
   */
  static exportNetwork(data: assetsPageQuery) {
    return request({
      url: `${ASSETS_BASE_URL}/exportNetwork`,
      method: "post",
      data: data,
      responseType: "blob",
    });
  }

  /**
   * 资产-安全设备导出
   */
  static exportSafety(data: assetsPageQuery) {
    return request({
      url: `${ASSETS_BASE_URL}/exportSafety`,
      method: "post",
      data: data,
      responseType: "blob",
    });
  }

  /**
   * 资产-所有导出
   */
  static exportAll(data: assetsPageQuery) {
    return request({
      url: `${ASSETS_BASE_URL}/exportAll`,
      method: "post",
      data: data,
      responseType: "blob",
    });
  }
}

export default assetsAPI;

/** 资产管理分页查询参数 */
export interface assetsPageQuery extends PageQuery {
  /** 资产id */
  id?: number;
  /** 资产名称 */
  name?: string;
  /** 资产ip */
  ip?: string;
  /** 资产链接 */
  url?: string;
  /** 资产端口 */
  port?: string;
  /** 资产管理员Id */
  managerId?: number;
  /** 资产管理员 */
  ownerName?: string;
  /** 资产所属部门id */
  deptId?: number;
  /** 资产管理者手机号 */
  ownerPhone?: string;
  /** 资产所属系统id */
  systemId?: number;
  /** 资产状态 */
  status?: number;
  /** 登记时间 */
  createTime?: [string, string];
  /** 资产所用系统 */
  os?: string;
  /** 描述 */
  notes?: string;
  /** 排序字段 */
  type?: number;
  /** 资产类型(assets(服务器，网络设备，安全设备)  assetsAll(信息系统和服务器，网络设备，安全设备)) */
  assetsType?: string;

  chk?: number;
  keyword?: string;
}

/** 资产管理表单对象 */
export interface assetsForm {
  id?: number;
  /** 资产名称 */
  name?: string;
  /** 资产ip */
  ip?: string;
  /** 资产链接 */
  url?: string;
  /** 资产端口 */
  port?: string;
  /** 资产管理员 */
  ownerName?: string;
  /** 资产所属部门id */
  deptId?: number;
  /** 资产管理者手机号 */
  ownerPhone?: string;
  /** 资产所属系统id */
  systemId?: number;
  /** 资产状态 */
  status?: string;
  /** 登记时间 */
  createTime?: Date;
  /** 资产所用系统 */
  os?: string;
  /** 描述 */
  notes?: string;
  region?: string;
  factory?: string;
  version?: string;
  model?: string;
  version1?: string;
  mac?: string;
  type?: number;
  otherManager?: string;

  otherFactory?: string;

  otherContact?: string;

  ownerId?: number;

  chk?: number;
}

export interface AssetsForm {
  basic: {
    serverName: string;
    osVersion: string;
    autoSystem: string;
    ipConfigs: IpConfig[];
    belongSystem: string;
    registerTime: string;
    serverType: string;
    serverModel: string;
    networkArea: string;
    physicalLocation: string;
    assetStatus: string;
    assetRemark: string;
    onlineTime: string;
    onlinePerson: string;
    onlineContact: string;
  };
  middleware: {
    hasOtherComponents: string;
    componentList: Array<{
      id: number;
      name: string;
      version: string;
      port: string;
    }>;
    hasMiddleware: string;
    middlewareList: Array<{
      id: number;
      name: string;
      version: string;
      port: string;
    }>;
    hasDatabase: string;
    databaseList: Array<{
      id: number;
      name: string;
      version: string;
      port: string;
    }>;
  };
  management: {
    // departmentName: string,
    deptId: number;
    officeAddress: string;
    manager: string;
    officePhone: string;
    mobile: string;
    email: string;
    sysAdmin: string;
    adminOfficePhone: string;
    adminMobile: string;
    adminEmail: string;
  };
  operations: {
    providerId?: number; // 添加服务商ID
    providerDetail?: {
      // 添加服务商详情
      name: string;
      projectManager: string;
      managerMobile: string;
      managerEmail: string;
      techLeader: string;
      techMobile: string;
      techEmail: string;
    };
  };
  inventory: {
    enableInventory: "是" | "否";
    inventoryTaskName: string;
    enableScheduledInventory: "是" | "否";
    inventoryCycle:
      | "daily"
      | "weekly"
      | "monthly"
      | "quarterly"
      | "yearly"
      | "";
    enableTemporaryInventory: "是" | "否";
    temporaryInventoryDate: string | null;
    lastInventoryTime?: string;
    nextInventoryTime?: string;
    inventoryStatus?: "未开始" | "进行中" | "已完成" | "已过期";
    inventoryResult?: "正常" | "异常" | "";
    inventoryRemark?: string;
  };
  detection: {
    enableDetection: "是" | "否";
    detectionTaskName: string;
    enableScheduledDetection: "是" | "否";
    detectionCycle:
      | "daily"
      | "weekly"
      | "monthly"
      | "quarterly"
      | "yearly"
      | "";
    enableTemporaryDetection: "是" | "否";
    temporaryDetectionDate: string | null;
    lastDetectionTime?: string;
    nextDetectionTime?: string;
    detectionStatus?: "未开始" | "进行中" | "已完成" | "已过期";
    detectionResult?: "正常" | "异常" | "";
    detectionRemark?: string;
    detectionMethod?: "PING" | "TCP" | "HTTP" | "HTTPS" | "SNMP" | "其他";
  };
}

// 安全设备
export interface SecurityDeviceForm {
  basic: {
    deviceName: string;
    osVersion: string;
    manufacturer: string;
    model: string;
    registerTime: Date | null;
    plannedOfflineTime: Date | null;
    ipConfigs: Array<{
      id: number;
      ip: string;
      protocols: Array<{
        id: number;
        protocol: string;
        usage: string;
        port: string;
      }>;
    }>;
    networkArea: string;
    location: string;
    status: string;
    remarks: string;
    certExpiry: Date | null;
    isInnovative: "是" | "否";
  };
  management: {
    deptId: number | undefined;
    officeAddress: string;
    manager: string;
    officePhone: string;
    mobile: string;
    email: string;
  };
  operations: {
    serviceName: string;
    projectManager: string;
    officePhone: string;
    mobile: string;
    email: string;
  };
  inventory: {
    enableInventory: "是" | "否";
    inventoryTaskName: string;
    enableScheduledInventory: "是" | "否";
    inventoryCycle:
      | "daily"
      | "weekly"
      | "monthly"
      | "quarterly"
      | "yearly"
      | "";
    enableTemporaryInventory: "是" | "否";
    temporaryInventoryDate: Date | null;
    lastInventoryTime: string;
    nextInventoryTime: string;
    inventoryStatus: "未开始" | "进行中" | "已完成" | "已过期" | "";
    inventoryResult: "正常" | "异常" | "";
    inventoryRemark: string;
  };
  detection: {
    enableDetection: "是" | "否";
    detectionTaskName: string;
    enableScheduledDetection: "是" | "否";
    detectionCycle:
      | "daily"
      | "weekly"
      | "monthly"
      | "quarterly"
      | "yearly"
      | "";
    enableTemporaryDetection: "是" | "否";
    temporaryDetectionDate: Date | null;
    lastDetectionTime: string;
    nextDetectionTime: string;
    detectionStatus: "未开始" | "进行中" | "已完成" | "已过期" | "";
    detectionResult: "正常" | "异常" | "";
    detectionRemark: string;
    detectionMethod: "PING" | "TCP" | "HTTP" | "HTTPS" | "SNMP" | "其他" | "";
  };
}

/** 资产管理分页对象 */
export interface assetsPageVO {
  type: number;
  /** 资产id */
  id?: number;
  /** 资产名称 */
  name?: string;
  /** 资产ip */
  ip?: string;
  /** 资产链接 */
  url?: string;
  /** 资产端口 */
  port?: string;
  /** 资产管理员 */
  ownerName?: string;
  /** 资产所属部门id */
  deptId?: number;
  /** 资产管理者手机号 */
  ownerPhone?: string;
  /** 资产所属系统id */
  systemId?: number;
  /** 资产状态 */
  status?: string;
  /** 登记时间 */
  createTime?: Date;
  /** 资产所用系统 */
  os?: string;
  /** 描述 */
  notes?: string;

  otherManager?: string;

  otherFactory?: string;

  otherContact?: string;

  ownerId?: number;

  chk?: number;
}

export interface Protocol {
  id: number;
  protocol: string;
  usage: string;
  port: string;
  hasOtherComponents: string;
  componentList: Array<{
    id: number;
    name: string;
    version: string;
    port: string;
  }>;
}

export interface IpConfig {
  id: number;
  ip: string;
  port: string;
  protocol: string;
  usage: string;
  isPublic: boolean;
  mappedPort?: string;
  mappedAddress?: string;
  portConfigs: PortConfig[];
}
export interface PortConfig {
  id: number;
  port: string;
  protocol: string;
  sortDescription: string;
  addWay: string;
  addTime: string;
}

export interface Middleware {
  id: number;
  name: string;
  version: string;
  port: string;
  hasOtherComponents: string;
  componentList: Array<{
    id: number;
    name: string;
    version: string;
    port: string;
  }>;
}

export interface Database {
  id: number;
  name: string;
  version: string;
  port: string;
  hasOtherComponents: string;
  componentList: Array<{
    id: number;
    name: string;
    version: string;
    port: string;
  }>;
}
