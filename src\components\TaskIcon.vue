<template>
  <el-icon :style="iconStyle">
    <component :is="iconComponent" />
  </el-icon>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Document, Folder, Flag } from "@element-plus/icons-vue";
import { TaskType, TASK_TYPE_CONFIGS } from "@/types/project";

interface Props {
  type: TaskType;
  size?: string;
  color?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: "16px",
  color: "#409eff",
});

// 图标组件映射
const iconMap = {
  milestone: Flag,
  taskFolder: Folder,
  task: Document,
};

const iconComponent = computed(() => {
  return iconMap[props.type] || Document;
});

const iconStyle = computed(() => ({
  fontSize: props.size,
  color: props.color,
  marginRight: "6px",
}));

// 获取类型配置
const typeConfig = computed(() => TASK_TYPE_CONFIGS[props.type]);
</script>

<style scoped>
.el-icon {
  vertical-align: middle;
}
</style>
