<template>
  <el-card class="sms-template-editor">
    <template #header>
      <div class="card-header">
        <span>短信模板管理</span>
        <el-button type="primary" @click="showTemplateStandards">查看模板规范</el-button>
      </div>
    </template>
    
    <el-table :data="templates" style="width: 100%" v-loading="loading">
      <el-table-column prop="name" label="模板名称" width="180" />
      <el-table-column prop="content" label="模板内容" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="editTemplate(scope.row)">
            <el-icon><Edit /></el-icon> 编辑
          </el-button>
          <el-button size="small" type="danger" @click="deleteTemplate(scope.row)">
            <el-icon><Delete /></el-icon> 删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-button type="primary" @click="showAddDialog" style="margin-top: 20px;">
      <el-icon><Plus /></el-icon> 添加新模板
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑模板' : '添加模板'"
      width="50%"
    >
      <el-form :model="form" label-width="120px" @submit.prevent="saveTemplate">
        <el-form-item label="模板名称" required>
          <el-input v-model="form.name" placeholder="请输入模板名称"/>
        </el-form-item>
        <el-form-item label="模板内容" required>
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入模板内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTemplate">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="standardsDialogVisible" title="短信模板规范" width="80%">
      <div class="standards-content">
        <!-- 模板规范内容 -->
      </div>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Edit, Delete, Plus } from '@element-plus/icons-vue';

interface Template {
  id: number;
  name: string;
  content: string;
}

const templates = ref<Template[]>([
  { id: 1, name: '实例模板', content: '这是模板1的内容' },
  { id: 2, name: '模板2', content: '这是模板2的内容' }
]);

const dialogVisible = ref(false);
const isEdit = ref(false);
const standardsDialogVisible = ref(false);
const loading = ref(false);
const form = ref({ name: '', content: '' });

const showAddDialog = () => {
  isEdit.value = false;
  form.value = { name: '', content: '' };
  dialogVisible.value = true;
};

const editTemplate = (template: Template) => {
  isEdit.value = true;
  form.value = { ...template };
  dialogVisible.value = true;
};

const saveTemplate = () => {
  // 保存模板逻辑
  dialogVisible.value = false;
  ElMessage.success(isEdit.value ? '模板更新成功' : '模板添加成功');
};

const deleteTemplate = (template: Template) => {
  ElMessageBox.confirm('确定要删除这个模板吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 删除模板逻辑
    ElMessage.success('模板删除成功');
  }).catch(() => {
    // 取消删除
  });
};

const showTemplateStandards = () => {
  standardsDialogVisible.value = true;
};
</script>

<style scoped>
.sms-template-editor {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standards-content {
  max-height: 70vh;
  overflow-y: auto;
}

.standards-content h3, .standards-content h4 {
  margin-top: 20px;
}

.standards-content ul {
  padding-left: 20px;
}
</style>
