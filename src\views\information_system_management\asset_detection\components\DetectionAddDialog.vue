<template>
  <el-dialog v-model="props.visible" :title="title" width="65%" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="detection-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <!-- 复用资产 -->
        <el-button type="success" @click="openAssetReuseDialog">
          <el-icon>
            <Share />
          </el-icon>
          复用资产
        </el-button>

        <!-- 资产选择部分 -->
        <div class="assets-section">
          <div class="assets-actions" style="display: flex; justify-content: space-between; align-items: center;">
            <div style="display: flex; gap: 8px;">
              <el-button type="primary" @click="openSelectAssets">
                <el-icon>
                  <Plus />
                </el-icon>
                选择资产
              </el-button>
            </div>

            <!-- 右侧：已选资产数量 -->
            <div class="selected-assets-count"
              style="font-size: 14px; background-color: #ecf5ff; color: #409EFF; padding: 4px 12px; border-radius: 6px;">
              已选 {{ selectedAssetsDetails?.length || 0 }} 个资产
            </div>

          </div>
          <!-- 使用表格显示已选资产 -->
          <el-table v-if="selectedAssetsDetails?.length" :data="selectedAssetsDetails" border style="width: 100%"
            v-loading="assetListLoading" max-height="400">
            <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
            <el-table-column prop="type" label="资产类型" min-width="100" align="center">
              <template #default="scope">
                <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="资产名称" min-width="120" align="center" />
            <el-table-column label="资产地址" min-width="200" align="center">
              <template #default="scope">
                <div v-if="scope.row.ip">{{ scope.row.ip }}</div>
                <div v-else-if="scope.row.url">{{ scope.row.url }}</div>
                <div v-else-if="scope.row.address">{{ scope.row.address }}</div>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="deptName" label="管理部门" min-width="120" align="center">
              <template #default="scope">
                <dictmap v-model="scope.row.deptId" code="dept0x0" />
              </template>
            </el-table-column>
            <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" />
            <el-table-column fixed="right" label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" link @click="removeAsset(scope.row.id)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-assets">
            <el-empty :image-size="60" description="暂无选择资产" />
          </div>
        </div>

        <div style="margin-top: 24px; margin-left: -50px;">
          <el-form-item label="任务名称" prop="detectionName">
            <el-input v-model="formData.detectionName" placeholder="请输入探测任务名称   限10个字符以内" :maxlength="10" />
          </el-form-item>
        </div>


      </div>

      <!-- 探测策略 -->
      <div class="form-section">
        <div class="section-title">探测策略</div>
        <el-form-item label="是否定期探测" prop="regularDetectionStatus">
          <el-radio-group v-model="formData.regularDetectionStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- <el-form-item v-if="formData.regularDetectionStatus === 1" label="探测周期" prop="detectionCycle">
          <el-select v-model="formData.detectionCycle" placeholder="请选择探测周期">
            <el-option label="关闭" :value="0" />
            <el-option label="每天" :value="1 * 24" />
            <el-option label="每周" :value="7 * 24" />
            <el-option label="每月" :value="30 * 24" />
            <el-option label="每季度" :value="90 * 24" />
            <el-option label="每年" :value="365 * 24" />
          </el-select>
        </el-form-item> -->

        <el-form-item v-if="formData.regularDetectionStatus === 1" label="探测时间范围" required>
          <el-row :gutter="10" style="width: 100%">
            <el-col :span="12">
              <el-date-picker v-model="formData.startTime" type="datetime" placeholder="开始时间" style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss" />
            </el-col>
            <el-col :span="12">
              <el-date-picker v-model="formData.endTime" type="datetime" placeholder="结束时间" style="width: 100%"
                value-format="YYYY-MM-DD HH:mm:ss" />
            </el-col>
          </el-row>
        </el-form-item>

        <el-form-item label="是否即时探测" prop="immediateDetectionStatus">
          <el-radio-group v-model="formData.immediateDetectionStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 通知策略 -->
      <div class="form-section">
        <div class="section-title">通知策略</div>
        <el-form-item label="启用通知" prop="notificationStatus">
          <el-radio-group v-model="formData.notificationStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="formData.notificationStatus === 1">
          <el-form-item label="启用短信通知" prop="isSms">
            <el-radio-group v-model="formData.isSms">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
      </div>
    </el-form>

    <!-- 资产选择对话框 -->
    <select-assets v-model:visible="transferDialog.visible" :title="'选择资产'"
      :selected-assets="transferDialog.selectedAssets" :selected-assets-data="selectedAssetsDetails"
      @selected="handleAssetsSelected" />
    <!-- 资产复用弹窗 -->
    <asset-reuse v-model:visible="assetReuseVisible" :current-selected-ids="formData.assetIds"
      @select-assets="handleAssetsSelected" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import SelectAssets from "@/components/AssetsManage/SelectAssets.vue"
import DetectionAPI from '@/api/assets_management/assets_detection/index'
import { useDictStore } from '@/store/modules/dictStore'
import { formatLocalDateTime } from "@/utils/dateUtils";
import AssetReuse from './AssetReuse.vue'

// 使用dictStore预加载部门数据
const dictStore = useDictStore()
// 资产列表加载状态
const assetListLoading = ref(false)
const loading = ref(false)

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '新增资产探测'
  },
  id: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'submitted'])

const formRef = ref<FormInstance>()
const submitLoading = ref(false)

// 部门数据缓存
const deptMap = ref<Record<string | number, string>>({})
const deptLoading = ref(false)

// 修改表单数据，将assetId改为assetIds数组
const formData = reactive({
  detectionName: '',
  assetIds: [] as number[],
  assetName: '',
  regularDetectionStatus: 0,
  detectionCycle: undefined,
  immediateDetectionStatus: 0,
  notificationStatus: 0,
  isSms: 0,
  // 后端可能仍需要这些字段，但前端不再让用户选择
  smsTemplate: '',
  notificationTargetId: undefined as number | undefined
})

// 添加控制弹窗显示的变量
const assetReuseVisible = ref(false)

// 添加打开弹窗的方法
const openAssetReuseDialog = () => {
  assetReuseVisible.value = true
}

// 表单验证规则
const rules = reactive({
  detectionName: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
  detectionCycle: [{
    required: true,
    message: '请选择探测周期',
    trigger: 'change'
  }]
})

// 资产选择相关
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

const selectedAssetsDetails = ref<assetsPageVO[]>([])

// 计算资产名称列表，用于保存和显示
const assetNameList = computed(() => {
  return selectedAssetsDetails.value.map(asset => asset.name).join('、')
})

// 资产类型名称转换
const getAssetTypeName = (type: number | string) => {
  if (typeof type === 'string') {
    type = parseInt(type)
  }

  const typeMap: Record<number, string> = {
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '信息系统'
}

// 通过部门ID获取部门名称
const getDeptName = (deptId: string | number) => {
  if (!deptId) return '-'
  return deptMap.value[deptId] || `部门${deptId}` // 如果没有找到映射，返回一个默认值
}

// 加载部门映射数据
const loadDeptMappings = async () => {
  if (deptLoading.value || Object.keys(deptMap.value).length > 0) return

  try {
    deptLoading.value = true
    console.log('开始加载部门映射数据')

    // 使用dictStore加载部门数据
    const options = await dictStore.fetchOptions('dept0x0')

    // 递归处理部门树，提取ID和名称
    const processDeptTree = (depts: any[]) => {
      depts.forEach(dept => {
        if (dept.value !== undefined && dept.label) {
          deptMap.value[dept.value] = dept.label
        }

        if (dept.children && dept.children.length > 0) {
          processDeptTree(dept.children)
        }
      })
    }

    if (Array.isArray(options)) {
      processDeptTree(options)
    }

    console.log('部门映射数据加载完成，共', Object.keys(deptMap.value).length, '条记录')
  } catch (error) {
    console.error('加载部门映射数据失败:', error)
  } finally {
    deptLoading.value = false
  }
}

// 打开资产选择对话框
const openSelectAssets = () => {
  transferDialog.selectedAssets = [...formData.assetIds];
  console.log('打开资产选择对话框，当前选择的资产ID:', transferDialog.selectedAssets);
  console.log('传递的资产详情:', selectedAssetsDetails.value);

  transferDialog.visible = true;
}

// 处理资产选择结果 - 修改为与盘点组件一致
const handleAssetsSelected = ({ selectedIds, selectedAssets }) => {
  console.log('选择资产回调数据：', selectedIds, selectedAssets)

  if (!selectedIds || !selectedIds.length) {
    return
  }

  // 更新资产ID数组
  formData.assetIds = selectedIds
  transferDialog.selectedAssets = selectedIds
  selectedAssetsDetails.value = selectedAssets

  // 设置资产名称 - 使用所有选择的资产名称
  formData.assetName = assetNameList.value
  console.log('更新后的资产ID列表:', formData.assetIds)
}

// 移除单个资产
const removeAsset = (assetId: number) => {
  // 从选择列表中移除
  formData.assetIds = formData.assetIds.filter(id => id !== assetId)
  transferDialog.selectedAssets = transferDialog.selectedAssets.filter(id => id !== assetId)
  selectedAssetsDetails.value = selectedAssetsDetails.value.filter(asset => asset.id !== assetId)

  // 更新资产名称
  formData.assetName = assetNameList.value
}

// 加载详情 - 修改为处理直接返回资产信息的API
const loadDetail = async (id: number) => {
  try {
    loading.value = true;
    console.log('加载探测详情, ID:', id);

    // 尝试加载编辑数据
    const data = await DetectionAPI.getFormData(id);
    console.log('获取到的探测详情数据:', data);

    if (data) {
      // 填充表单数据
      formData.detectionName = data.detectionName || data.taskName || '';
      formData.regularDetectionStatus = typeof data.regularDetectionStatus === 'string'
        ? (data.regularDetectionStatus === 'started' ? 1 : 0)
        : (data.regularDetectionStatus ?? 0);
      formData.detectionCycle = data.detectionCycle ?? undefined;
      formData.immediateDetectionStatus = data.immediateDetectionStatus ??
        (typeof data.temporaryDetectionStatus === 'string'
          ? (data.temporaryDetectionStatus === 'started' ? 1 : 0)
          : 0);
      formData.notificationStatus = data.notificationStatus ?? 0;
      formData.isSms = data.isSms ?? 0;
      formData.smsTemplate = data.smsTemplate || '';
      formData.notificationTargetId = data.notificationTargetId;

      // 处理资产ID - 可能是字符串需要转数组，或者已经是数组
      if (data.assetId) {
        // 处理可能是逗号分隔的多个资产ID
        const assetIds = typeof data.assetId === 'string'
          ? data.assetId.split(',').map(id => parseInt(id.trim()))
          : Array.isArray(data.assetId)
            ? data.assetId
            : [data.assetId];

        formData.assetIds = assetIds;
        // 重要：同步到transferDialog
        transferDialog.selectedAssets = [...assetIds];
        console.log('设置已选资产IDs:', transferDialog.selectedAssets);
      } else if (data.assetIds && Array.isArray(data.assetIds)) {
        // 如果有assetIds字段且是数组，直接使用
        formData.assetIds = [...data.assetIds];
        // 重要：同步到transferDialog
        transferDialog.selectedAssets = [...data.assetIds];
        console.log('从assetIds设置已选资产:', transferDialog.selectedAssets);
      } else {
        formData.assetIds = [];
        transferDialog.selectedAssets = [];
      }

      if (data.assetsList && Array.isArray(data.assetsList) && data.assetsList.length > 0) {
        console.log('API返回了assetsList:', data.assetsList);
        selectedAssetsDetails.value = data.assetsList.map(asset => ({
          id: asset.id || asset.assetsId,
          name: asset.name || '',
          type: asset.type,
          ip: asset.ip || '',
          url: asset.url || '',
          address: asset.address || '',
          deptId: asset.deptId,
          deptName: asset.deptName || '',
          ownerName: asset.ownerName || asset.managerName || '',
        }));
      }

      // 更新资产名称
      formData.assetName = assetNameList.value;
    }
  } catch (error) {
    console.error('加载探测详情失败:', error);
    ElMessage.error('加载探测详情失败');
  } finally {
    loading.value = false;
  }
}

// 提交表单 - 修改为支持assetIds数组
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      if (!formData.assetIds.length) {
        ElMessage.warning('请选择至少一个资产')
        return
      }

      // 额外验证定期探测周期
      if (formData.regularDetectionStatus === 1 && !formData.detectionCycle) {
        ElMessage.warning('请选择探测周期')
        return
      }

      try {
        submitLoading.value = true

        // 准备提交数据 - 将assetIds数组转为字符串提交
        const submitData = {
          ...formData,
          // 将assetIds数组转为字符串
          assetId: formData.assetIds,
          // 如果是即时探测，设置探测时间为当前时间
          immediateDetectionTime: formData.immediateDetectionStatus === 1 ? formatLocalDateTime(new Date()) : undefined,
          // 确保包含detectionId字段，这可能是后端需要的
          detectionId: props.id
        }

        console.log('提交数据:', submitData)

        // 提交请求
        if (props.id) {
          // 编辑
          await DetectionAPI.update(props.id, submitData)
          ElMessage.success('修改成功')
        } else {
          // 新增
          await DetectionAPI.add(submitData)
          ElMessage.success('新增成功')
        }

        emit('submitted')
        handleClose()
      } catch (error) {
        console.error('提交失败', error)
        ElMessage.error('操作失败，请重试')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  selectedAssetsDetails.value = []
  formData.assetIds = []
  formData.assetName = ''
  formData.regularDetectionStatus = 0
  formData.immediateDetectionStatus = 0
  formData.notificationStatus = 0
  formData.isSms = 0
  formData.detectionCycle = undefined
  formData.smsTemplate = ''
  formData.notificationTargetId = undefined
  transferDialog.selectedAssets = []
}

// 监听ID和visible的变化，确保在弹窗打开且有ID时加载数据
watch(() => [props.visible, props.id], ([visible, id]) => {
  if (visible && id) {
    console.log('弹窗打开且有ID，加载详情:', id)
    loadDetail(id)
  } else if (visible) {
    // 如果是新增，重置表单
    resetForm()
  }
}, { immediate: true })

// 组件挂载后执行
onMounted(async () => {
  // 预加载部门数据
  await loadDeptMappings()

  // 如果传入了ID且弹窗可见，加载详情
  if (props.id && props.visible) {
    await loadDetail(props.id)
  }
})
</script>

<style scoped>
.detection-form {
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.assets-section {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

.empty-assets {
  margin-top: 16px;
  padding: 24px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.assets-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
}
</style>
