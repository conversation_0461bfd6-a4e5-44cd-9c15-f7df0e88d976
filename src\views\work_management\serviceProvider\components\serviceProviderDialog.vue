<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="700px"
    @close="handleClose"
    top="5vh"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="service-provider-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="服务商名称" prop="providerName">
              <el-input
                v-model="formData.providerName"
                placeholder="请输入服务商名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="服务商描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="2"
                placeholder="请输入服务商描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-switch
                v-model="formData.status"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
                inline-prompt
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 项目负责人信息 -->
      <div class="form-section">
        <div class="section-title">项目负责人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目负责人" prop="projectManager">
              <el-input
                v-model="formData.projectManager"
                placeholder="请输入项目负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="managerMobile">
              <el-input
                v-model="formData.managerMobile"
                placeholder="请输入手机号码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="邮箱" prop="managerEmail">
          <el-input
            v-model="formData.managerEmail"
            placeholder="请输入邮箱"
            clearable
          />
        </el-form-item>
      </div>

      <!-- 技术负责人信息 -->
      <div class="form-section">
        <div class="section-title">技术负责人信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="技术负责人" prop="techLeader">
              <el-input
                v-model="formData.techLeader"
                placeholder="请输入技术负责人"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="techMobile">
              <el-input
                v-model="formData.techMobile"
                placeholder="请输入手机号码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="邮箱" prop="techEmail">
          <el-input
            v-model="formData.techEmail"
            placeholder="请输入邮箱"
            clearable
          />
        </el-form-item>
      </div>

      <!-- 合同信息 -->
      <div class="form-section">
        <div class="section-title">合同信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同开始时间" prop="contractStartTime">
              <el-date-picker
                v-model="formData.contractStartTime"
                type="date"
                placeholder="选择开始时间"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同结束时间" prop="contractEndTime">
              <el-date-picker
                v-model="formData.contractEndTime"
                type="date"
                placeholder="选择结束时间"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="合同文件" prop="contractFile">
          <div class="contract-file-container">
            <el-upload
              class="contract-upload"
              :headers="props.headers"
              :action="props.action"
              :file-list="fileList"
              :accept="'.pdf,.doc,.docx'"
              :on-success="handleUploadSuccess"
              :limit="1"
            >
              <el-button type="primary" :icon="Document">
                上传合同文件
              </el-button>
              <template #tip>
                <div class="upload-tip">支持 PDF、DOC、DOCX 格式文件</div>
              </template>
            </el-upload>
            <div v-if="formData.contractFile" class="current-file">
              <el-tag type="success" effect="light">已上传文件</el-tag>
              <el-link
                type="primary"
                :underline="false"
                :href="formData.contractFile"
                target="_blank"
              >
                <el-icon><Document /></el-icon>
                查看合同文件
              </el-link>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { Document } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { ProviderForm } from "@/api/work_management/serviceProvider";
import FileAPI from "@/api/file";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import { UploadUserFile } from "element-plus";

// 以下是原有的脚本代码，保持不变
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增服务商",
  },
  action: {
    type: String,
    default: FileAPI.uploadUrl,
  },
  headers: {
    type: Object,
    default: () => {
      return {
        Authorization: localStorage.getItem(TOKEN_KEY),
      };
    },
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "submit"]);

const formRef = ref<FormInstance>();
const loading = ref(false);
const fileList = ref([] as UploadUserFile[]);

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

let formData = reactive(<ProviderForm>{});

watch(
  props.data,
  (data) => {
    formData = data;
  },
  {
    deep: true,
    immediate: true,
  }
);

const rules: FormRules = {
  providerName: [
    { required: true, message: "请输入服务商名称", trigger: "blur" },
  ],
  projectManager: [
    { required: true, message: "请输入项目负责人", trigger: "blur" },
  ],
  managerMobile: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  managerEmail: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
  ],
  techLeader: [
    { required: true, message: "请输入技术负责人", trigger: "blur" },
  ],
  techMobile: [
    { required: true, message: "请输入手机号码", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  techEmail: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
  ],
};

const handleUploadSuccess = (response: any) => {
  formData.contractFile = response.data.url;
};

const downloadFile = (fileUrl) => {
  if (fileUrl) {
    window.open(fileUrl, "_blank");
  } else {
    ElMessage.error("文件不存在");
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;
    emit("submit", formData);
  } finally {
    loading.value = false;
    fileList.value = [];
  }
};

const handleClose = () => {
  dialogVisible.value = false;
  formRef.value?.resetFields();
  formData.id = undefined;
  fileList.value = [];
};
</script>

<style scoped>
.service-provider-form {
  padding: 0 10px;
}

.form-section {
  margin-bottom: 25px;
  padding-bottom: 10px;
  position: relative;
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 3px solid var(--el-color-primary);
}

.contract-file-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.current-file {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 8px;
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 5px;
}

.dialog-footer {
  padding-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
  }
}
</style>
