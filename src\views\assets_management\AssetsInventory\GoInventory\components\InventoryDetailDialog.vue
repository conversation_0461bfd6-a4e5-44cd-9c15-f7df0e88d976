<template>
  <el-dialog v-model="props.visible" :title="title" width="70%" :close-on-click-modal="false"
    :before-close="handleClose">
    <div v-loading="loading" class="inventory-detail">
      <!-- 基本信息卡片 -->
      <el-card shadow="hover" class="mb-4">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
          </div>
        </template>

        <el-descriptions :column="3" border>
          <el-descriptions-item label="任务ID" :span="1">
            {{ detailData.id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="任务名称" :span="1">
            {{ detailData.taskName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="盘点状态" :span="1">
            <el-tag :type="getStatusType(detailData.inventoryStatus)">
              {{ getStatusLabel(detailData.inventoryStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="1">
            {{ detailData.createTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="开始时间" :span="1">
            {{ detailData.startTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="截止时间" :span="1">
            {{ detailData.deadline || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="盘点进度" :span="1">
            <div style="display: flex; align-items: center;">
              <el-progress 
                :percentage="detailData.inventoryProgress || 0" 
                :show-text="false"
                style="flex-grow: 1;"
              />
              <span style="margin-left: 10px; white-space: nowrap; min-width: 40px;">
                {{ completedAssetsCount }}/{{ assetsList.length || 0 }}
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建部门" :span="1">
            <dictmap v-model="detailData.createDeptId" code="dept0x0" />
          </el-descriptions-item>
          <el-descriptions-item label="创建人员" :span="1">
            <dictmap v-model="detailData.createUserId" code="user0x0" />
          </el-descriptions-item>
          <el-descriptions-item label="短信通知" :span="1">
            <el-tag :type="detailData.isSmsNotification === '1' ? 'success' : 'info'">
              {{ detailData.isSmsNotification === '1' ? '已开启' : '未开启' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 资产明细卡片（包含操作记录） -->
      <el-card shadow="hover" class="mb-4">
        <template #header>
          <div class="card-header">
            <span class="card-title">资产明细</span>
            <span class="stat-info">总资产数: {{ assetsList.length }} | 已盘点: {{ completedAssetsCount }} | 未盘点: {{
              assetsList.length - completedAssetsCount }}</span>
          </div>
        </template>

        <el-table :data="assetsList" style="width: 100%" border max-height="500" v-loading="assetLoading" :row-class-name="getRowClassName">
          <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
          <el-table-column prop="type" label="资产类型" min-width="100" align="center">
            <template #default="scope">
              <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="资产名称" min-width="120" align="center" />
          <el-table-column label="资产地址" min-width="150" align="center">
            <template #default="scope">
              <div v-if="scope.row.ip">{{ scope.row.ip }}</div>
              <div v-else-if="scope.row.url">{{ scope.row.url }}</div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="管理部门" min-width="120" align="center">
          </el-table-column>
          <el-table-column prop="managerName" label="管理人员" min-width="100" align="center" />
          <el-table-column prop="inventoryStatus" label="盘点状态" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="getAssetInventoryType(scope.row.inventoryStatus)">
                {{ getAssetInventoryLabel(scope.row.inventoryStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- 添加操作记录列 -->
          <el-table-column label="操作记录" min-width="120" align="center">
            <template #default="scope">
              <el-popover placement="top" :width="300" trigger="hover" v-if="scope.row.inventoryStatus === 'completed'">
                <template #reference>
                  <el-tag :type="getChangeStatusType(scope.row.changeStatus)" class="cursor-pointer">
                    {{ getChangeStatusText(scope.row.changeStatus) }}
                    <el-icon class="ml-1">
                      <InfoFilled />
                    </el-icon>
                  </el-tag>
                </template>
                <div>
                  <div class="popover-title">盘点记录详情</div>
                  <el-divider class="my-2" />
                  <div class="popover-row">
                    <span class="label">盘点时间：</span>
                    <span>{{ scope.row.inventoryTime || '-' }}</span>
                  </div>
                  <!-- <div class="popover-row">
                    <span class="label">盘点类型：</span>
                    <span>{{ getInventoryTypeText(scope.row.inventoryType) }}</span>
                  </div> -->
                  <div class="popover-row">
                    <span class="label">盘点人员：</span>
                    <span>{{ scope.row.inventoryUserName || '-' }}</span>
                  </div>
                  <div class="popover-row">
                    <span class="label">变动状态：</span>
                    <span>{{ getChangeStatusText(scope.row.changeStatus) }}</span>
                  </div>
                </div>
              </el-popover>
              <span v-else class="text-muted">{{ scope.row.inventoryStatus === 'pending' ? '待盘点' : '暂无记录' }}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 如果没有资产时显示空状态 -->
        <el-empty v-if="assetsList.length === 0" description="暂无关联资产" />
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <!-- <el-button type="primary" @click="handleExport" :loading="exportLoading">导出明细</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import InventoryAPI from '@/api/assets_management/assets_inventory/index'
import { InfoFilled } from '@element-plus/icons-vue' // 添加图标导入

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '盘点任务详情'
  },
  id: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible'])

// 加载状态
const loading = ref(false)
const assetLoading = ref(false)
const exportLoading = ref(false)

// 详情数据
const detailData = reactive<any>({
  id: '',
  taskName: '',
  inventoryStatus: '',
  createTime: '',
  startTime: '',
  deadline: '',
  inventoryProgress: 0,
  isSmsNotification: '0',
  userName: '',
  deptId: '',
  deptName: '',
  createUserId: '',
  createDeptId: '',

})

// 定义日志项接口
interface LogItem {
  operateTime: string;
  operator: string;
  operateType: string;
  assetName: string;
  assetId: number;
  operateContent: string;
}

// 资产列表数据
const assetsList = ref<any[]>([])

// 计算已盘点资产数量
const completedAssetsCount = computed(() => {
  return assetsList.value.filter(asset => asset.inventoryStatus === 'completed').length
})

// 状态类型
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    'completed': 'success',
    'processing': 'primary',
    'timeout': 'danger',
    'timeoutCompleted': 'warning',
    '0': 'info',     // 待进行
    '1': 'primary',  // 进行中
    '2': 'success',  // 已完成
    '3': 'warning',  // 逾期完成
    '4': 'danger'    // 逾期未完成
  }
  return statusMap[status] || 'info'
}

// 状态标签
const getStatusLabel = (status: string): string => {
  const statusMap: Record<string, string> = {
    'completed': '已完成',
    'processing': '进行中',
    'timeout': '逾期未完成',
    'timeoutCompleted': '逾期完成',
    '0': '待进行',
    '1': '进行中',
    '2': '已完成',
    '3': '逾期完成',
    '4': '逾期未完成'
  }
  return statusMap[status] || status || '-'
}

// 获取详情数据
const getDetailData = async () => {
  if (!props.id) return

  loading.value = true
  assetLoading.value = true
  try {
    // 获取盘点表单数据作为详情数据
    const response = await InventoryAPI.getFormData(Number(props.id))
    const data = response
    console.log('获取到的盘点任务数据:', data)

    // 填充详情数据
    detailData.id = props.id
    detailData.taskName = data.inventoryName || '' // 使用inventoryName作为taskName
    detailData.inventoryStatus = mapStatusValue(data.status) // 进行状态值映射
    detailData.createTime = data.createTime || ''
    detailData.startTime = data.startTime || ''
    detailData.deadline = data.deadline || ''
    detailData.isSmsNotification = data.notificationStatus || '0'
    detailData.createDeptId = data.createDeptId || ''
    detailData.createUserId = data.createUserId || ''

    // 直接从表单响应中处理资产列表数据
    if (data.assetsList && Array.isArray(data.assetsList)) {
      console.log('从表单响应中获取资产列表:', data.assetsList.length, '条记录')

      // 处理每个资产的数据并转换为前端所需格式
      assetsList.value = data.assetsList.map(asset => {
        return {
          id: asset.assetsId,
          inventoryAssetsId: asset.inventoryAssetsId,
          name: asset.name || '',
          type: asset.type,
          ip: asset.ip || '',
          url: asset.url || '',
          deptId: asset.deptId,
          deptName: asset.deptName || '',
          managerName: asset.managerName || asset.ownerName || '',
          inventoryStatus: mapAssetInventoryStatus(asset.inventoryStatus),
          // 添加新增字段
          inventoryTime: asset.inventoryTime || '',
          inventoryType: asset.inventoryTime || '',
          inventoryUserName: asset.inventoryUserName || '',
          changeStatus: asset.changeStatus || '1', // 默认为"无变动"
        };
      });

      // 计算盘点进度
      if (assetsList.value.length > 0) {
        // 有资产时，计算完成百分比
        const completedCount = completedAssetsCount.value;
        detailData.inventoryProgress = Math.round((completedCount / assetsList.value.length) * 100);
      } else {
        // 没有资产时(0/0)，显示为100%完成
        detailData.inventoryProgress = 100;
      }
    } else {
      assetsList.value = [];
      console.log('无资产列表数据')
    }

  } catch (error) {
    console.error('获取盘点详情失败:', error)
    ElMessage.error('获取详情数据失败')
    assetsList.value = [];
  } finally {
    loading.value = false
    assetLoading.value = false
  }
}

// 将API返回的状态值映射为前端使用的状态值
const mapStatusValue = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': 'processing',    // 待进行 -> 进行中
    '1': 'processing',    // 进行中 -> 进行中
    '2': 'completed',     // 已完成 -> 已完成
    '3': 'timeoutCompleted', // 逾期完成 -> 逾期完成
    '4': 'timeout'        // 逾期未完成 -> 逾期未完成
  }
  return statusMap[status] || status || 'processing'
}

// 资产盘点状态映射
const mapAssetInventoryStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    '0': 'pending',       // 未盘点 -> 待盘点
    '1': 'completed',     // 已盘点 -> 已盘点
    '2': 'timeout',       // 逾期 -> 逾期未盘点
    '3': 'timeoutCompleted' // 逾期已盘点 -> 逾期已盘点
  }
  return statusMap[status] || status || 'pending'
}


// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 监听弹窗显示和ID变化
watch(
  () => [props.visible, props.id],
  ([visible, id]) => {
    if (visible && id) {
      getDetailData()
    }
  },
  { immediate: true }
)

onMounted(() => {
  if (props.visible && props.id) {
    getDetailData()
  }
})

// 资产类型名称
const getAssetTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    '1': '服务器',
    '2': '网络设备',
    '3': '安全设备',
    '10': '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 进度格式化
const percentageFormat = (percentage: number) => {
  return percentage === 100 ? '完成' : `${percentage}%`
}

// 资产盘点状态类型
const getAssetInventoryType = (status: string): string => {
  const statusMap: Record<string, string> = {
    'completed': 'success',
    'pending': 'info',
    'timeout': 'danger',
    'timeoutCompleted': 'warning'
  }
  return statusMap[status] || 'info'
}

// 资产盘点状态标签
const getAssetInventoryLabel = (status: string): string => {
  const statusMap: Record<string, string> = {
    'completed': '已盘点',
    'pending': '待盘点',
    'timeout': '逾期未盘点',
    'timeoutCompleted': '逾期已盘点'
  }
  return statusMap[status] || status || '-'
}

// 操作类型标签颜色
const getOperationTypeTag = (type: string): string => {
  const typeMap: Record<string, string> = {
    'create': 'primary',
    'update': 'warning',
    'complete': 'success',
    'check': 'info',
    'remind': 'info'
  }
  return typeMap[type] || 'info'
}

// 操作类型标签文本
const getOperationTypeLabel = (type: string): string => {
  const typeMap: Record<string, string> = {
    'create': '创建',
    'update': '更新',
    'complete': '完成盘点',
    'check': '核查',
    'remind': '发送提醒'
  }
  return typeMap[type] || type || '-'
}

// 获取变动状态类型
const getChangeStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': 'info',     // 无变动
    '2': 'warning',  // 已变动
    '3': 'danger'    // 已下线
  }
  return statusMap[status] || 'info'
}

// 获取变动状态文本
const getChangeStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1': '无变动',
    '2': '已变动',
    '3': '已下线'
  }
  return statusMap[status] || '未知状态'
}

// 获取盘点类型文本
const getInventoryTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    '1': '批量盘点',
    '2': '单个盘点'
  }
  return typeMap[type] || '未知类型'
}

// 格式化时间
const formatTime = (time: string): string => {
  if (!time) return '-'
  return time.split(' ')?.[0] || time
}

// 根据资产状态获取行样式类名
const getRowClassName = ({ row }) => {
  if (row.changeStatus === '3') return 'offline-row';
  if (row.changeStatus === '2') return 'changed-row';
  return '';
};
</script>

<style scoped>
/* CSS样式保持不变 */
.inventory-detail {
  padding: 10px;
}

.mb-4 {
  margin-bottom: 20px;
}

.mr-1 {
  margin-right: 5px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 500;
}

.card-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #409eff;
  border-radius: 2px;
  vertical-align: middle;
  margin-right: 8px;
}

.stat-info {
  font-size: 14px;
  color: #606266;
}

/* 操作记录样式 */
.operation-record {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-tag {
  font-weight: normal;
}

.timestamp {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.no-record {
  color: #c0c4cc;
  font-size: 13px;
}

/* 日志弹出层样式 */
.logs-popover h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.log-content {
  font-size: 13px;
  line-height: 1.5;
}

:deep(.el-descriptions__body) {
  width: 100%;
}

:deep(.el-descriptions__table) {
  width: 100%;
}

:deep(.el-progress) {
  width: 95%;
  margin: 0 auto;
}

:deep(.el-tag) {
  display: inline-flex;
}

.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

/* 变动状态相关样式 */
.cursor-pointer {
  cursor: pointer;
}

.ml-1 {
  margin-left: 4px;
}

.my-2 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.text-muted {
  color: #909399;
  font-size: 13px;
}

.popover-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.popover-row {
  margin-top: 8px;
  font-size: 13px;
  line-height: 1.4;
  color: #606266;
}

.popover-row .label {
  color: #909399;
  margin-right: 4px;
}

/* 表格行样式 */
:deep(.changed-row) {
  background-color: rgba(253, 246, 236, 0.3) !important;
}

:deep(.offline-row) {
  background-color: rgba(237, 237, 237, 0.5) !important;
  color: #909399 !important;
}
</style>
