<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1 class="dashboard-title">工单管理仪表盘</h1>
      <div class="dashboard-actions">
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" size="small" @change="handleDateRangeChange" />
        <el-button type="primary" size="small" @click="refreshData" :loading="loading">
          <el-icon>
            <Refresh />
          </el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 总览统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="(item, index) in statisticsCards" :key="index">
        <el-card shadow="hover" class="statistics-card" :body-style="{ padding: '0px' }">
          <div class="card-content">
            <div class="card-icon" :style="{ backgroundColor: item.color }">
              <el-icon :size="32">
                <component :is="item.icon"></component>
              </el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 工单流程卡片 -->
    <el-row :gutter="20">
      <!-- 安全审核与评估 -->
      <el-col :span="8">
        <el-card shadow="hover" class="process-card">
          <template #header>
            <div class="card-header">
              <span class="process-title">安全评估与问题管理</span>
              <!-- 移除状态标签 -->
            </div>
          </template>
          <div class="process-stats">
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.assessment.pending }}</div>
              <div class="stats-label">待处理</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.assessment.reviewing }}</div>
              <div class="stats-label">待审核</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.assessment.completed }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
          <div class="process-actions">
            <el-button type="primary" @click="navigateTo('/work_management/online_service')">
              进入安全评估与问题管理工单
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 安全漏洞工单 -->
      <el-col :span="8">
        <el-card shadow="hover" class="process-card">
          <template #header>
            <div class="card-header">
              <span class="process-title">安全漏洞工单</span>
              <!-- 移除状态标签 -->
            </div>
          </template>
          <div class="process-stats four-columns">
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.vulnerability.pending }}</div>
              <div class="stats-label">待处理</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.vulnerability.reviewing }}</div>
              <div class="stats-label">待审核</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.vulnerability.overdue || 0 }}</div>
              <div class="stats-label">逾期未处理</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.vulnerability.completed }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
          <div class="process-actions">
            <el-button type="primary" @click="navigateTo('/work_management/safety')">
              进入安全漏洞工单
            </el-button>
          </div>
        </el-card>
      </el-col>

      <!-- 资产下线工单 -->
      <el-col :span="8">
        <el-card shadow="hover" class="process-card">
          <template #header>
            <div class="card-header">
              <span class="process-title">资产下线工单</span>
              <!-- 移除状态标签 -->
            </div>
          </template>
          <div class="process-stats">
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.asset.pending }}</div>
              <div class="stats-label">待处理</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.asset.reviewing }}</div>
              <div class="stats-label">待审核</div>
            </div>
            <div class="stats-item">
              <div class="stats-value">{{ workflowStats.asset.completed }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
          <div class="process-actions">
            <el-button type="primary" @click="navigateTo('/assets_management/assetsOffline/GoOffline')">
              进入资产下线工单
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 工单动态 -->
    <!-- <el-row :gutter="20" class="mt-4">
        <el-col :span="16">
          <el-card shadow="hover" class="trend-card">
            <template #header>
              <div class="card-header">
                <span>工单趋势</span>
                <el-radio-group v-model="trendTimeRange" size="small">
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="year">年</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container" ref="trendChartRef"></div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="recent-card">
            <template #header>
              <div class="card-header">
                <span>最近工单活动</span>
              </div>
            </template>
            <div class="activity-list">
              <el-timeline>
                <el-timeline-item
                  v-for="(activity, index) in recentActivities"
                  :key="index"
                  :type="activity.type"
                  :timestamp="activity.time"
                  :color="getActivityColor(activity.status)"
                >
                  {{ activity.content }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </el-card>
        </el-col>
      </el-row> -->
    <CertificateExpiryNotice />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import * as echarts from 'echarts';
import DashboardAPI from "@/api/dashboard";
import { ElMessage } from 'element-plus';
import { Refresh, Warning, DataLine, Document, Check, Calendar } from '@element-plus/icons-vue';
import CertificateExpiryNotice from '@/components/GeneralModel/CertificateExpiryNotice.vue';

const router = useRouter();
const loading = ref(false);
const dateRange = ref<[Date, Date] | null>(null);
const trendTimeRange = ref('month');
const trendChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;

// 总览统计数据
const statisticsCards = reactive([
  { title: '总工单数', value: 0, icon: 'Document', color: '#1890FF' },
  { title: '待处理工单数', value: 0, icon: 'Warning', color: '#F56C6C' },
  { title: '已完成工单数', value: 0, icon: 'Check', color: '#67C23A' },
  { title: '逾期未处理工单数', value: 0, icon: 'Calendar', color: '#E6A23C' },
]);

// 工作流程状态数据
const workflowStats = reactive({
  assessment: { pending: 0, reviewing: 0, completed: 0 },
  vulnerability: { pending: 0, reviewing: 0, completed: 0 },
  asset: { pending: 0, reviewing: 0, completed: 0 }
});

// 最近活动数据
const recentActivities = ref<any[]>([]);

// 获取活动颜色
const getActivityColor = (status: string | number) => {
  const colorMap: Record<string, string> = {
    success: '#67C23A',
    warning: '#E6A23C',
    danger: '#F56C6C',
    primary: '#409EFF',
    info: '#909399'
  };
  return colorMap[status.toString()] || colorMap.info;
};

// 页面导航
const navigateTo = (path: string) => {
  router.push(path);
};

// 初始化趋势图表
const initTrendChart = (data: { categories?: string[], assessment?: number[], vulnerability?: number[], asset?: number[] }) => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['安全评估工单', '安全漏洞工单', '资产下线工单']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.categories || ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '安全评估工单',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#409EFF'
          },
          emphasis: {
            focus: 'series'
          },
          data: data.assessment || [10, 15, 12, 20, 16, 18]
        },
        {
          name: '安全漏洞工单',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#F56C6C'
          },
          emphasis: {
            focus: 'series'
          },
          data: data.vulnerability || [5, 8, 10, 12, 6, 9]
        },
        {
          name: '资产下线工单',
          type: 'bar',
          stack: 'total',
          itemStyle: {
            color: '#E6A23C'
          },
          emphasis: {
            focus: 'series'
          },
          data: data.asset || [3, 4, 6, 8, 5, 7]
        }
      ]
    };

    trendChart.setOption(option);
  }
};

// 处理日期范围变更
const handleDateRangeChange = () => {
  refreshData();
};

// 刷新数据
const refreshData = () => {
  loading.value = true;

  // 调用API获取数据
  DashboardAPI.getMainDashboard({
    timeRange: trendTimeRange.value,
    startDate: dateRange.value?.[0] ? dateRange.value[0].toISOString().split('T')[0] : null,
    endDate: dateRange.value?.[1] ? dateRange.value[1].toISOString().split('T')[0] : null,
  })
    .then(response => {
      // 更新统计卡片数据
      statisticsCards[0].value = response.totalTickets || 0;
      statisticsCards[1].value = response.pendingTickets || 0;
      statisticsCards[2].value = response.completedTickets || 0;
      statisticsCards[3].value = response.expectedPendingTickets || 0;

      // 更新工作流状态
      if (response.workflowStats) {
        Object.assign(workflowStats, response.workflowStats);
      }

      // 更新趋势图表
      if (response.trendData) {
        initTrendChart(response.trendData);
      }

      // 更新最近活动
      if (response.recentActivities && response.recentActivities.length > 0) {
        recentActivities.value = response.recentActivities;
      }
    })
    .catch(error => {
      console.error('获取仪表盘数据失败:', error);
      ElMessage.error('获取仪表盘数据失败，请稍后重试');

      // 加载失败时使用模拟数据（临时方案）
      useModelData();
    })
    .finally(() => {
      loading.value = false;
    });
};

// 临时使用模拟数据（在接口就绪前使用）
const useModelData = () => {
  // 模拟统计卡片数据
  statisticsCards[0].value = 125;
  statisticsCards[1].value = 18;
  statisticsCards[2].value = 95;
  statisticsCards[3].value = 12;

  // 模拟工作流状态
  Object.assign(workflowStats, {
    assessment: { pending: 7, reviewing: 4, completed: 35 },
    vulnerability: { pending: 5, reviewing: 3, completed: 28 },
    asset: { pending: 6, reviewing: 2, completed: 32 }
  });

  // 模拟趋势图表数据
  initTrendChart({
    categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
    assessment: [10, 15, 12, 20, 16, 18],
    vulnerability: [5, 8, 10, 12, 6, 9],
    asset: [3, 4, 6, 8, 5, 7]
  });

  // 模拟最近活动
  recentActivities.value = [
    { content: '安全评估工单#2023121001已完成', time: '2023-12-10 14:23', status: 'success', type: 'success' },
    { content: '新建漏洞整改工单#2023121002', time: '2023-12-10 11:30', status: 'primary', type: 'primary' },
    { content: '资产下线工单#2023120901被驳回', time: '2023-12-09 16:45', status: 'warning', type: 'warning' },
    { content: '漏洞工单#2023120801需要复核', time: '2023-12-08 09:12', status: 'info', type: 'info' },
    { content: '安全评估工单#2023120701已提交', time: '2023-12-07 17:05', status: 'primary', type: 'primary' },
  ];
};

// 处理窗口大小变化
const handleResize = () => {
  trendChart?.resize();
};

// 监听时间范围变化
watch(trendTimeRange, () => {
  refreshData();
});

onMounted(() => {
  refreshData();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);

  // 为了演示，手动初始化趋势图表
  initTrendChart({
    categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
    assessment: [10, 15, 12, 20, 16, 18],
    vulnerability: [5, 8, 10, 12, 6, 9],
    asset: [3, 4, 6, 8, 5, 7]
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  trendChart?.dispose();
});
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片样式 */
.statistics-card {
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

.statistics-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--el-box-shadow-light);
}

.card-content {
  display: flex;
  padding: 20px;
  height: 100%;
  align-items: center;
}

.card-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 16px;
}

.card-icon :deep(svg) {
  color: #ffffff;
}

.card-info {
  flex-grow: 1;
}

.card-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 流程卡片样式 */
.process-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

.process-card:hover {
  box-shadow: var(--el-box-shadow-light);
}

.card-header {
  display: flex;
  justify-content: center; /* 修改为居中 */
  align-items: center;
}

.process-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  text-align: center; /* 标题文本居中 */
}

.process-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

/* 添加四列布局样式 */
.process-stats.four-columns .stats-item {
  width: 25%;
  padding: 8px 5px;
}

.stats-item {
  text-align: center;
  padding: 12px;
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.process-actions {
  text-align: center;
}

/* 趋势图和活动列表 */
.trend-card, .recent-card {
  height: 420px;
  transition: all 0.3s;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
}

.chart-container {
  height: 340px;
}

.activity-list {
  height: 340px;
  overflow-y: auto;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
}

/* 边距工具类 */
.mb-4 {
  margin-bottom: 20px;
}

.mt-4 {
  margin-top: 20px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}
</style>
