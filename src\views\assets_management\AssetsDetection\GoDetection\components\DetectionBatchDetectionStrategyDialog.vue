<template>
  <el-dialog 
    v-model="props.visible" 
    :title="title" 
    width="600px" 
    @close="handleClose"
  >
    <el-alert
      type="info"
      :closable="false"
      show-icon
    >
      已选择 {{ selectedIds.length }} 个策略
    </el-alert>
    
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="140px" class="mt-4">
      <!-- 探测策略 -->
      <div class="form-section">
        <div class="section-title">探测策略</div>
        <el-form-item label="是否定期探测" prop="enableScheduledDetection">
          <el-radio-group v-model="formData.enableScheduledDetection">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item 
          v-if="formData.enableScheduledDetection" 
          label="探测周期" 
          prop="detectionCycle"
        >
          <el-select v-model="formData.detectionCycle" placeholder="请选择探测周期" class="w-full">
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
            <el-option label="每月" value="monthly" />
            <el-option label="每季度" value="quarterly" />
            <el-option label="每年" value="yearly" />
          </el-select>
        </el-form-item>

        <el-form-item label="是否即时探测" prop="enableImmediateDetection">
          <el-radio-group v-model="formData.enableImmediateDetection">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

      <!-- 通知策略 -->
      <div class="form-section">
        <div class="section-title">通知策略</div>
        <el-form-item label="启用短信通知" prop="enableSms">
          <el-radio-group v-model="formData.enableSms">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '批量编辑探测策略'
  },
  selectedIds: {
    type: Array as PropType<number[]>,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'submitted'])

const formRef = ref<FormInstance>()
const formData = reactive({
  enableScheduledDetection: false,
  detectionCycle: '',
  enableImmediateDetection: false,
  enableSms: false
})

const rules = reactive({
  enableScheduledDetection: [
    { required: true, message: '请选择是否定期探测', trigger: 'change' }
  ],
  detectionCycle: [
    { required: true, message: '请选择探测周期', trigger: 'change' }
  ],
  enableImmediateDetection: [
    { required: true, message: '请选择是否即时探测', trigger: 'change' }
  ],
  enableSms: [
    { required: true, message: '请选择是否启用短信通知', trigger: 'change' }
  ]
})

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    // TODO: 实现批量保存逻辑
    emit('submitted')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('请完善表单信息')
  }
}

const handleClose = () => {
  emit('update:visible', false)
  if (formRef.value) {
    formRef.value.resetFields()
  }
}
</script>

<style scoped>
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-select) {
  width: 100%;
}

.mt-4 {
  margin-top: 1rem;
}

.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-radio-group) {
  width: 100%;
  display: flex;
  gap: 30px;
}

:deep(.el-form-item__label) {
  font-weight: normal;
}

.w-full {
  width: 100%;
}
</style>