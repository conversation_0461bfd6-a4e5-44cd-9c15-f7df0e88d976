<template>
  <el-dialog v-model="dialogVisible" :title="'探测日志 - ' + taskName" width="1000px" destroy-on-close @close="handleClose">
    <div class="log-container">
      <div class="log-header">
        <el-form :inline="true" class="flex items-center flex-wrap gap-4 w-full">
          <!-- 日期范围 -->
          <el-form-item label="日期范围">
            <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至"
              start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"
              style="width: 330px" />
          </el-form-item>

          <!-- 探测类型 -->
          <el-form-item label="探测类型">
            <el-select v-model="queryParams.detectionType" placeholder="请选择" clearable style="width: 220px;">
              <el-option label="定期探测" value="regular" />
              <el-option label="临时探测" value="temporary" />
            </el-select>
          </el-form-item>

          <!-- 查询按钮 -->
          <el-form-item>
            <el-button type="primary" @click="queryLogs" :loading="loading">查询</el-button>
          </el-form-item>

          <!-- 重置按钮 -->
          <el-form-item>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>


      <el-table v-loading="loading" :data="logList" border stripe style="width: 100%">
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column prop="createTime" label="探测时间" min-width="160" align="center" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="detectionType" label="探测类型" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.detectionType === '1' ? 'warning' : 'primary'">
              {{ scope.row.detectionType === '1' ? '即时探测' : scope.row.detectionType === '2' ? '定时探测' : '未知' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="探测结果" align="center" min-width="260">
          <template #default="scope">
            <div class="detection-result" style="display: flex; flex-direction: column; gap: 6px;">
              <!-- 第一排：资产数量 -->
              <div>资产数量: {{ scope.row.assetCount || (scope.row.success + scope.row.fail) || 0 }}</div>

              <!-- 第二排：进度条 -->
              <el-progress :percentage="getSuccessRate(scope.row)" :stroke-width="12" status="success"
                :show-text="false" />

              <!-- 第三排：成功/失败/总数 -->
              <div class="result-stats" style="display: flex; justify-content: space-between;">
                <el-tag type="success" size="small">成功: {{ scope.row.success || 0 }}</el-tag>
                <el-tag type="danger" size="small">失败: {{ scope.row.fail || 0 }}</el-tag>
                <el-tag type="info" size="small">总数: {{ (scope.row.success + scope.row.fail) || 0 }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>


        <!--新增结果详情列 -->
        <el-table-column label="结果详情" width="100" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="viewDetails(scope.row)">
              <el-icon>
                <View />
              </el-icon> 详情
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="scope">
            <el-button type="danger" link @click="handleDelete(scope.row)">
              <el-icon>
                <Delete />
              </el-icon> 删除
            </el-button>
            <el-dropdown @command="(command) => handleExport(scope.row, command)">
              <el-button type="success" link>
                <el-icon>
                  <Download />
                </el-icon> 导出
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="1">导出所有</el-dropdown-item>
                  <el-dropdown-item command="2">导出探测成功</el-dropdown-item>
                  <el-dropdown-item command="3">导出探测失败</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>


      <div class="pagination-container">
        <el-pagination v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 日志详情弹窗 -->
    <el-dialog v-model="detailDialog.visible" title="探测日志详情" width="1000px" append-to-body destroy-on-close>
      <!-- 日志详情内容部分保持不变 -->
      <div v-if="detailDialog.currentLog" class="log-details">
        <div class="detail-item">
          <span class="detail-label">探测时间：</span>
          <span class="detail-value">{{ formatDateTime(detailDialog.currentLog.createTime) }}</span>
        </div>

        <!-- 四个统计卡片 -->
        <el-row :gutter="20" class="mb-4">
          <!-- 资产总数 -->
          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">资产总数</div>
                  <div class="text-2xl font-bold mt-2 text-blue-600">120</div>
                </div>
                <el-icon style="font-size: 32px; color: #3b82f6;">
                  <InfoFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <!-- 在线资产 -->
          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">部分存活</div>
                  <div class="text-2xl font-bold mt-2 text-green-500">15</div>
                </div>
                <el-icon style="font-size: 32px; color: #22c55e;">
                  <CircleCheckFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <!-- 部分存活 -->
          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">在线资产</div>
                  <div class="text-2xl font-bold mt-2 text-amber-500">85</div>
                </div>
                <el-icon style="font-size: 32px; color: #f59e0b;">
                  <Loading />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <!-- 离线资产 -->
          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">离线资产</div>
                  <div class="text-2xl font-bold mt-2 text-green-500">15</div>
                </div>
                <el-icon style="font-size: 32px; color: #22c55e;">
                  <CircleCheckFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- <div class="detail-item">
          <span class="detail-label">探测结果：</span>
          <div class="result-stats">
            <el-tag type="success" size="small" class="stat-tag">成功: {{ detailDialog.currentLog.success || 0 }}</el-tag>
            <el-tag type="danger" size="small" class="stat-tag">失败: {{ detailDialog.currentLog.fail || 0 }}</el-tag>
            <el-tag type="info" size="small" class="stat-tag">总数: {{ detailDialog.currentLog.fail +
              detailDialog.currentLog.success || 0 }}</el-tag>
          </div>
        </div> -->

        <!-- 资产列表区域 -->
        <div class="asset-section">

          <!-- 资产列表筛选 -->
          <div class="filter-area"
            style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px;">
            <div style="display: flex; align-items: center; gap: 12px;">
              <span class="detail-label" style="font-weight: 500;">资产筛选：</span>
              <el-radio-group v-model="assetFilter" size="small">
                <el-radio-button label="all">全部</el-radio-button>
                <el-radio-button label="success">成功</el-radio-button>
                <el-radio-button label="failed">失败</el-radio-button>
              </el-radio-group>
            </div>
            <!-- 你可根据需要放右侧额外操作按钮 -->
            <!-- <el-button type="primary" size="small">导出</el-button> -->
          </div>

          <!-- 资产表格 -->
          <div class="assets-list">
            <el-table :data="filteredAssets" style="width: 100%;" v-if="filteredAssets.length > 0" border stripe>
              <el-table-column prop="assetId" label="序号" width="70" align="center" />
              <el-table-column label="资产类型" width="100" align="center">
                <template #default="scope">
                  {{ formatAssetType(scope.row.type) }}
                </template>
              </el-table-column>
              <el-table-column prop="assetName" label="资产名称" min-width="130" />
              <el-table-column label="地址/IP" min-width="140" show-overflow-tooltip>
                <template #default="scope">
                  <span>{{ scope.row.type === 10 ? scope.row.url : scope.row.ip }}</span>
                </template>
              </el-table-column>
              <el-table-column label="管理单位" min-width="120" show-overflow-tooltip>
                <template #default="scope">
                  {{ scope.row.deptName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="管理人员" width="100" show-overflow-tooltip>
                <template #default="scope">
                  {{ scope.row.managerName || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="探测结果" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.isSuccess ? 'success' : 'danger'">
                    {{ scope.row.isSuccess ? '探测成功' : '探测失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="message" label="结果信息" min-width="160" align="center" show-overflow-tooltip />
            </el-table>
            <el-empty v-else :description="getEmptyDescription()" style="margin-top: 20px;" />
          </div>
        </div>

      </div>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DetectionAPI from "@/api/assets_management/assets_detection/index"
import { Delete, Download, View, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskName: {
    type: String,
    default: '探测任务'
  },
  detectionId: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 对话框可见状态
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 日志列表数据
const logList = ref<any[]>([])
const loading = ref(false)
const exportLoading = ref(false)
const total = ref(0)
const allAssets = ref<any[]>([]) // 存储所有资产（成功和失败）
const assetFilter = ref('all') // 资产筛选选项: all, success, failed

// 查询参数
const queryParams = reactive({
  detectionId: undefined as number | undefined,
  pageNum: 1,
  pageSize: 10,
  dateRange: [] as string[],
  startTime: undefined as string | undefined,
  endTime: undefined as string | undefined,
  detectionStatus: undefined as number | undefined,
  detectionResult: undefined as string | undefined
})

// 详情弹窗
const detailDialog = reactive({
  visible: false,
  currentLog: null as any
})

// 资产详情弹窗
const assetDetailDialog = reactive({
  visible: false,
  currentAsset: null as any
})

// 根据筛选条件过滤资产
const filteredAssets = computed(() => {
  if (!allAssets.value.length) return [];

  if (assetFilter.value === 'all') {
    return allAssets.value;
  } else if (assetFilter.value === 'success') {
    return allAssets.value.filter(asset => asset.isSuccess === true);
  } else if (assetFilter.value === 'failed') {
    return allAssets.value.filter(asset => asset.isSuccess === false);
  }

  return allAssets.value;
});

// 获取空状态的描述
const getEmptyDescription = () => {
  if (assetFilter.value === 'all') return '暂无资产数据';
  if (assetFilter.value === 'success') return '暂无成功探测的资产';
  return '暂无失败探测的资产';
};

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return '-'
  return dateTime.replace('T', ' ').substring(0, 19)
}

// 检查是否有失败资产
const hasFailedAssets = (log: any) => {
  return log.fail > 0 && log.assetsLogList && log.assetsLogList.some((asset: any) => asset.fail > 0)
}

// 格式化失败资产信息
const formatFailedAssets = (log: any) => {
  if (!log.assetsLogList) return '-'

  const failedAssetsCount = log.assetsLogList.filter((asset: any) => asset.fail > 0).length
  return `${failedAssetsCount} 个资产探测失败`
}

// 状态转换函数
const getStatusType = (status: string): string => {
  if (!status) return 'info'
  if (status.toLowerCase().includes('success')) return 'success'
  if (status.toLowerCase().includes('warn')) return 'warning'
  if (status.toLowerCase().includes('fail') || status.toLowerCase().includes('error')) return 'danger'
  return 'info'
}

// 资产类型格式化
const formatAssetType = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '服务器',
    2: '网络设备',
    3: '安全设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 查询日志数据
const queryLogs = async () => {
  if (!props.detectionId) {
    ElMessage.warning('缺少探测任务ID，无法查询日志')
    return
  }

  try {
    loading.value = true

    // 处理日期范围
    if (queryParams.dateRange && queryParams.dateRange.length === 2) {
      queryParams.startTime = queryParams.dateRange[0]
      queryParams.endTime = queryParams.dateRange[1]
    } else {
      queryParams.startTime = undefined
      queryParams.endTime = undefined
    }

    // 设置父ID
    queryParams.detectionId = props.detectionId

    // 构建查询参数
    const params = {
      detectionId: queryParams.detectionId,
      pageNum: queryParams.pageNum,
      pageSize: queryParams.pageSize,
      startTime: queryParams.startTime,
      endTime: queryParams.endTime,
      detectionStatus: queryParams.detectionStatus,
      detectionResult: queryParams.detectionResult
    }

    // 调用API获取日志
    const { list: resultList, total: resultTotal } = await DetectionAPI.getDetectionLogById(params)

    logList.value = resultList || []
    total.value = resultTotal || 0

    console.log('获取探测日志:', logList.value)
  } catch (error) {
    console.error('获取探测日志失败:', error)
    ElMessage.error('获取探测日志失败')
  } finally {
    loading.value = false
  }
}

// 重置查询参数
const resetQuery = () => {
  queryParams.dateRange = []
  queryParams.startTime = undefined
  queryParams.endTime = undefined
  queryParams.detectionStatus = undefined
  queryParams.detectionResult = undefined
  queryParams.pageNum = 1
  queryLogs()
}

// 处理分页大小变化
const handleSizeChange = (size: number) => {
  queryParams.pageSize = size
  queryLogs()
}

// 处理页码变化
const handleCurrentChange = (page: number) => {
  queryParams.pageNum = page
  queryLogs()
}

// 查看详情
const viewDetails = (log: any) => {
  console.log('查看日志详情:', log)
  detailDialog.currentLog = log

  // 重置筛选选项
  assetFilter.value = 'all'

  // 处理所有资产列表（包括成功和失败的）
  allAssets.value = []

  if (log.assetsLogList && log.assetsLogList.length > 0) {
    log.assetsLogList.forEach((assetLog: any) => {
      // 处理成功资产
      if (assetLog.success > 0 && assetLog.successAssetsList) {
        assetLog.successAssetsList.forEach((successItem: any) => {
          allAssets.value.push({
            assetId: assetLog.assetId,
            assetName: assetLog.assetName,
            type: assetLog.type,
            ip: successItem.ip,
            port: successItem.port,
            url: successItem.url,
            status: successItem.status || 'Success',
            message: successItem.message || '探测成功',
            createTime: log.createTime,
            isSuccess: true,
            deptName: assetLog.deptName,
            managerName: assetLog.managerName,
          })
        })
      }

      // 处理失败资产
      if (assetLog.fail > 0 && assetLog.failAssetsList) {
        assetLog.failAssetsList.forEach((failedItem: any) => {
          allAssets.value.push({
            assetId: assetLog.assetId,
            assetName: assetLog.assetName,
            type: assetLog.type,
            ip: failedItem.ip,
            port: failedItem.port,
            url: failedItem.url,
            status: failedItem.status || 'Failed',
            message: failedItem.message || '探测失败',
            createTime: log.createTime,
            isSuccess: false,
            deptName: assetLog.deptName,
            managerName: assetLog.managerName,
          })
        })
      }
    })
    console.log('所有资产列表:', allAssets.value)
  }

  detailDialog.visible = true
}

// 查看资产详情
const viewAssetDetails = (asset: any) => {
  console.log('查看资产详情:', asset)
  assetDetailDialog.currentAsset = asset
  assetDetailDialog.visible = true
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 删除探测日志
const handleDelete = async (log: any) => {
  try {

    // 刷新列表
    queryLogs()
    // 可选：通知父组件刷新
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 导出日志
const handleExport = async (log: any, exportType: string) => {
  if (!log || !log.id) {
    ElMessage.warning('无法获取日志ID，导出失败')
    return
  }

  try {
    exportLoading.value = true

    // 构建导出参数
    const exportParams = {
      detectionLogId: log.id,
      exportType: exportType // 1=所有，2=成功，3=失败
    }

    // 显示导出类型对应的提示
    const exportTypeText = {
      '1': '所有',
      '2': '探测成功',
      '3': '探测失败'
    }[exportType];

    await DetectionAPI.exportServer(exportParams).then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success(`导出${exportTypeText}资产成功`)
    });
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

//计算探测结果进度条
const getSuccessRate = (row) => {
  const success = row.success || 0;
  const fail = row.fail || 0;
  const total = success + fail;
  return total === 0 ? 0 : Math.round((success / total) * 100);
};

// 监听属性变化
watch(() => props.visible, (newValue) => {
  if (newValue && props.detectionId) {
    queryLogs()
  }
})
</script>

<style scoped>
.log-container {
  padding: 0 10px;
}

.log-header {
  margin-bottom: 16px;
}

.pagination-container {
  margin-top: 16px;
  padding: 10px 0;
  text-align: right;
}

.log-details {
  padding: 10px 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.detail-full {
  flex-direction: column;
  align-items: flex-start;
}

.detail-label {
  font-weight: bold;
  margin-right: 8px;
  min-width: 80px;
  color: var(--el-text-color-secondary);
}

.detail-value {
  color: var(--el-text-color-primary);
}

.detail-content {
  margin-top: 8px;
  width: 100%;
  background-color: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.detail-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 13px;
  line-height: 1.5;
}

.assets-list {
  padding: 0;
  background-color: transparent;
}

.filter-area {
  margin-bottom: 16px;
}

.result-stats {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.stat-tag {
  min-width: 60px;
}

.asset-details {
  padding: 0 10px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 资产探测结果样式 */
.detection-result {
  padding: 4px 8px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

/* 表单和筛选框的 */
.asset-section {
  padding: 20px 24px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.detail-label {
  font-size: 14px;
  color: #333;
  min-width: 70px;
}
</style>
