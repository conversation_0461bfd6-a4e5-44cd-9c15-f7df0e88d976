// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/dept.mock.ts
var dept_mock_default = defineMock([
  {
    url: "dept/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 1,
          label: "\u6709\u6765\u6280\u672F",
          children: [
            {
              value: 2,
              label: "\u7814\u53D1\u90E8\u95E8"
            },
            {
              value: 3,
              label: "\u6D4B\u8BD5\u90E8\u95E8"
            }
          ]
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "dept",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: 1,
          parentId: 0,
          name: "\u6709\u6765\u6280\u672F",
          code: "YOULAI",
          sort: 1,
          status: 1,
          children: [
            {
              id: 2,
              parentId: 1,
              name: "\u7814\u53D1\u90E8\u95E8",
              code: "RD001",
              sort: 1,
              status: 1,
              children: [],
              createTime: null,
              updateTime: "2022-04-19 12:46"
            },
            {
              id: 3,
              parentId: 1,
              name: "\u6D4B\u8BD5\u90E8\u95E8",
              code: "QA001",
              sort: 1,
              status: 1,
              children: [],
              createTime: null,
              updateTime: "2022-04-19 12:46"
            }
          ],
          createTime: null,
          updateTime: null
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  // 新增部门
  {
    url: "dept",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u65B0\u589E\u90E8\u95E8" + body.name + "\u6210\u529F"
      };
    }
  },
  // 获取部门表单数据
  {
    url: "dept/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: deptMap[params.id],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 修改部门
  {
    url: "dept/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u4FEE\u6539\u90E8\u95E8" + body.name + "\u6210\u529F"
      };
    }
  },
  // 删除部门
  {
    url: "dept/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "\u5220\u9664\u90E8\u95E8" + params.id + "\u6210\u529F"
      };
    }
  }
]);
var deptMap = {
  1: {
    id: 1,
    name: "\u6709\u6765\u6280\u672F",
    code: "YOULAI",
    parentId: 0,
    status: 1,
    sort: 1
  },
  2: {
    id: 2,
    name: "\u7814\u53D1\u90E8\u95E8",
    code: "RD001",
    parentId: 1,
    status: 1,
    sort: 1
  },
  3: {
    id: 3,
    name: "\u6D4B\u8BD5\u90E8\u95E8",
    code: "QA001",
    parentId: 1,
    status: 1,
    sort: 1
  }
};
export {
  dept_mock_default as default
};
