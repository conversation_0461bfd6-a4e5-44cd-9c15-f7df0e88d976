<template>
  <el-card shadow="never">
    <!-- 系统树 -->
    <el-input v-model="systemName" placeholder="系统名称" clearable>
      <template #prefix>
        <i-ep-search />
      </template>
    </el-input>

    <el-tree
      ref="systemTreeRef"
      class="mt-2 system-tree"
      :data="systemList"
      :props="{ children: 'children', label: 'label', disabled: '' }"
      :expand-on-click-node="false"
      :filter-node-method="handleSystemFilter"
      default-expand-all
      @node-click="handleSystemNodeClick"
    />
  </el-card>
</template>

<script setup lang="ts">
// import SystemAPI from "@/api/assets_management/details/system";
import systemsEntityAPI from "@/api/assets_management/details/systems-entity";

const props = defineProps({
  modelValue: {
    type: [Number],
    default: undefined,
  },
});

const systemList = ref<OptionType[]>(); // 系统列表
const systemTreeRef = ref(ElTree); // 系统树
const systemName = ref(); // 系统名称

const emits = defineEmits(["system-click"]);

const systemId = useVModel(props, "modelValue", emits);
// const systemId = ref<number | undefined>();

watchEffect(
  () => {
    systemTreeRef.value.filter(systemName.value);
  },
  {
    flush: "post", // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 系统筛选 */
function handleSystemFilter(value: string, data: any) {
  if (!value) {
    return true;
  }
  return data.label.indexOf(value) !== -1;
}

/** 系统树节点 Click */
function handleSystemNodeClick(data: { [key: string]: any }) {
  systemId.value = data.value;
  // console.log(systemId.value);
  emits("system-click");
}

onBeforeMount(() => {
  systemsEntityAPI.getOptions().then((data) => {
    systemList.value = data;
  });
});
</script>

<style scoped>
.system-tree {
  max-height: 600px; /* 设置最大高度 */
  overflow-y: auto; /* 超出时显示滚动条 */
}
</style>
