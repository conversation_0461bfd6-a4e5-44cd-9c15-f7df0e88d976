<template>
  <el-dialog
    v-model="dialogVisible"
    title="工单详情"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
    class="ticket-detail-dialog"
  >
    <div class="ticket-detail-container">
      <el-form
      :model="ticketData"
      label-width="140px"
    >
     <el-card class="info-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单基本信息</span>
          </div>
        </template>
         <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label-width="90px" label="工单状态：">
              <el-input disabled
              >
              <template v-slot:prepend>
                 <dictmap v-model="ticketData.status" code="VLUNS_TIC" />
              </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="90px" label="当前环节：">
              <el-input
                disabled
              >
              <template v-slot:prepend>
              <dictmap v-model="ticketData.step" code="VLUNS_STEP" />
              </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="120px" label="工单开始时间：">
              <el-input
                disabled
                v-model="ticketData.createTime"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="120px" label="工单结束时间：">
              <el-input
                disabled
                v-model="ticketData.deadline"
              >
              </el-input>
            </el-form-item>
          </el-col>
         </el-row>
       </el-card>
       <el-card class="info-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单创建人信息</span>
          </div>
        </template>
         <el-row :gutter="24">
          <el-col :span="6">
             <el-form-item label-width="90px" label="工号：">
              <el-input
                disabled
                v-model="ticketData.applicant"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
             <el-form-item label-width="90px" label="创建人：">
              <el-input
                disabled
                v-model="ticketData.employeeId"
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="6">
             <el-form-item label-width="90px" label="所属部门：">
             <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                  disabled
                    code="dept0x0"
                    v-model="ticketData.deptId"
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
           <el-col :span="6">
             <el-form-item label-width="90px" label="联系方式：">
              <el-input
                disabled
                v-model="ticketData.mobile"
              ></el-input>
            </el-form-item>
          </el-col>
         </el-row>
       </el-card>
       <el-card class="mb-4" >
        <template #header>
          <div class="card-header">
            <span>漏洞整改部门</span>
          </div>
        </template>
          <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="部门名称：">
              <el-input 
                v-model="shstep.deptName"
                disabled
              ></el-input>
        </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="联系人：" disabled>
              <el-input 
                v-model="shstep.userName"
                disabled
              ></el-input>
          
           
            </el-form-item>
          </el-col>
           <el-col :span="8">
             <el-form-item label="联系方式：">
              <el-input 
                v-model="shstep.userMobile"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
        </el-card>
         <!-- 安全工程师信息 -->
      <el-card class="mb-4" disabled>
        <template #header>
          <div class="card-header">
          <span>
             安全工程师信息
             <el-tooltip
            placement="top"
            effect="light"
            content="工程师基本信息"
            :raw-content="true"
          >
            <el-icon style="vertical-align: -0.15em" size="16">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
          </span>
          </div>
        </template>
        <!-- <p class="base_title">
          安全工程师信息
        </p> -->
        <!-- 安全工程师信息开始 -->
        <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
        <div>
              <el-form-item label-width="100px" label="工程师名称：" prop="engineerName" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerName"></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input  disabled v-model="SafetyEngineerbox.engineerMobile"> </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerWechat"></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="70px" prop="engineerQq" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerQq"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="60px" prop="engineerEmail" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerEmail"></el-input>
            </el-form-item>
        </div>
        </el-form>
        <!-- 安全工程师信息结束 -->
      </el-card>
      <el-card class="mb-4" disabled>
        <template #header>
          <div class="card-header">
          <span>
             安全漏洞信息
          </span>
          </div>
        </template>
         <el-row :gutter="24">
          <el-col :span="8">
             <el-form-item label="信息系统名称：" label-width="140px" prop="title" >
              <el-input disabled v-model="ticketData.title" >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="域名/IP：" label-width="140px" prop="domainIp" >
              <el-input disabled v-model="ticketData.domainIp"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="fontw" label-width="140px" label="漏洞来源：" prop="loopholeSource" disabled>
              <Dictionary v-model="ticketData.loopholeSource" code="VLUNS_O"
                disabled></Dictionary>
            </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item  class="fontw" label-width="140px" label="安全漏洞报告：">
               <ul  class="fileList_ul">
                  <li>
                    <span>{{ ticketData.newFileName }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile2(ticketData.newFileUrl,ticketData.newFileName)">下载</el-button>
                  </li>
                  <!-- <li v-if="fileList.length<=0">无</li> -->
                </ul>
              </el-form-item>
           </el-col>
           <el-col :span="8" v-if="ticketData.commentsReview">
             <el-form-item  class="fontw" label-width="140px" label="安全漏洞验证报告：">
                <ul  class="fileList_ul">
                  <li v-for="file in ticketData.commentsReview.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
           </el-col>
           <el-col :span="24">
             <el-form-item label="安全漏洞情况描述：" prop="content">
          <el-input type="textarea" v-model="ticketData.content" :rows="2" disabled></el-input>
        </el-form-item>
          </el-col>
           <el-col :span="24">
              <el-form-item class="fontw" label-width="140px" label="安全漏洞详情：" prop="" disabled>
          <el-table :data="vulnerabilityList" style="width: 100%" border>
          <el-table-column label="序号" min-width="60" align="center" type="index" :index="indexMethod" />
          <el-table-column align="center" min-width="150" prop="name" label="漏洞名称"></el-table-column>
          <el-table-column align="center" min-width="100" prop="level" label="漏洞级别">
            <template #default="{ row }">
              <el-tag :type="getVulnLevelType(row.level)">
                <dictmap v-model="row.level" code="VLUNS_LEVEL" />
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" min-width="150" prop="ip" label="服务器IP"></el-table-column>
          <el-table-column align="center" min-width="180" prop="url" label="URL地址" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" min-width="200" prop="remark" label="漏洞描述" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" min-width="100" prop="fix" label="是否修复">
            <template #default="{ row }">
              <el-tag :type="row.fix === 1 ? 'success' : 'danger'">
                {{ row.fix === 1 ? '已修复' : '未修复' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" min-width="180" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        </el-table>
        </el-form-item>
           </el-col>
         </el-row>
        </el-card>
    </el-form>
      <!-- 基本信息卡片 -->
      <!-- <el-card class="info-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单基本信息</span>
            <div class="header-actions">
              <el-tag :type="getStatusType(ticketData.status)">
                <dictmap v-model="ticketData.status" code="VLUNS_TIC" />
              </el-tag>
            </div>
          </div>
        </template>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="工单编号" width="180">{{ ticketData.id }}</el-descriptions-item>
          <el-descriptions-item label="标题" width="180">{{ ticketData.title }}</el-descriptions-item>
          <el-descriptions-item label="状态" width="180">
            <el-tag :type="getStatusType(ticketData.status)">
              <dictmap v-model="ticketData.status" code="VLUNS_TIC" />
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="申请人">
            <div class="user-info">
              <el-avatar :size="24" :icon="UserFilled" class="user-avatar" />
              <Dictmap code="user0x0" v-model="ticketData.applicant" />
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="工号">{{ ticketData.employeeId }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ ticketData.mobile }}</el-descriptions-item>
          
          <el-descriptions-item label="所属部门">
            <Dictmap code="dept0x0" v-model="ticketData.deptId" />
          </el-descriptions-item> -->
<!--          <el-descriptions-item label="应用系统">
            <Dictmap code="system0x0" v-model="ticketData.systemId" />
          </el-descriptions-item>-->
          <!-- <el-descriptions-item label="漏洞来源">
            <Dictmap code="VLUNS_O" v-model="ticketData.loopholeSource" />
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">{{ formatDateTime(ticketData.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="要求整改时间">{{ formatDateTime(ticketData.deadline) }}</el-descriptions-item>
          <el-descriptions-item label="当前步骤">
            <el-tag :type="getStepTagType(ticketData.step)">
              <dictmap v-model="ticketData.step" code="VLUNS_STEP" />
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="主要内容" :span="3">
            <div class="content-block">{{ ticketData.content }}</div>
          </el-descriptions-item>
          
          <el-descriptions-item label="备注" :span="3">
            <div class="remarks-block">{{ ticketData.remarks || '暂无备注' }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </el-card> -->
      
      <!-- 漏洞列表 -->
      <!-- <el-card class="vulnerability-card mb-4" v-if="vulnerabilityList.length > 0">
        <template #header>
          <div class="card-header">
            <span class="header-title">漏洞列表</span>
            <div class="header-actions">
              <el-tag>共 {{ vulnerabilityList.length }} 个漏洞</el-tag>
            </div>
          </div>
        </template>
        <el-table :data="vulnerabilityList" style="width: 100%" border>
          <el-table-column label="序号" align="center" type="index" :index="indexMethod" />
          <el-table-column align="center" width="150" prop="name" label="漏洞名称"></el-table-column>
          <el-table-column align="center" width="100" prop="level" label="漏洞级别">
            <template #default="{ row }">
              <el-tag :type="getVulnLevelType(row.level)">
                <dictmap v-model="row.level" code="VLUNS_LEVEL" />
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" width="150" prop="ip" label="服务器IP"></el-table-column>
          <el-table-column align="center" width="180" prop="url" label="URL地址" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" min-width="200" prop="remark" label="漏洞描述" show-overflow-tooltip></el-table-column>
          <el-table-column align="center" width="100" prop="fix" label="是否修复">
            <template #default="{ row }">
              <el-tag :type="row.fix === 1 ? 'success' : 'danger'">
                {{ row.fix === 1 ? '已修复' : '未修复' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" width="180" prop="createTime" label="创建时间" show-overflow-tooltip></el-table-column>
        </el-table>
      </el-card> -->
      
      <!-- 流转记录 -->
      <el-card class="flow-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单流转记录</span>
            <div class="header-actions">
              <el-switch
                v-model="flowViewMode"
                active-text="时间线"
                inactive-text="表格"
                inline-prompt
              />
            </div>
          </div>
        </template>
        
        <!-- 表格视图 -->
        <el-table v-if="!flowViewMode" :data="flowRecords" style="width: 100%" stripe border v-loading="flowLoading">
          <el-table-column prop="step" label="环节" width="120">
            <template #default="{ row }">
              <el-tag :type="getFlowTimelineType(row.step)" effect="plain">{{ row.step }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="处理人" width="180">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="24" :icon="UserFilled" class="user-avatar" />
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column label="执行时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.executeTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="result" label="处理结果" width="180">
            <template #default="{ row }">
              <el-tag v-if="row.result" :type="getResultTagType(row.result)" size="small">
                {{ row.result }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="处理意见" min-width="200" show-overflow-tooltip />
        </el-table>
        
        <!-- 时间线视图 -->
        <div v-else class="timeline-container" v-loading="flowLoading">
          <el-empty v-if="flowRecords.length === 0 && !flowLoading" description="暂无流转记录" />
          <el-timeline v-else>
            <el-timeline-item
              v-for="(flow, index) in flowRecords"
              :key="index"
              :type="getFlowTimelineType(flow.step)"
              :timestamp="formatDateTime(flow.executeTime || flow.startTime)"
              :hollow="index !== 0"
            >
              <h4>{{ flow.step }}</h4>
              <p class="timeline-content">
                <span class="timeline-user">处理人：
                  {{ flow.name }}
                </span>
                <span v-if="flow.result" class="timeline-result">
                  处理结果：
                  <el-tag :type="getResultTagType(flow.result)" size="small">{{ flow.result }}</el-tag>
                </span>
                <span v-if="flow.comment" class="timeline-msg">
                  处理意见：{{ flow.comment }}
                </span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
      
      <!-- 附件列表 -->
      <el-card class="attachment-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">附件列表</span>
            <div class="header-actions">
              <el-tag>共 {{ attachments.length }} 个附件</el-tag>
            </div>
          </div>
        </template>
        <div v-if="attachments.length === 0" class="attachment-empty">
          <el-empty description="暂无附件" />
        </div>
        <div v-else class="attachment-list">
          <el-table :data="attachments" style="width: 100%">
            <el-table-column prop="linkName" label="环节名称" />
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="createByName" label="创建人名称" />
            <el-table-column prop="createTime" label="上传时间" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  下载 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <!-- <el-button type="primary" @click="handleProcess">处理工单</el-button> -->
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import safetyAPI from "@/api/work_management/safety/index"

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  ticketId: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'navigateToProcess'])

// 对话框控制
const dialogVisible = ref(false)
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
  if (val && props.ticketId) {
    loadTicketData()
  }
})
const shstep=ref({})
// 数据状态
const ticketData = reactive({
  id: undefined,
  title: '',
  status: '',
  applicant: '',
  employeeId: '',
  mobile: '',
  deptId: '',
  systemId: '',
  loopholeSource: '',
  createTime: '',
  deadline: '',
  step: '',
  content: '',
  remarks: ''
})

const vulnerabilityList = ref([])
const flowRecords = ref<FlowRecord[]>([])
const attachments = ref([])

// 加载状态
const loading = ref(false)
const flowLoading = ref(false)

// 视图控制
const flowViewMode = ref(false) // true=时间线, false=表格

// 加载工单详情
const loadTicketData = async () => {
  if (!props.ticketId) return
  
  loading.value = true
  try {
    // 获取工单基本信息
    const data = await safetyAPI.getFormData(props.ticketId)
    Object.assign(ticketData, data)
      // 整改部门信息
   const foundItem = data.reviewProcessForms?.find(item => item.executeDeptType == 3);
   shstep.value=foundItem
    
    // 获取漏洞列表
    vulnerabilityList.value = data.vulns || []
    
    // 获取附件列表
    attachments.value = await safetyAPI.getFileList(props.ticketId) || []
    
    // 加载流转信息
    await loadFlowRecords()
    
  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

// 定义流转记录类型
interface FlowRecord {
  step: string;
  name: string;
  startTime: string | null;
  executeTime: string | null;
  result: string;
  comment: string;
  index: number;
}

// 加载流转记录
const loadFlowRecords = async () => {
  if (!props.ticketId) return
  
  flowLoading.value = true
  try {
    const res = await safetyAPI.getFlowInfo(props.ticketId)
    console.log('流转记录:', res)
    if (res) {
      // 处理流转记录数据
      flowRecords.value = res.map((item: any, index: number) => {
        return {
          step: item.name || '-',
          name: item.userName || '-',
          startTime: item.executeStartTime || null,
          executeTime: item.executeTime || null,
          result: item.processingResults || '',
          comment: item.commentContent || item.msg || '',
          index
        } as FlowRecord
      })
    }
  } catch (error) {
    console.error('加载流转记录失败:', error)
  } finally {
    flowLoading.value = false
  }
}

// 处理工单 - 跳转到处理流程
const handleProcess = () => {
  emit('navigateToProcess', props.ticketId)
  dialogVisible.value = false
}

// 下载文件
const downloadFile = (file) => {
  if (!file.url) {
    ElMessage.warning('文件地址不存在')
    return
  }
  try {
    // window.open(file.url, '_blank')
    if (file.url) {
  fetch(file.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = file.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  }); 
  } 
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}
const  downloadFile2=(newFileUrl,newFileName)=>{
  if(newFileUrl){
     fetch(newFileUrl)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download =newFileName; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  }); 
  }
}

// 格式化时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'
  
  try {
    return dateTimeStr.replace('T', ' ').substring(0, 19)
  } catch (error) {
    return dateTimeStr || '-'
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let formattedSize = size
  let unitIndex = 0
  
  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024
    unitIndex++
  }
  
  return `${formattedSize.toFixed(2)} ${units[unitIndex]}`
}

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '0': 'warning', // 处理中
    '1': 'success', // 已完成
    '2': 'info'     // 关闭
  }
  return statusMap[status] || 'info'
}

// 获取步骤标签类型
const getStepTagType = (step: string) => {
  const stepMap: Record<string, string> = {
    '1': 'primary',
    '2': 'success',
    '3': 'info',
    '4': 'warning',
    '5': 'danger',
    '6': ''
  }
  return stepMap[step] || 'info'
}

// 获取漏洞级别标签类型
const getVulnLevelType = (level) => {
  const levelMap = {
    '0': 'danger',
    '1': 'warning',
    '2': 'info',
    '4': 'info',
    '5': 'info',
  }
  return levelMap[level] || 'info'
}

// 获取流转记录时间线类型
const getFlowTimelineType = (step) => {
  const typeMap = {
    '1': 'primary',
    '2': 'success',
    '3': 'info',
    '4': 'warning',
    '5': 'danger',
    '6': ''
  }
  
  return typeMap[step] || 'info'
}

// 获取处理结果标签类型
const getResultTagType = (result) => {
  const resultTagMap = {
    '通过': 'success',
    '不通过': 'danger',
    '已修复': 'success',
    '未修复': 'warning',
    '已关闭': 'info',
    '满意': 'success',
    '不满意': 'danger'
  }
  
  return resultTagMap[result] || 'info'
}
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);
// 获取安全工程师信息
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}
// 组件挂载时加载数据
onMounted(() => {
  if (props.visible && props.ticketId) {
    loadTicketData()
  }
    SafetyEngineerConfig()

})
</script>

<style scoped>
.ticket-detail-container {
  padding: 0 10px;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量替换硬编码颜色 */
}

/* 修改内容块和备注块样式，使用主题变量 */
.content-block,
.remarks-block {
  padding: 10px;
  background-color: var(--el-fill-color-light);  /* 使用主题变量代替 #f9f9f9 */
  border-radius: 4px;
  white-space: pre-wrap;
  min-height: 60px;
  color: var(--el-text-color-primary);  /* 确保文字颜色适配主题 */
  border: 1px solid var(--el-border-color-lighter);  /* 添加边框增强可读性 */
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: var(--el-color-primary-light-9);  /* 使用主题变量 */
  color: var(--el-color-primary);  /* 使用主题变量 */
}

.timeline-container {
  padding: 10px 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
  color: var(--el-text-color-primary);  /* 使用主题变量 */
}

.timeline-result {
  margin: 5px 0;
}

.timeline-msg {
  color: var(--el-text-color-secondary);  /* 使用主题变量代替 #606266 */
  white-space: pre-wrap;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

/* 修改附件项样式，使用主题变量 */
.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid var(--el-border-color);  /* 使用主题变量代替 #ebeef5 */
  border-radius: 4px;
  width: 300px;
  transition: all 0.3s;
  background-color: var(--el-bg-color);  /* 添加背景色 */
}

.attachment-item:hover {
  background-color: var(--el-fill-color-light);  /* 使用主题变量代替 #f5f7fa */
  box-shadow: 0 2px 8px var(--el-box-shadow-lighter);  /* 使用主题变量 */
}

.attachment-icon {
  font-size: 24px;
  color: var(--el-color-primary);  /* 使用主题变量代替 #409EFF */
}

.attachment-info {
  flex: 1;
  overflow: hidden;
}

.attachment-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
  color: var(--el-text-color-primary);  /* 添加适配主题的文字颜色 */
}

.attachment-size {
  font-size: 12px;
  color: var(--el-text-color-secondary);  /* 使用主题变量代替 #909399 */
}

.attachment-empty {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

/* 添加表格暗色模式适配 */
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
}

/* 时间线暗色模式适配 */
:deep(.el-timeline-item__content) {
  color: var(--el-text-color-primary);
}

:deep(.el-timeline-item__timestamp) {
  color: var(--el-text-color-secondary);
}

/* 描述列表暗色模式适配 */
:deep(.el-descriptions__label) {
  color: var(--el-text-color-secondary);
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
}
:deep(.el-form-item) {
  margin-right: 0;
}
.fileList_ul{
  width: 100%;
}
 .fileList_ul li{
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f7f6f6;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
  }
</style>
