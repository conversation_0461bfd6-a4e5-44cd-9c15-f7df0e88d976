<!-- 物联网设备管理 -->
<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-blue-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-blue-500 text-3xl"><Tickets /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">
                物联网设备总数
              </span>
              <div class="text-4xl font-bold text-gray-900 mt-1">128</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">同比增长 5.2%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-green-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-green-500 text-3xl"><Check /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">正常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">105</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">占比 82.03%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-yellow-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-yellow-500 text-3xl"><Warning /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">部分存活</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">17</div>
            </div>
          </div>
          <div class="text-yellow-600 text-sm flex justify-start items-center">
            <el-icon><Bottom /></el-icon>
            <span class="ml-1">占比 13.28%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-red-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-red-500 text-3xl"><Close /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">异常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">6</div>
            </div>
          </div>
          <div class="text-red-600 text-sm flex justify-start items-center">
            <el-icon><Bell /></el-icon>
            <span class="ml-1">需要立即处理</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree
          v-model="queryParams.deptId"
          @node-click="handleQuery"
          class="mb-2"
        />
        <system-tree
          v-model="queryParams.systemId"
          @system-click="handleQuery"
        />
      </el-col>

      <!-- 设备列表 -->
      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <!-- 设备列表 -->

        <FuzzySearch
          v-model="queryParams"
          placeholder="搜索设备名称、管理员..."
          search-field="keyword"
          @search="handleQuery"
          @reset="handleResetQuery"
        >
          <template #filters="{ form }">
            <!-- 基础核心筛选条件（始终显示） -->
            <el-form-item label="设备名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="设备名称"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>

            <el-form-item label="管理员" prop="ownerName">
              <el-input
                v-model="form.ownerName"
                placeholder="资产管理员"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="ownerPhone">
              <el-input
                v-model="form.ownerPhone"
                placeholder="管理者手机号"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>
          </template>
        </FuzzySearch>

        <!-- 高级筛选条件（单独拆分，只放次要条件） -->
        <div
          v-if="showAdvancedFilters"
          class="advanced-filters"
          style="margin-top: 10px"
        >
          <el-form :model="queryParams" :inline="true">
            <!-- 只保留2-3个次要条件作为高级筛选 -->
            <el-form-item label="设备类型" prop="deviceType">
              <el-input
                v-model="queryParams.deviceType"
                placeholder="设备类型"
                clearable
                class="!max-w-[150px]"
              />
            </el-form-item>

            <el-form-item label="登记时间" prop="createTime">
              <el-date-picker
                v-model="queryParams.createTime"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="!max-w-[250px]"
              />
            </el-form-item>
          </el-form>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <el-button
                  v-hasPerm="['system:assets:add']"
                  type="success"
                  class="ml-3"
                  @click="handleOpenDialog()"
                >
                  <i-ep-plus />
                  新增
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  :disabled="ids.length === 0"
                  class="ml-3"
                  @click="handleDelete()"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </div>
              <!-- 显示选择信息和操作按钮 -->
              <span v-if="ids.length > 0" class="selection-info">
                已选择
                <el-tag type="info">{{ ids.length }}</el-tag>
                项
                <el-button type="primary" link @click="clearSelection">
                  清除选择
                </el-button>
              </span>
              <div>
                <el-button class="ml-3" @click="handleOpenImportDialog">
                  <template #icon><i-ep-upload /></template>
                  导入
                </el-button>

                <el-button
                  class="ml-3"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  <template #icon><i-ep-download /></template>
                  导出{{ ids.length > 0 ? "选中" : "全部" }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            ref="dataTableRef"
            :default-sort="{ prop: 'id', order: 'descending' }"
            v-loading="loading"
            :data="pageData"
            highlight-current-row
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              align="center"
              key="id"
              label="序号"
              prop="id"
              min-width="90"
              show-overflow-tooltip
            />
            <el-table-column
              align="center"
              key="name"
              label="设备名称"
              prop="name"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              align="center"
              key="deviceType"
              label="设备类型"
              prop="deviceType"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="factory"
              label="制造商"
              prop="factory"
              min-width="100"
            />
            <el-table-column
              align="center"
              key="model"
              label="设备序列号"
              prop="model"
              min-width="140"
            />
            <el-table-column
              align="center"
              key="macAddress"
              label="MAC地址"
              prop="macAddress"
              min-width="150"
            />
            <el-table-column
              align="center"
              key="firmwareVersion"
              label="固件版本"
              prop="firmwareVersion"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="deptName"
              label="管理部门"
              prop="deptName"
              min-width="150"
            />
            <el-table-column
              align="center"
              key="managerName"
              label="管理员"
              prop="managerName"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="createTime"
              label="创建时间"
              prop="createTime"
              min-width="160"
            />
            <el-table-column
              align="center"
              key="status"
              label="设备状态"
              prop="status"
              min-width="100"
            >
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.status == '1'
                      ? 'success'
                      : scope.row.status == '2'
                        ? 'danger'
                        : 'info'
                  "
                >
                  {{ getAssetStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" label="探测状态" width="150">
              <template #default="scope">
                <asset-detection-status
                  :survival-status-list="scope.row.survivalStatusList"
                  :asset-name="scope.row.name"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="220"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleViewDetail(scope.row.id)"
                >
                  <el-icon class="text-sm">
                    <View />
                  </el-icon>
                  查看详情
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:edit']"
                  type="primary"
                  size="small"
                  link
                  @click="handleOpenDialog(scope.row.id)"
                >
                  <i-ep-edit />
                  编辑
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  size="small"
                  link
                  @click="handleDelete(scope.row.id)"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery()"
          />
        </el-card>
      </el-col>
    </el-row>
    <!-- 物联网设备表单弹窗 -->
    <iotDialog
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :id="dialog.id"
      @submitted="handleQuery"
    />

    <!-- 物联网设备导入弹窗 -->
    <iotAssetsImport
      v-model:visible="importDialogVisible"
      @import-success="handleOpenImportDialogSuccess"
    />

    <IotDetailView
      v-model:visible="detailDialog.visible"
      :asset-id="detailDialog.id"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "iotAssets",
  inheritAttrs: false,
});
import IotDetailView from "./components/IotDetailView.vue";
import iotAssetsImport from "./components/iot-assets-import.vue";
import assetsAPI, {
  assetsPageVO,
  assetsForm,
  assetsPageQuery,
} from "@/api/assets_management/details/assets";
import DeptAPI from "@/api/dept";
import iotDialog from "./components/iotdialog.vue";
import AssetDetectionStatus from "../components/assetDetectionStatus.vue";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const dataTableRef = ref();
const importDialogVisible = ref(false);

const loading = ref(false);
const exportLoading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const queryParams = reactive<assetsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: 4, // 物联网设备类型为4
  status: 1,
});

import { useDictStore } from "@/store/modules/dictStore";
const dictStore = useDictStore();

const pageData = ref<assetsPageVO[]>([]);
const deptOptions = ref<any>([]);

const dialog = reactive({
  title: "",
  visible: false,
  id: undefined as number | undefined,
});

const toggleDeptTree = () => {
  showDeptTree.value = !showDeptTree.value;
};

const formData = reactive<assetsForm>({
  url: "http://",
});

const detailDialog = reactive({
  visible: false,
  id: undefined as number | undefined,
});

function handleViewDetail(id?: number) {
  if (!id) return;
  detailDialog.id = id;
  detailDialog.visible = true;
}

const showAdvancedFilters = ref(false);
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}

const dictCache = reactive<{
  asset_status: any[];
  dept0x0: any[];
  system0x0: any[];
}>({
  asset_status: [],
  dept0x0: [],
  system0x0: [],
});

const deptMap = ref<Record<string | number, string>>({});
const systemMap = ref<Record<string | number, string>>({});
const statusMap = ref<Record<string | number, string>>({});

function handleQuery() {
  loading.value = true;
  assetsAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleResetQuery() {
  queryFormRef.value!.resetFields();
  Object.keys(queryParams).forEach((key) => {
    (queryParams as any)[key] = undefined;
  });
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.type = 4;

  handleQuery();
}

function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

function clearSelection() {
  ids.value = [];
  if (dataTableRef.value) {
    dataTableRef.value.clearSelection();
  }
}

async function handleOpenDialog(id?: number) {
  dialog.visible = true;
  dialog.id = undefined;
  dialog.title = id ? "修改物联网设备" : "新增物联网设备";
  nextTick(() => {
    dialog.id = id;
  });
}

function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      assetsAPI
        .deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

async function preloadDictData() {
  try {
    loading.value = true;

    const [statusOptions, deptOptions, systemOptions] = await Promise.all([
      dictStore.fetchOptions("asset_status"),
      dictStore.fetchOptions("dept0x0"),
      dictStore.fetchOptions("system0x0"),
    ]);

    dictCache.asset_status = statusOptions || [];
    dictCache.dept0x0 = deptOptions || [];
    dictCache.system0x0 = systemOptions || [];

    processOptions(dictCache.asset_status, statusMap.value);
    processDeptTree(dictCache.dept0x0);
    processOptions(dictCache.system0x0, systemMap.value);
  } catch (error) {
    console.error("获取字典数据失败:", error);
  } finally {
    loading.value = false;
  }
}

function processDeptTree(depts: any[]) {
  if (!Array.isArray(depts)) return;

  depts.forEach((dept) => {
    if (dept.value !== undefined && dept.label) {
      deptMap.value[dept.value] = dept.label;
      if (typeof dept.value === "number") {
        deptMap.value[String(dept.value)] = dept.label;
      }
    }

    if (dept.children && dept.children.length > 0) {
      processDeptTree(dept.children);
    }
  });
}

function processOptions(
  options: any[],
  targetMap: Record<string | number, string>
) {
  if (!Array.isArray(options)) return;

  options.forEach((option) => {
    if (option.value !== undefined && option.label) {
      targetMap[option.value] = option.label;
      if (typeof option.value === "number") {
        targetMap[String(option.value)] = option.label;
      }
    }
  });
}

function getAssetStatusName(status: string | number): string {
  const item = dictCache.asset_status.find((item) => item.value == status);
  return item ? item.label : `未知状态${status}`;
}

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  const exportParams: any = {
    ...queryParams,
  };

  if (ids.value.length > 0) {
    exportParams.assetIds = ids.value;
  }

  exportLoading.value = true;

  assetsAPI
    .exportIot(exportParams)
    .then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success(
        `成功导出${ids.value.length > 0 ? "选中" : "全部"}物联网设备`
      );
    })
    .catch((error) => {
      console.error("导出失败:", error);
      ElMessage.error("导出失败，请重试");
    })
    .finally(() => {
      handleResetQuery();
      exportLoading.value = false;
    });
}

onMounted(async () => {
  await preloadDictData();
  handleQuery();
});
</script>
