<template>
    <div class="view-notes-container">
      <el-button type="primary" link size="small" @click="openDialog">
        {{ buttonText }}
      </el-button>
  
      <el-dialog
        v-model="dialogVisible"
        :title="title"
        width="500px"
        destroy-on-close
        append-to-body
      >
        <div class="notes-content" v-if="content">
          {{ content }}
        </div>
        <div class="empty-content" v-else>
          <el-empty description="暂无内容" />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  
  defineOptions({
    name: 'ViewNotes',
  });
  
  const props = defineProps({
    content: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '功能描述',
    },
    buttonText: {
      type: String,
      default: '查看内容',
    },
  });
  
  const dialogVisible = ref(false);
  
  const openDialog = () => {
    dialogVisible.value = true;
  };
  </script>
  
  <style scoped>
  .view-notes-container {
    display: inline-flex;
    align-items: center;
  }
  
  .notes-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
  }
  
  .empty-content {
    padding: 20px 0;
  }
  </style>
