<template>
  <el-container class="sms-management">
    <el-header>
      <h1>短信管理系统</h1>
    </el-header>
    <el-main>
      <el-tabs v-model="activeTab" type="border-card">
        <el-tab-pane label="发送短信" name="sender">
          <SMSSender />
        </el-tab-pane>
        <el-tab-pane label="模板编辑" name="template">
          <SMSTemplateEditor />
        </el-tab-pane>
        <el-tab-pane label="发送记录" name="records">
          <SMSRecords />
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import SMSSender from './SMS/SMSSender.vue';
import SMSTemplateEditor from './SMS/SMSTemplateEditor.vue';
import SMSRecords from './SMS/SMSRecords.vue';

const activeTab = ref('sender');
</script>

<style scoped>
.sms-management {
  height: 100vh;
}

.el-header {
  background-color: #409EFF;
  color: white;
  text-align: center;
  line-height: 25px;
}

.el-main {
  padding: 20px;
}

.el-tabs {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
