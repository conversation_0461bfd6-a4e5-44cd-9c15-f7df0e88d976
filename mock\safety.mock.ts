// Date: 2024-8-19
// Creator: chleynx

import { defineMock } from "./base";
import { TicketInfo, TicketQuery, TicketPageVO, StepPermission, StepStatus, AuditData } from "@/api/work_management/safety/pocess";

const tickets: TicketInfo[] = [
  {
    id: "T20230819001",
    employeeId: "E001",
    applicant: "张三",
    department: "IT部门",
    phone: "123456789",
    systemId: "2",
    loopholeSource: "漏洞扫描",
    title: "漏洞修复",
    content: "修复系统漏洞",
    remarks: "紧急处理",
    status: "待处理",
    createdAt: "2023-08-19T10:00:00Z",
    updatedAt: "2023-08-19T10:00:00Z",
    vulnerabilityList: [
      {
        id: "V001",
        name: "SQL注入",
        level: "高",
        IP: "***********",
        URL: "http://example.com/vuln1",
        remark: "需要尽快修复",
        fix: "已修复",
        submitTime: "2023-08-19T09:00:00Z",
        fileList: ['http://www.example.com/file1']
      },
      // 其他漏洞信息...
    ]
  },
  // 其他工单数据...
];

const AuditResult = {
  id: "T20230819001",
  comments: "审核通过",
  result: "通过",  // 通过/不通过
}

const fixResult = {
  id: "T20230819001",
  comments: "整改成功",
  result: "已整改",  // 已整改/未整改
}

// 步骤状态
const stepStatus = { // 步骤状态 wait:待处理 process:处理中 finish:已完成 error:异常
  initiateTicket: 'finish',
  vulnerabilityAudit: 'finish',
  vulnerabilityFix: 'finish',
  fixVerification: 'finish',
  fixEvaluation: 'finish',
  closeTicket: 'process'
};

const stepPermission = {
  initiateTicket: true,
  vulnerabilityAudit: true,
  vulnerabilityFix: true,
  fixVerification: true,
  fixEvaluation: true,
  closeTicket: false
} // 步骤权限

export default defineMock([
  {
    url: "files",
    method: ["POST"],
    body: {
      code: "00000",
      data: {
        url: "http://www.baidu.com" // 确保 data 包含 url 字段
      },
      msg: "上传成功",
    },
  },
  {
    url: "workManagement/safety/tickets",
    method: ["GET"],
    body({ query }: { query: TicketQuery }) {
      const { pageNum, pageSize, ...filters } = query;
      let filteredTickets = tickets;

      Object.keys(filters).forEach((key) => {
        if (filters[key]) {
          filteredTickets = filteredTickets.filter((ticket) =>
            ticket[key].includes(filters[key])
          );
        }
      });

      const total = filteredTickets.length;
      const start = (pageNum - 1) * pageSize;
      const end = start + pageSize;
      const list = filteredTickets.slice(start, end);

      return {
        code: "00000",
        data: { total, list },
        msg: "一切ok",
      };
    },
  },
  {
    url: "workManagement/safety/tickets",
    method: ["POST"],
    body({ body }: { body: TicketInfo }) {
      const newTicket = { ...body, id: `T${Date.now()}`, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
      tickets.push(newTicket);
      return {
        code: "00000",
        data: newTicket,
        msg: "新增工单成功",
      };
    },
  },
  {
    url: "workManagement/safety/tickets/:id",
    method: ["GET"],
    body({ params }: { params: { id: string } }) {
      const ticket = tickets.find((ticket) => ticket.id == params.id);
      if (ticket) {
        return {
          code: "00000",
          data: ticket,
          msg: "一切ok",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id",
    method: ["PUT"],
    body({ params, body }: { params: { id: string }; body: Partial<TicketInfo> }) {
      const index = tickets.findIndex((ticket) => ticket.id == params.id);
      if (index !== -1) {
        tickets[index] = { ...tickets[index], ...body, updatedAt: new Date().toISOString() };
        return {
          code: "00000",
          data: tickets[index],
          msg: "更新工单成功",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id",
    method: ["DELETE"],
    body({ params }: { params: { id: string } }) {
      const index = tickets.findIndex((ticket) => ticket.id == params.id);
      if (index !== -1) {
        tickets.splice(index, 1);
        return {
          code: "00000",
          data: null,
          msg: "删除工单成功",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/stepPermission",
    method: ["GET"],
    body: {
      code: "00000",
      data: stepPermission,
      msg: "一切ok",
    },
  },
  {
    url: "workManagement/safety/tickets/:id/stepStatus",
    method: ["GET"],
    body({ params }: { params: { id: string } }) {
      if (stepStatus) {
        return {
          code: "00000",
          data: stepStatus,
          msg: "一切ok",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id/audit",
    method: ["POST"],
    body({ params, body }: { params: { id: string }; body: AuditData }) {
      const ticket = tickets.find((ticket) => ticket.id == params.id);
      if (ticket) {
        // 模拟审核逻辑
        stepStatus.vulnerabilityAudit="finish";
        ticket.updatedAt = new Date().toISOString();
        return {
          code: "00000",
          data: ticket,
          msg: "审核提交成功",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id/auditResult",
    method: ["GET"],
    body({ params }: { params: { id: string } }) {
      const ticket = tickets.find((ticket) => ticket.id == params.id);
      if (ticket) {
        return {
          code: "00000",
          data: AuditResult,
          msg: "一切ok",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id/fixVulnerability",
    method: ["POST"],
    body({ params, body }: { params: { id: string }; body: TicketInfo }) {
      const ticket = tickets.find((ticket) => ticket.id == params.id);
      if (ticket) {
        // 模拟修复漏洞逻辑
        stepStatus.vulnerabilityFix="finish";
        ticket.updatedAt = new Date().toISOString();
        return {
          code: "00000",
          data: ticket,
          msg: "整改结果成功",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  },
  {
    url: "workManagement/safety/tickets/:id/fixResult",
    method: ["GET"],
    body({ params }: { params: { id: string } }) {
      const ticket = tickets.find((ticket) => ticket.id == params.id);
      if (ticket) {
        return {
          code: "00000",
          data: fixResult,
          msg: "一切ok",
        };
      } else {
        return {
          code: "404",
          msg: "工单未找到",
        };
      }
    },
  }
  // 其他接口的模拟实现...
]);
