<template>
  <el-dialog
    v-model="dialogVisible"
    title="安全评估工单详情"
    width="90%"
    :close-on-click-modal="false"
    destroy-on-close
    class="ticket-detail-dialog"
  >
    <div class="ticket-detail-container">
    <!-- 基本信息卡片 -->
      <el-form :model="ticketData" label-width="140px" class="form-container">
        <el-card class="info-card mb-4">
                <template #header>
                  <div class="card-header">
                    <span class="header-title">工单基本信息</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="6">
                    <el-form-item label-width="90px" label="工单状态：" prop="status">
                      <el-input v-model="status" disabled >
                         <template v-slot:prepend>
                            {{formatStatus(ticketData.status)}}
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                   <el-col :span="6">
                    <el-form-item label-width="90px" label="当前阶段：" prop="step">
                      <el-input v-model="step" disabled >
                        <template v-slot:prepend>
                            {{getStepName(ticketData.step)}}
                        </template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                   <el-col :span="6">
                    <el-form-item label-width="110px" label="工单创建时间：" prop="createTime">
                      <el-input v-model="ticketData.createTime" disabled >
                      </el-input>
                    </el-form-item>
                  </el-col>
                   <el-col :span="6">
                    <el-form-item label-width="110px" label="工单截止时间：" prop="deadline">
                      <el-input v-model="ticketData.deadline" disabled >
                      </el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
        </el-card>
         <el-card class="info-card mb-4">
                <template #header>
                  <div class="card-header">
                    <span class="header-title">工单创建人信息</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="6">
                     <el-form-item label-width="60px" label="工号：" prop="employeeId">
                          <el-input v-model="ticketData.employeeId"  label="工号：" disabled >
                      </el-input>
                     </el-form-item>
                
                  </el-col>
                    <el-col :span="6">
                      <el-form-item label-width="80px" label="创建人：" prop="applicantName">
                         <el-input v-model="ticketData.applicantName"  disabled >
                      </el-input>
                      </el-form-item>
                    </el-col>
                     <el-col :span="6">
                      <el-form-item label-width="90px" label="所在单位：" prop="">
                         <el-input  disabled >
                           <template v-slot:prepend>
                              <Dictmap v-model="ticketData.deptId" code="dept0x0" />
                          </template>
                         </el-input>
                      </el-form-item>
                     </el-col>
                      <el-col :span="6">
                        <el-form-item label-width="90px" label="联系方式：" prop="mobile">
                          <el-input v-model="ticketData.mobile"   disabled >
                      </el-input>
                        </el-form-item>
                  </el-col>
                </el-row>
         </el-card>
        
         <el-card class="info-card mb-4">
              <template #header>
                  <div class="card-header">
                    <span class="header-title">被评估部门</span>
                  </div>
             </template>
                <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="责任部门：" >
                  <el-input
                      v-model="shstep.deptName"
                    disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="责任人：">
                    <el-input
                      v-model="shstep.userName"
                    disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="联系方式：" >
                    <el-input
                      v-model="shstep.userMobile"
                    disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
             </el-row>
         </el-card>
          <el-card class="info-card mb-4">
                <template #header>
                  <div class="card-header">
                    <span class="header-title">网络安全评估工程师</span>
                  </div>
                </template>
                <!-- 安全工程师信息开始 -->
                <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
                <div style="display:flex; justify-content: space-between;">
                      <el-form-item label-width="110px" label="工程师名称：" prop="engineerName" style="width:20%">
                      <el-input disabled v-model="SafetyEngineerbox.engineerName"></el-input>
                    </el-form-item>
                    <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
                      <el-input disabled  v-model="SafetyEngineerbox.engineerMobile"> </el-input>
                    </el-form-item>
                    <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
                      <el-input disabled v-model="SafetyEngineerbox.engineerWechat"></el-input>
                    </el-form-item>
                    <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
                      <el-input disabled v-model="SafetyEngineerbox.engineerQq"></el-input>
                    </el-form-item>
                    <el-form-item label="邮箱：" label-width="80px" prop="engineerEmail" style="width:20%">
                      <el-input disabled v-model="SafetyEngineerbox.engineerEmail"></el-input>
                    </el-form-item>
                </div>
                </el-form>
                <!-- <el-row :gutter="24">
                  <el-col :span="24" v-if="ticketData.evaluationObjectFile">
                        <el-form-item  class="fontw" label-width="140px" label="评估对象清单：">
                            <ul>
                              <li >
                                <span>{{ ticketData.evaluationObjectFile.name }}</span>
                                <el-button type="primary" style="margin-left:20px" @click="downloadFile(ticketData.evaluationObjectFile)">下载</el-button>
                              </li>
                            </ul>
                          </el-form-item>
                  </el-col>
                   <el-col :span="24" v-if="ticketData.commentsAssessment">
                        <el-form-item  class="fontw" label-width="140px" label="业务上线评估报告：">
                            <ul>
                              <li v-for="file in ticketData.commentsAssessment.fileList" :key="file.id">
                                <span>{{ file.name }}</span>
                                <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                              </li>
                            </ul>
                          </el-form-item>
                  </el-col>
                </el-row> -->
          </el-card>
            <el-card class="info-card mb-4">
                <template #header>
                  <div class="card-header">
                    <span class="header-title">评估对象</span>
                  </div>
                </template>
                <el-row :gutter="24">
                  <el-col :span="12">
                     <el-form-item label-width="110px" label="信息系统名称：" prop="sysname">
                       <el-input v-model="ticketData.sysname"   disabled >
                      </el-input>
                     </el-form-item>
                   
                  </el-col>
                    <el-col :span="12">
                      <el-form-item label-width="110px" label="域名/IP："  prop="domainIp">
                         <el-input v-model="ticketData.domainIp" disabled >
                      </el-input>
                      </el-form-item>
                   
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label-width="110px" label="事件描述："  prop="">
                        <el-input v-model="ticketData.reason"  label="事件描述：" disabled >
                      </el-input>
                      </el-form-item>
                    </el-col>
                     <el-col :span="24" v-if="ticketData.evaluationObjectFile">
                        <el-form-item  label-width="110px"  class="fontw" label="评估对象清单：">
                            <ul>
                              <li >
                                <span>{{ ticketData.evaluationObjectFile.name }}</span>
                                <el-button type="primary" style="margin-left:20px" @click="downloadFile(ticketData.evaluationObjectFile)">下载</el-button>
                              </li>
                            </ul>
                          </el-form-item>
                  </el-col>
                </el-row>
         </el-card>

          <el-card class="info-card mb-4" v-if="ticketData.commentsAssessment">
                <template #header>
                  <div class="card-header">
                    <span class="header-title">安全评估情况反馈</span>
                  </div>
                </template>
              <el-row :gutter="24">
          <el-col :span="24">
           <el-form-item  :label="typetext=='OnlineDetails'?`业务上线评估情况反馈：`:'安全评估情况反馈：'" label-width="170px">
              <el-input v-model="ticketData.commentsAssessment.commentContent" disabled type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12"  v-if="ticketData.commentsAssessment">
             <el-form-item  class="fontw" label-width="170px" :label="typetext=='OnlineDetails'?`业务上线评估报告：`:'安全评估报告：'">
                <ul  class="fileList_ul">
                  <li v-for="file in ticketData.commentsAssessment.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
          </el-col>
        </el-row>
         </el-card>
        <el-card class="info-card mb-4" v-if="ticketData.commentsReview && typetext=='evaluateDetails'">
          <template #header>
            <div class="card-header">
              <span class="header-title">安全问题复核反馈</span>
            </div>
          </template>
        <el-row :gutter="24" v-if="ticketData.commentsReview.commentContent">
          <el-col :span="24">
           <el-form-item label="安全问题复核情况反馈："  label-width="170px" >
              <el-input v-model="ticketData.commentsReview.commentContent" disabled type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="ticketData.commentsReview.fileList">
             <el-form-item  class="fontw" label-width="170px" label="安全问题复核报告：">
                <ul  class="fileList_ul">
                  <li v-for="file in ticketData.commentsReview.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      </el-form>
      <!-- 流转记录 -->
      <el-card class="flow-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单流转记录</span>
            <div class="header-actions">
              <el-switch
                v-model="flowViewMode"
                active-text="时间线"
                inactive-text="表格"
                inline-prompt
              />
            </div>
          </div>
        </template>
        <!-- 表格视图 -->
        <el-table v-if="!flowViewMode" :data="flowRecords" style="width: 100%" stripe border v-loading="flowLoading">
          <el-table-column prop="step" label="环节" width="120">
            <template #default="{ row }">
              <el-tag :type="getFlowTimelineType(row.step)" effect="plain">{{ row.step }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="处理人" width="180">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="24" :icon="UserFilled" class="user-avatar" />
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column label="执行时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.executeTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="result" label="处理结果" width="180">
            <template #default="{ row }">
              <el-tag v-if="row.result" :type="getResultTagType(row.result)" size="small">
                {{ row.result }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="处理意见" min-width="200" show-overflow-tooltip />
        </el-table>
        <!-- 时间线视图 -->
        <div v-else class="timeline-container" v-loading="flowLoading">
          <el-empty v-if="flowRecords.length === 0 && !flowLoading" description="暂无流转记录" />
          <el-timeline v-else>
            <el-timeline-item
              v-for="(flow, index) in flowRecords"
              :key="index"
              :type="getFlowTimelineType(flow.step)"
              :timestamp="formatDateTime(flow.executeTime || flow.startTime)"
              :hollow="index !== 0"
            >
              <h4>{{ flow.step }}</h4>
              <p class="timeline-content">
                <span class="timeline-user">处理人：
                  {{ flow.name }}
                </span>
                <span v-if="flow.result" class="timeline-result">
                  处理结果：
                  <el-tag :type="getResultTagType(flow.result)" size="small">{{ flow.result }}</el-tag>
                </span>
                <span v-if="flow.comment" class="timeline-msg">
                  处理意见：{{ flow.comment }}
                </span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
      
      <!-- 附件列表 -->
      <el-card class="attachment-card">
        <template #header>
          <div class="card-header">
            <span class="header-title">附件列表</span>
            <div class="header-actions">
              <el-tag>共 {{ attachments.length }} 个附件</el-tag>
            </div>
          </div>
        </template>
        <div v-if="attachments.length === 0" class="attachment-empty">
          <el-empty description="暂无附件" />
        </div>
        <div v-else class="attachment-list">
          <el-table :data="attachments" style="width: 100%">
            <el-table-column prop="linkName" label="环节名称" />
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="createByName" label="创建人名称" />
            <el-table-column prop="createTime" label="上传时间" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleProcess">处理工单</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import businessAPI from '@/api/work_management/online_service/index'
import safetyAPI from "@/api/work_management/safety";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  ticketId: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'navigateToProcess'])
import { useRouter, useRoute } from "vue-router";
const route = useRoute();
// 获取当前页面参数。判断是业务上线页面还是安全评估页面
const typetext = ref(route.query.type as string);
// 对话框控制
const dialogVisible = ref(false)
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
  if (val && props.ticketId) {
    loadTicketData()
  }
})

// 数据状态
const ticketData = reactive({
  id: undefined,
  name: '',
  status: '',
  ticketType: '',
  createTime: '',
  deadline: '',
  step: '',
  reason: ''
})

const assetsList = ref([])
const flowRecords = ref([])
const attachments = ref([])

// 加载状态
const loading = ref(false)
const flowLoading = ref(false)

// 视图控制
const flowViewMode = ref(false) // true=时间线, false=表格
const shstep=ref({})
// 加载工单详情
const loadTicketData = async () => {
  if (!props.ticketId) return
  
  loading.value = true
  try {
    // 获取工单基本信息
    const data = await businessAPI.getFormData(props.ticketId)
    Object.assign(ticketData, data)
     // 整改部门信息
    const foundItem = data.reviewProcessForms?.find(item => item.executeDeptType ==4);
    shstep.value=foundItem
    assetsList.value = data.assetsList;
    // 获取附件列表
    try {
      const fileList = await businessAPI.getFileList(props.ticketId);
      if (fileList) {
        attachments.value = fileList;
      }
    } catch (err) {
      console.error('获取附件失败:', err);
    }

    // 加载流转信息
    await loadFlowRecords()
    
  } catch (error) {
    console.error('加载工单详情失败:', error)
    ElMessage.error('加载工单详情失败')
  } finally {
    loading.value = false
  }
}

// 定义流转记录类型
interface FlowRecord {
  step: string;
  name: string;
  startTime: string | null;
  executeTime: string | null;
  result: string;
  comment: string;
  index: number;
}

// 加载流转记录
const loadFlowRecords = async () => {
  if (!props.ticketId) return;
  
  flowLoading.value = true;
  try {
    // 使用流转列表接口
    const res = await businessAPI.getFlow(props.ticketId);
    console.log('流转记录:', res);
    if (res) {
      // 处理流转记录数据
      flowRecords.value = res.map((item, index) => {
        return {
          step: item.name || '-',
          name: item.userName || '-',
          startTime: item.executeStartTime || null,
          executeTime: item.executeTime || null,
          result: item.processingResults || '',
          comment: item.commentContent || item.msg || '',
          index
        };
      });
    }
  } catch (error) {
    console.error('加载流转记录失败:', error);
  } finally {
    flowLoading.value = false;
  }
}

// 处理工单 - 跳转到处理流程
const handleProcess = () => {
  emit('navigateToProcess', props.ticketId)
  dialogVisible.value = false
}

// 下载文件
const downloadFile = (file) => {
  if (!file.url) {
    ElMessage.warning('文件地址不存在')
    return
  }
  try {
    fetch(file.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = file.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  });
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 格式化时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'
  
  try {
    return dateTimeStr.replace('T', ' ').substring(0, 19)
  } catch (error) {
    return dateTimeStr || '-'
  }
}

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    "0":'待处理',
    "1":"待处理",
    "2":"待审核",
    "3":"已完成",
    "4":"未完成",
  }
  return statusMap[status] || '已关闭'
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (!size) return '未知大小'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let formattedSize = size
  let unitIndex = 0
  
  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024
    unitIndex++
  }
  
  return `${formattedSize.toFixed(2)} ${units[unitIndex]}`
}

// 获取工单类型
const getTicketTypeName = (type: string) => {
  const typeMap: Record<string, string> = {
    'emergency': '应急处置',
    'business': '业务上线',
    'security': '安全评估'
  }
  return typeMap[type] || '未知类型'
}

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    '0': 'warning', // 处理中
    '1': 'success', // 已完成
    '2': 'info'     // 关闭
  }
  return statusMap[status] || 'info'
}

// 获取步骤标签类型
const getStepTagType = (step) => {
  const stepMap = {
    '1': 'primary',
    '2': 'success',
    '3': 'warning',
    '4': 'info',
    '5': 'warning',
    '6': 'danger',
    '7': 'success'
  }
  return stepMap[step] || 'info'
}
// 获取步骤标签类型
const getStepName = (step) => {
  const stepNameMap = {
    '1': '发起工单',
    '2': '工单评估',
    '3': '工单审核',
    '4': '工单整改',
    '5': '工单复核',
    '6': '工单评价',
    '7': '关闭工单'
  }
  return stepNameMap[step] || ''
}

// 获取资产类型
const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 获取流转记录时间线类型
const getFlowTimelineType = (step) => {
  const typeMap = {
    '发起工单': 'primary',
    '工单审核': 'success',
    '安全评估': 'warning', 
    '工单整改': 'info',
    '工单复核': 'warning',
    '工单评价': 'danger',
    '完成工单': 'success'
  };
  
  return typeMap[step] || 'info'
}

// 获取处理结果标签类型
const getResultTagType = (result) => {
  const resultMap = {
    '通过': 'success',
    '不通过': 'danger',
    '已整改': 'success',
    '未整改': 'warning',
    '已关闭': 'info',
    '满意': 'success',
    '不满意': 'danger'
  }
  
  return resultMap[result] || 'info'
}
// 获取安全工程师信息
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}
// 组件挂载时加载数据
onMounted(() => {
  if (props.visible && props.ticketId) {
    loadTicketData()
  }
  SafetyEngineerConfig()
})
</script>

<style scoped>
.ticket-detail-container {
  padding: 0 10px;
  height: 70vh;
  overflow: auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.content-block,
.remarks-block {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  white-space: pre-wrap;
  min-height: 60px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #ecf5ff;
  color: #409EFF;
}

.timeline-container {
  padding: 10px 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
}

.timeline-result {
  margin: 5px 0;
}

.timeline-msg {
  color: #606266;
  white-space: pre-wrap;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  width: 300px;
  transition: all 0.3s;
}

.attachment-item:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.attachment-icon {
  font-size: 24px;
  color: #409EFF;
}

.attachment-info {
  flex: 1;
  overflow: hidden;
}

.attachment-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.attachment-size {
  font-size: 12px;
  color: #909399;
}

.attachment-empty {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
:deep(.el-form-item) {
  margin-right: 0; 
}
</style>
