<template>
  <!-- 关联用户弹窗 -->
  <el-dialog 
    :model-value="visible" 
    title="用户清单" 
    width="80%" 
    append-to-body 
    @close="handleClose"
    @update:model-value="updateVisible"
  >
    <div class="drawer-header">
      <div class="flex-x-between">
        <div>
          <el-button type="primary" @click="handleEditAssets">编辑用户</el-button>
        </div>
        <div>
          <el-button type="success" @click="handleExportUsers">
            <template #icon><i-ep-download /></template>
            导出用户
          </el-button>
        </div>
      </div>
    </div>
    
    <el-table :data="reinsuranceEvents" border v-loading="loading">
      <el-table-column label="序号" align="center" width="70">
        <template #default="{ $index }">
          {{ (queryParams.pageNum - 1) * queryParams.pageSize + $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="deptName" label="部门" align="center" />
      <el-table-column prop="username" label="用户名" align="center" />
      <el-table-column prop="mobile" label="手机号" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
            {{ scope.row.status == 1 ? '正常' : '异常' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination 
      v-if="total > 0" 
      v-model:total="total" 
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" 
      @pagination="fetchReinsuranceEvents" 
    />
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 穿梭框对话框 -->
    <el-dialog 
      v-model="transferDialog.visible" 
      title="选择用户" 
      width="1000px"
      append-to-body
    >
      <el-transfer 
        class="custom-transfer" 
        v-model="transferDialog.selectedAssets" 
        :data="transferDialog.allAssets"
        :titles="['可选用户', '已选用户']" 
        :props="{
          key: 'id',
          label: 'name'
        }" 
        :filter-method="filterMethod" 
        filterable
      >
        <template #default="{ option }">
          <span>{{ option.nickname }} ({{ option.name }}) - ({{ option.dept }})</span>
        </template>
      </el-transfer>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="transferDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmTransfer">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parameter_setAPI from "@/api/parameter_set";
import UserAPI from "@/api/user";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  groupId: {
    type: Number,
    default: null,
  },
  selectedUserIds: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:visible', 'refresh']);

const loading = ref(false);
const total = ref(0);
const reinsuranceEvents = ref<any>([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  id: undefined as number | undefined,
});

// 穿梭框对话框
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[],
  selectedAssets: [] as number[],
});

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    openDialog();
  }
});

// 监听groupId变化
watch(() => props.groupId, (newVal) => {
  if (newVal) {
    queryParams.id = newVal;
    // 解析已选用户ID
    transferDialog.selectedAssets = props.selectedUserIds 
      ? props.selectedUserIds.split(',').map(Number).filter(id => !isNaN(id))
      : [];
  }
});

function openDialog() {
  if (props.groupId) {
    queryParams.id = props.groupId;
    queryParams.pageNum = 1;
    transferDialog.selectedAssets = props.selectedUserIds 
      ? props.selectedUserIds.split(',').map(Number).filter(id => !isNaN(id))
      : [];
    fetchReinsuranceEvents();
  }
}

// 获取关联用户列表
function fetchReinsuranceEvents() {
  if (!queryParams.id) return;
  
  loading.value = true;
  parameter_setAPI.getList(queryParams)
    .then((data) => {
      reinsuranceEvents.value = data.list || [];
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 编辑用户按钮
function handleEditAssets() {
  if (!props.groupId) {
    ElMessage.error("未找到用户组ID");
    return;
  }
  
  transferDialog.visible = true;
  
  // 获取所有用户
  const querys = {
    pageNum: 1,
    pageSize: 1000,
  };
  
  UserAPI.getPage(querys)
    .then((data) => {
      transferDialog.allAssets = data.list.map(asset => ({
        id: asset.id,
        name: asset.username,
        dept: asset.deptName,
        nickname: asset.nickname,
        mobile: asset.mobile,
      }));
    })
    .catch((error) => {
      ElMessage.error("获取用户失败：" + error.message);
    });
}

// 导出用户功能
function handleExportUsers() {
  if (!props.groupId) {
    ElMessage.error("未找到用户组ID");
    return;
  }

  ElMessageBox.confirm("确认导出当前用户组的所有用户数据?", "导出确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "info",
  })
  .then(() => {
    const exportParams = {
      pageNum: 1,
      pageSize: 10000, 
      id: props.groupId,
    };

    parameter_setAPI.export(exportParams)
      .then((response: any) => {
        // 处理文件下载
        const fileData = response.data;
        let fileName = "用户组用户清单.xlsx"; 
        
        // 尝试从响应头获取文件名
        try {
          const contentDisposition = response.headers["content-disposition"];
          if (contentDisposition) {
            const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (fileNameMatch && fileNameMatch[1]) {
              fileName = decodeURI(fileNameMatch[1].replace(/['"]/g, ''));
            }
          }
        } catch (error) {
          console.warn("无法解析文件名，使用默认文件名");
        }

        const fileType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";
        const blob = new Blob([fileData], { type: fileType });
        const downloadUrl = window.URL.createObjectURL(blob);

        const downloadLink = document.createElement("a");
        downloadLink.href = downloadUrl;
        downloadLink.download = fileName;

        document.body.appendChild(downloadLink);
        downloadLink.click();

        document.body.removeChild(downloadLink);
        window.URL.revokeObjectURL(downloadUrl);

        ElMessage.success("用户数据导出成功");
      })
      .catch((error) => {
        console.error("导出失败:", error);
      });
  })
  .catch(() => {
    ElMessage.info("已取消导出");
  });
}

// 确认转移用户
function handleConfirmTransfer() {
  ElMessageBox.confirm("确认将选中的用户加入此用户组?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
  .then(() => {
    if (!props.groupId) {
      ElMessage.error("未找到用户组ID");
      return;
    }
    
    parameter_setAPI.setPeople(props.groupId, transferDialog.selectedAssets)
      .then(() => {
        ElMessage.success("成功将用户加入用户组");
        transferDialog.visible = false;
        fetchReinsuranceEvents(); 
        emits('refresh'); 
      })
      .catch((error) => {
        ElMessage.error("添加失败：" + error.message);
      });
  })
  .catch(() => {
    ElMessage.info("已取消操作");
  });
}

// 穿梭框过滤方法
function filterMethod(query: string, item: any) {
  return item.nickname.indexOf(query) > -1 || item.name.indexOf(query) > -1;
}

function handleClose() {
  emits('update:visible', false);
  resetDialog();
}

function updateVisible(value: boolean) {
  emits('update:visible', value);
}

function resetDialog() {
  queryParams.pageNum = 1;
  queryParams.id = undefined;
  reinsuranceEvents.value = [];
  total.value = 0;
  transferDialog.visible = false;
  transferDialog.selectedAssets = [];
  transferDialog.allAssets = [];
}
</script>

<style scoped>
.drawer-header {
  margin-bottom: 16px;
}

.flex-x-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.custom-transfer {
  width: 100%;
  height: 60vh;
}

.custom-transfer :deep(.el-transfer-panel) {
  width: 40% !important;
  height: 100%;
  min-width: 300px;
}

.custom-transfer :deep(.el-transfer-panel .el-transfer-panel__body) {
  height: calc(100% - 2px);
  overflow-y: auto;
}

.custom-transfer .el-transfer__buttons {
  width: 10%;
}

.custom-transfer .el-transfer__body {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}
</style>
