// 任务状态枚举类型
export type TaskStatus =
  | "not_started"
  | "in_progress"
  | "completed"
  | "overdue";

// 任务类型枚举
export type TaskType = "task" | "milestone" | "taskFolder";

// 任务类型层级约束配置
export interface TaskTypeConfig {
  type: TaskType;
  label: string;
  icon: string;
  canHaveChildren: boolean;
  allowedParentTypes: TaskType[];
  allowedChildTypes: TaskType[];
  canBeTopLevel: boolean;
}

// 任务类型配置映射
export const TASK_TYPE_CONFIGS: Record<TaskType, TaskTypeConfig> = {
  milestone: {
    type: "milestone",
    label: "里程碑",
    icon: "milestone",
    canHaveChildren: true,
    allowedParentTypes: [], // 里程碑不能有父节点
    allowedChildTypes: ["taskFolder", "task"],
    canBeTopLevel: true,
  },
  taskFolder: {
    type: "taskFolder",
    label: "任务文件夹",
    icon: "folder",
    canHaveChildren: true,
    allowedParentTypes: ["milestone", "taskFolder"],
    allowedChildTypes: ["taskFolder", "task"],
    canBeTopLevel: false, // 任务文件夹不能作为顶层
  },
  task: {
    type: "task",
    label: "任务",
    icon: "document",
    canHaveChildren: false, // 任务不能有子项
    allowedParentTypes: ["milestone", "taskFolder"],
    allowedChildTypes: [],
    canBeTopLevel: false, // 任务不能作为顶层
  },
};

// 任务优先级
export type TaskPriority = "low" | "medium" | "high" | "urgent";

// 项目状态
export type ProjectStatus =
  | "planning"
  | "active"
  | "completed"
  | "on_hold"
  | "cancelled";

// 上传后的文件信息
export interface UploadedFile {
  id: number; // 文件ID
  name: string; // 文件名称
  url: string; // 文件URL
  size?: number; // 文件大小(字节)
  uploadedAt: string; // 上传时间
  uploadedBy: number; // 上传人ID
}

// 任务数据类型（用于展示，包括子任务、文件等）
export interface TaskRow {
  id: number; // 任务ID
  title: string; // 任务标题
  type: TaskType; // 任务类型（taskFolder/task/milestone）
  priority: TaskPriority; // 任务优先级
  start: string; // 开始时间
  end: string; // 结束时间
  finish?: string | null; // 完成时间
  parentId?: number | null; // 父任务ID
  members?: number[]; // 成员ID列表
  children?: TaskRow[]; // 子任务（树结构）
  projectId: number; // 所属项目ID（必选）
  status: TaskStatus; // 任务状态（明确类型）
  owner: string; // 任务负责人（必选）
  canUpload: boolean; // 是否允许上传文件（必选）
  files?: UploadedFile[]; // 关联文件列表
  description?: string; // 任务描述（可选）
}

// 层级验证工具函数
export class TaskHierarchyValidator {
  /**
   * 验证是否可以创建指定类型的子项
   */
  static canCreateChild(parentType: TaskType, childType: TaskType): boolean {
    const parentConfig = TASK_TYPE_CONFIGS[parentType];
    return parentConfig.allowedChildTypes.includes(childType);
  }

  /**
   * 验证是否可以作为顶层节点
   */
  static canBeTopLevel(type: TaskType): boolean {
    return TASK_TYPE_CONFIGS[type].canBeTopLevel;
  }

  /**
   * 验证是否可以设置指定的父节点
   */
  static canHaveParent(childType: TaskType, parentType: TaskType): boolean {
    const childConfig = TASK_TYPE_CONFIGS[childType];
    return childConfig.allowedParentTypes.includes(parentType);
  }

  /**
   * 获取指定类型可以创建的子类型列表
   */
  static getAllowedChildTypes(parentType: TaskType): TaskType[] {
    return TASK_TYPE_CONFIGS[parentType].allowedChildTypes;
  }

  /**
   * 获取指定类型的配置信息
   */
  static getTypeConfig(type: TaskType): TaskTypeConfig {
    return TASK_TYPE_CONFIGS[type];
  }

  /**
   * 验证任务层级结构是否合法
   */
  static validateTaskHierarchy(
    task: TaskRow,
    allTasks: TaskRow[]
  ): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const config = TASK_TYPE_CONFIGS[task.type];

    // 检查是否可以作为顶层
    if (!task.parentId && !config.canBeTopLevel) {
      errors.push(`${config.label}不能作为顶层节点`);
    }

    // 检查父子关系
    if (task.parentId) {
      const parent = allTasks.find((t) => t.id === task.parentId);
      if (parent && !this.canCreateChild(parent.type, task.type)) {
        errors.push(
          `${TASK_TYPE_CONFIGS[parent.type].label}不能包含${config.label}`
        );
      }
    }

    // 检查是否有不允许的子项
    if (task.children && task.children.length > 0 && !config.canHaveChildren) {
      errors.push(`${config.label}不能包含子项`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 任务表单类型（用于新增/编辑任务）
export interface TaskForm {
  id?: number; // 任务ID（编辑时用，新增可不传）
  title: string; // 任务标题
  type: TaskType; // 任务类型
  priority: TaskPriority; // 任务优先级
  start: string; // 开始时间
  end: string; // 结束时间
  finish?: string | null; // 完成时间
  parentId?: number | null; // 父任务ID
  members?: number[]; // 成员ID列表
  projectId: number; // 所属项目ID（必选）
  status: TaskStatus; // 任务状态
  owner: string; // 任务负责人（必选）
  canUpload: boolean; // 是否允许上传文件（必选）
  description?: string; // 任务描述（新增字段，可选）
}

// 项目信息类型
export interface Project {
  id: number; // 项目ID（必选）
  name: string; // 项目名称（必选）
  owner: string; // 项目负责人（必选）
  startDate: string | null; // 项目开始时间
  endDate: string | null; // 项目结束时间
  status: ProjectStatus; // 项目状态（明确类型，必选）
  tasks?: TaskRow[]; // 可选：关联的任务列表

  // 项目概览相关字段
  stats?: ProjectStat[]; // 项目统计数据
  milestones?: Milestone[]; // 项目里程碑
  statusChartData?: StatusChartItem[]; // 任务状态分布数据
  divisionChartData?: DivisionChartItem[]; // 任务分工数据
}

// 项目表单类型（用于新增/编辑项目）
export interface ProjectForm {
  name: string; // 项目名称（必选）
  owner: string; // 项目负责人（必选）
  startDate: string | null; // 项目开始时间
  endDate: string | null; // 项目结束时间
  status: ProjectStatus; // 项目状态（必选）
}

// 项目统计项类型
export interface ProjectStat {
  label: string; // 统计项标签
  value: number; // 统计值
  color: "blue" | "orange" | "red" | "green"; // 颜色标识
}

// 里程碑类型
export interface Milestone {
  id: number; // 里程碑ID（必选）
  label: string; // 里程碑名称（必选）
  date: string; // 日期（必选）
  completed: boolean; // 是否已完成（必选）
  sortOrder: number; // 排序顺序（必选）
  isCurrent?: boolean; // 是否为当前里程碑
  isToday?: boolean; // 是否为今天
  projectId: number; // 所属项目ID（必选）
}

// 状态图表数据项
export interface StatusChartItem {
  name: string; // 状态名称
  value: number; // 数量
}

// 分工图表数据项
export interface DivisionChartItem {
  name: string; // 人员名称
  value: number; // 任务数量
}

// 用户信息类型
export interface User {
  id: number; // 用户ID（必选）
  username: string; // 用户姓名（必选）
  avatar?: string; // 头像URL（可选）
  role?: string; // 角色（可选）
  email?: string; // 邮箱（可选）
}

// 文件上传参数
export interface UploadFileParams {
  taskId: number; // 任务ID（必选）
  files: File[]; // 上传的文件列表（必选）
}

// 项目概览数据接口
export interface ProjectOverviewData {
  stats: ProjectStat[];
  milestones: Milestone[];
  statusChartData: StatusChartItem[];
  divisionChartData: DivisionChartItem[];
}

// 任务统计数据类型
export interface TaskStats {
  totalTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  completedTasks: number;
  projectTaskCounts: {
    projectId: number;
    projectName: string;
    taskCount: number;
  }[];
  yearlyTaskCounts: { year: number; taskCount: number }[];
}
export interface ApprovalFile {
  fileId: string;
  name: string;
  url: string;
}
