<!-- 资产管理 -->
<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-blue-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-blue-500 text-3xl"><Tickets /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">
                安全设备总数
              </span>
              <div class="text-4xl font-bold text-gray-900 mt-1">128</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">同比增长 5.2%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-green-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-green-500 text-3xl"><Check /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">正常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">105</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">占比 82.03%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-yellow-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-yellow-500 text-3xl"><Warning /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">部分存活</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">17</div>
            </div>
          </div>
          <div class="text-yellow-600 text-sm flex justify-start items-center">
            <el-icon><Bottom /></el-icon>
            <span class="ml-1">占比 13.28%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-red-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-red-500 text-3xl"><Close /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">异常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">6</div>
            </div>
          </div>
          <div class="text-red-600 text-sm flex justify-start items-center">
            <el-icon><Bell /></el-icon>
            <span class="ml-1">需要立即处理</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree
          v-model="queryParams.deptId"
          @node-click="handleQuery"
          class="mb-2"
        />
        <system-tree
          v-model="queryParams.systemId"
          @system-click="handleQuery"
        />
      </el-col>

      <!-- 资产列表 -->
      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <FuzzySearch
          v-model="queryParams"
          placeholder="搜索设备名称、管理员..."
          search-field="keyword"
          @search="handleQuery"
          @reset="handleResetQuery"
        >
          <template #filters="{ form }">
            <!-- 基础筛选条件 -->
            <el-form-item label="设备名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="设备名称"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>

            <el-form-item label="管理员" prop="ownerName">
              <el-input
                v-model="form.ownerName"
                placeholder="资产管理员"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="ownerPhone">
              <el-input
                v-model="form.ownerPhone"
                placeholder="资产管理者手机号"
                clearable
                class="!max-w-[120px]"
              />
            </el-form-item>

            <!-- 高级筛选条件 - 通过v-if控制显示 -->
            <el-form-item v-if="showAdvancedFilters" label="资产ip" prop="ip">
              <el-input v-model="form.ip" placeholder="资产ip" clearable />
            </el-form-item>

            <el-form-item v-if="showAdvancedFilters" label="端口" prop="port">
              <el-input v-model="form.port" placeholder="资产端口" clearable />
            </el-form-item>

            <el-form-item
              v-if="showAdvancedFilters"
              label="登记时间"
              prop="createTime"
            >
              <el-date-picker
                v-model="form.createTime"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item v-if="showAdvancedFilters" label="操作系统" prop="os">
              <el-select
                v-model="form.os"
                placeholder="操作系统"
                clearable
                class="!w-[100px]"
              >
                <el-option
                  v-for="item in dictCache.os"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </FuzzySearch>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <el-button
                  v-hasPerm="['system:assets:add']"
                  type="success"
                  class="ml-3"
                  @click="handleOpenDialog()"
                >
                  <i-ep-plus />
                  新增
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  :disabled="ids.length === 0"
                  class="ml-3"
                  @click="handleDelete()"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </div>
              <!-- 显示选择信息和操作按钮 -->
              <span v-if="ids.length > 0" class="selection-info">
                已选择
                <el-tag type="info">{{ ids.length }}</el-tag>
                项
                <el-button type="primary" link @click="clearSelection">
                  清除选择
                </el-button>
              </span>
              <div>
                <el-button class="ml-3" @click="handleOpenImportDialog">
                  <template #icon><i-ep-upload /></template>
                  导入
                </el-button>

                <el-button
                  class="ml-3"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  <template #icon><i-ep-download /></template>
                  导出{{ ids.length > 0 ? "选中" : "全部" }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            ref="dataTableRef"
            :default-sort="{ prop: 'id', order: 'descending' }"
            v-loading="loading"
            :data="pageData"
            highlight-current-row
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              align="center"
              key="id"
              label="序号"
              prop="id"
              min-width="90"
              show-overflow-tooltip
            />
            <el-table-column
              align="center"
              key="name"
              label="设备名称"
              prop="name"
              min-width="200"
              show-overflow-tooltip
            />
            <el-table-column
              align="center"
              key="ip"
              label="设备ip"
              prop="ip"
              min-width="140"
              show-overflow-tooltip
            />
            <el-table-column
              align="center"
              key="factory"
              label="设备厂家"
              prop="factory"
              min-width="100"
            />
            <el-table-column
              align="center"
              key="model"
              label="设备型号"
              prop="model"
              min-width="100"
            />
            <el-table-column
              align="center"
              key="notes"
              label="功能简述"
              prop="notes"
              min-width="100"
            >
              <template #default="scope">
                <div class="flex items-center">
                  <span class="truncate max-w-[80px]" :title="scope.row.notes">
                    {{ scope.row.notes || "-" }}
                  </span>
                  <ViewNotes
                    v-if="scope.row.notes"
                    :content="scope.row.notes"
                    :title="`${scope.row.name || '设备'}功能描述`"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              key="deptName"
              label="管理部门"
              prop="deptName"
              min-width="150"
            />
            <el-table-column
              align="center"
              key="managerName"
              label="管理员"
              prop="managerName"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="managerContact"
              label="联系方式"
              prop="managerContact"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="createTime"
              label="创建时间"
              prop="createTime"
              min-width="160"
            />
            <el-table-column
              align="center"
              key="status"
              label="资产状态"
              prop="status"
              min-width="100"
            >
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.status == '1'
                      ? 'success'
                      : scope.row.status == '2'
                        ? 'danger'
                        : 'info'
                  "
                >
                  {{ getAssetStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" label="探测状态" width="150">
              <template #default="scope">
                <asset-detection-status
                  :survival-status-list="scope.row.survivalStatusList"
                  :asset-name="scope.row.name"
                />
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="220"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleViewDetail(scope.row.id)"
                >
                  <el-icon class="text-sm">
                    <View />
                  </el-icon>
                  查看详情
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:edit']"
                  type="primary"
                  size="small"
                  link
                  @click="handleOpenDialog(scope.row.id)"
                >
                  <i-ep-edit />
                  编辑
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  size="small"
                  link
                  @click="handleDelete(scope.row.id)"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery()"
          />
        </el-card>
      </el-col>
    </el-row>
    <!-- 资产管理表单弹窗 -->
    <safetyDialog
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :id="dialog.id"
      @submitted="handleQuery"
    />

    <!-- 安全设备导入弹窗 -->
    <safetyAssetsImport
      v-model:visible="importDialogVisible"
      @import-success="handleOpenImportDialogSuccess"
    />

    <SafetyDetailView
      v-model:visible="detailDialog.visible"
      :asset-id="detailDialog.id"
    />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "assets",
  inheritAttrs: false,
});
import SafetyDetailView from "./components/SafetyDetailView.vue";
import safetyAssetsImport from "./components/safety-assets-import.vue";
import assetsAPI, {
  assetsPageVO,
  assetsForm,
  assetsPageQuery,
} from "@/api/assets_management/details/assets";
import eventsAPI, {
  eventsPageVO,
  eventsForm,
  eventsPageQuery,
} from "@/api/work_management/critical";
import DeptAPI from "@/api/dept";
import safetyDialog from "./components/safetydialog.vue";
import AssetDetectionStatus from "../components/assetDetectionStatus.vue";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const dataTableRef = ref(); // 添加表格引用
const importDialogVisible = ref(false);

const loading = ref(false);
const exportLoading = ref(false); // 添加导出加载状态
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const queryParams = reactive<assetsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: 3,
  status: 1,
});
import { useDictStore } from "@/store/modules/dictStore";
const dictStore = useDictStore();
// 资产管理表格数据
const pageData = ref<assetsPageVO[]>([]);
const deptOptions = ref<any>([]);
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined as number | undefined,
});
// 部门树显示状态
const toggleDeptTree = () => {
  showDeptTree.value = !showDeptTree.value;
};
// 资产管理表单数据
const formData = reactive<assetsForm>({
  url: "http://",
});
const detailDialog = reactive({
  visible: false,
  id: undefined as number | undefined,
});

// 添加查看详情的处理函数
function handleViewDetail(id?: number) {
  if (!id) return;
  detailDialog.id = id;
  detailDialog.visible = true;
}
// 搜索处理方法
function handleSearch(params: Record<string, any>) {
  console.log("搜索参数:", params);

  // // 处理模糊搜索逻辑
  // if (params.keyword) {
  //   // 如果有关键词，可以同时搜索名称、IP和管理员
  //   queryParams.name = params.keyword;
  //   queryParams.ip = params.keyword;
  //   queryParams.ownerName = params.keyword;
  // }

  // 更新其他查询参数
  Object.assign(queryParams, params);
  // queryParams.pageNum = 1; // 重置页码
  handleQuery();
}

const showAdvancedFilters = ref(false);
// 切换高级筛选
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}

const dictCache = reactive<{
  asset_status: any[];
  os: any[];
  dept0x0: any[];
  system0x0: any[];
}>({
  asset_status: [],
  os: [],
  dept0x0: [],
  system0x0: [],
});

// 4. 添加映射缓存对象
const deptMap = ref<Record<string | number, string>>({});
const systemMap = ref<Record<string | number, string>>({});
const statusMap = ref<Record<string | number, string>>({});
const osMap = ref<Record<string | number, string>>({});

/** 检测存活 */
function handleCheck(row?: any) {
  ElMessageBox.confirm("确认检测存活?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    loading.value = true;
    // assetsAPI.check(id || ids.value)
    //   .then(() => {
    //     ElMessage.success("检测成功");
    //     handleQuery();
    //   })
    console.log(row);
    assetsAPI
      .update(row.id, row)
      .then(() => {
        ElMessage.success("设置检测成功");
        handleQuery();
      })
      .finally(() => (loading.value = false));
  });
}

/** 查询资产管理 */
function handleQuery() {
  loading.value = true;
  assetsAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置资产管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  Object.keys(queryParams).forEach((key) => {
    (queryParams as any)[key] = undefined;
  });
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.type = 3;
  selectedEvent.value = undefined;

  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 清除所有选择 */
function clearSelection() {
  ids.value = [];
  // 清除表格选择状态
  if (dataTableRef.value) {
    dataTableRef.value.clearSelection();
  }
}

async function handleOpenDialog(id?: number) {
  dialog.visible = true;
  dialog.id = undefined;
  dialog.title = id ? "修改安全设备" : "新增安全设备";
  nextTick(() => {
    dialog.id = id;
  });
}

/** 删除资产管理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      assetsAPI
        .deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

async function preloadDictData() {
  try {
    loading.value = true;

    // 并行加载所有需要的字典数据
    const [statusOptions, osOptions, deptOptions, systemOptions] =
      await Promise.all([
        dictStore.fetchOptions("asset_status"),
        dictStore.fetchOptions("os"),
        dictStore.fetchOptions("dept0x0"),
        dictStore.fetchOptions("system0x0"),
      ]);

    // 更新缓存
    dictCache.asset_status = statusOptions || [];
    dictCache.os = osOptions || [];
    dictCache.dept0x0 = deptOptions || [];
    dictCache.system0x0 = systemOptions || [];

    // 处理映射数据
    processOptions(dictCache.asset_status, statusMap.value);
    processOptions(dictCache.os, osMap.value);
    processDeptTree(dictCache.dept0x0);
    processOptions(dictCache.system0x0, systemMap.value);
  } catch (error) {
    console.error("获取字典数据失败:", error);
  } finally {
    loading.value = false;
  }
}

// 6. 添加处理函数
function processDeptTree(depts: any[]) {
  if (!Array.isArray(depts)) return;

  depts.forEach((dept) => {
    if (dept.value !== undefined && dept.label) {
      deptMap.value[dept.value] = dept.label;
      // 同时存储字符串形式的键
      if (typeof dept.value === "number") {
        deptMap.value[String(dept.value)] = dept.label;
      }
    }

    if (dept.children && dept.children.length > 0) {
      processDeptTree(dept.children);
    }
  });
}

function processOptions(
  options: any[],
  targetMap: Record<string | number, string>
) {
  if (!Array.isArray(options)) return;

  options.forEach((option) => {
    if (option.value !== undefined && option.label) {
      targetMap[option.value] = option.label;
      // 同时存储字符串形式的键
      if (typeof option.value === "number") {
        targetMap[String(option.value)] = option.label;
      }
    }
  });
}

// 资产状态名称
function getAssetStatusName(status: string | number): string {
  const item = dictCache.asset_status.find((item) => item.value == status);
  return item ? item.label : `未知状态${status}`;
}

const selectedEvent = ref(undefined);

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  // 构建导出参数
  const exportParams: any = {
    ...queryParams,
  };

  // 如果当前页有选中项，则传入assetIds参数
  if (ids.value.length > 0) {
    exportParams.assetIds = ids.value;
  }

  exportLoading.value = true;

  assetsAPI
    .exportSafety(exportParams)
    .then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success(
        `成功导出${ids.value.length > 0 ? "选中" : "全部"}安全设备`
      );
    })
    .catch((error) => {
      console.error("导出失败:", error);
      ElMessage.error("导出失败，请重试");
    })
    .finally(() => {
      handleResetQuery();
      exportLoading.value = false;
    });
}

onMounted(async () => {
  await preloadDictData();
  handleQuery();
});
</script>
