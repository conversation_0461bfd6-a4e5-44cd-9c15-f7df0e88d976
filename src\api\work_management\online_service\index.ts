import request from "@/utils/request";

const BUSINESS_BASE_URL = "/api/v1/businesss";

class businessAPI {
    /** 获取业务上线管理分页数据 */
    static getPage(queryParams?: businessPageQuery) {
        return request<any, PageResult<businessPageVO[]>>({
            url: `${BUSINESS_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 导出
     *
     * @param queryParams 查询参数
     */
    static export(queryParams: businessPageQuery) {
      return request({
        url: `${BUSINESS_BASE_URL}/export`,
        method: "get",
        params: queryParams,
        responseType: "arraybuffer",
      });
    }

    /**
     * 上传模版
     * /api/v1/businesss/{id}/evaluationObject
     * @param id businessID
     * @param data business表单数据
     */
   
    static updateevaluationObject(id: any, data:any) {
      return request({
          url: `${BUSINESS_BASE_URL}/${id}/evaluationObject`,
          method: "put",
          data: data,
      });
  }

  /**
     * 下载模版
     * /api/v1/businesss/downloadEvaluationObject
     * @param id businessID
     * @param data business表单数据
     */
   
  static downloadEvaluationObject() {
    return request({
        url: `${BUSINESS_BASE_URL}/downloadEvaluationObject`,
        method: "get",
    });
}

  /**
     * 安全事件管理列表页面数据统计
     * /api/v1/safetyIncident/pageCount
     */
  static getpageCount(ticketType:any,queryParams:{}) {
    return request({
        url: `${BUSINESS_BASE_URL}/${ticketType}/pageCount`,
        method: "get",
        params:queryParams
    });
}
    /**
     * 获取业务上线管理表单数据
     *
     * @param id businessID
     * @returns business表单数据
     */
    static getFormData(id: number) {
        return request<any, businessForm>({
            url: `${BUSINESS_BASE_URL}/${id}/form`,
            method: "get",
  
        });
    }

    /** 添加业务上线管理*/
    static add(data: any) {
        return request({
            url: `${BUSINESS_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新业务上线管理
     *
     * @param id businessID
     * @param data business表单数据
     */
    static update(id: number, data: businessForm) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除业务上线管理，多个以英文逗号(,)分割
     *
     * @param ids 业务上线管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${BUSINESS_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 个人业务上线管理列表
     */
    static getMyBusiness() {
        return request({
            url: `${BUSINESS_BASE_URL}/person/page`,
            method: "get",
        });
      }

      /**
       * 第二步安全评估
      */
      static assess(data: any,id:any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/safetyAssessment`,
            method: "put",
            data: data,
        });
      }

      /**
       * 第三步业务上线审核
      */
      static audit(data: any,id:any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/audit1`,
            method: "put",
            data: data,
        });
      }

      /** 
       * 第四步业务上线整改提交
      */
      static rectification(data: any,id:any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/assetsSubmit`,
            method: "put",
            data: data,
        });
      }

      /**
       * 资产整改查询
        */
      static getRectification(id: any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/fix`,
            method: "get",
        });
      }

      /**
       * 漏洞复核查询
        */
      static getReview(id: any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/recheck`,
            method: "get",
        });
      }

      /**
       * 第五步漏洞复核提交
        */
      static review(data: any,id:any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/recheck`,
            method: "post",
            data: data,
        });
      }

      /**
       * 第六步评价提交
        */
      static evaluation(data: any,id:any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/commentsBussiness`,
            method: "post",
            data: data,
        });
      }

      /**
       * 获取状态
       */
      static getStepStatus(id: any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/status`,
            method: "get",
        });
      }

      /**
       * 流转列表
       */
      static getFlow(id: any) {
        return request({
            url: `${BUSINESS_BASE_URL}/reviewProcess/${id}`,
            method: "get",
        });
      }
      /**
       * 获取文件列表
       * */
      static getFileList(id: any) {
        return request({
            url: `${BUSINESS_BASE_URL}/${id}/fileList`,
            method: "put",
        });
      }

}

export default businessAPI;

/** 业务上线管理分页查询参数 */
export interface businessPageQuery extends PageQuery {
    /** id */
    id?: number;
    /** 当前步骤 */
    step?: number;
    /** 工单类型 */
    ticketType?: string;
    /** 工单名称 */
    name?: string;
    /** 申请人Id */
    applicantId?: number;
    /** 申请人部门Id */
    deptId?: number;
    createTime?: [string, string];
}

/** 业务上线管理表单对象 */
export interface businessForm {
    /** id */
    id?:  number;
    /** 当前步骤 */
    step?:  number;
    /** 未通过步骤 */
    errStep?:  number;
    /** 关联资产 */
    concatAsset?:  string;
    /** 文件列表 */
    fileList?:  string;
    comments?:  string;
    updateTime?:  Date;
    createTime?:  Date;
}

/**
 * 步骤状态
 */
export interface StepStatus {
  initiateTicket?: string;  //  wait process finish error 
  assetsAudit?: string;
  assetsFix?: string;
  fixVerification?: string;
  fixEvaluation?: string;
  closeTicket?: string;
  closeComments?: string;
}


/** 业务上线管理分页对象 */
export interface businessPageVO {
    /** id */
    id?: number;
    /** 当前步骤 */
    step?: number;
    /** 未通过步骤 */
    errStep?: number;
    /** 关联资产 */
    concatAsset?: string;
    /** 文件列表 */
    fileList?: string;
    comments?: string;
    updateTime?: Date;
    createTime?: Date;
}
