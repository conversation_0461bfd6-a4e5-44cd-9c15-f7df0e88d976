// Date: 2024-8-19
// Creator: chleynx

import request from "@/utils/request";

const SAFETY_BASE_URL = "/api/v1/workManagement/safety";

/**
 * 工单信息
 */
export interface TicketInfo {
  id?: string | number;         // 编号
  employeeId?: string;     // 工号
  applicant?: number;      // 申请人
  deptId?: number;     // 所在单位
  mobile?: string;          // 联系电话
  systemId?: string;     // 应用系统名称  id对应的是系统名称
  loopholeSource?: string;         // 漏洞来源
  title?: string;          // 标题
  content?: string;        // 主要内容
  remarks?: string;        // 备注
  fileList?: File[];       // 整改附件
  status?: number;         // 状态
  createTime?: string;      // 创建时间
  updateTime?: string;      // 更新时间
  stepStatus?: StepStatus[]; // 步骤状态
  vulnerabilityList?: VulnerabilityForm[]; // 漏洞表单信息
}

/** 
 * 漏洞表单信息
*/
export interface VulnerabilityForm {
  id: number;
  name?: string;
  level?: string;
  ip?: string;
  url?: string;
  remark?: string;
  fix?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 工单分页查询参数
 */
export interface TicketQuery extends PageQuery {
  page: number;
  limit: number;
  number?: string;
  employeeId?: string;
  applicant?: string;
  department?: string;
  systemId?: string;
  loopholeSource?: string;
  title?: string;
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 流程权限
 */
export interface StepPermission {
  initiateTicket: boolean;
  vulnerabilityAudit: boolean;
  vulnerabilityFix: boolean;
  fixVerification: boolean;
  fixEvaluation: boolean;
  closeTicket: boolean;
}

/**
 * 步骤状态
 */
export interface StepStatus {
  initiateTicket?: string;  //  wait process finish error 
  vulnerabilityAudit?: string;
  vulnerabilityFix?: string;
  fixVerification?: string;
  fixEvaluation?: string;
  closeTicket?: string;
}

/**
 * 页面查询参数
 */
export interface TicketPageQuery {
  pageNum: number;
  pageSize: number;
  deptId?: string | number;
  keywords?: string;
  status?: string;
  systemId?: number;
  createTime?: string;
}

/**
 * 工单分页数据
 */
export interface TicketPageVO {
  total: number;
  list: TicketInfo[];
}

/**
 * 审核提交数据
 */
export interface AuditData {
  Result: string;
  Opinion: string;
  remarks: string;
}

class SafetyManagementAPI {

  // 创建工单
  static createTicket(data: TicketInfo) {
    return request({
      url: `${SAFETY_BASE_URL}/tickets`,
      method: "post",
      data: data,
    });
  }

  /**
   * 根据权限获取工单列表
   * @param params
   */
  static getTicketList(params: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets",
      method: "get",
      params,
    });
  }


  /**
   * 获取工单详情
   * @param id
   */
  static getTicketDetail(id: number) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id,
      method: "get",
    });
  }

  /**
   * 删除工单
   */
  static deleteTicket(id: string) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id,
      method: "delete",
    });
  }

  /**
   * 更新工单
   */
  static updateTicket(id: string, data: TicketInfo) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id,
      method: "put",
      data,
    });
  }

  /**
   * 获取流程权限
   */
  static getStepPermission() {
    return request({
      url: SAFETY_BASE_URL + "/stepPermission",
      method: "get",
    });
  }

  /**
   * 获取工单流程状态
   */
  static getStepStatus(id: number) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/stepStatus",
      method: "get",
    });
  }

  /** 
   * 流程状态更新
  */
  static updateStepStatus(id: number, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/stepStatus",
      method: "put",
      data,
    });
  }

  /**
   * 审核提交
   */
  static auditTicket(id: number, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/audit",
      method: "post",
      data,
    });
  }

  /**
   * 审核结果查询
   */
  static getAuditResult(id: number) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/auditResult",
      method: "get",
    });
  }

  /**
   * 审核编辑
   * @param id
   * @param data
   * @returns
   */
  static editAudit(id: string, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/editAudit",
      method: "put",
      data,
    });
  }

  /**
   * 漏洞整改提交  
   */
  static fixVulnerability(id: number, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/fixVulnerability",
      method: "post",
      data,
    });
  }

  /**
   * 整改结果查询
   */
  static getFixResult(id: number) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/fixResult",
      method: "get",
    });
  }

  /**
   * 整改编辑
   */
  static editFix(id: string, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/editFix",
      method: "put",
      data,
    });
  }

  /**
   * 漏洞复核提交
   */
  static verifyVulnerability(id: number, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/verifyVulnerability",
      method: "post",
      data,
    });
  }

  /**
   * 复核结果查询
   */
  static getVerifyResult(id: number) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/verifyResult",
      method: "get",
    });
  }

  /**
   * 复核编辑
   */
  static editVerify(id: string, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/editVerify",
      method: "put",
      data,
    });
  }

  /**
   * 整改评估提交
   */
  static evaluateFix(id: string, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/evaluateFix",
      method: "post",
      data,
    });
  }

  /**
   * 评估结果查询
   */
  static getEvaluateResult(id: string) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/evaluateResult",
      method: "get",
    });
  }

  /**
   * 评估编辑
   */
  static editEvaluate(id: string, data: object) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/editEvaluate",
      method: "put",
      data,
    });
  }

  /**
   * 关闭工单
   */
  static closeTicket(id: string) {
    return request({
      url: SAFETY_BASE_URL + "/tickets/" + id + "/close",
      method: "post",
    });
  }

    /** 
   * 导出
   * @param {PageQuery} queryParams - 查询参数
  */
    static export(queryParams: PageQuery) {
      return request({
        url: `${SAFETY_BASE_URL}/export`,
        method: "get",
        params: queryParams,
        responseType: "arraybuffer",
      });
    }

}

export default SafetyManagementAPI;
