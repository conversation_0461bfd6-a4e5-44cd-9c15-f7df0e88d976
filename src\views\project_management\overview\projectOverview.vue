<template>
  <div class="project-overview-container">
    <div class="dashboard-header">
      <h1 class="dashboard-title">项目概览仪表盘</h1>
      <el-button
        type="primary"
        size="small"
        @click="fetchOverviewData"
        :loading="loading"
      >
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 遮罩层 -->
    <div v-if="error" class="overlay">
      <div class="error">{{ error }}</div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="item in stats" :key="item.label">
        <el-card
          shadow="hover"
          class="data-card"
          :body-style="{ padding: '0px' }"
        >
          <div class="card-content">
            <div class="card-info">
              <div class="card-type">
                {{ item.label }}
              </div>
              <div :style="{ color: item.color }" class="card-count">
                {{ item.value }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 历年项目完成趋势折线图 -->
    <el-row :gutter="20">
      <el-col>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="chart-title">历年项目完成趋势图</span>
            </div>
          </template>
          <div class="chart-container" ref="barRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 项目列表展示 -->
    <el-row :gutter="20">
      <el-card shadow="hover" class="project-card">
        <template #header>
          <div class="card-header">
            <span class="chart-title">项目状态</span>
          </div>
        </template>
        <el-table :data="projects" style="" v-loading="isLoading">
          <el-table-column prop="name" label="项目名称" />
          <el-table-column prop="owner" label="负责人" />
          <el-table-column prop="startDate" label="开始日期">
            <template #default="{ row }">
              {{ formatDate(row.startDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="endDate" label="结束日期">
            <template #default="{ row }">
              {{
                formatDate(new Date(new Date(row.endDate).getTime() - 86400000))
              }}
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" disable-transitions>
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container" v-if="pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </el-row>

    <!-- 项目状态分布饼图 -->
    <el-row :gutter="20">
      <el-col>
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="chart-title">项目状态分布</span>
            </div>
          </template>
          <div class="chart-container" ref="pieRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  nextTick,
  onUnmounted,
  watch,
  reactive,
} from "vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { Refresh } from "@element-plus/icons-vue";
import { ProjectAPI } from "../../../api/progress_management/createProject";
import type { Project } from "@/types/project.ts";

const projects = ref<Project[]>([]);
const isLoading = ref(false);

// 分页相关状态
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 5,
  totalPages: 0,
});

// 获取项目列表
const fetchProjects = async () => {
  const oldProjects = [...projects.value];
  try {
    isLoading.value = true;
    const response = await ProjectAPI.getList({
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
    });

    // 处理分页响应数据
    if (response && typeof response === "object" && "items" in response) {
      const data = response as any;
      projects.value = Array.isArray(data.items) ? data.items : [];
      pagination.total = data.total || 0;
      pagination.totalPages = data.totalPages || 0;
    } else {
      // 兼容旧的数组格式响应
      projects.value = Array.isArray(response) ? response : [];
      pagination.total = projects.value.length;
      pagination.totalPages = Math.ceil(pagination.total / pagination.pageSize);
    }
  } catch (error) {
    console.error("获取项目列表失败:", error);
    ElMessage.error("获取项目列表失败");
    projects.value = oldProjects;
  } finally {
    isLoading.value = false;
  }
};

// 分页处理函数
const handleSizeChange = (newSize: number) => {
  pagination.pageSize = newSize;
  pagination.page = 1; // 重置到第一页
  fetchProjects();
};

const handleCurrentChange = (newPage: number) => {
  pagination.page = newPage;
  fetchProjects();
};

// 添加主题检测
const isDark = ref(document.documentElement.classList.contains("dark"));

// 创建 MutationObserver 监听主题变化
let themeObserver: MutationObserver;

// 定义数据类型接口，匹配实际响应结构
interface YearlyData {
  year: number;
  total: number;
  completed: number;
}

interface ProjectOverviewData {
  projectId: number;
  projectName: string;
  totalTasks: number;
  completedTasks: number;
  yearlyData: YearlyData[];
}

// 组件状态
const loading = ref(false);
const error = ref<string | null>(null);
const selectedProjectId = ref<number>(0); // 0表示所有项目
const overviewData = ref<ProjectOverviewData | null>(null);
const projectViewData = ref();

// 图表ref
const pieRef = ref<HTMLElement | null>(null);
const barRef = ref<HTMLElement | null>(null);
let pieChart: echarts.ECharts | null = null;
let barChart: echarts.ECharts | null = null;

// 保存最后一次加载的数据，方便主题切换时重新渲染
const lastData = reactive({
  yearlyData: [] as YearlyData[],
});

// 计算完成率
const calculateRate = (completed: number, total: number) => {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
};

// 统计卡片数据 - 基于新的响应数据计算
const stats = computed(() => {
  if (!projectViewData.value) {
    return [
      { label: "总项目数", value: 0, color: "blue" },
      { label: "已完成项目", value: 0, color: "green" },
      { label: "待完成项目", value: 0, color: "orange" },
      { label: "项目完成率", value: "0%", color: "purple" },
    ];
  }

  const { total, finish } = projectViewData.value;
  const pendingCount = total - finish;
  const completionRate = calculateRate(finish, total);

  return [
    { label: "总项目数", value: total, color: "blue" },
    { label: "已完成项目", value: finish, color: "green" },
    { label: "待完成项目", value: pendingCount, color: "orange" },
    { label: "总体完成率", value: `${completionRate}%`, color: "purple" },
  ];
});

// 获取综合项目概览数据
const fetchProjectData = async () => {
  try {
    const response = await ProjectAPI.getProjectOverview();
    projectViewData.value = response;
  } catch (error: any) {
    console.error("获取概览数据失败");
  }
};

// 获取概览数据 - 适配新的响应结构
async function fetchOverviewData() {
  loading.value = true;
  error.value = null;

  try {
    const response = await ProjectAPI.getOverview(selectedProjectId.value);
    const data = response as any; // 临时类型断言

    overviewData.value = data;
    fetchProjectData();
    // 保存数据，用于主题切换时重新渲染
    if (data.yearlyData) {
      lastData.yearlyData = [...data.yearlyData];
    }

    // 渲染图表
    await nextTick();
    initCharts();
    renderPie();
    renderBar();
  } catch (err: any) {
    console.error("获取概览数据失败:", err);
    error.value = err.message || "无法加载数据，请稍后重试";
    // 如果需要，可以在这里添加模拟数据加载
  } finally {
    loading.value = false;
  }
}

// 刷新图表
const refreshCharts = () => {
  if (lastData.yearlyData.length > 0) {
    renderPie();
    renderBar();
  }
};

// 初始化图表
function initCharts() {
  if (pieRef.value) {
    // 销毁旧图表
    if (pieChart) {
      pieChart.dispose();
    }
    // 使用当前主题初始化图表
    pieChart = echarts.init(pieRef.value, isDark.value ? "dark" : undefined);
  }

  if (barRef.value) {
    // 销毁旧图表
    if (barChart) {
      barChart.dispose();
    }
    // 使用当前主题初始化图表
    barChart = echarts.init(barRef.value, isDark.value ? "dark" : undefined);
  }
}

// 渲染饼图 - 项目状态分布
function renderPie() {
  if (!pieChart || !pieRef.value || !overviewData.value) return;

  const { completedTasks, totalTasks } = overviewData.value;
  const pendingTasks = totalTasks - completedTasks;

  // 获取主题颜色
  const textColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-text-color-primary")
      .trim() || "#303133";
  const borderColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-border-color")
      .trim() || "#E0E6F1";

  // 获取颜色变量
  const successColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-color-success")
      .trim() || "#52C41A";
  const warningColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-color-warning")
      .trim() || "#FAAD14";

  // 构造饼图数据
  const pieData = [
    { value: completedTasks, name: "已完成" },
    { value: pendingTasks, name: "待完成" },
  ].filter((item) => item.value > 0);

  pieChart.setOption({
    backgroundColor: "transparent", // 透明背景，适配主题
    tooltip: {
      trigger: "item",
      backgroundColor: isDark.value ? "#1f2d3d" : "#fff",
      borderColor: isDark.value ? "#2d3b4d" : "#dcdfe6",
      textStyle: {
        color: textColor,
      },
    },
    legend: {
      left: "left",
      top: "bottom",

      orient: "vertical",
      textStyle: {
        color: textColor,
      },
    },
    color: [successColor, warningColor],
    series: [
      {
        name: "项目状态",
        type: "pie",
        radius: ["50%", "80%"],
        center: ["50%", "50%"],
        label: {
          show: true,
          formatter: "{b}: {d}%",
          color: textColor,
          position: "outside",
          alignTo: "labelLine",
          margin: 20,
        },
        labelLine: {
          length: 15,
          length2: 10,
          smooth: true,
        },
        data: pieData,
      },
    ],
  });
  pieChart.resize();
}

// 渲染柱状图 - 历年项目完成趋势
function renderBar() {
  if (!barChart || !barRef.value || !overviewData.value) return;

  const yearlyData = overviewData.value.yearlyData || [];
  const sortedData = [...yearlyData].sort((a, b) => a.year - b.year);
  const years = sortedData.map((item) => item.year);

  // 获取主题颜色
  const textColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-text-color-primary")
      .trim() || "#303133";
  const borderColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-border-color")
      .trim() || "#E0E6F1";

  // 获取颜色变量
  const primaryColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-color-primary")
      .trim() || "#1890FF";
  const successColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-color-success")
      .trim() || "#52C41A";
  const warningColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-color-warning")
      .trim() || "#FAAD14";

  barChart.setOption({
    backgroundColor: "transparent", // 透明背景，适配主题
    tooltip: {
      trigger: "axis",
      backgroundColor: isDark.value ? "#1f2d3d" : "#fff",
      borderColor: isDark.value ? "#2d3b4d" : "#dcdfe6",
      textStyle: {
        color: textColor,
      },
    },
    legend: {
      data: ["总项目数", "已完成", "未完成"],
      textStyle: {
        color: textColor,
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: years,
      axisLine: {
        lineStyle: {
          color: borderColor,
        },
      },
      axisLabel: {
        fontSize: 13,
        color: textColor,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 13,
        color: textColor,
      },
      splitLine: {
        lineStyle: {
          color: isDark.value
            ? "rgba(255, 255, 255, 0.1)"
            : "rgba(0, 0, 0, 0.05)",
          type: "dashed",
        },
      },
    },
    series: [
      {
        name: "总项目数",
        data: sortedData.map((item) => item.total),
        type: "line",
        // smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: primaryColor,
        },
        itemStyle: {
          color: primaryColor,
          borderWidth: 2,
          borderColor: isDark.value ? "#1a1a1a" : "#ffffff",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: isDark.value ? `${primaryColor}50` : `${primaryColor}30`,
            },
            {
              offset: 1,
              color: isDark.value ? `${primaryColor}10` : `${primaryColor}05`,
            },
          ]),
        },
      },
      {
        name: "已完成",
        data: sortedData.map((item) => item.completed),
        type: "line",
        // smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: successColor,
        },
        itemStyle: {
          color: successColor,
          borderWidth: 2,
          borderColor: isDark.value ? "#1a1a1a" : "#ffffff",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: isDark.value ? `${successColor}50` : `${successColor}30`,
            },
            {
              offset: 1,
              color: isDark.value ? `${successColor}10` : `${successColor}05`,
            },
          ]),
        },
      },
      {
        name: "未完成",
        data: sortedData.map((item) => item.total - item.completed),
        type: "line",
        // smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: warningColor,
        },
        itemStyle: {
          color: warningColor,
          borderWidth: 2,
          borderColor: isDark.value ? "#1a1a1a" : "#ffffff",
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: isDark.value ? `${warningColor}50` : `${warningColor}30`,
            },
            {
              offset: 1,
              color: isDark.value ? `${warningColor}10` : `${warningColor}05`,
            },
          ]),
        },
      },
    ],
  });
  barChart.resize();
}

// 创建一个单独的函数用于处理窗口大小变化
const handleResize = () => {
  pieChart?.resize();
  barChart?.resize();
};

// 监听主题变化
watch(isDark, () => {
  initCharts();
  refreshCharts();
});

// 销毁图表
function destroyCharts() {
  if (pieChart) {
    pieChart.dispose();
    pieChart = null;
  }
  if (barChart) {
    barChart.dispose();
    barChart = null;
  }
}

onMounted(() => {
  // 设置主题观察器
  themeObserver = new MutationObserver(() => {
    fetchProjects();
    const newIsDark = document.documentElement.classList.contains("dark");
    if (isDark.value !== newIsDark) {
      isDark.value = newIsDark;
    }
  });

  // 开始观察
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["class"],
  });

  // 加载数据
  fetchOverviewData();

  // 监听窗口大小变化，重置图表大小
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  // 停止主题观察
  themeObserver?.disconnect();

  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);

  // 销毁图表实例，避免内存泄漏
  destroyCharts();
});

// 格式化日期显示
const formatDate = (date: string | Date | null): string => {
  if (!date) return "未设置";
  try {
    const d = new Date(date);
    return d.toLocaleDateString("zh-CN");
  } catch (error) {
    console.error("日期格式化错误:", error);
    return "格式错误";
  }
};
// 根据状态获取标签类型
const getStatusType = (
  status: string
): "primary" | "success" | "warning" | "info" | "danger" => {
  const statusMap: Record<
    string,
    "primary" | "success" | "warning" | "info" | "danger"
  > = {
    未开始: "info",
    已开始: "warning",
    已完成: "success",
    已逾期: "danger",
  };
  return statusMap[status] || "info";
};
</script>

<style scoped>
.project-overview-container {
  padding: 24px;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  min-height: calc(100vh - 60px);
  position: relative;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary); /* 使用 Element Plus 变量 */
  margin: 0;
}

.data-card {
  transition: all 0.3s;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color-light); /* 使用 Element Plus 变量 */
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px var(--el-box-shadow-lighter); /* 使用 Element Plus 变量 */
}

.card-content {
  display: flex;
  padding: 20px;
  height: 100%;
}

.card-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 16px;
}

.card-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-type {
  font-size: 16px;
  color: var(--el-text-color-secondary); /* 使用 Element Plus 变量 */
}

.card-count {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary); /* 使用 Element Plus 变量 */
  margin: 5px 0;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color-light); /* 使用 Element Plus 变量 */
}

.chart-card:hover {
  box-shadow: 0 6px 16px var(--el-box-shadow-lighter); /* 使用 Element Plus 变量 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary); /* 使用 Element Plus 变量 */
}

.chart-container {
  height: 380px;
  padding: 10px;
  transition: background-color 0.3s;
}

/* 图标颜色 */
.card-icon :deep(svg) {
  color: #ffffff; /* 图标颜色保持白色，在彩色背景上 */
}
.project-card {
  width: 100%;
  margin: 10px;
}
.mb-4 {
  margin-bottom: 24px;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--el-mask-color);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error {
  font-size: 18px;
  color: var(--el-color-danger);
  text-align: center;
  background-color: var(--el-bg-color-overlay);
  padding: 20px 30px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

:deep(.el-card) {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter); /* 使用 Element Plus 变量 */
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 适配暗色主题下的卡片图标背景色 */
:deep(.el-col:nth-child(1) .card-icon) {
  background-color: var(--el-color-primary);
}

:deep(.el-col:nth-child(2) .card-icon) {
  background-color: var(--el-color-success);
}

:deep(.el-col:nth-child(3) .card-icon) {
  background-color: var(--el-color-warning);
}

:deep(.el-col:nth-child(4) .card-icon) {
  background-color: var(--el-color-danger);
}

/* 分页容器样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}
</style>
