<template>
  <div class="initiate-ticket">
    <h3 class="page-title">申请下线</h3>
    <el-form :model="form" :rules="rules" ref="dataFormRef" label-width="120px" class="offline-form">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下线业务id" prop="id">
              <el-input v-model="form.id" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicantName">
              <el-input v-model="form.applicantName" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="concat">
              <el-input v-model="form.concat" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下线任务名称" prop="name">
              <el-input v-model="form.name" :disabled="!showStep" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 将其他信息移到这里 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker disabled v-model="form.createTime" type="datetime" placeholder="选择时间"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划下线时间" prop="createTime">
              <el-date-picker :disabled="!showStep" v-model="form.createTime" type="datetime" placeholder="选择时间"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item label="下线原因" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="4" :disabled="!showStep" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="相关文档" prop="fileList" v-if="showStep">
              <file-upload :upload-max-size="20 * 1024 * 1024" v-model="fileList" @update:modelValue="handleFileChange"
                :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'" :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'" />
              <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
            <!-- 文件列表 (只在查看模式显示) -->
            <el-form-item label="相关文档" v-if="!showStep && fileList.length > 0">
              <ul class="file-list">
                <li v-for="file in fileList" :key="file.id" class="file-item">
                  <span>{{ file.name }}</span>
                  <el-button type="primary" size="small" @click="downloadFile(file)">下载</el-button>
                </li>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 资产关联区域 -->
      <div class="form-section">
        <h4 class="section-title">下线资产列表</h4>
        <el-row :gutter="20">
          <!-- <el-col :span="12">
            <el-form-item label="所属单位" prop="deptId">
              <el-button type="primary" plain @click="openDeptDialog" :disabled="!showStep">
                <i-ep-office-building />选择所属单位
              </el-button>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="">
              <el-button type="primary" plain @click="openTransferDialog" :disabled="!showStep">
                <i-ep-link />选择关联资产
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 选择的关联资产展示 -->
        <el-form-item label="已选择的资产" v-if="showStep">
          <el-table :data="assetsData" style="width: 100%" border stripe>
            <!-- 资产类型 -->
            <el-table-column prop="type" label="资产类型" show-overflow-tooltip min-width="100" align="center">
              <template #default="scope">
                <el-tag size="small" class="asset-type-tag">
                  {{ getAssetTypeName(Number(scope.row.type)) }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 资产名称 -->
            <el-table-column prop="name" label="资产名称" show-overflow-tooltip min-width="120" align="center" />

            <!-- 资产地址 -->
            <el-table-column align="center" key="url" label="地址/ip" min-width="150">
              <template #default="scope">
                <!-- 如果是信息系统(type=10)则显示url，否则显示ip -->
                <span>{{ scope.row.type === 10 ? scope.row.url : scope.row.ip }}</span>
              </template>
            </el-table-column>

            <!-- 管理部门 -->
            <el-table-column prop="deptId" label="管理部门" show-overflow-tooltip min-width="120" align="center">
              <template #default="scope">
                <Dictmap v-model="scope.row.deptId" code="dept0x0" />
              </template>
            </el-table-column>

            <!-- 管理人员 -->
            <el-table-column prop="ownerName" label="管理人员" show-overflow-tooltip min-width="100" align="center" />

            <!-- 联系方式 -->
            <!-- <el-table-column prop="concat" label="联系方式" show-overflow-tooltip min-width="120" align="center" /> -->
          </el-table>
        </el-form-item>
      </div>

      <!-- 审核部门区域 -->
      <div class="form-section">
        <h4 class="section-title">流程配置</h4>
        <offline-process-group ref="processGroupRef" @validation-change="handleValidationChange" />
      </div>
    </el-form>

    <!-- 底部按钮区 -->
    <div class="form-actions" v-if="showStep">
      <el-button type="primary" @click="submitForm">
        <i-ep-check />提交工单
      </el-button>
      <el-button @click="resetForm">
        <i-ep-refresh />重置
      </el-button>
    </div>

    <!-- 弹窗组件 -->
    <assetsDialog v-model:visible="dialog.visible" :title="dialog.title" :id="dialog.id" @submitted="handleQuery"
      :disabled="!showStep" />

    <!-- 资产选择弹窗 -->
    <select-assets v-model:visible="transferDialog.visible" :title="'选择关联资产'"
      :selected-assets="transferDialog.selectedAssets" @selected="handleAssetsSelected" />

    <!-- 所属单位选择 -->
    <dept-select v-model:visible="deptDialog.visible" title="选择所属单位" @selected="handleDeptSelected" />

    <!-- 审核部门选择 -->
    <dept-select v-model:visible="reviewDeptDialog.visible" :title="`选择${reviewDeptDialog.currentIndex + 1}级审核部门`"
      @selected="handleReviewDeptSelected" />

    <!-- 评价部门选择 -->
    <dept-select v-model:visible="evaluationDeptDialog.visible"
      :title="`选择${evaluationDeptDialog.currentIndex + 1}级评价部门`" @selected="handleEvaluationDeptSelected" />

  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import assetsDialog from '@/views/assets_management/DetailOfAssets/details/components/assetsDialog.vue'
import offlineAPI from "@/api/assets_management/details/offline"
import UserAPI, { UserQuery } from "@/api/user"
import DeptSelect from '@/components/AssetsManage/SelectDept.vue'
import SelectAssets from '@/components/AssetsManage/SelectAssets.vue'
import { formatLocalDateTime } from "@/utils/dateUtils";
import OfflineProcessGroup from '@/components/ProcessGroup/OfflineProcessGroup.vue'

// 表单引用
const dataFormRef = ref<FormInstance | null>(null)
// 流程组配置引用
const processGroupRef = ref<InstanceType<typeof OfflineProcessGroup> | null>(null)
const emit = defineEmits(['next'])
const isEditMode = ref(false)
// 定义接口
interface TicketData {
  id: string
  currentStep: string
  isClick: boolean
}

// props定义
const props = defineProps<{
  ticketdata: TicketData
}>()

// 表单数据
const form = reactive<any>({
  createTime: formatLocalDateTime(new Date()),
  name: "",
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  applicantId: undefined as number | undefined,
  concat: '',
  id: 0,
  remark: "",
  deptId: null,
  deptName: "",
  assetIds: [],
  fileIds: [],
  fileList: [],
  reviews: [{
    deptId: null,
    deptName: '',
    reviewerId: null,
    reviewerName: '',
    phone: '',
    reviewerOptions: []
  }],
  evaluations: [{
    deptId: null,
    deptName: '',
    evaluatorId: null,
    evaluatorName: '',
    phone: '',
    evaluatorOptions: []
  }]
})

// 弹窗状态
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined,
})

// 部门选择弹窗状态
const deptDialog = reactive({
  visible: false
})

const reviewDeptDialog = reactive({
  visible: false,
  currentIndex: 0
})

const evaluationDeptDialog = reactive({
  visible: false,
  currentIndex: 0
})

// 资产选择弹窗状态
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

const handleAssetsSelected = ({ selectedIds, selectedAssets }: any) => {
  console.log('选择资产回调', { selectedIds, selectedAssets });
  transferDialog.selectedAssets = selectedIds;

  // 确保接收到的资产数据是完整的
  if (selectedAssets && selectedAssets.length > 0) {
    // 深度复制并确保包含必要字段
    sleectedAssetsData.value = selectedAssets.map(asset => ({
      id: asset.id,
      name: asset.name || `资产${asset.id}`,
      type: asset.type,
      status: asset.status || '',
      ownerName: asset.ownerName || asset.managerName || '',
      concat: asset.mobile || asset.otherContact || '',
      deptId: asset.deptId || '',
      address: asset.url || asset.address || asset.ip || '',
      ip: asset.ip || '',
      url: asset.url || '',
    }));
    console.log('设置选中资产数据:', sleectedAssetsData.value);
  }
}

// 分页参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 2000
})

// 步骤控制
const currentStep = ref(props.ticketdata.currentStep)
const showStep = ref(true)
const nowStep = ref('')
const stepStatus = ref<any | null>(null)

const userParams = reactive<UserQuery>({});

// 选中的资产数据
const sleectedAssetsData = ref<any[]>([])
// 新增提交状态控制
const submitting = ref(false)
// 流程验证状态
const processValidationStatus = ref<{ valid: boolean; missingSteps: any[] } | null>(null)
// 计算属性
const assetsData = computed(() => {
  // 首先确认我们有选中的资产ID
  if (!transferDialog.selectedAssets || transferDialog.selectedAssets.length === 0) {
    return [];
  }

  // 然后确认我们有资产数据
  if (!sleectedAssetsData.value || sleectedAssetsData.value.length === 0) {
    return [];
  }

  // 根据选中的ID筛选资产数据
  const filteredAssets = sleectedAssetsData.value.filter(asset =>
    transferDialog.selectedAssets.includes(asset.id)
  );

  console.log('筛选后的资产数据:', filteredAssets);
  return filteredAssets;
})

// 文件列表
const fileList = ref<any[]>([])

// 下载文件方法
const downloadFile = (file: any) => {
  const fileUrl = file.url
  if (fileUrl) {
    window.open(fileUrl, '_blank')
  } else {
    ElMessage.error('文件不存在或无法下载')
  }
}

const handleValidationChange = (status: any) => {
  processValidationStatus.value = status;
};

// 验证规则
const rules = {
  reason: [{ required: true, message: '请填写下线原因', trigger: 'blur' }],
  createTime: [{ required: true, message: '请选择创建时间', trigger: 'change' }],
  name: [{ required: true, message: '请输入下线业务名称', trigger: 'change' }],
  deptId: [{ required: true, message: '请选择所属单位', trigger: 'change' }],
  'reviews.*.deptId': [{ required: true, message: '请选择审核部门', trigger: 'change' }],
  'reviews.*.reviewerId': [{ required: true, message: '请选择审核人', trigger: 'change' }],
  'reviews.*.phone': [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  'evaluations.*.deptId': [{ required: true, message: '请选择评价部门', trigger: 'change' }],
  'evaluations.*.evaluatorId': [{ required: true, message: '请选择评价人员', trigger: 'change' }],
  'evaluations.*.phone': [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

// 打开弹窗方法
const openDeptDialog = () => {
  deptDialog.visible = true
}

const openReviewDeptDialog = (index: number) => {
  reviewDeptDialog.visible = true
  reviewDeptDialog.currentIndex = index
}

const openEvaluationDeptDialog = (index: number) => {
  evaluationDeptDialog.visible = true
  evaluationDeptDialog.currentIndex = index
}

const openTransferDialog = () => {
  transferDialog.visible = true
}

// 部门选择处理方法
const handleDeptSelected = (dept: any) => {
  form.deptId = dept.value
  form.deptName = dept.label
  ElMessage.success('部门已选择')
}

const handleReviewDeptSelected = async (dept: any) => {
  form.reviews[reviewDeptDialog.currentIndex].deptId = dept.value
  form.reviews[reviewDeptDialog.currentIndex].deptName = dept.label
  form.reviews[reviewDeptDialog.currentIndex].reviewerId = null
  form.reviews[reviewDeptDialog.currentIndex].reviewerName = ''
  await loadReviewerOptions(reviewDeptDialog.currentIndex)
  ElMessage.success('审核部门已选择')
}

const handleEvaluationDeptSelected = async (dept: any) => {
  form.evaluations[evaluationDeptDialog.currentIndex].deptId = dept.value
  form.evaluations[evaluationDeptDialog.currentIndex].deptName = dept.label
  form.evaluations[evaluationDeptDialog.currentIndex].evaluatorId = null
  form.evaluations[evaluationDeptDialog.currentIndex].evaluatorName = ''
  await loadEvaluatorOptions(evaluationDeptDialog.currentIndex)
  ElMessage.success('评价部门已选择')
}

// 审核：人员选项加载方法
const loadReviewerOptions = async (index: number) => {
  try {
    userParams.deptId = form.reviews[index].deptId;
    const response = await UserAPI.getList(userParams)
    if (Array.isArray(response)) {
      form.reviews[index].reviewerOptions = response.map(user => ({
        id: user.userId,
        name: user.nickname
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 评价：人员选项加载方法
const loadEvaluatorOptions = async (index: number) => {
  try {
    userParams.deptId = form.evaluations[index].deptId;
    const response = await UserAPI.getList(userParams)
    if (Array.isArray(response)) {
      form.evaluations[index].evaluatorOptions = response.map(user => ({
        id: user.userId,
        name: user.nickname
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 其他方法
const generateTimeBasedId = () => {
  const now = new Date()
  return `${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`
}

const isCurrentStep = () => {
  if (currentStep.value == nowStep.value) {
    showStep.value = true
  } else {
    showStep.value = false
  }
}


// 表单提交与重置
// 表单提交与重置
const submitForm = async () => {
  if (!dataFormRef.value) return

  // 表单验证
  await dataFormRef.value.validate(async (valid, fields) => {
    if (!valid) {
      ElMessage.error('表单验证失败，请检查填写的内容')
      return
    }

    // 验证是否选择了资产
    if (!transferDialog.selectedAssets.length) {
      ElMessage.warning('请至少选择一个需要下线的资产')
      return
    }

    // 检查流程配置是否完整
    const currentStatus = processGroupRef.value?.getValidationStatus()
    if (!currentStatus?.valid) {
      const missingSteps = currentStatus?.missingSteps.map(s => s.name).join('、')
      ElMessage.warning(`流程配置不完整，请完成以下步骤配置: ${missingSteps}`)
      return
    }

    try {
      submitting.value = true
      await ElMessageBox.confirm('确定要提交申请工单吗？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })

      // 获取流程配置数据
      const processData = processGroupRef.value?.getProcessData()

      // 准备文件IDs数组
      const fileIds = fileList.value.map(file => file.id).filter(id => id !== undefined)

      // 准备提交的表单数据
      const submitData = {
        id: form.id || 0,
        name: form.name,
        remark: form.remark || '',
        step: 0, // 初始步骤
        applicantName: form.applicantName,
        applicantId: form.applicantId,
        deptId: form.deptId,
        concat: form.concat, // API中是concat字段，不是contact
        reason: form.reason,
        createTime: formatLocalDateTime(form.createTime),
        updateTime: formatLocalDateTime(new Date()),
        assetIds: transferDialog.selectedAssets,
        fileIds: fileIds, // 使用处理过的fileIds
      }

      // 如果存在流程配置数据，添加到提交表单中
      if (processData) {
        submitData.reviewProcessForms = convertToReviewProcessForms(processData);
      } else {
        ElMessage.error('请配置完整的流程处理人员');
        submitting.value = false;
        return;
      }

      console.log('提交的表单数据:', submitData);

      // 根据是否是编辑模式确定调用的API方法
      if (isEditMode.value) {
        // 更新工单
        await offlineAPI.update(form.id, submitData)
        ElMessage.success('下线申请已更新')
      } else {
        // 提交新工单
        await offlineAPI.add(submitData)
        ElMessage.success('下线申请已提交')
      }

      // 通知父组件完成
      emit('next', form.id)
    } catch (error) {
      if (error instanceof Error) {
        console.error('Error submitting form:', error)
        ElMessage.error(`提交失败: ${error.message}`)
      } else {
        console.error('Unknown error:', error)
        ElMessage.error('提交失败，请重试')
      }
    } finally {
      submitting.value = false
    }
  })
}

// 映射流程数据 - 从工单数据中提取流程配置
const getProcessData = (data) => {
  try {
    // 检查是否有流程数据
    if (!data.reviewProcessForms || !Array.isArray(data.reviewProcessForms)) {
      console.log('未找到有效的流程数据');
      return;
    }

    // 准备执行和通知列表数据
    const execList = [];
    const notifyList = [];

    // 准备人员数据的映射
    const execPersonsMap = {};
    const notifyPersonsMap = {};

    // 遍历流程数据
    data.reviewProcessForms.forEach(process => {
      // 确定是执行部门还是通知部门
      const isExecution = process.executeType === '1';

      // 映射逆向转换 (2->1, 3->2, 4->3, 5->4)
      const deptTypeReverseMap = { '2': 1, '3': 2, '4': 3, '5': 4 };
      const deptTypeNum = deptTypeReverseMap[process.executeDeptType] || Number(process.executeDeptType);

      // 创建基础项目
      const baseItem = {
        id: process.id || 0,
        departmentId: deptTypeNum,
        selectedDeptId: process.deptId || 0,
        deptName: '',  // 在组件中处理
        personName: '', // 在组件中处理
        enableSms: process.enableSms === '1',
        smsTemplateId: process.smsTemplateId ? Number(process.smsTemplateId) : undefined,
        smsContent: '',
        notifyType: process.notifyType || 'once',
        notifyPeriod: process.notifyPeriod || 'daily',
        userIds: []
      };

      // 解析逗号分隔的用户ID列表
      const userIdList = process.userId ? process.userId.split(',').map(id => id.trim()) : [];

      // 根据类型添加到不同列表
      if (isExecution) {
        // 检查执行列表是否已有相同部门类型的项
        const existingIndex = execList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          execList.push(baseItem);
        }

        // 添加用户到执行人员映射
        if (userIdList.length > 0) {
          if (!execPersonsMap[deptTypeNum]) {
            execPersonsMap[deptTypeNum] = [];
          }

          userIdList.forEach(userId => {
            const userInfo = {
              id: userId,
              username: '', // 组件中处理
              nickname: '', // 组件中处理
              deptName: '', // 组件中处理
              mobile: ''    // 组件中处理
            };

            // 确保不重复添加用户
            const exists = execPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              execPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      } else {
        // 通知列表逻辑，与执行列表类似
        const existingIndex = notifyList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          notifyList.push(baseItem);
        }

        if (userIdList.length > 0) {
          if (!notifyPersonsMap[deptTypeNum]) {
            notifyPersonsMap[deptTypeNum] = [];
          }

          userIdList.forEach(userId => {
            const userInfo = {
              id: userId,
              username: '',
              nickname: '',
              deptName: '',
              mobile: ''
            };

            const exists = notifyPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              notifyPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      }
    });

    // 初始化流程组件数据
    nextTick(() => {
      if (processGroupRef.value) {
        processGroupRef.value.initFromExistingData({
          executionList: execList,
          notificationList: notifyList,
          executionPersons: execPersonsMap,
          notificationPersons: notifyPersonsMap
        });
      }
    });
  } catch (error) {
    console.error('流程数据映射出错:', error);
  }
}

// 重置表单
const resetForm = () => {
  // 确认是否重置
  ElMessageBox.confirm('确定要重置表单吗？所有已填写的内容将被清空', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 重置表单字段
    form.name = ""
    form.reason = ''
    form.createTime = formatLocalDateTime(new Date())
    form.plannedOfflineTime = ''

    // 清空已选择的资产
    transferDialog.selectedAssets = []
    sleectedAssetsData.value = []

    // 清空文件列表
    fileList.value = []

    // 重置流程配置
    if (processGroupRef.value) {
      processGroupRef.value.reset()
    }

    // 重置表单验证
    if (dataFormRef.value) {
      dataFormRef.value.resetFields()
    }

    ElMessage.success('表单已重置')
  }).catch(() => {
    // 用户取消重置
  })
}


// 监听文件上传变化
const handleFileChange = (files: any[]) => {
  console.log('文件列表变化:', files)
  fileList.value = files
}

// 将流程配置数据转换为API需要的格式
const convertToReviewProcessForms = (processData: any) => {
  const result: any = [];
  const now = formatLocalDateTime(new Date());

  try {
    // 处理执行列表
    if (processData.executionList && processData.executionList.length > 0) {
      processData.executionList.forEach(item => {
        const departmentId = item.departmentId;

        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5)
        const deptTypeMap = { 1: '2', 2: '3', 3: '4', 4: '5' };
        const deptType = deptTypeMap[departmentId] || departmentId.toString();

        const selectedDeptId = item.selectedDeptId || 0;
        const persons = processData.executionPersons?.[departmentId] || [];

        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map(person => person.id).join(',');

          result.push({
            id: null,
            businessType: "offline", // 资产下线业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType,
            executeType: "1", // 执行类型: 1执行 
            deptId: selectedDeptId,
            userId: userIds,
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }

    // 处理通知列表
    if (processData.notificationList && processData.notificationList.length > 0) {
      processData.notificationList.forEach(item => {
        const departmentId = item.departmentId;

        const deptTypeMap = { 1: '2', 2: '3', 3: '4', 4: '5' };
        const deptType = deptTypeMap[departmentId] || departmentId.toString();

        const selectedDeptId = item.selectedDeptId || 0;
        const persons = processData.notificationPersons?.[departmentId] || [];

        if (selectedDeptId && persons.length > 0) {
          const userIds = persons.map(person => person.id).join(',');

          result.push({
            id: null,
            businessType: "offline", // 资产下线业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType,
            executeType: "2", // 执行类型: 2通知
            deptId: selectedDeptId,
            userId: userIds,
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }

    return result;
  } catch (error) {
    console.error('转换流程数据出错:', error);
    return [];
  }
}

// 数据初始化
const handleQuery = async () => {
  try {
    console.log('执行handleQuery, ticketId:', props.ticketdata.id);

    // 获取当前用户信息
    const userData = await UserAPI.getProfile();
    form.applicantId = userData.id;
    form.applicantName = userData.username;
    form.concat = userData.mobile;

    if (props.ticketdata.id) {
      isEditMode.value = true; // 标记为编辑模式

      // 加载现有工单状态
      const statusRes: any = await offlineAPI.status(props.ticketdata.id);
      stepStatus.value = statusRes;

      // 确定当前步骤
      for (const step in stepStatus.value) {
        if (stepStatus.value[step as keyof any] == 'process') {
          nowStep.value = step as string;
          break;
        }
      }

      isCurrentStep();

      // 加载表单数据
      console.log('加载表单数据...');
      const formData = await offlineAPI.getFormData(props.ticketdata.id);
      console.log('获取到表单数据:', formData);

      // 合并到表单
      Object.assign(form, formData);

      // 处理文件列表
      if (formData.fileList && Array.isArray(formData.fileList)) {
        fileList.value = formData.fileList.map(file => ({
          id: file.id,
          name: file.name,
          url: file.url,
          createTime: file.createTime,
          updateTime: file.updateTime
        }));
      }

      // 如果有流程配置数据，映射并初始化到流程组件
      if (form.reviewProcessForms && form.reviewProcessForms.length > 0) {
        getProcessData(form);
      }

      // 处理关联资产 - 全面改进资产数据处理
      sleectedAssetsData.value = [];
      transferDialog.selectedAssets = [];

      if (formData.assetsList && Array.isArray(formData.assetsList) && formData.assetsList.length > 0) {
        console.log('从表单中获取关联资产列表:', formData.assetsList);

        // 过滤出有效的资产记录（至少有ID）
        const validAssets = formData.assetsList.filter(asset => asset && asset.id);

        if (validAssets.length > 0) {
          // 提取资产ID
          const assetIds = validAssets.map(asset => Number(asset.id));
          transferDialog.selectedAssets = assetIds;

          // 处理资产详细信息，确保字段映射完整
          sleectedAssetsData.value = validAssets

          console.log('已设置关联资产数据:', sleectedAssetsData.value);
        }
      }

      // 验证是否成功设置了资产数据
      console.log('资产数据处理完成:', {
        selectedIds: transferDialog.selectedAssets,
        assetsData: sleectedAssetsData.value,
        displayData: assetsData.value
      });
    } else {
      isEditMode.value = false; // 标记为新建模式
      // 生成ID（如果没有）
      if (!form.id) {
        form.id = generateTimeBasedId();
      }
      await nextTick();
      if (processGroupRef.value) {
        console.log('新建工单，应用默认配置');
        try {
          // 调用流程组件的应用默认配置方法
          await processGroupRef.value.applyDefaultConfig();
          console.log('默认配置应用成功');
        } catch (error) {
          console.error('应用默认配置失败:', error);
        }
      }
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('加载数据失败，请刷新页面重试');
  }
}

watch(
  () => props.ticketdata.id,
  (newId, oldId) => {
    console.log(`工单ID变化: ${oldId} -> ${newId}`)
    if (newId !== oldId) {
      handleQuery()
    }
  }
)

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>
.initiate-ticket {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  margin-bottom: 30px;
  color: var(--el-color-primary);
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  color: var(--el-text-color-primary);
  font-size: 18px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.offline-form :deep(.el-form-item__label) {
  font-weight: 500;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}

.selected-dept {
  margin-right: 20px;
  color: var(--el-color-primary);
}

:deep(.el-tree) {
  background: transparent;
  margin: 10px 0;
}

:deep(.el-tree-node) {
  white-space: normal;
  width: 100%;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 8px 0;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}
</style>
