<template>
  <div class="member-manage-layout">
    <!-- 左侧分组栏 -->
    <div class="member-sidebar">
      <el-menu
        :default-active="activeSidebarGroup"
        class="sidebar-menu"
        @select="handleSidebarSelect"
      >
        <el-menu-item v-for="g in sidebarGroups" :key="g.key" :index="g.key">
          {{ g.label }}
          <span class="member-count">({{ members[g.key]?.length || 0 }})</span>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 右侧成员表格区 -->
    <div class="member-main">
      <div class="member-main-header">
        <el-input
          v-model="sidebarSearch"
          placeholder="请输入搜索"
          size="default"
          class="main-search"
          clearable
          prefix-icon="el-icon-search"
          style="width: 220px; margin-right: 18px"
        />
        <el-button
          size="default"
          @click="handleAddRole"
          style="margin-right: 12px"
        >
          添加角色
        </el-button>
        <el-button
          type="primary"
          size="default"
          @click="addDialogVisible = true"
        >
          添加成员
        </el-button>
      </div>
      <el-table :data="filteredMembers" border stripe class="member-table">
        <el-table-column label="用户名" prop="name" min-width="180">
          <template #default="scope">
            <el-avatar
              :size="32"
              style="background: #8bc34a; margin-right: 8px"
            >
              {{ scope.row.name.slice(0, 2) }}
            </el-avatar>
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="邮箱" prop="email" min-width="220" />
        <el-table-column label="角色" prop="role" min-width="120">
          <template #default="scope">
            <span v-if="scope.row.role">{{ scope.row.role }}</span>
            <span v-else style="color: #bbb">无</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-dropdown trigger="click">
              <el-button type="primary" link size="small">更多</el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="editMember(scope.row)">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="removeMember(scope.row)">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 添加成员弹窗 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加成员"
      width="720px"
      :close-on-click-modal="false"
    >
      <div class="add-dialog-body">
        <!-- 左侧部门/用户组树 -->
        <div class="add-dialog-tree-pane">
          <el-tabs v-model="addTab" class="add-dialog-tabs">
            <el-tab-pane label="部门" name="dept" />
            <el-tab-pane label="用户组" name="group" />
          </el-tabs>
          <el-input
            v-model="treeSearch"
            placeholder="搜索用户名、邮箱、部门名称"
            size="default"
            class="tree-search"
            clearable
            prefix-icon="el-icon-search"
            style="margin-bottom: 10px"
          />
          <el-tree
            :data="filteredTreeData"
            :props="treeProps"
            node-key="id"
            show-checkbox
            highlight-current
            :default-checked-keys="selectedIds"
            @check="handleTreeCheck"
            class="member-tree"
          />
        </div>
        <!-- 右侧已选成员列表 -->
        <div class="add-dialog-selected-pane">
          <div class="selected-title">
            已选 (共{{ selectedMembers.length }}个成员)
          </div>
          <div v-if="selectedMembers.length === 0" class="empty-selected">
            暂无成员
          </div>
          <ul v-else class="selected-list">
            <li
              v-for="item in selectedMembers"
              :key="item.id"
              class="selected-item"
            >
              <el-avatar
                :size="28"
                style="background: #8bc34a; margin-right: 8px"
              >
                {{ item.name.slice(0, 2) }}
              </el-avatar>
              <span>{{ item.name }}</span>
              <span class="selected-email">{{ item.email }}</span>
              <el-button
                type="danger"
                link
                size="small"
                @click="removeSelected(item)"
              >
                ×
              </el-button>
            </li>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";

interface Member {
  id: string;
  name: string;
  email: string;
  role: string;
}
interface MemberGroup {
  [key: string]: Member[];
}
// mock成员数据
const members = ref<MemberGroup>({
  project: [
    {
      id: "u1",
      name: "hooke",
      email: "<EMAIL>",
      role: "组长",
    },
    { id: "u2", name: "alice", email: "<EMAIL>", role: "开发" },
    { id: "u4", name: "charlie", email: "<EMAIL>", role: "前端" },
  ],
  external: [
    { id: "u5", name: "david", email: "<EMAIL>", role: "外包" },
    { id: "u8", name: "lucy", email: "<EMAIL>", role: "外协设计" },
  ],
  pending: [
    { id: "u3", name: "bob", email: "<EMAIL>", role: "" },
    { id: "u9", name: "tom", email: "<EMAIL>", role: "" },
  ],
  history: [
    { id: "u6", name: "eve", email: "<EMAIL>", role: "前成员" },
    { id: "u7", name: "frank", email: "<EMAIL>", role: "前成员" },
    { id: "u10", name: "jack", email: "<EMAIL>", role: "前成员" },
  ],
});
const sidebarGroups = [
  { key: "project", label: "项目成员" },
  { key: "external", label: "外部协作" },
  { key: "pending", label: "待激活成员" },
  { key: "history", label: "历史成员" },
];
const activeSidebarGroup = ref<string>("project");
const sidebarSearch = ref("");
const filteredMembers = computed(() => {
  const groupKey = activeSidebarGroup.value;
  const list = members.value[groupKey] || [];
  if (!sidebarSearch.value.trim()) return list;
  return list.filter(
    (m: Member) =>
      m.name.includes(sidebarSearch.value) ||
      m.email.includes(sidebarSearch.value)
  );
});
function handleSidebarSelect(val: string) {
  activeSidebarGroup.value = val;
  sidebarSearch.value = "";
}
// 添加成员弹窗相关
const addDialogVisible = ref(false);
const addTab = ref("dept");
const treeSearch = ref("");
const treeProps = { label: "name", children: "children" };
const treeData = ref([
  {
    id: "dept1",
    name: "技术部",
    children: [
      {
        id: "dept1-1",
        name: "未分配部门",
        children: [
          { id: "u1", name: "hooke", email: "<EMAIL>" },
          { id: "u2", name: "alice", email: "<EMAIL>" },
        ],
      },
      {
        id: "dept1-2",
        name: "未激活成员",
        children: [{ id: "u3", name: "bob", email: "<EMAIL>" }],
      },
      {
        id: "dept1-3",
        name: "前端组",
        children: [
          { id: "u4", name: "charlie", email: "<EMAIL>" },
          { id: "u5", name: "david", email: "<EMAIL>" },
        ],
      },
    ],
  },
  {
    id: "dept2",
    name: "产品部",
    children: [
      {
        id: "dept2-1",
        name: "产品经理",
        children: [{ id: "u6", name: "eve", email: "<EMAIL>" }],
      },
      {
        id: "dept2-2",
        name: "设计师",
        children: [{ id: "u7", name: "frank", email: "<EMAIL>" }],
      },
    ],
  },
]);
const allUsers = [
  { id: "u1", name: "hooke", email: "<EMAIL>" },
  { id: "u2", name: "alice", email: "<EMAIL>" },
  { id: "u3", name: "bob", email: "<EMAIL>" },
  { id: "u4", name: "charlie", email: "<EMAIL>" },
  { id: "u5", name: "david", email: "<EMAIL>" },
  { id: "u6", name: "eve", email: "<EMAIL>" },
  { id: "u7", name: "frank", email: "<EMAIL>" },
];
const selectedIds = ref<string[]>([]);
const selectedMembers = computed(() => {
  return allUsers.filter((u) => selectedIds.value.includes(u.id));
});
const filteredTreeData = computed(() => {
  // 简单过滤树节点（只支持一级）
  if (!treeSearch.value.trim()) return treeData.value;
  const keyword = treeSearch.value.trim().toLowerCase();
  function filter(nodes: any[]): any[] {
    return nodes
      .map((node: any) => {
        if (node.children) {
          const children: any[] = filter(node.children);
          if (children.length) return { ...node, children };
        }
        if (node.name.toLowerCase().includes(keyword)) return { ...node };
        return null;
      })
      .filter(Boolean);
  }
  return filter(treeData.value);
});
function handleTreeCheck(checked: any, { checkedKeys }: any) {
  // 这里只模拟选中用户id
  selectedIds.value = checkedKeys.filter((id: string) => id.startsWith("u"));
}
function removeSelected(item: any) {
  selectedIds.value = selectedIds.value.filter((id) => id !== item.id);
}
function confirmAdd() {
  // 将已选成员添加到当前分组，避免重复
  const group = activeSidebarGroup.value;
  selectedIds.value.forEach((id) => {
    const user = allUsers.find((u) => u.id === id);
    if (user && !members.value[group].some((m: any) => m.id === user.id)) {
      members.value[group].push({ ...user, role: "" });
    }
  });
  addDialogVisible.value = false;
  selectedIds.value = [];
  ElMessage.success("添加成功");
}
// 删除直接生效，无需弹窗
function removeMember(row: any) {
  const group = activeSidebarGroup.value;
  members.value[group] = members.value[group].filter(
    (m: any) => m.id !== row.id
  );
  ElMessage.success("已删除");
}
function editMember(row: any) {
  ElMessage.info("编辑功能暂未实现");
}
function handleAddRole() {
  ElMessage.info("角色管理功能暂未实现");
}
</script>

<style scoped>
.member-manage-layout {
  display: flex;
  height: 100vh;
  background: #fff;
}
.member-sidebar {
  width: 220px;
  background: #f8f9fb;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  padding: 24px 0 0 0;
}
.sidebar-search {
  margin: 0 18px 18px 18px;
  border-radius: 8px;
}
.sidebar-menu {
  border: none;
  background: transparent;
  margin-bottom: 8px;
}
.member-count {
  color: #409eff;
  font-size: 13px;
  margin-left: 6px;
}
.member-main {
  flex: 1;
  padding: 32px 40px 0 40px;
  background: #fff;
  min-width: 0;
}
.member-main-header {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  justify-content: flex-end;
}
.member-table {
  background: #fff;
  border-radius: 10px;
  font-size: 15px;
}
.add-dialog-body {
  display: flex;
  min-height: 380px;
}
.add-dialog-tree-pane {
  width: 320px;
  border-right: 1px solid #f0f0f0;
  padding: 0 18px 0 0;
  display: flex;
  flex-direction: column;
}
.add-dialog-tabs {
  margin-bottom: 8px;
}
.tree-search {
  margin-bottom: 8px;
  border-radius: 8px;
}
.member-tree {
  background: transparent;
  font-size: 15px;
  border-radius: 8px;
  min-height: 220px;
}
.add-dialog-selected-pane {
  flex: 1;
  padding: 0 0 0 32px;
  display: flex;
  flex-direction: column;
}
.selected-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 10px;
}
.empty-selected {
  color: #bbb;
  font-size: 15px;
  margin-top: 40px;
}
.selected-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.selected-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}
.selected-email {
  color: #888;
  font-size: 13px;
  margin-left: 12px;
  flex: 1;
}
.main-search {
  border-radius: 8px;
}
</style>
