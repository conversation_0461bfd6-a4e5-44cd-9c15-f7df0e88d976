<template>
  <div v-if="visible" class="dialog-backdrop">
    <div class="dialog">
      <h2>
        {{ isView ? '查看系统' : (isEdit ? '编辑系统' : '新增系统') }}
      </h2>

      <form @submit.prevent="submit">
        <!-- 基本信息区域 -->
        <div class="form-wrapper">
          <div class="form-group">
            <label>系统名称</label>
            <input v-model="form.name" type="text" :disabled="isView" placeholder="请输入系统名称" required />
          </div>
          <div class="form-group">
            <label>所属部门</label>
            <input v-model="form.department" type="text" :disabled="isView" placeholder="请输入所属部门" required />
          </div>
        </div>

        <!-- 系统配置区域 -->
        <div class="form-wrapper">
          <div class="form-group">
            <label>等保级别</label>
            <select v-model="form.level" :disabled="isView" required>
              <option value="" disabled>请选择等保级别</option>
              <option v-for="option in formattedLevelOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label>是否对公网开放</label>
            <select v-model="form.isOpen" :disabled="isView" required>
              <option value="" disabled>请选择是否开放</option>
              <option v-for="option in formattedIsOpenOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </option>
            </select>
          </div>
        </div>

        <!-- 联系人信息区域 -->
        <div class="form-wrapper">
          <div class="form-group">
            <label>责任人</label>
            <input v-model="form.responsiblePerson" type="text" :disabled="isView" placeholder="请输入责任人姓名" required />
          </div>
          <div class="form-group">
            <label>联系电话</label>
            <input v-model="form.respPersonPhone" type="text" :disabled="isView" placeholder="请输入联系电话" required />
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="dialog-actions">
          <button type="button" @click="close">关闭</button>
          <button v-if="!isView" type="submit">提交</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import type { GradingVO } from "@/api/grade_protection";

const props = defineProps<{
  visible: boolean;
  record?: GradingVO | null;
  isEdit?: boolean;
  isView?: boolean;
  levelOptions: string[];
  isOpenOptions: string[];
}>();

// 将字符串数组转换为对象数组，以便在下拉框中使用
const formattedLevelOptions = [
  { label: '零级', value: '0' },
  { label: '一级', value: '1' },
  { label: '二级', value: '2' },
  { label: '三级', value: '3' },
  { label: '四级', value: '4' },
  { label: '五级', value: '5' },
];

const formattedIsOpenOptions = [
  { label: '否', value: '0' },
  { label: '是', value: '1' }
];

const emit = defineEmits<{
  (e: "close"): void;
  (e: "submit", record: GradingVO): void;
}>();


const form = ref({
  id: "",
  name: "",
  department: "",
  level: "",
  isOpen: "",
  responsiblePerson: "",
  respPersonPhone: "",
});

const submit = () => {
  // 确保提交的数据包含所有必要字段
  emit("submit", { ...form.value });
  close();
};

watch(
  () => props.record,
  (newRecord) => {
    console.log("newRecord", newRecord);
    if (newRecord) {
      form.value = {
        id: newRecord.id ?? "",
        name: newRecord.name ?? "",
        department: newRecord.department ?? "",
        level: newRecord.level ?? "",
        isOpen: newRecord.isOpen ?? "",
        responsiblePerson: newRecord.responsiblePerson ?? "",
        respPersonPhone: newRecord.respPersonPhone ?? "",
      };
    } else {
      form.value = {
        id: "",
        name: "",
        department: "",
        level: "",
        isOpen: "",
        responsiblePerson: "",
        respPersonPhone: "",
      };
    }
  },
  { immediate: true }
);


const close = () => {
  emit("close");
};
</script>

<style scoped>
/* 弹窗背景 - 高级模糊效果 */
.dialog-backdrop {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(15, 23, 42, 0.65);
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 弹窗容器 - 现代化设计 */
.dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 840px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 0;
  background-color: var(--el-bg-color-overlay);
  border-radius: 16px;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transform: translateY(0);
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹窗标题区域 */
h2 {
  position: relative;
  margin: 0;
  padding: 24px 32px;
  font-size: 22px;
  font-weight: 600;
  letter-spacing: -0.01em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

h2::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 32px;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border-radius: 3px;
}

/* 表单容器 */
form {
  padding: 28px 32px 32px;
}

/* 表单布局 */
.form-wrapper {
  display: flex;
  gap: 24px;
  margin-bottom: 4px;
}

.form-wrapper .form-group {
  flex: 1;
  min-width: 0;
}

/* 表单组 */
.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
}

/* 标签样式 */
label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

/* 输入框和选择框基础样式 */
input[type="text"],
input[type="date"],
select {
  width: 100%;
  padding: 12px 16px;
  font-size: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 禁用状态 */
input:disabled,
select:disabled {
  border-color: #e2e8f0;
  cursor: not-allowed;
  opacity: 0.8;
}

/* 聚焦状态 */
input[type="text"]:focus,
input[type="date"]:focus,
select:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 文件上传样式 */
input[type="file"] {
  position: relative;
  width: 100%;
  padding: 12px;
  font-size: 14px;
  border: 1px dashed #cbd5e1;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]:hover {
  border-color: #94a3b8;
}

input[type="file"]::file-selector-button {
  margin-right: 12px;
  padding: 8px 14px;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]::file-selector-button:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
}

/* 按钮容器 */
.dialog-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 按钮基础样式 */
button {
  position: relative;
  min-width: 100px;
  padding: 12px 24px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 10px;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
}

/* 关闭按钮 */
button[type="button"] {
  border: 1px solid #e2e8f0;
}

button[type="button"]:hover {
  color: #334155;
  background-color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

button[type="button"]:active {
  transform: translateY(0);
  box-shadow: none;
}

/* 提交按钮 */
button[type="submit"] {
  box-shadow:
    0 4px 12px rgba(37, 99, 235, 0.25),
    0 1px 3px rgba(37, 99, 235, 0.1);
}

button[type="submit"]:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow:
    0 6px 16px rgba(37, 99, 235, 0.3),
    0 2px 5px rgba(37, 99, 235, 0.15);
}

button[type="submit"]:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(37, 99, 235, 0.25),
    0 1px 2px rgba(37, 99, 235, 0.1);
}

/* 佐证材料容器 */
.evidence-container {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.current-file-label {
  margin-right: 12px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 佐证材料链接样式 */
.evidence-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 18px;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.evidence-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 1px 3px rgba(59, 130, 246, 0.1);
}

.evidence-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.evidence-icon {
  margin-right: 10px;
  font-style: normal;
  font-size: 18px;
}

/* 无佐证材料提示 */
.no-evidence {
  display: inline-block;
  margin-top: 12px;
  padding: 10px 18px;
  color: #f43f5e;
  background-color: rgba(244, 63, 94, 0.08);
  border: 1px solid rgba(244, 63, 94, 0.15);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* 已选文件显示 */
.selected-file {
  display: flex;
  align-items: center;
  margin-top: 12px;
  padding: 10px 16px;
  font-size: 14px;
  color: #0f766e;
  background-color: rgba(20, 184, 166, 0.08);
  border: 1px solid rgba(20, 184, 166, 0.2);
  border-radius: 8px;
  font-weight: 500;
}

.selected-file .evidence-icon {
  margin-right: 10px;
  font-style: normal;
  font-size: 16px;
  color: #0d9488;
}

/* 响应式调整 */
@media (width <=768px) {
  .dialog {
    width: 95vw;
    max-width: 600px;
  }

  .form-wrapper {
    flex-direction: column;
    gap: 16px;
  }
}

@media (width <=480px) {
  .dialog {
    width: 92vw;
  }

  h2 {
    padding: 20px 24px;
    font-size: 20px;
  }

  form {
    padding: 20px 24px 24px;
  }

  button {
    min-width: 90px;
    padding: 10px 18px;
    font-size: 14px;
  }

  .dialog-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
}
</style>
