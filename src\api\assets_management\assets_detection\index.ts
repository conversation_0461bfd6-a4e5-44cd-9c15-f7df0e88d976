import request from "@/utils/request";

const DETECTION_BASE_URL = "/api/v1/detections";

class DetectionAPI {
    /** 获取资产探测分页数据 */
    static getPage(queryParams?: DetectionPageQuery) {
        return request<any, PageResult<DetectionPageVO[]>>({
            url: `${DETECTION_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 获取资产探测表单数据
     *
     * @param id DetectionID
     * @returns Detection表单数据
     */
    static getFormData(id: number) {
        return request<any, DetectionForm>({
            url: `${DETECTION_BASE_URL}/${id}/form`,
            method: "get",
        });
    }
    /**
     * 批量修改资产探测
     * @ids 资产探测ID，多个以英文逗号(,)分割
     */
    static updateByIds(ids: string) {
        return request({
            url: `${DETECTION_BASE_URL}/${ids}`,
            method: "post",
        });
    }

    /**
     * 对指定id的进行探测任务
     * @id
     */
    static detectionById(id: number) {
        return request({
            url: `${DETECTION_BASE_URL}/detect/${id}`,
            method: "get",
        });
    }

    /**
     * 获取指定探测任务id的探测日志
     * @queryParams  parentId:number, pageNum: number, pageSize: number
     */
    static getDetectionLogById(queryParams: any) {
        return request<any, PageResult<DetectionPageVO[]>>({
            url: `${DETECTION_BASE_URL}/queryLog`,
            method: "post",
            params: queryParams,
        });
    }

    /** 添加资产探测*/
    static add(data: DetectionForm) {
        return request({
            url: `${DETECTION_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新资产探测
     *
     * @param id DetectionID
     * @param data Detection表单数据
     */
    static update(id: number, data: DetectionForm) {
        return request({
            url: `${DETECTION_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除资产探测，多个以英文逗号(,)分割
     *
     * @param ids 资产探测ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${DETECTION_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 导出
     */
        static exportServer(data: any) {
            return request({
              url: `${DETECTION_BASE_URL}/logExport`,
              method: "post",
              data: data,
              responseType: "blob",
            });
          }
}

export default DetectionAPI;

/** 资产探测分页查询参数 */
export interface DetectionPageQuery extends PageQuery {
    /** 探测ID */
    detectionId?: string;
    /** 任务名称 */
    taskName?: string;
    /** 探测时间 */
    detectionTime?: [string, string];
    /** 探测状态 */
    detectionStatus?: string;
    /** 定期探测状态 */
    regularDetectionStatus?: string;
    /** 临时探测状态 */
    temporaryDetectionStatus?: string;
  }
  
  /** 资产探测分页对象 */
  export interface DetectionPageVO {
    /** 探测ID */
    detectionId: string;
    /** 任务名称 */
    taskName: string;
    /** 探测时间 */
    detectionTime: string;
    /** 探测状态 */ 
    detectionStatus: string;
    /** 定期探测状态 */
    regularDetectionStatus: string;
    /** 临时探测状态 */
    temporaryDetectionStatus: string;
  }
/** 资产探测表单对象 */
export interface DetectionForm {
    isSms: number;
    temporaryDetectionStatus: string;
    assetsList: boolean;
    assetsList(assetsList: any): unknown;
    /** 资产ID */
    assetId?:  bigint;
    /** 资产名称 */
    assetName?:  string;
    /** 探测周期 */
    detectionCycle?:  string;
    /** 资产探测ID */
    detectionId?:  bigint;
    /** 探测名称 */
    detectionName?:  string;
    /** 即时探测状态 */
    immediateDetectionStatus?:  number;
    /** 即时探测时间 */
    immediateDetectionTime?:  Date;
    /** 最新探测时间 */
    latestDetectionTime?:  Date;
    /** 通知状态 */
    notificationStatus?:  number;
    /** 通知策略ID */
    notificationStrategyId?:  bigint;
    /** 通知策略名称 */
    notificationStrategyName?:  string;
    /** 通知对象ID */
    notificationTargetId?:  bigint;
    /** 通知时间 */
    notificationTime?:  Date;
    /** 定期探测状态 */
    regularDetectionStatus?:  number;
    /** 短信模板 */
    smsTemplate?:  string;
}
