import request from "@/utils/request";

const USER_BASE_URL = "/api/v1/assets";

class assetsAPI {
  /**
   * 获取资产详情
   * @param {number} id - 资产ID
   * @returns {Promise} - 请求的Promise对象
   */
  static getAsset() {
    return request({
      url: `${USER_BASE_URL}/assetsAll`,
      method: "get",
    });
  }

  /**
   * 创建资产
   * @param {AssetsForm} data - 资产表单数据
   * @returns {Promise} - 请求的Promise对象
   */
  static add(data: AssetsForm) {
    return request({
      url: USER_BASE_URL,
      method: "post",
      data,
    });
  }

  /**
   * 更新资产
   * @param {number} id - 资产ID
   * @param {AssetsForm} data - 资产表单数据
   * @returns {Promise} - 请求的Promise对象
   */
  static update(id: number, data: AssetsForm) {
    return request({
      url: `${USER_BASE_URL}/${id}`,
      method: "put",
      data,
    });
  }

  /**
   * 删除资产
   * @param {string} id - 资产ID
   * @returns {Promise} - 请求的Promise对象
   */
  static deleteAsset(id: string) {
    return request({
      url: `${USER_BASE_URL}/${id}`,
      method: "delete",
    });
  }

  /**
   * 资产表单数据详情
   * @param {number} id - 资产ID
   * @returns {Promise} - 请求的Promise对象
   */
  static getFormData(id: number) {
    return request<any, AssetsForm>({
      url: `${USER_BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 批量资产删除，以逗号分隔
   * @param {string} ids - 资产ID
   */
  static deleteByIds(ids: string) {
    return request({
      url: `${USER_BASE_URL}/${ids}`,
      method: "delete",
    });
  }

  /** 
   * 下载资产模板
   * @returns {Promise} - 请求的Promise对象
  */
  static downloadTemplate() {
    return request({
      url: `${USER_BASE_URL}/template`,
      method: "get",
      responseType: "arraybuffer",
    });
  }

  /**
   * 导入资产
   * @param {FormData} data - 资产表单数据
   * @param file 导入文件
   */
  static importAssets(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request({
      url: `${USER_BASE_URL}/import`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /** 
   * 导出资产
   * @param {AssetsPageQuery} queryParams - 查询参数
  */
  static export(queryParams: AssetsPageQuery) {
    return request({
      url: `${USER_BASE_URL}/export`,
      method: "get",
      params: queryParams,
      responseType: "arraybuffer",
    });
  }

  /**
   * 分页查询资产
   * @param {AssetsPageQuery} params - 分页查询参数
   * @returns {Promise} - 请求的Promise对象
   */
  static getPage(params: AssetsPageQuery) {
    return request({
      url: USER_BASE_URL,
      method: "get",
      params,
    });
  }
}

export default assetsAPI;

/** 信息 */
export interface AssetsInfo {
  id: string;
  name: string;
  systemId?: number; 
  type?: string;
  IP: string;
  url: string;
  port?: number;
  ownerName ?: string;
  deptId ?: string | number;
  ownerphone?: string;
  status: number;
  createTime: string;
  os ?: string;
  notes ?: string;
}

/**
 * 分页查询对象
 */
export interface AssetsPageQuery extends PageQuery {
  name?: string;
  type?: string;
  systemId?: number; // 系统ID
  ownerName ?: string;
  deptId ?: string | number;
  keywords?: string;
  ownerphone?: string;
  os ?: string;
  status?: number;
  port?: number;
  createTime?: string;
}

/** 分页对象 */
export interface AssetsPageVO {
  total: number;
  list: AssetsInfo[];
}

/** 表单类型 */
export interface AssetsForm {
  id ?: number;
  name ?: string;
  systemId?: number; 
  type ?: string;
  IP ?: string;
  url ?: string;
  port?: number;
  ownerName ?: string;
  ownerphone?: string;
  deptId ?: string | number;
  status ?: number;
  os ?: string;
  notes ?: string;
}
