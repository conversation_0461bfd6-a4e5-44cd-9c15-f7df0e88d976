<template>
  <div v-if="props.multiple">
    <!-- 多选显示模式 -->
    <el-tag
      v-for="(value, index) in multipleValues"
      :key="index"
      :type="getTagType(value, index)"
      size="small"
      class="mr-1 mb-1"
    >
      {{ getLabelByValue(props.code, value) }}
    </el-tag>
    <!-- 如果没有选中任何值，显示占位符 -->
    <span v-if="multipleValues.length === 0" class="text-gray-400">{{ placeholder }}</span>
  </div>
  <div v-else>
    <!-- 单选显示模式（保留原有逻辑） -->
    {{ getLabelByValue(props.code, props.modelValue) }}
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeMount, computed } from 'vue';
import { useDictStore } from '@/store/modules/dictStore';

const props = defineProps({
  /**
   * 字典编码(eg: 性别-gender)
   */
  code: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [String, Number, Array],
  },
  placeholder: {
    type: String,
    default: "未设置",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 新增：是否多选显示
  multiple: {
    type: Boolean,
    default: false,
  },
  // 新增：标签颜色类型，默认使用循环颜色
  tagType: {
    type: String,
    default: '',
  },
  // 新增：传入的字典选项（优先使用，用于预加载）
  dictOptions: {
    type: Array,
    default: () => []
  },
});

const dictStore = useDictStore();
const options = ref<{ label: string, value: string | number }[]>([]);

// 获取当前使用的字典选项（优先使用传入的）
const currentOptions = computed(() => {
  return props.dictOptions && props.dictOptions.length > 0 
    ? props.dictOptions 
    : options.value;
});

// 多选值数组
const multipleValues = computed(() => {
  if (!props.modelValue) return [];
  
  if (Array.isArray(props.modelValue)) {
    return props.modelValue;
  }
  
  if (typeof props.modelValue === 'string') {
    // 如果包含逗号，按逗号分割；否则作为单个值处理
    if (props.modelValue.includes(',')) {
      return props.modelValue.split(',').filter(item => item.trim() !== '');
    } else {
      // 单个值也作为数组返回
      return props.modelValue.trim() ? [props.modelValue.trim()] : [];
    }
  }
  
  // 数字或其他类型，作为单个值处理
  return [props.modelValue];
});

watch(() => props.modelValue, (newValue) => {
  // 保留原有的用户数据获取逻辑
  if (props.code == "user0x0" && newValue !== undefined) {
    fetchUserData(newValue);
  }
});

async function fetchUserData(id: string | number) {
  console.log('id', id);
  options.value = await dictStore.fetchUserData(id);
}

onBeforeMount(async () => {
  // 如果传入了字典选项，直接使用，不需要请求
  if (props.dictOptions && props.dictOptions.length > 0) {
    return;
  }

  // 保留原有的逻辑
  if (props.code == "user0x0") {
    console.log('props.modelValue', props.modelValue);
    if (props.modelValue !== undefined) {
      fetchUserData(props.modelValue);
    }
  } else {
    options.value = await dictStore.fetchOptions(props.code);
  }
});

// 保留原有的 getLabelByValue 函数逻辑
function getLabelByValue(code: string, value: string | number | undefined): string {
  function findLabel(options: any[], value: string | number): string | undefined {
    for (const option of options) {
      // 修改：支持不同类型的值比较
      if (option.value == value || 
          String(option.value) === String(value) || 
          Number(option.value) === Number(value)) {
        return option.label;
      }
      if (option.children) {
        const childLabel = findLabel(option.children, value);
        if (childLabel) {
          return childLabel;
        }
      }
    }
    return undefined;
  }
  
  // 使用当前的字典选项（可能是传入的或请求的）
  const label = value !== undefined ? findLabel(currentOptions.value, value) : undefined;
  return label || '';
}

// 获取标签类型（通用化处理）
function getTagType(value: string | number, index: number): string {
  // 如果指定了固定的标签类型，直接使用
  if (props.tagType) {
    return props.tagType;
  }
  
  // 默认使用循环颜色方案
  const tagTypes = ['primary', 'success', 'warning', 'danger', 'info'];
  return tagTypes[index % tagTypes.length];
}
</script>

<style scoped>
.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
