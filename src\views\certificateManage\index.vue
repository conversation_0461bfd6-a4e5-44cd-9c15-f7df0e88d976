<template>
    <div class="app-container">
      <div class="search-container">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true">
          <el-form-item label="产品名称" prop="productName">
            <el-input v-model="queryParams.productName" placeholder="产品名称" clearable class="!max-w-[200px]" />
          </el-form-item>
          <el-form-item label="序列号" prop="serialNumber">
            <el-input v-model="queryParams.serialNumber" placeholder="序列号" clearable class="!max-w-[200px]" />
          </el-form-item>
          <el-form-item label="用户名称" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="用户名称" clearable class="!max-w-[200px]" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery"><i-ep-search />搜索</el-button>
            <el-button @click="handleReset"><i-ep-refresh />重置</el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <el-card shadow="never" class="table-container">
        <template #header>
          <div class="flex-x-between">
            <el-button type="primary" @click="openCreateDialog">
              <i-ep-plus />生成证书
            </el-button>
          </div>
        </template>
        <el-table :data="certificateList" border stripe v-loading="loading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="productName" label="产品名称" min-width="140" show-overflow-tooltip />
          <el-table-column prop="serialNumber" label="序列号" min-width="140" show-overflow-tooltip />
          <el-table-column prop="certType" label="证书类型" min-width="180" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.certType ==='official'">正式证书</span>
              <span v-else-if="scope.row.certType ==='temporary'">临时证书</span>
            </template>
          </el-table-column>
          <el-table-column prop="userName" label="用户名称" min-width="120" show-overflow-tooltip />
          <el-table-column label="有效期" min-width="180" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.validFrom) }} 至 {{ formatDate(scope.row.validTo) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" min-width="160" align="center">
            <template #default="scope">
              {{ formatDateTime(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="300" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="viewCertificate(scope.row)">
                <el-icon><i-ep-view /></el-icon>查看
              </el-button>
              <el-button type="primary" link @click="editCertificate(scope.row)">
                <el-icon><i-ep-edit /></el-icon>编辑
              </el-button>
              <el-button type="primary" link @click="downloadCertificate(scope.row)">
                <el-icon><i-ep-edit /></el-icon>导出证书
              </el-button>
              <el-button type="danger" link @click="deleteCertificate(scope.row)">
                <el-icon><i-ep-delete /></el-icon>删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
  
      <!-- 证书详情弹窗 -->
      <el-dialog v-model="viewDialog.visible" title="证书详情" width="600px" destroy-on-close>
        <el-descriptions :column="2" border size="default">
          <el-descriptions-item label="产品名称">{{ viewDialog.certificate.productName }}</el-descriptions-item>
          <el-descriptions-item label="序列号">{{ viewDialog.certificate.serialNumber }}</el-descriptions-item>
          <el-descriptions-item label="证书类型">{{ viewDialog.certificate.certType==='official' ? '正式证书' : '临时证书' }}</el-descriptions-item>
          <el-descriptions-item label="用户名称">{{ viewDialog.certificate.userName }}</el-descriptions-item>
          <el-descriptions-item label="有效期" :span="2">
            {{ formatDate(viewDialog.certificate.validFrom) }} 至 {{ formatDate(viewDialog.certificate.validTo) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(viewDialog.certificate.createTime) }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="modules-section" v-if="viewDialog.certificate.modules && viewDialog.certificate.modules.length">
          <h3 class="section-title">授权模块</h3>
          <div class="modules-list">
            <el-tag
              v-for="(module, index) in viewDialog.certificate.modules"
              :key="index"
              class="module-item"
              effect="plain"
              size="small"
            >
              {{ module }}
            </el-tag>
          </div>
        </div>
      </el-dialog>
  
      <!-- 证书生成/编辑弹窗 -->
      <CertificateDialog
        v-model:visible="dialog.visible"
        :title="dialog.title"
        :id="dialog.id"
        @submitted="handleQuery"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
  import { format } from '@/utils/dateUtils';
  import CertificateManageAPI, { CertificateVO, CertificateQuery } from '@/api/certificateManage';
  import CertificateDialog from './components/CertificateDialog.vue';
  
  const loading = ref(false);
  const total = ref(0);
  const certificateList = ref<CertificateVO[]>([]);
  const queryFormRef = ref<FormInstance>();
  const ids = ref<string[]>([]);
  
  const queryParams = reactive<CertificateQuery>({
    pageNum: 1,
    pageSize: 10,
    productName: '',
    serialNumber: '',
    certType: '',
    userName: '',
  });
  
  const viewDialog = reactive({
    visible: false,
    certificate: {} as CertificateVO
  });
  
  const dialog = reactive({
    title: '',
    visible: false,
    id: undefined as string | undefined,
  });
  
  // 格式化日期
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return format(new Date(dateString), 'yyyy-MM-dd');
    } catch (error) {
      return dateString;
    }
  };
  const formatDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return '-';
    try {
      return format(new Date(dateTimeString), 'yyyy-MM-dd HH:mm:ss');
    } catch (error) {
      return dateTimeString;
    }
  };
  
  // 查询
  const handleQuery = () => {
    loading.value = true;
    CertificateManageAPI.getCertificateList(queryParams)
      .then(res => {
        certificateList.value = res.list || [];
        total.value = res.total || 0;
      })
      .catch(() => {
        ElMessage.error('获取证书列表失败');
      })
      .finally(() => {
        loading.value = false;
      });
  };
  const handleReset = () => {
    queryFormRef.value?.resetFields();
    Object.keys(queryParams).forEach((key) => {
      if (key !== 'pageNum' && key !== 'pageSize') {
        queryParams[key] = '';
      }
    });
    queryParams.pageNum = 1;
    queryParams.pageSize = 10;
    handleQuery();
  };
  const handleSelectionChange = (selection: CertificateVO[]) => {
    ids.value = selection.map(item => item.id);
  };
  const viewCertificate = (row: CertificateVO) => {
    viewDialog.certificate = { ...row };
    viewDialog.visible = true;
  };
  const openCreateDialog = () => {
    dialog.visible = true;
    dialog.id = undefined;
    dialog.title = '生成证书';
  };
  const editCertificate = (row: CertificateVO) => {
    dialog.visible = true;
    dialog.id = row.id;
    dialog.title = '编辑证书';
  };
  const downloadCertificate = (row: CertificateVO) => {
    CertificateManageAPI.downloadCertificate(row.id).then((response: any) => {
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'client-cert.p12');
      document.body.appendChild(link);
      link.click();

      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    });
  };
  const deleteCertificate = (row: CertificateVO) => {
    ElMessageBox.confirm(`确认删除证书【${row.productName}】?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(() => {
      loading.value = true;
      CertificateManageAPI.deleteCertificate(row.id)
        .then(() => {
          ElMessage.success('删除成功');
          handleQuery();
        })
        .catch(() => {
          ElMessage.error('删除失败');
        })
        .finally(() => {
          loading.value = false;
        });
    });
  };
  const handleSizeChange = (size: number) => {
    queryParams.pageSize = size;
    handleQuery();
  };
  const handleCurrentChange = (page: number) => {
    queryParams.pageNum = page;
    handleQuery();
  };
  
  onMounted(() => {
    handleQuery();
  });
  </script>
  
  <style scoped>
  .app-container {
    padding: 20px;
  }
  .search-container {
    margin-bottom: 20px;
  }
  .table-container {
    margin-bottom: 20px;
  }
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  .flex-x-between {
    display: flex;
    justify-content: space-between;
  }
  .modules-section {
    margin-top: 20px;
  }
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
  }
  .modules-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }
  .module-item {
    margin-right: 0;
    padding: 6px 12px;
    font-size: 14px;
  }
  </style>
