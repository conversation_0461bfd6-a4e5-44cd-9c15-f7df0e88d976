<template>
  <div class="initiate-ticket">
    <div class="page-header">
      <h3 >发起工单</h3>
    </div>

    <el-form 
      :model="form" 
      :rules="rules" 
      ref="dataFormRef" 
      label-width="100px"
      class="form-container"
    >
      <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label-width="60px" label="工号：" prop="employeeId">
              <el-input v-model="form.employeeId" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人：" label-width="80px" prop="applicantName">
              <el-input v-model="form.applicantName" disabled/>
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label-width="100px" label="所在单位：" prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                    code="dept0x0"
                    v-model="form.deptId"
                    
                  />
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="100px" label="联系方式：" prop="mobile">
              <el-input
                v-model="form.mobile"
              ></el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="创建时间：" prop="createTime">
              <el-date-picker 
                v-model="form.createTime" 
                type="datetime" 
                placeholder="选择时间"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item label="截止日期：" prop="deadline">
            <el-date-picker 
              v-model="form.deadline" 
              type="datetime" 
              placeholder="选择截止时间"
              style="width: 100%"
              :min-date="form.deadline"
            />
          </el-form-item>
        </el-col>
      </el-row>
      </el-card>
        <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>事件对象</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="信息系统名称：" label-width="120px" prop="sysname">
              <el-input v-model="form.sysname" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="域名/IP：" prop="domainIp">
              <el-input v-model="form.domainIp" />
            </el-form-item>
          </el-col>
        </el-row>
        </el-card>
         <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>责任单位</span>
          </div>
        </template>
        <el-row :gutter="24">
         <el-col :span="8">
            
            <el-form-item label="部门名称：">
          <el-tree-select v-model="shstep.deptId" placeholder="请选择所属部门" :data="deptOptions" filterable
            check-strictly :render-after-expand="false"  @change="handleDeptSelectChange"
            :props="{ value: 'value', label: 'label', children: 'children' }" />
        </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item label="联系人：">
              <!-- 树形人员选择器 -->
              <el-select v-model="shstep.id" @change="handleuserSelectChange" clearable>
                <el-option v-for="item in personOptions" :key="item.id"  :value="item.id" :label="item.nickname">
                    <span>{{item.nickname}}--<span v-for="(itemc,index) in item.dutytext" :key="index">{{dutyCache[itemc]}}{{index<item.dutytext.length-1?"，":''}}</span>（--{{item.deptName}}）</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="8">
             <el-form-item label="联系方式：">
              <el-input
                v-model="shstep.mobile"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          </el-row>
         </el-card>
           <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>事件信息</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="8">
             <el-form-item label="事件名称：" prop="name">
              <el-input v-model="form.name" />
            </el-form-item>
           </el-col>
           <el-col :span="8">
           <el-form-item label="事件来源：" prop="incidentSource">
              <Dictionary
                v-model="form.incidentSource"
                code="incidentsResouce"
                
              ></Dictionary>
            </el-form-item>
           </el-col>
           <el-col :span="8">
            <el-form-item label="事件类型：" prop="incidentType">
              <Dictionary
                v-model="form.incidentType"
                code="incidentsType"
                
              ></Dictionary>
            </el-form-item>
          </el-col>
            <el-col :span="24">
           <el-form-item label="事件描述：" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="3"  />
            </el-form-item>
           </el-col>
        </el-row>
           </el-card>
            <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>网络安全应急工程师</span>
          </div>
        </template>
       <!-- 安全工程师信息开始 -->
        <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
        <div style="display:flex; justify-content: space-between;">
              <el-form-item label-width="110px" label="工程师名称："   prop="engineerName" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerName"  ></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input   v-model="SafetyEngineerbox.engineerMobile"  > </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerWechat"  ></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerQq"  ></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="80px" prop="engineerEmail" style="width:20%">
              <el-input  v-model="SafetyEngineerbox.engineerEmail"  ></el-input>
            </el-form-item>
        </div>
        </el-form>
        <el-col :span="24">
          <!-- 文件上传 -->
        <el-form-item
          label="安全事件报告："
          label-width="110px"
          prop="fileList"
          v-if="showStep"
        >
          <file-upload
            uploadBtnText="上传安全事件报告"
            :upload-max-size="20 * 1024 * 1024"
            v-model="fileList"
            :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'"
            :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'"
          >
          </file-upload>
          <div>仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
        </el-form-item>
          </el-col>
         <!-- 添加已上传文件展示 -->
        <!-- <el-row :gutter="20" v-if="fileList && fileList.length">
          <el-col :span="24">
            <el-form-item label="已上传文件">
              <div class="file-list">
                <el-tag 
                  v-for="file in fileList"
                  :key="file.id"
                  class="file-item"
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row> -->
        <!-- 安全工程师信息结束 -->
            </el-card>
      <!-- 工作流程卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工作流程</span>
          </div>
        </template>
          <process-group  :shstep="shstep"  ref="processGroupRef" @validation-change="handleValidationChange" />
      </el-card>
      <!-- 操作按钮 -->
      <div class="form-actions" >
        <el-button type="primary" @click="submitForm" size="large">
          <el-icon><check /></el-icon>提交工单
        </el-button>
        <el-button @click="resetForm" size="large">
          <el-icon><refresh /></el-icon>重置
        </el-button>
         <el-button type="info" @click="concelForm" size="large">取消</el-button>
      </div>
    </el-form>

    <!-- 弹窗组件 -->
    <assetsDialog
      v-model:visible="dialog.visible" 
      :title="dialog.title" 
      :id="dialog.id"
      @submitted="handleQuery"
    />
    <!-- <select-assets
      v-model:visible="transferDialog.visible"  
      :title="'选择关联资产'"
      :selected-assets="transferDialog.selectedAssets"
      @selected="handleAssetsSelected"
    /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import safetyIncidentAPI from '@/api/work_management/online_service/new_index'
import assetsAPI from "@/api/assets_management/details/assets"
import UserAPI from "@/api/user";
import safetyAPI from "@/api/work_management/safety";
import assetsDialog from '@/views/assets_management/DetailOfAssets/details/components/assetsDialog.vue'
import SelectAssets from '@/components/AssetsManage/SelectAssets.vue'
import ProcessGroup from '@/components/ProcessGroup/safeProcessIncident.vue'
import {formatLocalDateTime} from "@/utils/dateUtils";
import { fileUtils } from '@/utils/fileUtils';
import { useRoute } from 'vue-router' 
import DeptAPI from "@/api/dept";
import { useDictStore } from '@/store/modules/dictStore'
const dictStore = useDictStore()

const dataFormRef = ref<FormInstance | null>(null)
const emit = defineEmits(['next'])
const route = useRoute() 
interface TicketData {
  id: number
  currentStep: string
  isClick: boolean
}

const props = defineProps<{
  ticketdata: TicketData
  ticketType?: string
}>()

const getTicketType = () => {
  
  if (props.ticketType) {
    return props.ticketType
  }
  
  // 其次使用路由参数
  const routeType = route.query.type as string
  if (routeType) {
    switch (routeType) {
      case 'safetyDetails':
        return 'emergency'
      case 'evaluateDetails':
        return 'security'
      case 'onlineDetails':
        return 'business'
      default:
        return 'business'
    }
  }
  
  return 'business' // 默认值
}
const loadDutyCache = async () => {
      // console.log('加载DUTY字典缓存')
      const options = await dictStore.fetchOptions('DUTY')
      // console.log('加载DUTY字典选项:', options)
      options.forEach(item => {
        dutyCache.value[item.value] = item.label
      })
      
}
// 部门
const deptOptions = ref<any>([])
const isExecutionList = ref(true)
// 人员
const dutyCache = ref<Record<string, string>>({}) 
const personOptions = ref<any[]>([])
const shstep=ref({})
// 处理部门选择变更
const handleDeptSelectChange = async (value: number,id) => {
  console.log("--------",value)
  shstep.value.id=''
  const response = await UserAPI.getPage({
      deptId:value,
      pageNum: 1,
      pageSize: 100
    })
    personOptions.value=JSON.parse(JSON.stringify(response.list)) 
    personOptions.value.forEach((item)=>{
      let myduty=item.duty && item.duty!=''? JSON.parse(JSON.stringify( item.duty)):''

        item.dutytext=myduty  && myduty!=''?myduty.split(","):''
    })
    loadDutyCache()
    if(id){
      handleuserSelectChange(id)
    }

}
const handleuserSelectChange  = (value) => {
  console.log( personOptions.value)
  const foundItem = personOptions.value.find(item => item.id == value);
  shstep.value=JSON.parse(JSON.stringify(foundItem))
  console.log(foundItem)

}
// 安全工程师
const rules2=reactive({
  engineerName: [{ required: true, message: "请输入工程师名称", trigger: "blur" }],
});
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);

// 新增安全工程师
const saveSafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.saveSafetyEngineerConfig(SafetyEngineerbox.value)
}
// 修改安全工程师
const updateSafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.updateSafetyEngineerConfig(SafetyEngineerbox.value.id,SafetyEngineerbox.value)
}
// 获取安全工程师信息
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}

// 映射流程数据
const getProcessData = async (data) => {
  console.log("映射流程数据:", data);
  try {
    // 检查是否有流程数据
    if (!data.reviewProcessForms || !Array.isArray(data.reviewProcessForms)) {
      console.log("未找到有效的流程数据");
      return;
    }

    console.log("原始流程数据:", data.reviewProcessForms);

    // 准备执行和通知列表数据
    const execList = [];
    const notifyList = [];

    // 准备人员数据的映射
    const execPersonsMap = {};
    const notifyPersonsMap = {};

    // 遍历流程数据
    for (const process of data.reviewProcessForms) {
      // 确定是执行部门还是通知部门
      const isExecution = process.executeType === "1";

      // 映射逆向转换 (2->1, 3->2, 4->3, 5->4)
      const deptTypeReverseMap = { "2": 1, "3": 2, "4": 3, "5": 4 };
      const deptTypeNum =
        deptTypeReverseMap[process.executeDeptType] ||
        Number(process.executeDeptType);

      // 解析逗号分隔的用户ID列表
      const userIdList = process.userId
        ? process.userId.split(",").map((id) => id.trim())
        : [];

      // 获取部门名称
      let deptName = "";
      try {
        const deptOptions = await dictStore.fetchOptions("dept0x0");
        const findDeptName = (options: any[], id: number): string => {
          for (const option of options) {
            if (option.value === id) {
              return option.label;
            }
            if (option.children && option.children.length > 0) {
              const found = findDeptName(option.children, id);
              if (found) return found;
            }
          }
          return "";
        };
        deptName =
          findDeptName(deptOptions, process.deptId) || `${process.deptName}`;
      } catch (error) {
        deptName = `${process.deptName}`;
      }

      // 创建基础项目
      const baseItem = {
        id: process.id || 0,
        departmentId: deptTypeNum,
        selectedDeptId: process.deptId || 0,
        deptName: deptName,
        personName: "", // 稍后设置
        enableSms: process.enableSms === "1",
        smsTemplateId: process.smsTemplateId
          ? Number(process.smsTemplateId)
          : undefined,
        smsContent: "",
        notifyType: process.notifyType || "once",
        notifyPeriod: process.notifyPeriod || "daily",
        userIds: userIdList,
      };

      // 获取用户详细信息
      const userInfoArray = [];
      if (userIdList.length > 0) {
        for (const userId of userIdList) {
          try {
            const userResponse = await UserAPI.getFormData(Number(userId));
            if (userResponse) {
              const userInfo = {
                id: userResponse.id || userId,
                username: userResponse.username || "",
                nickname: userResponse.nickname || `用户${userId}`,
                deptName: deptName,
                mobile: userResponse.mobile || "",
              };
              userInfoArray.push(userInfo);
            }
          } catch (error) {
            console.warn(`获取用户详情失败 ID:${userId}`, error);
            // 添加基本用户信息作为备选
            userInfoArray.push({
              id: userId,
              username: "",
              nickname: `用户${userId}`,
              deptName: deptName,
              mobile: "",
            });
          }
        }
      }

      // 设置人员名称
      baseItem.personName =
        userInfoArray.map((u) => u.nickname).join("、") || "未设置";

      // 根据类型添加到不同列表
      if (isExecution) {
        // 检查执行列表是否已有相同部门类型的项
        const existingIndex = execList.findIndex(
          (item) => item.departmentId === deptTypeNum
        );
        if (existingIndex === -1) {
          execList.push(baseItem);
        }

        // 添加用户到执行人员映射
        if (userInfoArray.length > 0) {
          execPersonsMap[deptTypeNum] = userInfoArray;
        }
      } else {
        // 检查通知列表是否已有相同部门类型的项
        const existingIndex = notifyList.findIndex(
          (item) => item.departmentId === deptTypeNum
        );
        if (existingIndex === -1) {
          notifyList.push(baseItem);
        }

        // 添加用户到通知人员映射
        if (userInfoArray.length > 0) {
          notifyPersonsMap[deptTypeNum] = userInfoArray;
        }
      }
    }

    // 更新表单数据
    form.executionList = execList;
    form.notificationList = notifyList;
    form.executionPersons = execPersonsMap;
    form.notificationPersons = notifyPersonsMap;

    console.log("映射完成的数据:", {
      executionList: execList,
      notificationList: notifyList,
      executionPersons: execPersonsMap,
      notificationPersons: notifyPersonsMap,
    });

    // 初始化流程组件数据
    await nextTick();
    if (processGroupRef.value) {
      processGroupRef.value.initFromExistingData({
        executionList: execList,
        notificationList: notifyList,
        executionPersons: execPersonsMap,
        notificationPersons: notifyPersonsMap,
      });

      // 重要：如果整改部门已配置，需要重新触发自动配置以确保界面正确显示
      const remediationDeptConfig = execPersonsMap[2]; // 整改部门对应索引2
      if (
        remediationDeptConfig &&
        remediationDeptConfig.length > 0 &&
        sleectedAssetsData.value.length > 0
      ) {
        console.log("重新触发整改部门自动配置");
        // 延迟一下确保组件完全初始化
        setTimeout(() => {
          autoConfigProcessGroup(sleectedAssetsData.value[0]);
        }, 200);
      }
    }
  } catch (error) {
    console.error("流程数据映射出错:", error);
  }
};

// 自动配置流程组件中的整改部门
const autoConfigProcessGroup = async (asset: any) => {
  if (!processGroupRef.value) {
    console.warn("流程组件引用不存在");
    return;
  }

  try {
    // 获取资产的管理部门和管理员信息
    const assetDeptId = asset.deptId;
    const assetManagerName = asset.ownerName || asset.managerName;
    let assetDeptName = asset.deptName;

    console.log("自动配置流程组件:", {
      assetDeptId,
      assetManagerName,
      assetDeptName,
      asset,
    });

    // 确保有必要的信息
    // if (!assetDeptId || !assetManagerName) {
    //   ElMessage.warning("资产缺少必要的部门或管理员信息，无法自动配置");
    //   return;
    // }

    // 直接调用流程组件的自动配置方法（强制重新配置）
    await processGroupRef.value.autoConfigRemediationDept({
      deptId: assetDeptId,
      deptName: assetDeptName || `部门${assetDeptId}`,
      managerName: assetManagerName,
      asset: asset,
      forceUpdate: true, // 强制更新标志
    });

    // 等待DOM更新
    await nextTick();

    // 延迟验证以确保数据完全同步
    setTimeout(() => {
      const validation = processGroupRef.value.getValidationStatus();
      console.log("自动配置后的验证状态:", validation);
    }, 500);
  } catch (error) {
    console.error("自动配置流程组件失败:", error);
    // ElMessage.error("自动配置整改部门失败");
  }
};
// 表单数据
const form = reactive({
  createTime: formatLocalDateTime(new Date()),
  name: "",
  ticketType: getTicketType(),
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  id: 0,
  assetIds: [] as number[],
  fileIds: [] as number[],
  fileList: [] as any[],
    deadline: (() => {
    const date = new Date();
    date.setDate(date.getDate() + 3); // 设置为3天后
    return date;
  })(),
})

// 新增资产弹窗
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined,
})

// 选择资产弹窗状态
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

// 已选资产数据
const sleectedAssetsData = ref<any[]>([])

// 文件列表
const fileList = ref([] as any[]);

// 计算已选择的关联资产数据
const assetsData = computed(() => {
  return sleectedAssetsData.value.filter(asset => 
    transferDialog.selectedAssets.includes(asset.id)
  )
})

// 打开资产选择弹窗
const openTransferDialog = () => {
  transferDialog.visible = true
}

// 处理资产选择结果
const handleAssetsSelected = ({ selectedIds, selectedAssets }: any) => {
  transferDialog.selectedAssets = selectedIds
  sleectedAssetsData.value = selectedAssets
}

// 其他原有代码保持不变...
const currentStep = ref(props.ticketdata.currentStep)
const showStep = ref(true)
const nowStep = ref('')
const stepStatus = ref<any | null>(null)
// 添加流程组件引用
const processGroupRef = ref()

// 流程验证状态
const processValidationStatus = ref<{valid: boolean; missingSteps: any[]} | null>(null);

// 是否为当前步骤
function isCurrentStep() {
 
  if (currentStep.value == nowStep.value) {
    showStep.value = true
  } else {
    showStep.value = false
  }
}

// 表单验证规则
const rules = {
  reason: [
    { required: false, message: '请填写上线原因', trigger: 'blur' }
  ],
  createTime: [
    { required: true, message: '请选择创建时间', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入上线业务名称', trigger: 'change' }
  ],
}

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 处理验证状态变化
const handleValidationChange = (status: any) => {
  processValidationStatus.value = status;
};


// 检查流程配置
const checkProcessConfiguration = () => {
  if (processGroupRef.value) {
    processValidationStatus.value = processGroupRef.value.getValidationStatus();
    return processValidationStatus.value?.valid;
  }
  return false;
};

// 生成工单编号
const generateTimeBasedId = () => {
  const now = new Date()
  return `${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`
}

// 提交表单
const submitForm = async () => {
  // 判断是否是新增
     if(SafetyEngineerbox.value.id){
      updateSafetyEngineerConfig()
      form.engineerId=SafetyEngineerbox.value.id
      form.engineerName=SafetyEngineerbox.value.engineerName
      form.engineerMobile=SafetyEngineerbox.value.engineerMobile
      form.engineerWechat=SafetyEngineerbox.value.engineerWechat
      form.engineerQq=SafetyEngineerbox.value.engineerQq
      form.engineerEmail=SafetyEngineerbox.value.engineerEmail
    }else{
      if(SafetyEngineerbox.value.engineerName!=''){
         saveSafetyEngineerConfig()
         form.engineerName=SafetyEngineerbox.value.engineerName
        form.engineerMobile=SafetyEngineerbox.value.engineerMobile
        form.engineerWechat=SafetyEngineerbox.value.engineerWechat
        form.engineerQq=SafetyEngineerbox.value.engineerQq
        form.engineerEmail=SafetyEngineerbox.value.engineerEmail
      }
  }
  console.log(form)
  // 检查流程配置
  if (!checkProcessConfiguration()) {
    const missingStepNames = processValidationStatus.value?.missingSteps
      .map(step => step.name)
      .join('、');
    
    ElMessage.error(`请配置流程步骤: ${missingStepNames}`);
    return;
  }
  
  const processData = processGroupRef.value?.getProcessData();
  
  await ElMessageBox.confirm(
    '确定要提交申请工单吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  
  try {
    // 准备表单数据
      form.deadline = formatLocalDateTime(
        new Date(form.deadline)
      );
    const formData = {
      ...form,
      reviewProcessForms: convertToReviewProcessForms(processData),
      fileIds: fileUtils.extractFileIds(fileList.value),
      assetIds: transferDialog.selectedAssets,
    };
    await safetyIncidentAPI.add(formData);
    ElMessage.success('已提交');
    emit('next');
  } catch (error) {
    console.error('Error submitting ticket:', error);
    ElMessage.error('提交失败，请重试');
  }
}

// 将前端流程数据转换为后端需要的格式
const convertToReviewProcessForms = (processData) => {
  const result = [];
  const now = formatLocalDateTime(new Date());
  
  try {
    // 处理执行列表
    if (processData.executionList && processData.executionList.length > 0) {
      processData.executionList.forEach(item => {
        const departmentId = item.departmentId; // 流程步骤 ID
        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5, 5->6)
        const deptTypeMap = {1: '2', 2: '3', 3: '4', 4: '5', 5: '6'};
        const deptType = deptTypeMap[departmentId] || departmentId.toString(); 
        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.executionPersons?.[departmentId] || [];
        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map(person => person.id).join(',');
          result.push({
            id: null, // 使用原有ID或0
            businessType: "business", // 业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-6
            executeType: "1", // 执行类型: 1执行 
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }
    
    // 处理通知列表
    if (processData.notificationList && processData.notificationList.length > 0) {
      processData.notificationList.forEach(item => {
        const departmentId = item.departmentId; // 流程步骤 ID
        
        // 转换部门类型映射 (1->2, 2->3, 3->4, 4->5, 5->6)
        const deptTypeMap = {1: '2', 2: '3', 3: '4', 4: '5', 5: '6'};
        const deptType = deptTypeMap[departmentId] || departmentId.toString(); 
        
        const selectedDeptId = item.selectedDeptId || 0; // 选择的部门ID
        const persons = processData.notificationPersons?.[departmentId] || [];
        
        // 只有选择了部门和人员才进行提交
        if (selectedDeptId && persons.length > 0) {
          // 将多个用户ID合并为逗号分隔的字符串
          const userIds = persons.map(person => person.id).join(',');
          
          result.push({
            id: null, 
            businessType: "business", // 业务类型
            businessId: form.id ? Number(form.id) : 0,
            executeDeptType: deptType, // 部门类型: 2-6
            executeType: "2", // 执行类型: 2通知
            deptId: selectedDeptId, // 选择的具体部门ID
            userId: userIds, // 多个用户ID以逗号分隔
            enableSms: item.enableSms ? "1" : "0",
            smsTemplateId: item.smsTemplateId || 0,
            notifyType: item.notifyType || "once",
            notifyPeriod: item.notifyPeriod || "daily",
            createTime: now,
            updateTime: now
          });
        }
      });
    }
    return result;
  } catch (error) {
    console.error('转换流程数据出错:', error);
    return [];
  }
}

const downloadFile = (row: any) => {
  const fileUrl = row.url ? row.url : null;
  if (fileUrl) {
    window.open(fileUrl, '_blank');
  } else {
    ElMessage.error('附件不存在');
  }
}
// 取消按钮
const concelForm=()=>{
emit('next')
}
// 重置表单
const resetForm = () => {
  if (dataFormRef.value) {
    dataFormRef.value.resetFields()
  }
  transferDialog.selectedAssets = []
  sleectedAssetsData.value = []
  
  // 重置流程组件
  processGroupRef.value?.reset()
}

  // 加载流程数据到组件
  const loadProcessData = (flowData) => {
  try {
    // 准备执行和通知列表数据
    const execList = [];
    const notifyList = [];
    
    // 准备人员数据的映射
    const execPersonsMap = {};
    const notifyPersonsMap = {};
    
    // 遍历流程数据
    flowData.forEach(process => {
      // 确定是执行部门还是通知部门
      const isExecution = process.executeType === '1';
      
      // 映射逆向转换 (2->1, 3->2, 4->3, 5->4)
      const deptTypeReverseMap = {'2': 1, '3': 2, '4': 3, '5': 4};
      const deptTypeNum = deptTypeReverseMap[process.executeDeptType] || Number(process.executeDeptType);
      
      // 创建基础项目
      const baseItem = {
        id: process.id || 0,
        departmentId: deptTypeNum,
        selectedDeptId: process.deptId || 0,
        deptName: '',  // 在组件中处理
        personName: '', // 在组件中处理
        enableSms: process.enableSms === '1',
        smsTemplateId: process.smsTemplateId ? Number(process.smsTemplateId) : undefined,
        smsContent: '',
        notifyType: process.notifyType || 'once',
        notifyPeriod: process.notifyPeriod || 'daily',
        userIds: []
      };
      
      // 解析逗号分隔的用户ID列表
      const userIdList = process.userId ? process.userId.split(',').map(id => id.trim()) : [];
      
      // 根据类型添加到不同列表
      if (isExecution) {
        // 检查执行列表是否已有相同部门类型的项
        const existingIndex = execList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          execList.push(baseItem);
        }
        
        // 添加用户到执行人员映射
        if (userIdList.length > 0) {
          if (!execPersonsMap[deptTypeNum]) {
            execPersonsMap[deptTypeNum] = [];
          }
          
          // 处理每个用户ID
          userIdList.forEach(userId => {
            // 基本用户信息，其他信息将由组件负责加载
            const userInfo = {
              id: userId,
              username: '', // 组件中处理
              nickname: '', // 组件中处理
              deptName: '', // 组件中处理
              mobile: ''    // 组件中处理
            };
            
            // 确保不重复添加用户
            const exists = execPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              execPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      } else {
        // 通知列表处理
        const existingIndex = notifyList.findIndex(item => item.departmentId === deptTypeNum);
        if (existingIndex === -1) {
          notifyList.push(baseItem);
        }
        
        if (userIdList.length > 0) {
          if (!notifyPersonsMap[deptTypeNum]) {
            notifyPersonsMap[deptTypeNum] = [];
          }
          
          userIdList.forEach(userId => {
            const userInfo = {
              id: userId,
              username: '',
              nickname: '',
              deptName: '',
              mobile: ''
            };
            
            const exists = notifyPersonsMap[deptTypeNum].some(p => p.id === userId);
            if (!exists) {
              notifyPersonsMap[deptTypeNum].push(userInfo);
            }
          });
        }
      }
    });
    
    // 初始化流程组件数据
    nextTick(() => {
      if (processGroupRef.value) {
        processGroupRef.value.initFromExistingData({
          executionList: execList,
          notificationList: notifyList,
          executionPersons: execPersonsMap,
          notificationPersons: notifyPersonsMap
        });
      }
    });
  } catch (error) {
    console.error('流程数据映射出错:', error);
  }
}
// 获取列表传过来的值
const ID = ref(props.ticketdata ? props.ticketdata.id : null);
//获取当前用户信息 填充name,employeeId,department,applicant
const getUserInfo = async () => {
  const data = await UserAPI.getProfile();
  form.applicantId = data.id;
  form.applicantName = data.nickname;
  form.employeeId = data.username;
  form.deptId = data.deptId;
  form.mobile = data.mobile || "";
};

const steparr={
  1:"InitiateTicket",
  2:"AssetsAudit",
  3:"AssetsFix",
  4:"FixVerification",
  5:"fixEvaluation",
  6:"CloseTicket"
}
// 加载数据
const handleQuery = async () => {
  // 获取资产列表
  const { list } = await assetsAPI.getPage({ pageNum: 1, pageSize: 999 })
  sleectedAssetsData.value = list
  if (props.ticketdata.id) {
    // 获取步骤状态
    // const statusRes: any = await safetyIncidentAPI.getStepStatus(props.ticketdata.id)
    // stepStatus.value = statusRes
    // for (const step in stepStatus.value) {
    //   if (stepStatus.value[step] == 'process') {
    //     nowStep.value = step
    //     break
    //   }
    // }
    currentStep.value=''
    // if (!showStep.value) {
      const data = await safetyIncidentAPI.getFormData(props.ticketdata.id)
      Object.assign(form, data)
        const foundItem = form.reviewProcessForms?.find(item => item.executeDeptType == 3);
       handleDeptSelectChange(foundItem.deptId,foundItem.userId)
        nowStep.value=steparr[form.step]
          isCurrentStep()
      if (form.fileList && Array.isArray(form.fileList)) {
            fileList.value = form.fileList;
          }
      // 加载流程数据
      try {
        const flowData = await safetyIncidentAPI.getFlow(props.ticketdata.id);
        if (flowData && Array.isArray(flowData)) {
          loadProcessData(flowData);
        }
      } catch (error) {
        console.error('加载流程数据失败:', error);
      }
          await nextTick();
    await getProcessData(form); // 映射已有的流程数据
    // }
  } else {
    // 新建模式：应用默认配置
    await nextTick();
    if (processGroupRef.value) {
      console.log('新建工单，应用默认配置');
      try {
        // 调用流程组件的应用默认配置方法
        await processGroupRef.value.applyDefaultConfig();
        console.log('默认配置应用成功');
      } catch (error) {
        console.error('应用默认配置失败:', error);
      }
    }
    
  //  const foundItem = processGroupRef.value?.find(item => item.executeDeptType ==4);
  // shstep.value=foundItem?JSON.parse(JSON.stringify(foundItem)):{}
  }

  if (!form.id) {
    form.id = Number(generateTimeBasedId().slice(0, -1))
  }
  
}

onMounted(() => {
   DeptAPI.getOptions().then((data) => {
    deptOptions.value = data
  })
  handleQuery()
  getUserInfo()
  SafetyEngineerConfig()
})
</script>

<style scoped>

/* 在 style 部分添加 */
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.initiate-ticket {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h3 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin: 0;
}

.form-container {
  /* max-width: 1200px; */
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

.asset-buttons {
  display: flex;
  gap: 12px;
}

.asset-buttons .el-button {
  min-width: 120px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}

/* 输入框样式统一 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 卡片内容区域padding */
:deep(.el-card__body) {
  padding: 20px;
}
:deep(.el-form-item) {
  margin-right: 0;
}
</style>
