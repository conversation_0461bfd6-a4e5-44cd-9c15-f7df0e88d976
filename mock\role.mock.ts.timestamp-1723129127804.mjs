// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/role.mock.ts
var role_mock_default = defineMock([
  {
    url: "roles/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 2,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
        },
        {
          value: 4,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54581"
        },
        {
          value: 5,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54582"
        },
        {
          value: 6,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54583"
        },
        {
          value: 7,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54584"
        },
        {
          value: 8,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54585"
        },
        {
          value: 9,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54586"
        },
        {
          value: 10,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54587"
        },
        {
          value: 11,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54588"
        },
        {
          value: 12,
          label: "\u7CFB\u7EDF\u7BA1\u7406\u54589"
        },
        {
          value: 3,
          label: "\u8BBF\u95EE\u6E38\u5BA2"
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "roles/page",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        list: [
          {
            id: 2,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
            code: "ADMIN",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 3,
            name: "\u8BBF\u95EE\u6E38\u5BA2",
            code: "GUEST",
            status: 1,
            sort: 3,
            createTime: "2021-05-26 15:49:05",
            updateTime: "2019-05-05 16:00:00"
          },
          {
            id: 4,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54581",
            code: "ADMIN1",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 5,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54582",
            code: "ADMIN2",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 6,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54583",
            code: "ADMIN3",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 7,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54584",
            code: "ADMIN4",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 8,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54585",
            code: "ADMIN5",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 9,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54586",
            code: "ADMIN6",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: "2023-12-04 11:43:15"
          },
          {
            id: 10,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54587",
            code: "ADMIN7",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          },
          {
            id: 11,
            name: "\u7CFB\u7EDF\u7BA1\u7406\u54588",
            code: "ADMIN8",
            status: 1,
            sort: 2,
            createTime: "2021-03-25 12:39:54",
            updateTime: null
          }
        ],
        total: 10
      },
      msg: "\u4E00\u5207ok"
    }
  },
  // 新增角色
  {
    url: "roles",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u65B0\u589E\u89D2\u8272" + body.name + "\u6210\u529F"
      };
    }
  },
  // 获取角色表单数据
  {
    url: "roles/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: roleMap[params.id],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 修改角色
  {
    url: "roles/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u4FEE\u6539\u89D2\u8272" + body.name + "\u6210\u529F"
      };
    }
  },
  // 删除角色
  {
    url: "roles/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "\u5220\u9664\u89D2\u8272" + params.id + "\u6210\u529F"
      };
    }
  },
  // 获取角色拥有的菜单ID
  {
    url: "roles/:id/menuIds",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: [
          1,
          2,
          31,
          32,
          33,
          88,
          3,
          70,
          71,
          72,
          4,
          73,
          75,
          74,
          5,
          76,
          77,
          78,
          6,
          79,
          81,
          84,
          85,
          86,
          87,
          40,
          41,
          26,
          30,
          20,
          21,
          22,
          23,
          24,
          89,
          90,
          91,
          36,
          37,
          38,
          39,
          93,
          94,
          95,
          97,
          102,
          89,
          90,
          91,
          93,
          94,
          95,
          97,
          102,
          103,
          104
        ],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 保存角色菜单
  {
    url: "roles/:id/menus",
    method: ["PUT"],
    body: {
      code: "00000",
      data: null,
      msg: "\u4E00\u5207ok"
    }
  }
]);
var roleMap = {
  2: {
    id: 2,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
    code: "ADMIN",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  3: {
    id: 3,
    name: "\u8BBF\u95EE\u6E38\u5BA2",
    code: "GUEST",
    status: 1,
    sort: 3,
    createTime: "2021-05-26 15:49:05",
    updateTime: "2019-05-05 16:00:00"
  },
  4: {
    id: 4,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54581",
    code: "ADMIN1",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  5: {
    id: 5,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54582",
    code: "ADMIN2",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  6: {
    id: 6,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54583",
    code: "ADMIN3",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  7: {
    id: 7,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54584",
    code: "ADMIN4",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  8: {
    id: 8,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54585",
    code: "ADMIN5",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  9: {
    id: 9,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54586",
    code: "ADMIN6",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: "2023-12-04 11:43:15"
  },
  10: {
    id: 10,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54587",
    code: "ADMIN7",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  },
  11: {
    id: 11,
    name: "\u7CFB\u7EDF\u7BA1\u7406\u54588",
    code: "ADMIN8",
    status: 1,
    sort: 2,
    createTime: "2021-03-25 12:39:54",
    updateTime: null
  }
};
export {
  role_mock_default as default
};
