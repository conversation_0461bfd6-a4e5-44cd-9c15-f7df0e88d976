<template>
  <div class="initiate-ticket">
    <h3 class="page-title">下线审核</h3>
    <el-form :model="form" :rules="rules" ref="dataFormRef" label-width="120px" class="offline-form">
      <!-- 基本信息区域 -->
      <div class="form-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下线业务id" prop="id">
              <el-input v-model="form.id" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicantName">
              <el-input v-model="form.applicantName" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式" prop="concat">
              <el-input v-model="form.concat" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下线业务名称" prop="name">
              <el-input v-model="form.name" disabled/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="下线原因" prop="reason">
              <el-input 
                v-model="form.reason" 
                type="textarea" 
                :rows="4"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker 
                disabled
                v-model="form.createTime" 
                type="datetime" 
                placeholder="选择时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 资产关联信息 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="关联资产">
              <el-button type="primary" plain @click="openTransferDialog">
                <i-ep-view />查看关联资产
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 审核信息区域 -->
      <div class="form-section">
        <h4 class="section-title">审核信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="审核结果" prop="commentType" :rules="[{ required: true, message: '请选择审核结果', trigger: 'change' }]">
              <el-select v-model="auditform.commentType" :disabled="!showStep" placeholder="请选择">
                <el-option label="通过" :value="1" />
                <el-option label="不通过" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核时间" prop="createTime">
              <el-date-picker 
                v-model="auditform.createTime" 
                type="datetime" 
                disabled
                placeholder="自动填充当前时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="审核备注" prop="commentContent" :rules="[{ required: true, message: '请输入审核备注', trigger: 'blur' }]">
              <el-input 
                v-model="auditform.commentContent" 
                :disabled="!showStep"
                type="textarea" 
                :rows="4"
                placeholder="请输入审核意见..."
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传审核附件" v-if="showStep">
              <file-upload 
                :upload-max-size="20 * 1024 * 1024" 
                v-model="auditFileList" 
                :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'" 
                :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'"
              />
              <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
            
            <!-- 已上传文件列表 -->
            <el-form-item label="审核附件" v-if="!showStep && auditFileList.length > 0">
              <div class="file-list">
                <el-tag 
                  v-for="file in auditFileList" 
                  :key="file.id" 
                  class="file-item" 
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <!-- 底部按钮区 -->
    <div class="form-actions" v-if="showStep">
      <el-button type="primary" @click="submitForm">
        <i-ep-check />提交审核
      </el-button>
      <el-button @click="resetForm">
        <i-ep-refresh />重置
      </el-button>
    </div>

    <!-- 关联资产弹窗 -->
    <el-dialog v-model="transferDialog.visible" title="关联资产" width="800px">
      <el-table :data="transferDialog.allAssets" border stripe>
        <el-table-column prop="name" label="资产名称" show-overflow-tooltip />
        <el-table-column align="center" label="资产状态" prop="status">
          <template #default="scope">
            <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
              {{ scope.row.status == '1' ? '正常' : (scope.row.status == '0' ? '异常' : '废弃') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="管理员" prop="ownerName" />
        <el-table-column label="管理部门" prop="deptId">
          <template #default="scope">
            <Dictmap v-model="scope.row.deptId" code="dept0x0" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import offlineAPI, { offlinePageVO, offlineForm, offlinePageQuery } from "@/api/assets_management/details/offline";
import businessAPI, { businessPageVO, businessForm, businessPageQuery } from '@/api/work_management/online_service/index'
import type { FormInstance } from 'element-plus'
import assetsAPI, { assetsPageVO, assetsForm, assetsPageQuery } from "@/api/assets_management/details/assets";
import UserAPI from "@/api/user";
import {formatLocalDateTime} from "@/utils/dateUtils";

const auditFileList = ref<any[]>([]) // 审核附件列表
const submitting = ref(false) // 提交状态控制
const emit = defineEmits(['next'])
interface TicketData {
  id: string;
  currentStep: string;
  isClick: boolean;
}

const props = defineProps<{
  ticketdata: TicketData,
}>();

// 添加新的响应式变量
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[],
  selectedAssets: [] as number[],
})

const form = reactive<any>({
  createTime: "",
  name: "",
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  applicantId: undefined as number | undefined,
  concat: '',
  remark: '', //状态
  assetsList: [], //关联资产列表
  id: 0,
})

const auditform = reactive({
  id: '', // Change type to string
  createTime: formatLocalDateTime(new Date()), // 自动填充当前时间
  commentContent: '',
  commentBy: 0,
  commentType: undefined as number | undefined,
  parentCode: '',
  step: 2,
  reviewLevel: '', // 审核级别
  reviewDept: '', // 审核部门 
  reviewer: '', // 审核人员
  reviews: [] as any[], // 添加 reviews 属性
})

const fixFileList = ref([] as any[]);

const currentStep = ref(props.ticketdata.currentStep);
const showStep = ref(true);
const nowStep = ref('');
const stepStatus = ref<any | null>(null);
// 是否为当前步骤
async function isCurrentStep() {
  console.log("12312312312",currentStep.value)
  console.log("12312312312",nowStep.value)
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
  console.log("showStep",showStep.value)
}


// 重置表单方法
const resetForm = () => {
  const dataFormRef = ref<FormInstance>()
  if (dataFormRef.value) {
    dataFormRef.value.resetFields()
  }
}

// 提交表单方法修改
const submitForm = async () => {
  if (auditform.commentType === undefined || auditform.commentType === null) {
    ElMessage.warning('请选择审核结果')
    return
  }
  
  if (!auditform.commentContent.trim()) {
    ElMessage.warning('请输入审核备注')
    return
  }

  try {
    submitting.value = true
    await ElMessageBox.confirm(
      '确定要提交审核吗？',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 获取当前用户信息
    const userData = await UserAPI.getProfile()
    form.handlerId = userData.id
    form.handler = userData.username
    
    // 将文件列表添加到审核表单
    auditform.fileList = auditFileList.value.map(file => file.id)
    
    // 提交表单和审核结果
    await offlineAPI.update(form.id, form)
    await offlineAPI.audit(auditform, form.id)
    
    ElMessage.success('审核已提交')
    emit('next')
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error submitting audit:', error)
      ElMessage.error(`提交失败: ${error.message}`)
    } else {
      console.error('Unknown error:', error)
      ElMessage.error('提交失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

//查看关联资产
const openTransferDialog = () => {
  transferDialog.visible = true;
  transferDialog.allAssets = form.assetsList;
}

// 附件下载
const downloadFile = (row: any) => {
  const fileUrl = row.url ? row.url : null; // 添加 null check
  if (fileUrl) {
    window.open(fileUrl, '_blank');
  } else {
    ElMessage.error('附件不存在');
  }
}

// 数据初始化修改
const handleQuery = async () => {
  if (props.ticketdata.id) {
    // 获取工单状态
    const statusRes: any = await offlineAPI.status(props.ticketdata.id)
    stepStatus.value = statusRes
    for (const step in stepStatus.value) {
      if (stepStatus.value[step as keyof any] == 'process') {
        nowStep.value = step as string
        break
      }
    }

    isCurrentStep()

    // 获取表单数据
    await offlineAPI.getFormData(props.ticketdata.id).then((data) => {
      Object.assign(form, data)
      // 设置当前审核人信息
      if (data.reviews && data.reviews.length > 0) {
        const currentReview = data.reviews.find((r: any) => r.step === auditform.step)
        if (currentReview) {
          auditform.reviewLevel = `${currentReview.level}级审核`
          auditform.reviewDept = currentReview.deptName
          auditform.reviewer = currentReview.reviewerName
        }
      }
    })

    // 如果是查看历史审核
    if (!showStep.value) {
      const filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 2) : []
      if (filteredComments.length > 0) {
        Object.assign(auditform, filteredComments[filteredComments.length - 1])
        
        // 尝试获取已上传的附件
        if (auditform.fileList && auditform.fileList.length > 0) {
          // 如果审核表单中包含文件列表
          auditFileList.value = Array.isArray(auditform.fileList) 
            ? [...auditform.fileList] 
            : []
        } else {
          // 尝试从API获取文件列表
          try {
            const files = await offlineAPI.getAuditFiles(props.ticketdata.id, auditform.id)
            if (files && Array.isArray(files)) {
              auditFileList.value = files
            }
          } catch (error) {
            console.error('获取审核文件列表失败:', error)
          }
        }
      }
    }
  }
}
// 添加验证规则
const rules = {
  reason: [
    { required: true, message: '请填写下线原因', trigger: 'blur' }
  ],
  createTime: [
    { required: true, message: '请选择创建时间', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入下线业务名称', trigger: 'change' }
  ],
}

onMounted(() => {
  handleQuery()
})

</script>

<style scoped>
.initiate-ticket {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.page-title {
  margin-bottom: 30px;
  color: var(--el-color-primary);
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.form-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: var(--el-bg-color-page);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  color: var(--el-text-color-primary);
  font-size: 18px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px 10px;
}

.file-item .el-icon {
  margin-right: 5px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
}


</style>
