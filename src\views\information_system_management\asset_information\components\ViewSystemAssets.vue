<template>
  <el-dialog v-model="props.visible" title="信息系统详情" width="75%" :before-close="handleClose">
    <el-tabs type="border-card">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息">
        <div class="detail-section">
          <div class="section-title">基础信息</div>

          <el-descriptions :column="2" border class="mb-6">
            <el-descriptions-item label="信息系统名称">
              {{ systemDetail.systemName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="信息系统版本">
              {{ systemDetail.systemVersion || '-' }}
            </el-descriptions-item>

            <el-descriptions-item label="域名/IP地址" :span="2">
              {{ systemDetail.webAddresses?.[0]?.url || '-' }}
            </el-descriptions-item>

            <el-descriptions-item label="主要功能描述" :span="2">
              {{ systemDetail.funcDes || '-' }}
            </el-descriptions-item>
          </el-descriptions>

          <el-col :span="24">
            <AddressList :modelValue="systemDetail.addressList" readonly />
          </el-col>


          <div class="section-title">其他信息</div>
          <el-descriptions :column="2" border class="mb-6">
            <el-descriptions-item label="开发框架">
              {{ systemDetail.framework || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="等保等级">
              <el-tag>{{ getSecurityLevelName(systemDetail.securityLevel) }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="是否对公网开放">
              {{ systemDetail.isOpen === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否接入统一认证">
              {{ systemDetail.useUnifiedAuth === '1' ? '是' : '否' }}
            </el-descriptions-item>

            <el-descriptions-item label="是否加入重保">
              {{ systemDetail.joinCritical === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="重保事件" v-if="systemDetail.joinCritical === '1'" :span="2">
              <el-tag v-for="eventId in systemDetail.eventsIds" :key="eventId" class="mr-1 mb-1" size="small">
                {{ getCriticalEventName(eventId) || `事件${eventId}` }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item label="备注" :span="2">
              {{ systemDetail.description || '-' }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="section-title">时间信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间">
              {{ formatDate(systemDetail.createTime) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="上线日期">
              {{ formatDate(systemDetail.onlineDate) || '-' }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="下线日期">
              {{ formatDate(systemDetail.offlineDate) || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="更新日期">
              {{ formatDate(systemDetail.updateDate) || '-' }}
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="上线负责人">
              {{ systemDetail.launchLeader || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="联系方式">
              {{ systemDetail.contactPhone || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>


      <!-- 数据基本信息 -->
      <el-tab-pane label="数据基本信息">
        <div class="detail-section">
          <div class="section-title">基本数据信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="数据描述" :span="2">{{ systemDetail.dataDescription || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="总数据量">{{ systemDetail.dataTotal || '-' }}</el-descriptions-item>
            <el-descriptions-item label="总数据条数">{{ systemDetail.dataNum || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <div class="section-title">数据安全</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="是否有个人信息">
              {{ systemDetail.hasPersonalInfo === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否在互联网中传输">
              {{ systemDetail.isTrans === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="systemDetail.hasPersonalInfo === '1'" label="个人信息数据量">
              {{ systemDetail.personalInfoTotal || '-' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="systemDetail.hasPersonalInfo === '1'" label="个人信息数据条数">
              {{ systemDetail.personalInfoNum || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="传输过程中是否加密重要信息">
              {{ systemDetail.isEncrypt === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item label="存储过程中是否加密重要信息">
              {{ systemDetail.isEncrypt === '1' ? '是' : '否' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="systemDetail.isEncrypt === '1'" label="传输过程加密算法">
              {{ systemDetail.encryptionAlgorithm || '-' }}
            </el-descriptions-item>
            <el-descriptions-item v-if="systemDetail.isEncrypt === '1'" label="存储过程加密算法">
              {{ systemDetail.encryptionAlgorithm2 || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>

      <!-- 管理单位 -->
      <el-tab-pane label="管理单位">
        <div class="detail-section">
          <div class="section-title">部门基本信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="管理部门">{{ getDeptName(systemDetail.deptId) }}</el-descriptions-item>
            <el-descriptions-item label="办公地址">{{ systemDetail.officeAddress || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 管理领导信息 -->
        <div class="detail-section">
          <div class="section-title">部门领导信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="领导姓名">{{ systemDetail.leader || '-' }}</el-descriptions-item>
            <el-descriptions-item label="办公电话">{{ systemDetail.officePhone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ systemDetail.phone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ systemDetail.email || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 系统管理员信息 -->
        <div class="detail-section">
          <div class="section-title">系统管理员信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="管理员姓名">{{ systemDetail.sysManager || systemDetail.sysManagerName || '-'
            }}</el-descriptions-item>
            <el-descriptions-item label="办公电话">{{ systemDetail.sysOfficePhone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ systemDetail.sysPhone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ systemDetail.sysEmail || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>

      <!-- 服务商 -->
      <el-tab-pane label="服务商">
        <div class="detail-section">
          <div class="section-title">基本信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="开发与运维服务商是否相同">
              {{ systemDetail.isSame === '1' ? '同一公司/团队' : '不同公司/团队' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <template v-if="systemDetail.isSame === '1'">
          <!-- 同一服务商信息 -->
          <div class="detail-section">
            <div class="section-title">开发与运维服务商信息</div>
            <div v-if="providerDetail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="公司名称" :span="2">
                  {{ providerDetail.providerName || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="项目负责人">
                  {{ providerDetail.projectManager || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                  {{ providerDetail.managerMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系邮箱" :span="2">
                  {{ providerDetail.managerEmail || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术负责人">
                  {{ providerDetail.techLeader || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术电话">
                  {{ providerDetail.techMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术邮箱" :span="2">
                  {{ providerDetail.techEmail || '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <el-empty v-else description="暂无服务商信息" />
          </div>
        </template>

        <template v-else>
          <!-- 开发商信息 -->
          <div class="detail-section">
            <div class="section-title">开发商信息</div>
            <div v-if="developerDetail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="公司名称" :span="2">
                  {{ developerDetail.providerName || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="项目负责人">
                  {{ developerDetail.projectManager || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                  {{ developerDetail.managerMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系邮箱" :span="2">
                  {{ developerDetail.managerEmail || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术负责人">
                  {{ developerDetail.techLeader || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术电话">
                  {{ developerDetail.techMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术邮箱" :span="2">
                  {{ developerDetail.techEmail || '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <el-empty v-else description="暂无开发商信息" />
          </div>

          <!-- 运维商信息 -->
          <div class="detail-section">
            <div class="section-title">运维商信息</div>
            <div v-if="operatorDetail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="公司名称" :span="2">
                  {{ operatorDetail.providerName || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="项目负责人">
                  {{ operatorDetail.projectManager || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                  {{ operatorDetail.managerMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系邮箱" :span="2">
                  {{ operatorDetail.managerEmail || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术负责人">
                  {{ operatorDetail.techLeader || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术电话">
                  {{ operatorDetail.techMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术邮箱" :span="2">
                  {{ operatorDetail.techEmail || '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <el-empty v-else description="暂无运维商信息" />
          </div>
        </template>
      </el-tab-pane>

      <!-- 关联资产 -->
      <el-tab-pane label="关联资产">
        <div class="detail-section">
          <div class="section-title">关联的资产</div>
          <div v-if="systemDetail.assetsList && systemDetail.assetsList.length > 0">
            <el-table :data="systemDetail.assetsList" border style="width: 100%">
              <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
              <el-table-column prop="name" label="资产名称" min-width="120" align="center" />
              <el-table-column prop="type" label="资产类型" min-width="100" align="center">
                <template #default="scope">
                  <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="资产地址" min-width="200" align="center">
                <template #default="scope">
                  <div>IP：{{ scope.row.ip || '-' }}</div>
                  <div v-if="scope.row.url">URL：{{ scope.row.url }}</div>
                  <div v-if="scope.row.port">端口：{{ scope.row.port }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="deptId" label="管理单位" min-width="120" align="center">
                <template #default="scope">
                  {{ getDeptName(scope.row.deptId) || scope.row.deptName || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" />
              <el-table-column prop="status" label="资产状态" min-width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
                    {{ scope.row.status == '1' ? '正常' : (scope.row.status == '0' ? '异常' : '废弃') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="notes" label="备注" min-width="120" align="center" />
            </el-table>
          </div>
          <el-empty v-else description="暂无关联资产" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import systemAPI from '@/api/assets_management/details/systems-entity';
import { useDictStore } from '@/store/modules/dictStore';
import ProviderAPI from "@/api/work_management/serviceProvider/index";
import eventsAPI from "@/api/work_management/critical";
import UserAPI from '@/api/user';
import AddressList from './AddressList.vue' // 确保路径正确


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  systemId: {
    type: Number,
    default: undefined
  }
});

const emit = defineEmits(['update:visible']);

// 获取字典Store
const dictStore = useDictStore();

// 系统详情
const systemDetail = ref<any>({});
const loading = ref(false);

// 服务商详情
const providerDetail = ref<any>(null);
const developerDetail = ref<any>(null);
const operatorDetail = ref<any>(null);

// 重保事件
const criticalEvents = ref<any[]>([]);

// 字典映射
const deptMap = ref<Record<string | number, string>>({});
const deptCacheLoaded = ref(false);

// 获取系统详情
async function fetchSystemDetail(id: number) {
  if (!id) return;

  try {
    loading.value = true;
    const data = await systemAPI.getFormData(id);
    systemDetail.value = data;

    // 加载相关数据
    await Promise.all([
      loadDeptData(),
      loadCriticalEvents(),
      loadProviderDetails(data),
      loadUserInformation(data)
    ]);

  } catch (error) {
    console.error('获取系统详情失败:', error);
    ElMessage.error('获取系统详情失败');
  } finally {
    loading.value = false;
  }
}

// 加载部门信息
async function loadDeptData() {
  if (deptCacheLoaded.value) return;

  try {
    // 获取部门选项
    const deptOptions = await dictStore.fetchOptions('dept0x0');

    // 处理部门树数据，构建ID到名称的映射
    const processDeptTree = (depts: any[]) => {
      if (!Array.isArray(depts)) return;

      depts.forEach(dept => {
        if (dept.value !== undefined && dept.label) {
          deptMap.value[dept.value] = dept.label;
          // 同时存储字符串形式的键
          if (typeof dept.value === 'number') {
            deptMap.value[String(dept.value)] = dept.label;
          }
        }

        if (dept.children && dept.children.length > 0) {
          processDeptTree(dept.children);
        }
      });
    };

    processDeptTree(deptOptions);
    deptCacheLoaded.value = true;
  } catch (error) {
    console.error('加载部门数据失败:', error);
  }
}

// 加载重保事件列表
async function loadCriticalEvents() {
  try {
    const res = await eventsAPI.getPage({ pageNum: 1, pageSize: 999 });
    criticalEvents.value = res.list || [];
  } catch (error) {
    console.error('获取重保事件列表失败:', error);
  }
}

// 获取重保事件名称
function getCriticalEventName(eventId: number | string): string {
  const event = criticalEvents.value.find(event => event.id === eventId);
  return event ? event.eventName : '';
}

// 获取部门名称
function getDeptName(deptId: string | number): string {
  if (!deptId) return '-';
  return deptMap.value[deptId] || `部门${deptId}`;
}

// 获取资产类型名称
function getAssetTypeName(type: any): string {
  const typeMap = {
    1: '服务器',
    2: '网络设备',
    3: '安全设备',
    4: '物联网设备',
    10: '信息系统'
    // 根据实际需要添加更多类型
  };
  return typeMap[type] || '未知类型';
}

// 获取等保等级名称
function getSecurityLevelName(level: string): string {
  const levelMap = {
    '0': '未定级',
    '1': '等保一级',
    '2': '等保二级',
    '3': '等保三级',
    '4': '等保四级',
    '5': '等保五级'
  };
  return levelMap[level] || `-`;
}

// 获取开放时长名称
function getOpenDurationName(duration: string): string {
  const durationMap = {
    '1week': '1周',
    '1month': '1月',
    '3months': '1季度',
    '1year': '1年',
    'permanent': '永久',
    'undefined': '未定义'
  };
  return durationMap[duration] || duration;
}

// 加载服务商详情
async function loadProviderDetails(data: any) {
  try {
    if (data.isSame === '1' && data.developer) {
      // 同一家服务商
      providerDetail.value = await ProviderAPI.getFormData(data.developer);
    } else {
      // 不同服务商
      if (data.developer) {
        developerDetail.value = await ProviderAPI.getFormData(data.developer);
      }

      if (data.operator) {
        operatorDetail.value = await ProviderAPI.getFormData(data.operator);
      }
    }
  } catch (error) {
    console.error('加载服务商详情失败:', error);
  }
}

// 获取用户详细信息
async function loadUserInformation(data: any) {
  try {
    // 检查是否有管理者ID但缺少详细信息
    if (data) {
      // 加载系统管理员信息
      if (data.sysManagerId && (!data.sysManager || !data.sysPhone)) {
        const adminData = await UserAPI.getFormData(data.sysManagerId);
        if (adminData) {
          // 补充系统管理员信息
          data.sysManager = data.sysManager || adminData.nickname || adminData.username;
          data.sysPhone = data.sysPhone || adminData.mobile;
          data.sysOfficePhone = data.sysOfficePhone || adminData.officePhone;
          data.sysEmail = data.sysEmail || adminData.email;
        }
      }

      // 加载管理领导信息
      if (data.managerId && (!data.leader || !data.phone)) {
        const leaderData = await UserAPI.getFormData(data.managerId);
        if (leaderData) {
          // 补充管理领导信息
          data.leader = data.leader || leaderData.nickname || leaderData.username;
          data.officePhone = data.officePhone || leaderData.officePhone;
          data.phone = data.phone || leaderData.mobile;
          data.email = data.email || leaderData.email;
        }
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
}

// 格式化日期
function formatDate(dateStr: string | null | undefined): string {
  if (!dateStr) return '-';
  try {
    const date = new Date(dateStr);
    return date.toLocaleString();
  } catch (e) {
    return dateStr;
  }
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false);
}

// 监听ID变化，加载系统详情
watchEffect(() => {
  if (props.visible && props.systemId) {
    fetchSystemDetail(props.systemId);
  }
});
</script>

<style scoped>
.detail-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-light);
}

.section-title {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.event-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.event-tag {
  background-color: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  color: var(--el-color-primary);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

:deep(.el-descriptions) {
  --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
}

:deep(.el-descriptions__body) {
  background-color: var(--el-bg-color);
}

:deep(.el-descriptions__label) {
  width: 140px;
  color: var(--el-text-color-regular);
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
}

:deep(.el-tabs__content) {
  padding: 20px;
  background-color: var(--el-bg-color);
}

:deep(.el-tab-pane) {
  max-height: 65vh;
  overflow-y: auto;
}

:deep(.el-table) {
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table th),
:deep(.el-table td) {
  background-color: var(--el-bg-color);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: var(--el-fill-color-lighter);
}

:deep(.el-dialog) {
  background-color: var(--el-bg-color);
}

:deep(.el-tabs__item) {
  color: var(--el-text-color-regular);
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: var(--el-border-color-light);
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
  background-color: var(--el-bg-color);
}

/* 适配空状态提示 */
:deep(.el-empty__description) {
  color: var(--el-text-color-secondary);
}

/* 标签适配 */
:deep(.el-tag) {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
}

:deep(.el-tag--success) {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

:deep(.el-tag--danger) {
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

:deep(.el-tag--info) {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

:deep(.el-tag--warning) {
  background-color: var(--el-color-warning-light-9);
  border-color: var(--el-color-warning-light-8);
  color: var(--el-color-warning);
}

/* 系统特有的样式 */
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
</style>
