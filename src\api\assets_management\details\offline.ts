import request from "@/utils/request";

const OFFLINE_BASE_URL = "/api/v1/offlines";

class offlineAPI {
    static getAuditHistory(id: string) {
        return request<any, PageResult<offlinePageVO[]>>({
          url: `/system/offline/audit/history/${id}`,
          method: 'get'
        })
      };
    
      // 获取评价信息
    static getEvaluate(id: string) {
        return request<any, PageResult<offlinePageVO[]>>({
          url: `/system/offline/evaluate/${id}`,
          method: 'get'
        })
      };

    /** 获取下线管理分页数据 */
    static getPage(queryParams?: offlinePageQuery) {
        return request<any, PageResult<offlinePageVO[]>>({
            url: `${OFFLINE_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }

    /**
     * 获取个人下线管理分页数据
     *
     * @returns offline详情数据
     */
    static getPersonalPage(queryParams?: offlinePageQuery) {
        return request<any, PageResult<offlinePageVO[]>>({
            url: `${OFFLINE_BASE_URL}/pagePerson`,
            method: "get",
            params: queryParams,
        });
    }

    /**
     * 获取下线管理表单数据
     *
     * @param id offlineID
     * @returns offline表单数据
     */
    static getFormData(id: string) {
        return request<any, offlineForm>({
            url: `${OFFLINE_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加下线管理*/
    static add(data: offlineForm) {
        return request({
            url: `${OFFLINE_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新下线管理
     *
     * @param id offlineID
     * @param data offline表单数据
     */
    static update(id: string, data: offlineForm) {
        return request({
            url: `${OFFLINE_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除下线管理，多个以英文逗号(,)分割
     *
     * @param ids 下线管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${OFFLINE_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 下线状态
     *
     * @param id 下线管理ID
     */
    static status(id: string) {
        return request({
            url: `${OFFLINE_BASE_URL}/${id}/status`,
            method: "get",
        });
    }

    /**
     * 工单审核
     *
     * @param id ID
     */
    static audit(data: offlineForm, id: number) {
        return request({
            url: `${OFFLINE_BASE_URL}/${id}/audit1`,
            method: "put",
            data: data,
        });
      }

    /**评价工单 */
    static evaluate(id: string, data: offlineForm) {
        return request({
            url: `${OFFLINE_BASE_URL}/${id}/commentsOffline`,
            method: "post",
            data: data,
        });
    }

    /** 流转信息 */
    static getFlowInfo(id: string) {
        return request({
            url: `${OFFLINE_BASE_URL}/${id}/reviewProcess`,
            method: "get",
        });
    }

    /**
     * 获取文件列表
     */
    static getFileList(id: number) {
      return request({
        url: `${OFFLINE_BASE_URL}/${id}/fileList`,
        method: "get",
      });
    }
}

export default offlineAPI;

/** 下线管理分页查询参数 */
export interface offlinePageQuery extends PageQuery {
    /** id */
    id?: string;
    /** 下线事件名 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 下线步骤 */
    step?: number;
    /** 申请人姓名 */
    applicantName?: string;
    /** 申请人id */
    applicantId?: number;
    /** 处理人姓名 */
    handler?: string;
    /** 处理人id */
    handlerId?: number;
    /** 申请人联系方式 */
    concat?: string;
    /** 原因 */
    reason?: string;
    /** 申请时间 */
    createTime?: [string, string];
    /** 更新时间 */
    updateTime?: [string, string];
    errStep?: number;
}

/** 下线管理表单对象 */
export interface offlineForm {
    fileList: boolean;
    comments: any;
    reviews: boolean;
    /** id */
    id?:  string;
    /** 下线事件名 */
    name?:  string;
    /** 备注 */
    remark?:  string;
    /** 下线步骤 */
    step?:  number;
    /** 申请人姓名 */
    applicantName?:  string;
    /** 申请人id */
    applicantId?:  number;
    /** 处理人姓名 */
    handler?:  string;
    /** 处理人id */
    handlerId?:  number;
    /** 申请人联系方式 */
    concat?:  string;
    /** 原因 */
    reason?:  string;
    /** 申请时间 */
    createTime?:  string;
    /** 更新时间 */
    updateTime?:  string;
    errStep?:  number;
}

/** 下线管理分页对象 */
export interface offlinePageVO {
    /** id */
    id?: string;
    /** 下线事件名 */
    name?: string;
    /** 备注 */
    remark?: string;
    /** 下线步骤 */
    step?: number;
    /** 申请人姓名 */
    applicantName?: string;
    /** 申请人id */
    applicantId?: number;
    /** 处理人姓名 */
    handler?: string;
    /** 处理人id */
    handlerId?: number;
    /** 申请人联系方式 */
    concat?: string;
    /** 原因 */
    reason?: string;
    /** 审核部门类型 */
    reviewDeptType?: string;
    /** 申请时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
    errStep?: number;
}
