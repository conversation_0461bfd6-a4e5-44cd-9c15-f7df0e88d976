<template>
  <div class="editor-wrapper">
    <!-- 文件输入 -->
    

    <!-- <el-button type="primary" @click="triggerFileInput">上传 Word 文件</el-button>
    <input 
      type="file" 
      @change="handleFileChange" 
      accept=".doc,.docx" 
      style="display: none;" 
      ref="fileInput"
    />
    <p style="margin-top: 10px; color: #666;">提示: 文件大小不能超过 20MB，且只能上传 .doc，.docx文件。</p>
     -->
    
    <!-- 工具栏 -->
    <Toolbar
      id="toolbar-container"
      :editor="editorRef"
      :default-config="toolbarConfig"
      :mode="mode"
    />
    <!-- 编辑器 -->
    <Editor
      id="editor-container"
      v-model="modelValue"
      :default-config="editorConfig"
      :mode="mode"
      @on-change="handleChange"
      @on-created="handleCreated"
    />

    
  </div>
</template>

<script setup lang="ts">
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";

// API 引用
import FileAPI from "@/api/file";
import mammoth from "mammoth";
const props = defineProps({
  modelValue: {
    type: [String],
    default: "",
  },
});
const fileInput = ref<HTMLInputElement | null>(null);
const MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB
const triggerFileInput = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

const emit = defineEmits(["update:modelValue"]);

const modelValue = useVModel(props, "modelValue", emit);

const editorRef = shallowRef(); // 编辑器实例，必须用 shallowRef
const mode = ref("default"); // 编辑器模式
const toolbarConfig = ref({}); // 工具条配置

// 编辑器配置
const editorConfig = ref({
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      // 自定义图片上传
      async customUpload(file: any, insertFn: any) {
        FileAPI.upload(file).then((data) => {
          insertFn(data.url);
        });
      },
    },
  },
});


// 处理文件输入变化
const handleFileChange = async (event: any) => {
  console.log(event);
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;
  if (file.size > MAX_FILE_SIZE) {
      ElMessage.error("文件大小不能超过 20MB");
      return;
    }
  const reader = new FileReader();
  reader.onload = async (e) => {
    const arrayBuffer = e.target?.result;
    if (arrayBuffer) {
      const result = await mammoth.convertToHtml({ arrayBuffer });
      const html = result.value;
      if (editorRef.value) {
        editorRef.value.setHtml(html); // 使用 txt.html 方法插入 HTML 内容
        console.log(editorRef.value);
      }
    }
    (event.target as HTMLInputElement).value = '';
  };
  reader.readAsArrayBuffer(file);
};

const handleCreated = (editor: any) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

function handleChange(editor: any) {
  modelValue.value = editor.getHtml();
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
