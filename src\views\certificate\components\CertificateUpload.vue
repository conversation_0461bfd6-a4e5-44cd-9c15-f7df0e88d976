<template>
    <el-dialog
      v-model="dialogVisible"
      title="上传证书"
      width="580px"
      :before-close="handleClose"
      append-to-body
      destroy-on-close
    >
      <el-alert
        title="上传新的授权证书将会替换当前证书，请确保证书文件的有效性。"
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      />
      
      <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="100px">
        <el-form-item label="证书文件" prop="file" required>
          <div class="upload-area">
            <template v-if="!uploadForm.file">
              <el-upload
                class="certificate-upload"
                drag
                accept=".p12"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleFileChange"
                :limit="1"
              >
                <el-icon class="el-icon--upload"><i-ep-upload-filled /></el-icon>
                <div class="el-upload__text">
                  拖拽文件到此处或 <em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 .p12 格式的证书文件
                  </div>
                </template>
              </el-upload>
            </template>
            <template v-else>
              <div class="file-info">
                <el-icon class="file-icon"><i-ep-document /></el-icon>
                <span class="file-name">{{ uploadForm.file.name }}</span>
                <div class="file-actions">
                  <el-button type="danger" size="small" circle @click="removeFile">
                    <el-icon><i-ep-delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </div>
        </el-form-item>
      </el-form>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading" :disabled="!uploadForm.file">
            上传证书
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, defineProps, defineEmits, computed, watch } from 'vue';
  import { ElMessage, FormInstance, UploadFile } from 'element-plus';
  import AuthAPI from "@/api/auth";
  
  const props = defineProps({
    visible: {
      type: Boolean,
      required: true
    }
  });
  
  const emit = defineEmits(['update:visible', 'upload-success']);
  
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  });
  
  const uploadFormRef = ref<FormInstance>();
  const uploading = ref(false);
  
  const uploadForm = ref({
    file: null as File | null,
    remarks: ''
  });
  
  const uploadRules = {
    file: [{ required: true, message: '请选择证书文件', trigger: 'change' }]
  };
  
  // 监听对话框可见性变化
  watch(() => props.visible, (val) => {
    if (!val) {
      resetForm();
    }
  });
  
  // 文件变更处理
  const handleFileChange = (file: UploadFile) => {
    if (file.raw) {
      uploadForm.value.file = file.raw;
    }
  };
  
  // 移除文件
  const removeFile = () => {
    uploadForm.value.file = null;
  };
  
  // 关闭对话框
  const handleClose = () => {
    dialogVisible.value = false;
  };
  
  // 重置表单
  const resetForm = () => {
    uploadForm.value.file = null;
    uploadForm.value.remarks = '';
    uploadFormRef.value?.resetFields();
  };
  
  // 上传证书
  const handleUpload = async () => {
    if (!uploadForm.value.file) {
      ElMessage.warning('请先选择证书文件');
      return;
    }
    
    try {
      await uploadFormRef.value?.validate();
      
      uploading.value = true;
      
      const formData = new FormData();
      formData.append('file', uploadForm.value.file);
      
      if (uploadForm.value.remarks) {
        formData.append('remarks', uploadForm.value.remarks);
      }
      
      await AuthAPI.uploadCertificate(formData);
      
      ElMessage.success('证书上传成功');
      emit('upload-success');
      dialogVisible.value = false;
    } catch (error) {
      console.error('上传证书失败:', error);
    } finally {
      uploading.value = false;
    }
  };
  </script>
  
  <style scoped>
  .upload-area {
    border: 1px dashed var(--el-border-color);
    border-radius: 4px;
    padding: 5px;
    transition: all 0.3s;
  }
  
  .upload-area:hover {
    border-color: var(--el-color-primary);
  }
  
  .certificate-upload {
    width: 100%;
  }
  
  .file-info {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
  }
  
  .file-icon {
    font-size: 24px;
    color: var(--el-color-primary);
    margin-right: 10px;
  }
  
  .file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .file-actions {
    margin-left: 10px;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  </style>
