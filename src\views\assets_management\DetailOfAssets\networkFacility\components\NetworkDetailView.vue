<template>
    <el-dialog v-model="props.visible" title="网络设备详情" width="75%" :before-close="handleClose">
      <el-tabs type="border-card">
        <!-- 网络设备基本信息 -->
        <el-tab-pane label="基本信息">
          <div class="detail-section">
            <div class="section-title">基础信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="设备名称">{{ assetDetail.name || '-' }}</el-descriptions-item>
              <el-descriptions-item label="系统名称及版本">{{ assetDetail.os || '-' }}</el-descriptions-item>
              <el-descriptions-item label="设备厂家">{{ assetDetail.factory || '-' }}</el-descriptions-item>
              <el-descriptions-item label="网络设备型号">{{ assetDetail.model || '-' }}</el-descriptions-item>
              <el-descriptions-item label="登记时间">{{ formatDate(assetDetail.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="计划下线时间">{{ formatDate(assetDetail.plannedOfflineTime) }}</el-descriptions-item>
              <el-descriptions-item label="功能简述" :span="2">{{ assetDetail.notes || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
  
          <!-- 网络配置信息 -->
          <div class="detail-section">
            <div class="section-title">网络配置</div>
            <div v-if="ipConfigs.length > 0">
              <div v-for="(ipConfig, index) in ipConfigs" :key="index" class="ip-config-card">
                <div class="card-header">IP配置 #{{ index + 1 }}</div>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="IP地址">{{ ipConfig.ip || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="端口">{{ ipConfig.port || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="协议">{{ ipConfig.protocol || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="用途描述">{{ ipConfig.usage || '-' }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
            <el-empty v-else description="暂无IP配置信息" />
          </div>
  
          <!-- 位置信息 -->
          <div class="detail-section">
            <div class="section-title">位置信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="所在网络区域">{{ assetDetail.netArea || '-' }}</el-descriptions-item>
              <el-descriptions-item label="所在物理位置">{{ assetDetail.local || '-' }}</el-descriptions-item>
              <el-descriptions-item label="资产状态">
                <el-tag :type="assetDetail.status == '1' ? 'success' : (assetDetail.status == '0' ? 'danger' : 'info')">
                  {{ assetDetail.status == '1' ? '正常' : (assetDetail.status == '0' ? '异常' : '废弃') }}
                </el-tag>
              </el-descriptions-item>
              <!-- <el-descriptions-item label="所属信息系统">
                {{ getSystemName(assetDetail.systemId) }}
              </el-descriptions-item> -->
            </el-descriptions>
          </div>
        </el-tab-pane>
  
        <!-- 管理单位信息 -->
        <el-tab-pane label="管理单位">
          <div class="detail-section">
            <div class="section-title">管理单位信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="管理单位">{{ getDeptName(assetDetail.deptId) }}</el-descriptions-item>
              <el-descriptions-item label="办公地址">{{ assetDetail.depAddress || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
  
          <!-- 管理领导信息 -->
          <div class="detail-section">
            <div class="section-title">管理领导信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="领导姓名">{{ assetDetail.leader || '-' }}</el-descriptions-item>
              <el-descriptions-item label="办公电话">{{ assetDetail.leaderPhone || '-' }}</el-descriptions-item>
              <el-descriptions-item label="手机号码">{{ assetDetail.leaderPhone1 || '-' }}</el-descriptions-item>
              <el-descriptions-item label="联系邮箱">{{ assetDetail.leaderEmail || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
  
          <!-- 系统管理员信息 -->
          <div class="detail-section">
            <div class="section-title">系统管理员信息</div>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="管理员姓名">{{ assetDetail.ownerName || '-' }}</el-descriptions-item>
              <el-descriptions-item label="办公电话">{{ assetDetail.ownerPhone1 || '-' }}</el-descriptions-item>
              <el-descriptions-item label="手机号码">{{ assetDetail.ownerPhone || '-' }}</el-descriptions-item>
              <el-descriptions-item label="联系邮箱">{{ assetDetail.ownerEmail || '-' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
  
        <!-- 运维单位信息 -->
        <el-tab-pane label="运维单位">
          <div class="detail-section">
            <div class="section-title">运维服务商信息</div>
            <div v-if="assetDetail.provider">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="公司名称" :span="2">
                  {{ assetDetail.provider.providerName || assetDetail.otherFactory || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="项目负责人">
                  {{ assetDetail.provider.projectManager || assetDetail.otherManager || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系电话">
                  {{ assetDetail.provider.managerMobile || assetDetail.otherContact || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="联系邮箱" :span="2">
                  {{ assetDetail.provider.managerEmail || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术负责人">
                  {{ assetDetail.provider.techLeader || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术电话">
                  {{ assetDetail.provider.techMobile || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="技术邮箱" :span="2">
                  {{ assetDetail.provider.techEmail || '-' }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
            <el-empty v-else description="暂无运维服务商信息" />
          </div>
        </el-tab-pane>
      </el-tabs>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, watchEffect } from 'vue';
  import { ElMessage } from 'element-plus';
  import assetsAPI from '@/api/assets_management/details/assets';
  import { useDictStore } from '@/store/modules/dictStore';
  import UserAPI from '@/api/user';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    assetId: {
      type: Number,
      default: undefined
    }
  });
  
  const emit = defineEmits(['update:visible']);
  
  // 获取字典Store
  const dictStore = useDictStore();
  
  // 资产详情
  const assetDetail = ref<any>({});
  const loading = ref(false);
  
  // 解析后的数据
  const ipConfigs = ref<any[]>([]);
  
  // 字典映射
  const deptMap = ref<Record<string | number, string>>({});
  const systemMap = ref<Record<string | number, string>>({});
  
  // 获取资产详情
  async function fetchAssetDetail(id: number) {
    if (!id) return;
    
    try {
      loading.value = true;
      const data = await assetsAPI.getServerDetail(id);
      assetDetail.value = data;
      
      // 解析数据
      parseIpConfigs(data);
      
      // 加载字典映射
      await loadDictMappings();
      
      // 加载用户信息
      await loadUserInformation();
      
    } catch (error) {
      console.error('获取网络设备详情失败:', error);
      ElMessage.error('获取网络设备详情失败');
    } finally {
      loading.value = false;
    }
  }
  
  // 获取用户详细信息
  async function loadUserInformation() {
    try {
      // 检查是否有管理者ID但缺少详细信息
      if (assetDetail.value) {
        // 加载系统管理员信息
        if (assetDetail.value.sysManagerId && (!assetDetail.value.ownerName || !assetDetail.value.ownerPhone)) {
          const adminData = await UserAPI.getFormData(assetDetail.value.sysManagerId);
          if (adminData) {
            // 补充系统管理员信息
            assetDetail.value.ownerName = assetDetail.value.ownerName || adminData.nickname || adminData.username;
            assetDetail.value.ownerPhone = assetDetail.value.ownerPhone || adminData.mobile;
            assetDetail.value.ownerPhone1 = assetDetail.value.ownerPhone1 || adminData.officePhone;
            assetDetail.value.ownerEmail = assetDetail.value.ownerEmail || adminData.email;
          }
        }
        
        // 加载管理领导信息
        if (assetDetail.value.managerId && (!assetDetail.value.leader || !assetDetail.value.leaderPhone)) {
          const leaderData = await UserAPI.getFormData(assetDetail.value.managerId);
          if (leaderData) {
            // 补充管理领导信息
            assetDetail.value.leader = assetDetail.value.leader || leaderData.nickname || leaderData.username;
            assetDetail.value.leaderPhone = assetDetail.value.leaderPhone || leaderData.officePhone;
            assetDetail.value.leaderPhone1 = assetDetail.value.leaderPhone1 || leaderData.mobile;
            assetDetail.value.leaderEmail = assetDetail.value.leaderEmail || leaderData.email;
          }
        }
      }
    } catch (error) {
      console.error('加载用户信息失败:', error);
    }
  }
  
  // 解析IP配置
  function parseIpConfigs(data: any) {
  ipConfigs.value = [];
  
  // 用于跟踪已添加的IP:port组合，避免重复
  const addedConfigs = new Set<string>();
  
  // 添加主IP配置
  if (data.ip) {
    const mainConfigKey = `${data.ip}:${data.port || ''}`;
    const mainConfig = {
      id: 1,
      ip: data.ip,
      port: data.port || '',
      protocol: '',
      usage: '主IP'
    };
    
    // 尝试从URL中提取协议
    if (data.url) {
      const urlMatch = data.url.match(/^(\w+):\/\//);
      if (urlMatch) {
        mainConfig.protocol = urlMatch[1];
      }
    }
    
    ipConfigs.value.push(mainConfig);
    addedConfigs.add(mainConfigKey);
  }
  
  // 处理 addressList 数组字段
  if (data.addressList && Array.isArray(data.addressList)) {
    data.addressList.forEach((addr: any, index: number) => {
      if (!addr.ip) return; // 跳过空IP
      
      const configKey = `${addr.ip}:${addr.port || ''}`;
      
      // 只添加与已存在配置不重复的IP配置
      if (!addedConfigs.has(configKey)) {
        ipConfigs.value.push({
          id: ipConfigs.value.length + 1,
          ip: addr.ip,
          port: addr.port || '',
          protocol: addr.protocol || '',
          usage: addr.usage || '附加IP'
        });
        addedConfigs.add(configKey);
      }
    });
  }
  
  // 如果没有任何IP配置，显示空状态
  if (ipConfigs.value.length === 0) {
    console.log('没有找到任何IP配置信息');
  } else {
    console.log('解析后的IP配置:', ipConfigs.value);
  }
}
  
  // 加载字典映射
  async function loadDictMappings() {
    try {
      // 加载部门树和系统字典
      const [deptOptions, systemOptions] = await Promise.all([
        dictStore.fetchOptions('dept0x0'),
        dictStore.fetchOptions('system0x0')
      ]);
      
      // 处理部门树
      processDeptTree(deptOptions || []);
      
      // 处理系统选项
      processOptions(systemOptions || [], systemMap.value);
      
    } catch (error) {
      console.error('加载字典映射失败:', error);
    }
  }
  
  // 处理部门树数据
  function processDeptTree(depts: any[]) {
    if (!Array.isArray(depts)) return;
    
    depts.forEach(dept => {
      if (dept.value !== undefined && dept.label) {
        deptMap.value[dept.value] = dept.label;
        // 同时存储字符串形式的键
        if (typeof dept.value === 'number') {
          deptMap.value[String(dept.value)] = dept.label;
        }
      }
      
      if (dept.children && dept.children.length > 0) {
        processDeptTree(dept.children);
      }
    });
  }
  
  // 处理选项数据
  function processOptions(options: any[], targetMap: Record<string | number, string>) {
    if (!Array.isArray(options)) return;
    
    options.forEach(option => {
      if (option.value !== undefined && option.label) {
        targetMap[option.value] = option.label;
        // 同时存储字符串形式的键
        if (typeof option.value === 'number') {
          targetMap[String(option.value)] = option.label;
        }
      }
    });
  }
  
  // 获取部门名称
  function getDeptName(deptId: string | number): string {
    if (!deptId) return '-';
    return deptMap.value[deptId] || `部门${deptId}`;
  }
  
  // 获取系统名称
  function getSystemName(systemId: string | number): string {
    if (!systemId) return '-';
    return systemMap.value[systemId] || `系统${systemId}`;
  }
  
  // 格式化日期
  function formatDate(dateStr: string | null | undefined): string {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      return date.toLocaleString();
    } catch (e) {
      return dateStr;
    }
  }
  
  // 关闭弹窗
  function handleClose() {
    emit('update:visible', false);
  }
  
  // 监听ID变化，加载资产详情
  watchEffect(() => {
    if (props.visible && props.assetId) {
      fetchAssetDetail(props.assetId);
    }
  });
  </script>
  
  <style scoped>
.detail-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-light);
}

.section-title {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.ip-config-card {
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--el-bg-color);
}

.card-header {
  padding: 8px 16px;
  background-color: var(--el-fill-color-light);
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color);
}

:deep(.el-descriptions) {
  --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
}

:deep(.el-descriptions__body) {
  background-color: var(--el-bg-color);
}

:deep(.el-descriptions__label) {
  width: 140px;
  color: var(--el-text-color-regular);
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
}

:deep(.el-tabs__content) {
  padding: 20px;
  background-color: var(--el-bg-color);
}

:deep(.el-tab-pane) {
  max-height: 65vh;
  overflow-y: auto;
}

:deep(.el-table) {
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table th),
:deep(.el-table td) {
  background-color: var(--el-bg-color);
}

:deep(.el-table--border) {
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-dialog) {
  background-color: var(--el-bg-color);
}

:deep(.el-tabs__item) {
  color: var(--el-text-color-regular);
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: var(--el-border-color-light);
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
  background-color: var(--el-bg-color);
}

/* 适配空状态提示 */
:deep(.el-empty__description) {
  color: var(--el-text-color-secondary);
}

/* 标签适配 */
:deep(.el-tag) {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-8);
}

:deep(.el-tag--success) {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

:deep(.el-tag--danger) {
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

:deep(.el-tag--info) {
  background-color: var(--el-color-info-light-9);
  border-color: var(--el-color-info-light-8);
  color: var(--el-color-info);
}
</style>
