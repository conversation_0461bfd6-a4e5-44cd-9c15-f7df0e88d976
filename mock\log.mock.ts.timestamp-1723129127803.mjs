// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/log.mock.ts
var log_mock_default = defineMock([
  {
    url: "logs/page",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        list: [
          {
            id: 36192,
            module: "\u83DC\u5355",
            content: "\u83DC\u5355\u5217\u8868",
            requestUri: "/api/v1/menus",
            method: null,
            ip: "***************",
            region: "\u6D59\u6C5F\u7701 \u676D\u5DDE\u5E02",
            browser: "Chrome *********",
            os: "OSX",
            executionTime: 5,
            createBy: null,
            createTime: "2024-07-07 20:38:47",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36190,
            module: "\u5B57\u5178",
            content: "\u5B57\u5178\u5206\u9875\u5217\u8868",
            requestUri: "/api/v1/dict/page",
            method: null,
            ip: "***************",
            region: "\u6D59\u6C5F\u7701 \u676D\u5DDE\u5E02",
            browser: "Chrome *********",
            os: "OSX",
            executionTime: 9,
            createBy: null,
            createTime: "2024-07-07 20:38:45",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36193,
            module: "\u90E8\u95E8",
            content: "\u90E8\u95E8\u5217\u8868",
            requestUri: "/api/v1/dept",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 27,
            createBy: null,
            createTime: "2024-07-07 20:38:45",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36191,
            module: "\u83DC\u5355",
            content: "\u83DC\u5355\u5217\u8868",
            requestUri: "/api/v1/menus",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 39,
            createBy: null,
            createTime: "2024-07-07 20:38:44",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36189,
            module: "\u89D2\u8272",
            content: "\u89D2\u8272\u5206\u9875\u5217\u8868",
            requestUri: "/api/v1/roles/page",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 55,
            createBy: null,
            createTime: "2024-07-07 20:38:43",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36188,
            module: "\u7528\u6237",
            content: "\u7528\u6237\u5206\u9875\u5217\u8868",
            requestUri: "/api/v1/users/page",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 92,
            createBy: null,
            createTime: "2024-07-07 20:38:42",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36187,
            module: "\u767B\u5F55",
            content: "\u767B\u5F55",
            requestUri: "/api/v1/auth/login",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 19340,
            createBy: null,
            createTime: "2024-07-07 20:38:09",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36186,
            module: "\u767B\u5F55",
            content: "\u767B\u5F55",
            requestUri: "/api/v1/auth/login",
            method: null,
            ip: "**************",
            region: "0 \u5185\u7F51IP",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 19869,
            createBy: null,
            createTime: "2024-07-07 20:37:59",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36185,
            module: "\u767B\u5F55",
            content: "\u767B\u5F55",
            requestUri: "/api/v1/auth/login",
            method: null,
            ip: "**************",
            region: "\u9ED1\u9F99\u6C5F\u7701 \u54C8\u5C14\u6EE8\u5E02",
            browser: "Chrome 97.0.4692.98",
            os: "Android",
            executionTime: 96,
            createBy: null,
            createTime: "2024-07-07 20:37:21",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          },
          {
            id: 36184,
            module: "\u767B\u5F55",
            content: "\u767B\u5F55",
            requestUri: "/api/v1/auth/login",
            method: null,
            ip: "**************",
            region: "\u4E0A\u6D77 \u4E0A\u6D77\u5E02",
            browser: "Chrome *********",
            os: "Windows 10 or Windows Server 2016",
            executionTime: 89,
            createBy: null,
            createTime: "2024-07-07 20:29:37",
            operator: "\u7CFB\u7EDF\u7BA1\u7406\u5458"
          }
        ],
        total: 36188
      },
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "logs/visit-trend",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        dates: [
          "2024-06-30",
          "2024-07-01",
          "2024-07-02",
          "2024-07-03",
          "2024-07-04",
          "2024-07-05",
          "2024-07-06",
          "2024-07-07"
        ],
        pvList: [1751, 5168, 4882, 5301, 4721, 4885, 1901, 1003],
        uvList: null,
        ipList: [207, 566, 565, 631, 579, 496, 222, 152]
      },
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "logs/visit-stats",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          type: "pv",
          title: "\u6D4F\u89C8\u91CF",
          todayCount: 1003,
          totalCount: 36193,
          growthRate: -0.35,
          granularityLabel: "\u65E5"
        },
        {
          type: "uv",
          title: "\u8BBF\u5BA2\u6570",
          todayCount: 100,
          totalCount: 2e3,
          growthRate: 0,
          granularityLabel: "\u65E5"
        },
        {
          type: "ip",
          title: "IP\u6570",
          todayCount: 152,
          totalCount: 3234,
          growthRate: -0.2,
          granularityLabel: "\u65E5"
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  }
]);
export {
  log_mock_default as default
};
