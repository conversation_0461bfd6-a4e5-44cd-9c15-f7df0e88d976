// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/menu.mock.ts
var menu_mock_default = defineMock([
  {
    url: "menus/routes",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          path: "/system",
          component: "Layout",
          redirect: "/system/user",
          name: "/system",
          meta: {
            title: "\u7CFB\u7EDF\u7BA1\u7406",
            icon: "system",
            hidden: false,
            alwaysShow: false,
            params: null
          },
          children: [
            {
              path: "test",
              component: "test/index",
              name: "test",
              meta: {
                title: "Test",
                icon: "el-icon-User",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "user",
              component: "system/user/index",
              name: "User",
              meta: {
                title: "\u7528\u6237\u7BA1\u7406",
                icon: "el-icon-User",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "role",
              component: "system/role/index",
              name: "Role",
              meta: {
                title: "\u89D2\u8272\u7BA1\u7406",
                icon: "role",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "menu",
              component: "system/menu/index",
              name: "Menu",
              meta: {
                title: "\u83DC\u5355\u7BA1\u7406",
                icon: "menu",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "dept",
              component: "system/dept/index",
              name: "Dept",
              meta: {
                title: "\u90E8\u95E8\u7BA1\u7406",
                icon: "tree",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "dict",
              component: "system/dict/index",
              name: "Dict",
              meta: {
                title: "\u5B57\u5178\u7BA1\u7406",
                icon: "dict",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            }
          ]
        },
        {
          path: "/api",
          component: "Layout",
          name: "/api",
          meta: {
            title: "\u63A5\u53E3\u6587\u6863",
            icon: "api",
            hidden: false,
            alwaysShow: true,
            params: null
          },
          children: [
            {
              path: "apifox",
              component: "demo/api/apifox",
              name: "Apifox",
              meta: {
                title: "Apifox",
                icon: "api",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            }
          ]
        },
        {
          path: "/doc",
          component: "Layout",
          redirect: "https://juejin.cn/post/7228990409909108793",
          name: "/doc",
          meta: {
            title: "\u5E73\u53F0\u6587\u6863",
            icon: "document",
            hidden: false,
            alwaysShow: false,
            params: null
          },
          children: [
            {
              path: "internal-doc",
              component: "demo/internal-doc",
              name: "InternalDoc",
              meta: {
                title: "\u5E73\u53F0\u6587\u6863(\u5185\u5D4C)",
                icon: "document",
                hidden: false,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "https://juejin.cn/post/7228990409909108793",
              name: "Https://juejin.cn/post/7228990409909108793",
              meta: {
                title: "\u5E73\u53F0\u6587\u6863(\u5916\u94FE)",
                icon: "el-icon-Link",
                hidden: false,
                alwaysShow: false,
                params: null
              }
            }
          ]
        },
        {
          path: "/multi-level",
          component: "Layout",
          name: "/multiLevel",
          meta: {
            title: "\u591A\u7EA7\u83DC\u5355",
            icon: "cascader",
            hidden: false,
            alwaysShow: true,
            params: null
          },
          children: [
            {
              path: "multi-level1",
              component: "demo/multi-level/level1",
              name: "MultiLevel1",
              meta: {
                title: "\u83DC\u5355\u4E00\u7EA7",
                icon: "",
                hidden: false,
                alwaysShow: true,
                params: null
              },
              children: [
                {
                  path: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  name: "MultiLevel2",
                  meta: {
                    title: "\u83DC\u5355\u4E8C\u7EA7",
                    icon: "",
                    hidden: false,
                    alwaysShow: false,
                    params: null
                  },
                  children: [
                    {
                      path: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      name: "MultiLevel31",
                      meta: {
                        title: "\u83DC\u5355\u4E09\u7EA7-1",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null
                      }
                    },
                    {
                      path: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      name: "MultiLevel32",
                      meta: {
                        title: "\u83DC\u5355\u4E09\u7EA7-2",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null
                      }
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          path: "/component",
          component: "Layout",
          name: "/component",
          meta: {
            title: "\u7EC4\u4EF6\u5C01\u88C5",
            icon: "menu",
            hidden: false,
            alwaysShow: false,
            params: null
          },
          children: [
            {
              path: "curd",
              component: "demo/curd/index",
              name: "Curd",
              meta: {
                title: "\u589E\u5220\u6539\u67E5",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "table-select",
              component: "demo/table-select/index",
              name: "TableSelect",
              meta: {
                title: "\u5217\u8868\u9009\u62E9\u5668",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "wang-editor",
              component: "demo/wang-editor",
              name: "WangEditor",
              meta: {
                title: "\u5BCC\u6587\u672C\u7F16\u8F91\u5668",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "upload",
              component: "demo/upload",
              name: "Upload",
              meta: {
                title: "\u56FE\u7247\u4E0A\u4F20",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "icon-selector",
              component: "demo/icon-selector",
              name: "IconSelector",
              meta: {
                title: "\u56FE\u6807\u9009\u62E9\u5668",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "dict-demo",
              component: "demo/dict",
              name: "DictDemo",
              meta: {
                title: "\u5B57\u5178\u7EC4\u4EF6",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            }
          ]
        },
        {
          path: "/route-param",
          component: "Layout",
          name: "/routeParam",
          meta: {
            title: "\u8DEF\u7531\u53C2\u6570",
            icon: "el-icon-ElementPlus",
            hidden: false,
            alwaysShow: true,
            params: null
          },
          children: [
            {
              path: "route-param-type1",
              component: "demo/route-param",
              name: "RouteParamType1",
              meta: {
                title: "\u53C2\u6570(type=1)",
                icon: "el-icon-Star",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "1"
                }
              }
            },
            {
              path: "route-param-type2",
              component: "demo/route-param",
              name: "RouteParamType2",
              meta: {
                title: "\u53C2\u6570(type=2)",
                icon: "el-icon-StarFilled",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "2"
                }
              }
            }
          ]
        },
        {
          path: "/function",
          component: "Layout",
          name: "/function",
          meta: {
            title: "\u529F\u80FD\u6F14\u793A",
            icon: "menu",
            hidden: false,
            alwaysShow: false,
            params: null
          },
          children: [
            {
              path: "icon-demo",
              component: "demo/icons",
              name: "IconDemo",
              meta: {
                title: "Icons",
                icon: "el-icon-Notification",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "/function/websocket",
              component: "demo/websocket",
              name: "/function/websocket",
              meta: {
                title: "Websocket",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null
              }
            },
            {
              path: "other/:id",
              component: "demo/other",
              name: "Other/:id",
              meta: {
                title: "\u656C\u8BF7\u671F\u5F85...",
                icon: "",
                hidden: false,
                alwaysShow: false,
                params: null
              }
            }
          ]
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "menus",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: 1,
          parentId: 0,
          name: "\u7CFB\u7EDF\u7BA1\u7406",
          type: "CATALOG",
          routeName: "",
          routePath: "/system",
          component: "Layout",
          sort: 1,
          visible: 1,
          icon: "system",
          redirect: "/system/user",
          perm: null,
          children: [
            {
              id: 2,
              parentId: 1,
              name: "\u7528\u6237\u7BA1\u7406",
              type: "MENU",
              routeName: "User",
              routePath: "user",
              component: "system/user/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-User",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 105,
                  parentId: 2,
                  name: "\u7528\u6237\u67E5\u8BE2",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 0,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:query",
                  children: []
                },
                {
                  id: 31,
                  parentId: 2,
                  name: "\u7528\u6237\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:add",
                  children: []
                },
                {
                  id: 32,
                  parentId: 2,
                  name: "\u7528\u6237\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:edit",
                  children: []
                },
                {
                  id: 33,
                  parentId: 2,
                  name: "\u7528\u6237\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:delete",
                  children: []
                },
                {
                  id: 88,
                  parentId: 2,
                  name: "\u91CD\u7F6E\u5BC6\u7801",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:password:reset",
                  children: []
                },
                {
                  id: 106,
                  parentId: 2,
                  name: "\u7528\u6237\u5BFC\u5165",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:import",
                  children: []
                },
                {
                  id: 107,
                  parentId: 2,
                  name: "\u7528\u6237\u5BFC\u51FA",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:export",
                  children: []
                }
              ]
            },
            {
              id: 3,
              parentId: 1,
              name: "\u89D2\u8272\u7BA1\u7406",
              type: "MENU",
              routeName: "Role",
              routePath: "role",
              component: "system/role/index",
              sort: 2,
              visible: 1,
              icon: "role",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 70,
                  parentId: 3,
                  name: "\u89D2\u8272\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:add",
                  children: []
                },
                {
                  id: 71,
                  parentId: 3,
                  name: "\u89D2\u8272\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:edit",
                  children: []
                },
                {
                  id: 72,
                  parentId: 3,
                  name: "\u89D2\u8272\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:delete",
                  children: []
                }
              ]
            },
            {
              id: 4,
              parentId: 1,
              name: "\u83DC\u5355\u7BA1\u7406",
              type: "MENU",
              routeName: "Menu",
              routePath: "menu",
              component: "system/menu/index",
              sort: 3,
              visible: 1,
              icon: "menu",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 73,
                  parentId: 4,
                  name: "\u83DC\u5355\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:add",
                  children: []
                },
                {
                  id: 75,
                  parentId: 4,
                  name: "\u83DC\u5355\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:delete",
                  children: []
                },
                {
                  id: 74,
                  parentId: 4,
                  name: "\u83DC\u5355\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:edit",
                  children: []
                }
              ]
            },
            {
              id: 5,
              parentId: 1,
              name: "\u90E8\u95E8\u7BA1\u7406",
              type: "MENU",
              routeName: "Dept",
              routePath: "dept",
              component: "system/dept/index",
              sort: 4,
              visible: 1,
              icon: "tree",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 76,
                  parentId: 5,
                  name: "\u90E8\u95E8\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:add",
                  children: []
                },
                {
                  id: 77,
                  parentId: 5,
                  name: "\u90E8\u95E8\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:edit",
                  children: []
                },
                {
                  id: 78,
                  parentId: 5,
                  name: "\u90E8\u95E8\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:delete",
                  children: []
                }
              ]
            },
            {
              id: 6,
              parentId: 1,
              name: "\u5B57\u5178\u7BA1\u7406",
              type: "MENU",
              routeName: "Dict",
              routePath: "dict",
              component: "system/dict/index",
              sort: 5,
              visible: 1,
              icon: "dict",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 79,
                  parentId: 6,
                  name: "\u5B57\u5178\u7C7B\u578B\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:add",
                  children: []
                },
                {
                  id: 81,
                  parentId: 6,
                  name: "\u5B57\u5178\u7C7B\u578B\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:edit",
                  children: []
                },
                {
                  id: 84,
                  parentId: 6,
                  name: "\u5B57\u5178\u7C7B\u578B\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:delete",
                  children: []
                },
                {
                  id: 85,
                  parentId: 6,
                  name: "\u5B57\u5178\u6570\u636E\u65B0\u589E",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:add",
                  children: []
                },
                {
                  id: 86,
                  parentId: 6,
                  name: "\u5B57\u5178\u6570\u636E\u7F16\u8F91",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:edit",
                  children: []
                },
                {
                  id: 87,
                  parentId: 6,
                  name: "\u5B57\u5178\u6570\u636E\u5220\u9664",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:delete",
                  children: []
                }
              ]
            }
          ]
        },
        {
          id: 40,
          parentId: 0,
          name: "\u63A5\u53E3\u6587\u6863",
          type: "CATALOG",
          routeName: null,
          routePath: "/api",
          component: "Layout",
          sort: 7,
          visible: 1,
          icon: "api",
          redirect: "",
          perm: null,
          children: [
            {
              id: 41,
              parentId: 40,
              name: "Apifox",
              type: "MENU",
              routeName: null,
              routePath: "apifox",
              component: "demo/api/apifox",
              sort: 1,
              visible: 1,
              icon: "api",
              redirect: "",
              perm: null,
              children: []
            }
          ]
        },
        {
          id: 26,
          parentId: 0,
          name: "\u5E73\u53F0\u6587\u6863",
          type: "CATALOG",
          routeName: null,
          routePath: "/doc",
          component: "Layout",
          sort: 8,
          visible: 1,
          icon: "document",
          redirect: "https://juejin.cn/post/7228990409909108793",
          perm: null,
          children: [
            {
              id: 102,
              parentId: 26,
              name: "\u5E73\u53F0\u6587\u6863(\u5185\u5D4C)",
              type: "EXTLINK",
              routeName: null,
              routePath: "internal-doc",
              component: "demo/internal-doc",
              sort: 1,
              visible: 1,
              icon: "document",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 30,
              parentId: 26,
              name: "\u5E73\u53F0\u6587\u6863(\u5916\u94FE)",
              type: "EXTLINK",
              routeName: null,
              routePath: "https://juejin.cn/post/7228990409909108793",
              component: "",
              sort: 2,
              visible: 1,
              icon: "link",
              redirect: "",
              perm: null,
              children: []
            }
          ]
        },
        {
          id: 20,
          parentId: 0,
          name: "\u591A\u7EA7\u83DC\u5355",
          type: "CATALOG",
          routeName: null,
          routePath: "/multi-level",
          component: "Layout",
          sort: 9,
          visible: 1,
          icon: "cascader",
          redirect: "",
          perm: null,
          children: [
            {
              id: 21,
              parentId: 20,
              name: "\u83DC\u5355\u4E00\u7EA7",
              type: "MENU",
              routeName: null,
              routePath: "multi-level1",
              component: "demo/multi-level/level1",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [
                {
                  id: 22,
                  parentId: 21,
                  name: "\u83DC\u5355\u4E8C\u7EA7",
                  type: "MENU",
                  routeName: null,
                  routePath: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: null,
                  children: [
                    {
                      id: 23,
                      parentId: 22,
                      name: "\u83DC\u5355\u4E09\u7EA7-1",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      sort: 1,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: []
                    },
                    {
                      id: 24,
                      parentId: 22,
                      name: "\u83DC\u5355\u4E09\u7EA7-2",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      sort: 2,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: []
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          id: 36,
          parentId: 0,
          name: "\u7EC4\u4EF6\u5C01\u88C5",
          type: "CATALOG",
          routeName: null,
          routePath: "/component",
          component: "Layout",
          sort: 10,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 108,
              parentId: 36,
              name: "\u589E\u5220\u6539\u67E5",
              type: "MENU",
              routeName: null,
              routePath: "curd",
              component: "demo/curd/index",
              sort: 0,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 109,
              parentId: 36,
              name: "\u5217\u8868\u9009\u62E9\u5668",
              type: "MENU",
              routeName: null,
              routePath: "table-select",
              component: "demo/table-select/index",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 37,
              parentId: 36,
              name: "\u5BCC\u6587\u672C\u7F16\u8F91\u5668",
              type: "MENU",
              routeName: null,
              routePath: "wang-editor",
              component: "demo/wang-editor",
              sort: 2,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 38,
              parentId: 36,
              name: "\u56FE\u7247\u4E0A\u4F20",
              type: "MENU",
              routeName: null,
              routePath: "upload",
              component: "demo/upload",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 95,
              parentId: 36,
              name: "\u5B57\u5178\u7EC4\u4EF6",
              type: "MENU",
              routeName: null,
              routePath: "dict-demo",
              component: "demo/dict",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 39,
              parentId: 36,
              name: "\u56FE\u6807\u9009\u62E9\u5668",
              type: "MENU",
              routeName: null,
              routePath: "icon-selector",
              component: "demo/icon-selector",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            }
          ]
        },
        {
          id: 110,
          parentId: 0,
          name: "\u8DEF\u7531\u53C2\u6570",
          type: "CATALOG",
          routeName: null,
          routePath: "/route-param",
          component: "Layout",
          sort: 11,
          visible: 1,
          icon: "el-icon-ElementPlus",
          redirect: null,
          perm: null,
          children: [
            {
              id: 111,
              parentId: 110,
              name: "\u53C2\u6570(type=1)",
              type: "MENU",
              routeName: null,
              routePath: "route-param-type1",
              component: "demo/route-param",
              sort: 1,
              visible: 1,
              icon: "el-icon-Star",
              redirect: null,
              perm: null,
              children: []
            },
            {
              id: 112,
              parentId: 110,
              name: "\u53C2\u6570(type=2)",
              type: "MENU",
              routeName: null,
              routePath: "route-param-type2",
              component: "demo/route-param",
              sort: 2,
              visible: 1,
              icon: "el-icon-StarFilled",
              redirect: null,
              perm: null,
              children: []
            }
          ]
        },
        {
          id: 89,
          parentId: 0,
          name: "\u529F\u80FD\u6F14\u793A",
          type: "CATALOG",
          routeName: null,
          routePath: "/function",
          component: "Layout",
          sort: 12,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 97,
              parentId: 89,
              name: "Icons",
              type: "MENU",
              routeName: null,
              routePath: "icon-demo",
              component: "demo/icons",
              sort: 2,
              visible: 1,
              icon: "el-icon-Notification",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 90,
              parentId: 89,
              name: "Websocket",
              type: "MENU",
              routeName: null,
              routePath: "/function/websocket",
              component: "demo/websocket",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            },
            {
              id: 91,
              parentId: 89,
              name: "\u656C\u8BF7\u671F\u5F85...",
              type: "CATALOG",
              routeName: null,
              routePath: "other/:id",
              component: "demo/other",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: []
            }
          ]
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "menus/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 1,
          label: "\u7CFB\u7EDF\u7BA1\u7406",
          children: [
            {
              value: 2,
              label: "\u7528\u6237\u7BA1\u7406",
              children: [
                {
                  value: 105,
                  label: "\u7528\u6237\u67E5\u8BE2"
                },
                {
                  value: 31,
                  label: "\u7528\u6237\u65B0\u589E"
                },
                {
                  value: 32,
                  label: "\u7528\u6237\u7F16\u8F91"
                },
                {
                  value: 33,
                  label: "\u7528\u6237\u5220\u9664"
                },
                {
                  value: 88,
                  label: "\u91CD\u7F6E\u5BC6\u7801"
                },
                {
                  value: 106,
                  label: "\u7528\u6237\u5BFC\u5165"
                },
                {
                  value: 107,
                  label: "\u7528\u6237\u5BFC\u51FA"
                }
              ]
            },
            {
              value: 3,
              label: "\u89D2\u8272\u7BA1\u7406",
              children: [
                {
                  value: 70,
                  label: "\u89D2\u8272\u65B0\u589E"
                },
                {
                  value: 71,
                  label: "\u89D2\u8272\u7F16\u8F91"
                },
                {
                  value: 72,
                  label: "\u89D2\u8272\u5220\u9664"
                }
              ]
            },
            {
              value: 4,
              label: "\u83DC\u5355\u7BA1\u7406",
              children: [
                {
                  value: 73,
                  label: "\u83DC\u5355\u65B0\u589E"
                },
                {
                  value: 75,
                  label: "\u83DC\u5355\u5220\u9664"
                },
                {
                  value: 74,
                  label: "\u83DC\u5355\u7F16\u8F91"
                }
              ]
            },
            {
              value: 5,
              label: "\u90E8\u95E8\u7BA1\u7406",
              children: [
                {
                  value: 76,
                  label: "\u90E8\u95E8\u65B0\u589E"
                },
                {
                  value: 77,
                  label: "\u90E8\u95E8\u7F16\u8F91"
                },
                {
                  value: 78,
                  label: "\u90E8\u95E8\u5220\u9664"
                }
              ]
            },
            {
              value: 6,
              label: "\u5B57\u5178\u7BA1\u7406",
              children: [
                {
                  value: 79,
                  label: "\u5B57\u5178\u7C7B\u578B\u65B0\u589E"
                },
                {
                  value: 81,
                  label: "\u5B57\u5178\u7C7B\u578B\u7F16\u8F91"
                },
                {
                  value: 84,
                  label: "\u5B57\u5178\u7C7B\u578B\u5220\u9664"
                },
                {
                  value: 85,
                  label: "\u5B57\u5178\u6570\u636E\u65B0\u589E"
                },
                {
                  value: 86,
                  label: "\u5B57\u5178\u6570\u636E\u7F16\u8F91"
                },
                {
                  value: 87,
                  label: "\u5B57\u5178\u6570\u636E\u5220\u9664"
                }
              ]
            }
          ]
        },
        {
          value: 40,
          label: "\u63A5\u53E3\u6587\u6863",
          children: [
            {
              value: 41,
              label: "Apifox"
            }
          ]
        },
        {
          value: 26,
          label: "\u5E73\u53F0\u6587\u6863",
          children: [
            {
              value: 102,
              label: "\u5E73\u53F0\u6587\u6863(\u5185\u5D4C)"
            },
            {
              value: 30,
              label: "\u5E73\u53F0\u6587\u6863(\u5916\u94FE)"
            }
          ]
        },
        {
          value: 20,
          label: "\u591A\u7EA7\u83DC\u5355",
          children: [
            {
              value: 21,
              label: "\u83DC\u5355\u4E00\u7EA7",
              children: [
                {
                  value: 22,
                  label: "\u83DC\u5355\u4E8C\u7EA7",
                  children: [
                    {
                      value: 23,
                      label: "\u83DC\u5355\u4E09\u7EA7-1"
                    },
                    {
                      value: 24,
                      label: "\u83DC\u5355\u4E09\u7EA7-2"
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          value: 36,
          label: "\u7EC4\u4EF6\u5C01\u88C5",
          children: [
            {
              value: 108,
              label: "\u589E\u5220\u6539\u67E5"
            },
            {
              value: 109,
              label: "\u5217\u8868\u9009\u62E9\u5668"
            },
            {
              value: 37,
              label: "\u5BCC\u6587\u672C\u7F16\u8F91\u5668"
            },
            {
              value: 38,
              label: "\u56FE\u7247\u4E0A\u4F20"
            },
            {
              value: 95,
              label: "\u5B57\u5178\u7EC4\u4EF6"
            },
            {
              value: 39,
              label: "\u56FE\u6807\u9009\u62E9\u5668"
            }
          ]
        },
        {
          value: 110,
          label: "\u8DEF\u7531\u53C2\u6570",
          children: [
            {
              value: 111,
              label: "\u53C2\u6570(type=1)"
            },
            {
              value: 112,
              label: "\u53C2\u6570(type=2)"
            }
          ]
        },
        {
          value: 89,
          label: "\u529F\u80FD\u6F14\u793A",
          children: [
            {
              value: 97,
              label: "Icons"
            },
            {
              value: 90,
              label: "Websocket"
            },
            {
              value: 91,
              label: "\u656C\u8BF7\u671F\u5F85..."
            }
          ]
        }
      ],
      msg: "\u4E00\u5207ok"
    }
  },
  // 新增菜单
  {
    url: "menus",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u65B0\u589E\u83DC\u5355" + body.name + "\u6210\u529F"
      };
    }
  },
  // 获取菜单表单数据
  {
    url: "menus/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: menuMap[params.id],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 修改菜单
  {
    url: "menus/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u4FEE\u6539\u83DC\u5355" + body.name + "\u6210\u529F"
      };
    }
  },
  // 删除菜单
  {
    url: "menus/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "\u5220\u9664\u83DC\u5355" + params.id + "\u6210\u529F"
      };
    }
  }
]);
var menuMap = {
  1: {
    id: 1,
    parentId: 0,
    name: "\u7CFB\u7EDF\u7BA1\u7406",
    type: "CATALOG",
    routeName: "",
    routePath: "/system",
    component: "Layout",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "system",
    redirect: "/system/user",
    keepAlive: null,
    alwaysShow: null,
    params: null
  },
  2: {
    id: 2,
    parentId: 1,
    name: "\u7528\u6237\u7BA1\u7406",
    type: "MENU",
    routeName: "User",
    routePath: "user",
    component: "system/user/index",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "user",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null
  },
  3: {
    id: 3,
    parentId: 1,
    name: "\u89D2\u8272\u7BA1\u7406",
    type: "MENU",
    routeName: "Role",
    routePath: "role",
    component: "system/role/index",
    perm: null,
    visible: 1,
    sort: 2,
    icon: "role",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null
  },
  4: {
    id: 4,
    parentId: 1,
    name: "\u83DC\u5355\u7BA1\u7406",
    type: "MENU",
    routeName: "Menu",
    routePath: "menu",
    component: "system/menu/index",
    perm: null,
    visible: 1,
    sort: 3,
    icon: "menu",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null
  },
  5: {
    id: 5,
    parentId: 1,
    name: "\u90E8\u95E8\u7BA1\u7406",
    type: "MENU",
    routeName: "Dept",
    routePath: "dept",
    component: "system/dept/index",
    perm: null,
    visible: 1,
    sort: 4,
    icon: "tree",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null
  },
  6: {
    id: 6,
    parentId: 1,
    name: "\u5B57\u5178\u7BA1\u7406",
    type: "MENU",
    routeName: "Dict",
    routePath: "dict",
    component: "system/dict/index",
    perm: null,
    visible: 1,
    sort: 5,
    icon: "dict",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null
  }
};
export {
  menu_mock_default as default
};
