<template>
    <el-dialog v-model="dialogVisible" :title="`任务盘点: ${taskData?.taskName || ''}`" width="80%" destroy-on-close
        @closed="handleDialogClosed">
        <div v-loading="loading" class="inventory-dialog-container">
            <!-- 任务基本信息 -->
            <el-descriptions v-if="taskData" :column="3" border size="medium" class="mb-4">
                <el-descriptions-item label="任务编号">{{ taskData.taskId }}</el-descriptions-item>
                <el-descriptions-item label="发起部门">{{ taskData.deptName }}</el-descriptions-item>
                <el-descriptions-item label="截止时间">{{ taskData.deadline }}</el-descriptions-item>
                <el-descriptions-item label="资产数量">{{ taskData.assetCount }} 个</el-descriptions-item>
                <el-descriptions-item label="完成进度">
                    <div style="display: flex; align-items: center;">
                        <el-progress :percentage="taskData.completionRate || 0"
                            :status="getProgressStatus(taskData.completionRate)" :show-text="false"
                            style="flex-grow: 1;" />
                        <span style="margin-left: 10px; white-space: nowrap; min-width: 40px;">
                            {{ taskData.inventoryAssetsNum || 0 }}/{{ taskData.assetsNum || 0 }}
                        </span>
                    </div>
                </el-descriptions-item>
                <el-descriptions-item label="盘点状态">
                    <el-tag :type="getStatusType(taskData.inventoryStatus)">
                        {{ getStatusText(taskData.inventoryStatus) }}
                    </el-tag>
                </el-descriptions-item>
            </el-descriptions>

            <!-- 资产列表与过滤 -->
<!--            <div class="filter-actions mb-3">-->
<!--                <el-select v-model="filterStatus" placeholder="盘点状态" clearable style="width: 130px"-->
<!--                    @change="filterAssets">-->
<!--                    <el-option label="未盘点" value="0" />-->
<!--                    <el-option label="已盘点" value="1" />-->
<!--                </el-select>-->
<!--                <el-select v-model="filterType" placeholder="资产类型" clearable style="width: 130px; margin-left: 10px"-->
<!--                    @change="filterAssets">-->
<!--                    <el-option label="服务器" value="1" />-->
<!--                    <el-option label="网络设备" value="2" />-->
<!--                    <el-option label="安全设备" value="3" />-->
<!--                    <el-option label="信息系统" value="10" />-->
<!--                </el-select>-->
<!--                <el-input v-model="searchKeyword" placeholder="搜索资产" clearable style="width: 200px; margin-left: 10px"-->
<!--                    @input="filterAssets">-->
<!--                    <template #prefix>-->
<!--                        <el-icon>-->
<!--                            <Search />-->
<!--                        </el-icon>-->
<!--                    </template>-->
<!--                </el-input>-->
<!--            </div>-->

            <!-- 批量操作按钮 -->
<!--            <div class="batch-actions mb-3">-->
<!--                <el-button type="primary" :disabled="!selectedAssets.length" @click="openBatchInventoryDialog">-->
<!--                    <el-icon>-->
<!--                        <Edit />-->
<!--                    </el-icon> 批量盘点-->
<!--                </el-button>-->
<!--                <span v-if="selectedAssets.length > 0" class="selected-count">-->
<!--                    已选择 {{ selectedAssets.length }} 个资产-->
<!--                </span>-->
<!--            </div>-->
          <!-- Replace the current filter-actions and batch-actions divs with this: -->
          <div class="action-container mb-3">
            <div class="batch-actions">
              <el-button type="primary" :disabled="!selectedAssets.length" @click="openBatchInventoryDialog">
                <el-icon>
                  <Edit />
                </el-icon> 批量盘点
              </el-button>
              <span v-if="selectedAssets.length > 0" class="selected-count">
            已选择 {{ selectedAssets.length }} 个资产
        </span>
            </div>

            <div class="filter-actions">
              <el-select v-model="filterStatus" placeholder="盘点状态" clearable style="width: 130px"
                         @change="filterAssets">
                <el-option label="未盘点" value="0" />
                <el-option label="已盘点" value="1" />
              </el-select>
              <el-select v-model="filterType" placeholder="资产类型" clearable style="width: 130px; margin-left: 10px"
                         @change="filterAssets">
                <el-option label="服务器" value="1" />
                <el-option label="网络设备" value="2" />
                <el-option label="安全设备" value="3" />
                <el-option label="信息系统" value="10" />
                <el-option label="其他类型" value="other" /> <!-- 使用特殊值标识其他类型 -->
              </el-select>
              <el-input v-model="searchKeyword" placeholder="搜索资产" clearable style="width: 200px; margin-left: 10px"
                        @input="filterAssets">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </div>
          </div>
            <!-- 资产表格 -->
            <el-table :data="filteredAssets" border style="width: 100%" :row-class-name="getRowClassName"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="50" />
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="inventoryStatus" label="盘点状态" width="90" align="center">
                    <template #default="scope">
                        <el-tag :type="getInventoryStatusType(scope.row.inventoryStatus)">
                            {{ getInventoryStatusText(scope.row.inventoryStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="type" label="资产类型" width="90" align="center">
                    <template #default="scope">
                        <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="资产名称" min-width="120" show-overflow-tooltip align="center" />
                <el-table-column label="IP/域名" min-width="120" show-overflow-tooltip align="center">
                    <template #default="scope">
                        <span>{{ scope.row.ip || scope.row.url || '-' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="deptName" label="管理部门" min-width="100" show-overflow-tooltip align="center" />
                <el-table-column prop="managerName" label="管理人员" width="100" align="center" show-overflow-tooltip />
                <el-table-column label="变动状况" width="120" align="center">
                    <template #default="scope">
                        <el-popover placement="top" :width="300" trigger="hover" v-if="scope.row.changeStatus === '2'">
                            <template #reference>
                                <el-tag type="warning" class="cursor-pointer">
                                    已变更 <el-icon class="ml-1">
                                        <InfoFilled />
                                    </el-icon>
                                </el-tag>
                            </template>
                            <div>
                                <div class="popover-title">变更详情</div>
                                <el-divider class="my-2" />
                                <div class="popover-row">
                                    <span class="label">变更时间：</span>
                                    <span>{{ scope.row.inventoryTime || '-' }}</span>
                                </div>
                                <div class="popover-row">
                                    <span class="label">变更状态：</span>
                                    <span>{{ getChangeStatusText(scope.row.changeStatus) }}</span>
                                </div>
                                <div class="popover-row">
                                    <span class="label">操作人员：</span>
                                    <span>{{ scope.row.managerName || '当前用户' }}</span>
                                </div>
                            </div>
                        </el-popover>
                        <span v-else class="text-muted">{{ getChangeStatusText(scope.row.changeStatus) || '无变动'
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column fixed="right" label="操作" width="280" align="center">
                    <template #default="scope">
                        <el-button type="primary" link @click="viewAssetDetail(scope.row)">
                            查看详情
                        </el-button>
                        <el-button type="success" link @click="handleEditAsset(scope.row)"
                            :disabled="scope.row.inventoryStatus === '1'">
                            编辑
                        </el-button>
                        <el-button type="warning" link @click="markAsInventoried(scope.row)"
                            :disabled="scope.row.inventoryStatus === '1'">
                            确认盘点
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 空数据显示 -->
            <el-empty v-if="filteredAssets.length === 0" description="无匹配资产" />
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">关闭</el-button>
                <!-- <el-button type="primary" @click="handleSubmitInventory" :loading="submitLoading">
                    提交盘点结果
                </el-button> -->
            </div>
        </template>

        <!-- 资产详情弹窗 -->
        <asset-detail-view v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '1'"
            v-model:visible="assetDetailDialog.visible" :title="'查看服务器详情: ' + (assetDetailDialog.asset?.name || '')"
            :asset-id="assetDetailDialog.assetId" />

        <!-- 网络设备详情 -->
        <network-detail-view v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '2'"
            v-model:visible="assetDetailDialog.visible" :title="'查看网络设备详情: ' + (assetDetailDialog.asset?.name || '')"
            :asset-id="assetDetailDialog.assetId" />

        <!-- 安全设备详情 -->
        <safety-detail-view v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '3'"
            v-model:visible="assetDetailDialog.visible" :title="'查看安全设备详情: ' + (assetDetailDialog.asset?.name || '')"
            :asset-id="assetDetailDialog.assetId" />

        <!-- 信息系统详情 -->
        <system-detail-view v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '10'"
            v-model:visible="assetDetailDialog.visible" :title="'查看信息系统详情: ' + (assetDetailDialog.asset?.name || '')"
            :asset-id="assetDetailDialog.assetId" />

        <!-- 批量盘点弹窗 -->
        <inventory-batch-inventory-strategy-dialog v-if="batchStrategyDialog.visible"
            v-model:visible="batchStrategyDialog.visible" :title="'批量资产盘点'" :selected-ids="selectedAssetsIds"
            :taskId="props.taskId" @submitted="handleBatchStrategySubmitted" />

        <assets-dialog v-if="editDialog.visible && editDialog.assetType === '1'" v-model:visible="editDialog.visible"
            :title="'编辑服务器: ' + (editDialog.asset?.name || '')" :id="editDialog.asset?.id"
            @submitted="handleEditSubmitted(editDialog.asset?.id)" />

        <!-- 网络设备 -->
        <network-dialog v-if="editDialog.visible && editDialog.assetType === '2'" v-model:visible="editDialog.visible"
            :title="'编辑网络设备: ' + (editDialog.asset?.name || '')" :id="editDialog.asset?.id"
            @submitted="handleEditSubmitted(editDialog.asset?.id)" />

        <!-- 安全设备 -->
        <safety-dialog v-if="editDialog.visible && editDialog.assetType === '3'" v-model:visible="editDialog.visible"
            :title="'编辑安全设备: ' + (editDialog.asset?.name || '')" :id="editDialog.asset?.id"
            @submitted="handleEditSubmitted(editDialog.asset?.id)" />

        <!-- 信息系统 -->
        <system-dialog v-if="editDialog.visible && editDialog.assetType === '10'" v-model:visible="editDialog.visible"
            :title="'编辑信息系统: ' + (editDialog.asset?.name || '')" :id="editDialog.asset?.id"
            @submitted="handleEditSubmitted(editDialog.asset?.id)" />
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search, Edit, InfoFilled } from '@element-plus/icons-vue';
import InventoryAPI, { AssetInventoryAListVO, AssetInventorySingleForm, AssetInventoryBatchForm } from "@/api/assets_management/assets_inventory/index";
import InventoryDetailDialog from './InventoryDetailDialog.vue';
import InventoryBatchInventoryStrategyDialog from './InventoryBatchInventoryStrategyDialog.vue';
import { useUserStore } from '@/store/modules/user';

const userStore = useUserStore();
const userInfo = userStore.getUserInfo();
// 引入各类资产编辑组件
import AssetsDialog from '../../../DetailOfAssets/details/components/assetsDialog.vue'; // 服务器
import NetworkDialog from '../../../DetailOfAssets/networkFacility/components/networkdialog.vue'; // 网络设备
import SafetyDialog from '../../../DetailOfAssets/safetyFacility/components/safetydialog.vue'; // 安全设备
import SystemDialog from '../../../DetailOfAssets/system/components/SystemDialog.vue'; // 信息系统

//查看资产详情
import AssetDetailView from '../../../DetailOfAssets/details/components/AssetDetailView.vue';
import NetworkDetailView from '../../../DetailOfAssets/networkFacility/components/NetworkDetailView.vue';
import SafetyDetailView from '../../../DetailOfAssets/safetyFacility/components/SafetyDetailView.vue';
import SystemDetailView from '../../../DetailOfAssets/system/components/ViewSystemAssets.vue';
import { ta } from 'element-plus/es/locale';

// 添加一个集合来记录被编辑过的资产ID
const editedAssetIds = ref<Set<number>>(new Set());

const editDialog = reactive({
    visible: false,
    asset: null as any,
    assetType: ''
});

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    taskId: {
        type: [Number, String],
        default: ''
    }
});

const emit = defineEmits(['update:visible', 'submit-success']);

// 对话框显示状态
const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
});

// 定义资产对象接口
interface Asset {
    id: number;
    inventoryAssetsId: number;
    name: string;
    type: string | number;
    ip?: string;
    url?: string;
    deptId?: number;
    deptName?: string;
    managerName?: string;
    inventoryStatus: string;
    inventoryTime?: string | null;
    changeStatus?: string; // 资产变动状态（1无变动 2已变动 3已下线）
}

const taskData = ref<any>({
});

// 状态管理
const loading = ref(false);
const submitLoading = ref(false);
const filterStatus = ref('');
const filterType = ref('');
const searchKeyword = ref('');
const selectedAssets = ref<Asset[]>([]);
const assetsList = ref<Asset[]>([]);
const filteredAssets = ref<Asset[]>([]);

// 资产详情弹窗配置
const assetDetailDialog = reactive({
    visible: false,
    title: '资产详情',
    assetId: 0,
    asset: null as any,
    assetType: ''
});

// 批量策略弹窗
const batchStrategyDialog = reactive({
    visible: false
});

// 计算选中的资产IDs
const selectedAssetsIds = computed(() => {
    return selectedAssets.value.map(asset => asset.id);
});

// 获取盘点任务状态类型颜色
const getStatusType = (status: string | number) => {
    const statusMap: Record<string, string> = {
        '0': 'info',    // 待进行
        '1': 'primary',  // 进行中
        '2': 'success', // 已完成
        '3': 'warning',  // 逾期完成
        '4': 'danger'   // 逾期未完成
    };
    return statusMap[String(status)] || 'info';
};

// 获取盘点任务状态文本
const getStatusText = (status: string | number) => {
    const statusMap: Record<string, string> = {
        '0': '待进行',
        '1': '进行中',
        '2': '已完成',
        '3': '逾期完成',
        '4': '逾期未完成'
    };
    return statusMap[String(status)] || '未知状态';
};

// 获取进度条状态
const getProgressStatus = (percentage: number) => {
    if (percentage >= 100) return 'success';
    if (percentage > 0) return '';
    return 'exception';
};

// 获取资产类型名称
const getAssetTypeName = (type: string | number) => {
    const typeMap: Record<string, string> = {
        '1': '服务器',
        '2': '网络设备',
        '3': '安全设备',
        '10': '信息系统'
    };
    return typeMap[String(type)] || '其他类型';
};

// 获取资产盘点状态类型
const getInventoryStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
        '0': 'info',     // 未盘点
        '1': 'success',  // 已盘点
        '2': 'danger',    // 逾期未盘点
        '3': 'warning'   // 逾期已盘点
    };
    return statusMap[status] || 'info';
};

// 获取资产盘点状态文本
const getInventoryStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        '0': '未盘点',
        '1': '已盘点',
        '2': '逾期未盘点',
        '3': '逾期已盘点'
    };
    return statusMap[status] || '未知状态';
};

// 获取变更状态文本
const getChangeStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        '1': '无变动',
        '2': '已变动',
        '3': '已下线'
    };
    return statusMap[status] || '无变动';
};

// 获取行样式类名
const getRowClassName = ({ row }) => {
    if (row.inventoryStatus === '0') return 'unverified-row';
    if (row.changeStatus === '3') return 'offline-row';
    if (row.changeStatus === '2') return 'changed-row';
    return '';
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
    selectedAssets.value = selection;
};

// 过滤资产列表
const filterAssets = () => {
    const filtered = assetsList.value.filter(asset => {
        // 按状态筛选
        if (filterStatus.value && asset.inventoryStatus !== filterStatus.value) {
            return false;
        }

        // 按资产类型筛
        if (filterType.value) {
          if (filterType.value === 'other') {
            // 选择"其他类型"时，显示不属于1,2,3,10的类型
            return !['1', '2', '3', '10'].includes(String(asset.type));
          } else {
            // 正常类型筛选
            return String(asset.type) === filterType.value;
          }
        }

        // 按关键词搜索
        if (searchKeyword.value) {
            const keyword = searchKeyword.value.toLowerCase();
            return (asset.name || '').toLowerCase().includes(keyword) ||
                (asset.ip || '').toLowerCase().includes(keyword) ||
                (asset.url || '').toLowerCase().includes(keyword) ||
                (asset.deptName || '').toLowerCase().includes(keyword) ||
                (asset.managerName || '').toLowerCase().includes(keyword);
        }

        return true;
    });

    filteredAssets.value = filtered;
};

// 查看资产详情
const viewAssetDetail = (asset) => {
    assetDetailDialog.assetId = asset.id;
    assetDetailDialog.title = `资产详情: ${asset.name}`;
    assetDetailDialog.asset = asset;
    assetDetailDialog.assetType = String(asset.type);
    assetDetailDialog.visible = true;
};

// 编辑资产 - 修改此方法以记录被编辑的资产
const handleEditAsset = (asset) => {
    // 记录此资产已被编辑
    editedAssetIds.value.add(asset.id);

    editDialog.asset = asset;
    editDialog.assetType = String(asset.type);
    editDialog.visible = true;
};

// 编辑提交后回调
const handleEditSubmitted = (id: any) => {
    // 标记资产为已盘点
    markAsInventoried(assetsList.value.find(asset => asset.id === id) as Asset);
    console.log('资产编辑提交成功，重新加载资产数据');
    loadTaskAssets();
};

// 打开批量盘点弹窗
const openBatchInventoryDialog = () => {
    if (selectedAssets.value.length === 0) {
        ElMessage.warning('请先选择要批量盘点的资产');
        return;
    }
    batchStrategyDialog.visible = true;
};

// 标记资产为已盘点 - 修改此方法以根据是否编辑过设置变动状态
const markAsInventoried = (asset) => {
    if (asset.inventoryStatus === '1') {
        ElMessage.info('该资产已盘点');
        return;
    }

    // 检查资产是否被编辑过来判断是否有变动
    const isChanged = editedAssetIds.value.has(asset.id) ? '1' : '0';

    // 使用API提交盘点结果
    const singleInventoryData: AssetInventorySingleForm = {
        assetId: asset.id,
        isChange: isChanged  // 根据是否编辑过来设置变动状态
    };

    const taskId = Number(props.taskId);

    InventoryAPI.singleAssetInventory(taskId, singleInventoryData)
        .then(() => {
            ElMessage.success('已成功标记为已盘点');

            // 更新本地资产状态
            asset.inventoryStatus = '1'; // 已盘点
            asset.inventoryTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

            // 如果有变动，更新变动状态
            if (isChanged === '1') {
                asset.changeStatus = '2'; // 已变动
            } else {
                asset.changeStatus = '1'; // 无变动
            }

            // 更新任务完成率
            updateTaskCompletionRate();
            loadTaskAssets();
        })
        .catch(error => {
            console.error('提交盘点结果失败:', error);
            ElMessage.error('标记资产盘点状态失败');
        });
};

// 处理批量策略提交
const handleBatchStrategySubmitted = (formData) => {
    // 获取批量策略表单返回的数据（如果有）
    const { assetIds, isChange } = formData || {};

    // 更新所有选中资产的状态
    selectedAssets.value.forEach(asset => {
        asset.inventoryStatus = '1'; // 已盘点
        asset.inventoryTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

        // 根据批量表单设置变动状态（如果有）
        if (isChange === '1') {
            asset.changeStatus = '2'; // 已变动
            // 如果标记为已变动，添加到已编辑集合中
            editedAssetIds.value.add(asset.id);
        } else {
            asset.changeStatus = '1'; // 无变动
        }
    });

    // 更新任务完成率
    updateTaskCompletionRate();
    loadTaskAssets()
    ElMessage.success(`已成功批量盘点 ${selectedAssets.value.length} 个资产`);
};

// 更新任务完成率
const updateTaskCompletionRate = () => {
    const totalAssets = assetsList.value.length;
    if (totalAssets === 0) {
        // 更新父组件的完成率
        emit('submit-success', {
            taskId: props.taskId,
            completionRate: 100,
            inventoryStatus: '2' // 已完成
        });
        return;
    }

    const completedAssets = assetsList.value.filter(asset => asset.inventoryStatus === '1').length;
    const completionRate = Math.round((completedAssets / totalAssets) * 100);

    // 更新父组件的完成率
    emit('submit-success', {
        taskId: props.taskId,
        completionRate,
        // 如果完成率为100%，则状态变为已完成
        inventoryStatus: completionRate === 100 ? '2' : taskData?.inventoryStatus || '1'
    });
};

// 关闭对话框
const handleClose = () => {
    dialogVisible.value = false;
};

// 对话框关闭后
const handleDialogClosed = () => {
    // 重置过滤条件和选择
    filterStatus.value = '';
    filterType.value = '';
    searchKeyword.value = '';
    selectedAssets.value = [];
    filteredAssets.value = [];
    assetsList.value = [];
    // 重置已编辑的资产ID集合
    editedAssetIds.value.clear();
};

// 提交整个盘点任务
// const handleSubmitInventory = () => {
//     const unverifiedCount = assetsList.value.filter(asset => asset.inventoryStatus === '0').length;

//     if (unverifiedCount > 0) {
//         ElMessageBox.confirm(
//             `还有 ${unverifiedCount} 个资产未盘点，确定要提交盘点结果吗？`,
//             '提示',
//             {
//                 confirmButtonText: '确定提交',
//                 cancelButtonText: '继续盘点',
//                 type: 'warning'
//             }
//         ).then(() => {
//             submitInventoryTask();
//         }).catch(() => {
//             // 用户选择继续盘点，不做操作
//         });
//     } else {
//         submitInventoryTask();
//     }
// };

// 提交盘点任务到后端
// const submitInventoryTask = () => {
//     submitLoading.value = true;

//     // 调用完成盘点任务API
//     InventoryAPI.completeInventory(Number(props.taskId))
//         .then(() => {
//             ElMessage.success('盘点任务提交成功');

//             // 通知父组件
//             emit('submit-success', {
//                 taskId: props.taskId,
//                 completionRate: 100,
//                 inventoryStatus: '2' // 已完成
//             });

//             // 关闭弹窗
//             handleClose();
//         })
//         .catch(error => {
//             console.error('提交盘点任务失败:', error);
//             ElMessage.error('提交盘点任务失败，请重试');
//         })
//         .finally(() => {
//             submitLoading.value = false;
//         });
// };

// 加载任务资产数据
const loadTaskAssets = () => {
    if (!props.taskId) return;

    loading.value = true;

    // 使用API获取任务资产数据
    const taskId = Number(props.taskId);
    InventoryAPI.getAssetInventory(taskId)
        .then(response => {
            if (response) {
                const data = response;

                // 更新任务基本信息
                taskData.value = {
                    id: data.id,
                    taskId: data.id,
                    inventoryName: data.inventoryName || "",
                    status: data.status || "0",
                    deadline: data.deadline || "",
                    completionRate: data.assetsNum === 0 ? 100 : (data.inventoryProgress || 0),
                    inventoryAssetsNum: data.inventoryAssetsNum || 0,
                    assetCount: data.assetsNum || 0,
                    assetsNum: data.assetsNum || 0,
                    deptName: data.createDeptName || "",
                    createTime: data.createTime || "",
                    inventoryStatus: data.status || "0",
                };

                // 将API返回的资产数据转换为组件需要的格式
                if (data.assetsList && Array.isArray(data.assetsList)) {
                    assetsList.value = data.assetsList.map((item: AssetInventoryAListVO) => ({
                        id: item.assetsId,
                        inventoryAssetsId: item.inventoryAssetsId,
                        name: item.name || '',
                        type: item.type,
                        ip: item.ip || '',
                        url: item.url || '',
                        deptId: item.deptId,
                        deptName: item.deptName || '',
                        managerName: item.managerName || '',
                        inventoryStatus: item.inventoryStatus || '0',
                        inventoryTime: item.inventoryTime || item.createTime,
                        changeStatus: item.changeStatus // 资产变动状态
                    }));
                } else {
                    assetsList.value = [];
                }

                // 初始化过滤后的资产列表
                filterAssets();
            }
        })
        .catch(error => {
            console.error('获取任务资产失败:', error);
            ElMessage.error('获取任务资产数据失败');
            assetsList.value = [];
            filteredAssets.value = [];
        })
        .finally(() => {
            loading.value = false;
        });
};

// 监听对话框显示状态变化
watch(dialogVisible, (visible) => {
    if (visible && props.taskId) {
        loadTaskAssets();
    }
});

// 组件挂载
onMounted(() => {
    if (dialogVisible.value && props.taskId) {
        loadTaskAssets();
    }
});
</script>

<style scoped>
.inventory-dialog-container {
    padding: 0;
}

.filter-actions {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.batch-actions {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.mb-3 {
    margin-bottom: 16px;
}

.mb-4 {
    margin-bottom: 24px;
}

.selected-count {
    margin-left: 16px;
    color: #606266;
    font-size: 14px;
}

/* 表格行样式 */
:deep(.unverified-row) {
    --el-table-tr-bg-color: rgba(247, 247, 247, 0.15);
    color: var(--el-text-color-secondary);
}

:deep(.offline-row) {
    --el-table-tr-bg-color: rgba(230, 230, 230, 0.3);
    color: var(--el-text-color-disabled);
}

:deep(.changed-row) {
    --el-table-tr-bg-color: rgba(253, 246, 236, 0.3);
    color: var(--el-color-warning-dark-2);
}

.cursor-pointer {
    cursor: pointer;
}

.ml-1 {
    margin-left: 4px;
}

.text-muted {
    color: #909399;
    font-size: 13px;
}

.my-2 {
    margin-top: 8px;
    margin-bottom: 8px;
}

.popover-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
}

.popover-row {
    margin-top: 8px;
    font-size: 13px;
    line-height: 1.4;
    color: #606266;
}

.popover-row .label {
    color: #909399;
    margin-right: 4px;
}

.progress-container {
    display: flex;
    align-items: center;
    width: 100%;
}

.progress-count {
    min-width: 60px;
    text-align: right;
    font-size: 14px;
    color: #606266;
}

/* 确保进度条有足够空间 */
:deep(.el-progress-bar) {
    width: 100%;
    margin-right: 8px;
}

.action-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-actions {
  display: flex;
  align-items: center;
}

.filter-actions {
  display: flex;
  align-items: center;
  margin-left: auto; /* Push filters to the right */
}
</style>
