<template>
  <el-table :data="notices">
    <el-table-column prop="title" label="标题" />
    <el-table-column prop="type" label="类型">
      <template #default="{ row }">
        {{ MessageTypeLabels[row.type] }}
      </template>
    </el-table-column>
    <el-table-column prop="createdAt" label="创建时间" />
    <el-table-column label="操作">
      <template #default="{ row }">
        <el-button @click="$emit('view', row)">查看</el-button>
        <el-button @click="$emit('edit', row)">编辑</el-button>
        <el-button @click="$emit('delete', row.id)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script lang="ts" setup>
import { Notice ,MessageTypeLabels } from '@/enums/MessageTypeEnum';

defineProps<{
  notices: Notice[];
}>();

defineEmits<{
  (e: 'view', notice: Notice): void;
  (e: 'edit', notice: Notice): void;
  (e: 'delete', id: number): void;
}>();
</script>
