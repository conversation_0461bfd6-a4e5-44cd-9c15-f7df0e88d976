<template>
  <el-dialog 
    v-model="props.visible" 
    :title="title" 
    width="550px" 
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="是否定期资产盘点" prop="isPeriodicInventory">
        <el-select v-model="formData.isPeriodicInventory" placeholder="请选择" clearable>
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="盘点周期" prop="inventoryCycle">
        <el-select v-model="formData.inventoryCycle" placeholder="请选择盘点周期" clearable>
          <el-option label="每天" value="daily" />
          <el-option label="每周" value="weekly" />
          <el-option label="每月" value="monthly" />
          <el-option label="每季度" value="quarterly" />
          <el-option label="每年" value="yearly" />
        </el-select>
      </el-form-item>

      <el-form-item label="盘点期限" prop="inventoryLimit">
        <el-date-picker
          v-model="formData.inventoryLimit"
          type="datetime"
          placeholder="请选择盘点期限"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import InventoryAPI from "@/api/assets_management/assets_inventory/index"

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '盘点策略编辑'
  },
  id: {
    type: Number,
    default: undefined
  }
})

const emit = defineEmits(['update:visible', 'submitted'])

const formRef = ref<FormInstance>()
const loading = ref(false)
const submitLoading = ref(false)

const formData = reactive({
  isPeriodicInventory: false,
  inventoryCycle: '',
  inventoryLimit: '',
  notificationMethod: [] as string[],
  timeoutDuration: 24
})

const rules = reactive({
  isPeriodicInventory: [{ required: true, message: "请选择是否定期盘点", trigger: "change" }],
  inventoryCycle: [{ required: true, message: "请选择盘点周期", trigger: "change" }],
  inventoryLimit: [{ required: true, message: "请选择盘点期限", trigger: "change" }],
})

// 获取策略数据
const getStrategyData = async () => {
  if (!props.id) return
  
  loading.value = true
  try {
    const data = await InventoryAPI.getInventoryStrategy(props.id)
    // 填充表单数据
    formData.isPeriodicInventory = data.isPeriodicInventory
    formData.inventoryCycle = data.inventoryCycle || ''
    formData.inventoryLimit = data.inventoryLimit || ''
    // formData.notificationMethod = data.notificationMethod || []
    // formData.timeoutDuration = data.timeoutDuration || 24
  } catch (error) {
    console.error('获取盘点策略失败:', error)
    ElMessage.error('获取盘点策略数据失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!props.id) {
      ElMessage.error('缺少任务ID，无法更新策略')
      return
    }
    
    submitLoading.value = true
    
    await InventoryAPI.updateInventoryStrategy(props.id, formData)
    ElMessage.success('策略更新成功')
    emit('submitted')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    submitLoading.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  if (props.id) {
    getStrategyData()
  }
})
</script>

<style scoped>
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
