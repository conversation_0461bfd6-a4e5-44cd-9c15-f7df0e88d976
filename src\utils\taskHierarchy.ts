import { TaskRow, TaskType, TASK_TYPE_CONFIGS, TaskHierarchyValidator } from "@/types/project";

/**
 * 任务层级管理工具类
 * 提供层级约束验证、树结构操作等功能
 */
export class TaskHierarchyManager {
  /**
   * 验证创建任务的层级约束
   */
  static validateCreateTask(
    taskType: TaskType,
    parentId: number | null,
    allTasks: TaskRow[]
  ): { isValid: boolean; message?: string } {
    // 检查是否可以作为顶层
    if (!parentId || parentId === -1) {
      if (!TaskHierarchyValidator.canBeTopLevel(taskType)) {
        const config = TASK_TYPE_CONFIGS[taskType];
        return {
          isValid: false,
          message: `${config.label}不能作为顶层节点，只有里程碑可以作为顶层节点`,
        };
      }
      return { isValid: true };
    }

    // 查找父节点
    const parent = allTasks.find(task => task.id === parentId);
    if (!parent) {
      return {
        isValid: false,
        message: "找不到指定的父节点",
      };
    }

    // 检查父节点是否可以包含此类型的子项
    if (!TaskHierarchyValidator.canCreateChild(parent.type, taskType)) {
      const parentConfig = TASK_TYPE_CONFIGS[parent.type];
      const childConfig = TASK_TYPE_CONFIGS[taskType];
      return {
        isValid: false,
        message: `${parentConfig.label}不能包含${childConfig.label}类型的子项`,
      };
    }

    return { isValid: true };
  }

  /**
   * 获取指定节点可以创建的子类型选项
   */
  static getCreateOptions(parentType: TaskType): Array<{
    value: TaskType;
    label: string;
    icon: string;
  }> {
    const allowedTypes = TaskHierarchyValidator.getAllowedChildTypes(parentType);
    return allowedTypes.map(type => {
      const config = TASK_TYPE_CONFIGS[type];
      return {
        value: type,
        label: config.label,
        icon: config.icon,
      };
    });
  }

  /**
   * 获取全局（顶层）可创建的类型选项
   */
  static getTopLevelCreateOptions(): Array<{
    value: TaskType;
    label: string;
    icon: string;
  }> {
    const topLevelTypes = Object.values(TASK_TYPE_CONFIGS)
      .filter(config => config.canBeTopLevel)
      .map(config => config.type);

    return topLevelTypes.map(type => {
      const config = TASK_TYPE_CONFIGS[type];
      return {
        value: type,
        label: config.label,
        icon: config.icon,
      };
    });
  }

  /**
   * 检查节点是否可以有子项
   */
  static canHaveChildren(taskType: TaskType): boolean {
    return TASK_TYPE_CONFIGS[taskType].canHaveChildren;
  }

  /**
   * 获取节点的图标类型
   */
  static getNodeIcon(taskType: TaskType): string {
    return TASK_TYPE_CONFIGS[taskType].icon;
  }

  /**
   * 获取节点的显示标签
   */
  static getNodeLabel(taskType: TaskType): string {
    return TASK_TYPE_CONFIGS[taskType].label;
  }

  /**
   * 验证编辑任务时的层级约束
   */
  static validateEditTask(
    taskId: number,
    newType: TaskType,
    newParentId: number | null,
    allTasks: TaskRow[]
  ): { isValid: boolean; message?: string } {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
      return { isValid: false, message: "找不到要编辑的任务" };
    }

    // 如果类型改变为不能有子项的类型，检查是否有子项
    if (newType !== task.type && !TASK_TYPE_CONFIGS[newType].canHaveChildren) {
      const hasChildren = allTasks.some(t => t.parentId === taskId);
      if (hasChildren) {
        return {
          isValid: false,
          message: `${TASK_TYPE_CONFIGS[newType].label}不能包含子项，请先删除或移动所有子项`,
        };
      }
    }

    // 验证新的父子关系
    return this.validateCreateTask(newType, newParentId, allTasks);
  }

  /**
   * 构建层级树结构
   */
  static buildHierarchyTree(flatTasks: TaskRow[]): TaskRow[] {
    const taskMap = new Map<number, TaskRow>();
    const rootTasks: TaskRow[] = [];

    // 创建任务映射
    flatTasks.forEach(task => {
      taskMap.set(task.id, { ...task, children: [] });
    });

    // 构建树结构
    flatTasks.forEach(task => {
      const currentTask = taskMap.get(task.id)!;
      
      if (!task.parentId || task.parentId === -1) {
        // 验证顶层节点
        if (TaskHierarchyValidator.canBeTopLevel(task.type)) {
          rootTasks.push(currentTask);
        } else {
          console.warn(`任务 ${task.title} (${task.type}) 不能作为顶层节点`);
        }
      } else {
        const parent = taskMap.get(task.parentId);
        if (parent) {
          // 验证父子关系
          if (TaskHierarchyValidator.canCreateChild(parent.type, task.type)) {
            parent.children = parent.children || [];
            parent.children.push(currentTask);
          } else {
            console.warn(`层级关系错误: ${parent.type} 不能包含 ${task.type}`);
            // 将无效的子项移到根级别（如果允许）
            if (TaskHierarchyValidator.canBeTopLevel(task.type)) {
              rootTasks.push(currentTask);
            }
          }
        } else {
          console.warn(`找不到父节点 ${task.parentId}，将任务移到根级别`);
          if (TaskHierarchyValidator.canBeTopLevel(task.type)) {
            rootTasks.push(currentTask);
          }
        }
      }
    });

    return rootTasks;
  }

  /**
   * 获取所有子任务ID（递归）
   */
  static getAllChildIds(taskId: number, allTasks: TaskRow[]): number[] {
    const childIds: number[] = [];
    const directChildren = allTasks.filter(task => task.parentId === taskId);
    
    directChildren.forEach(child => {
      childIds.push(child.id);
      childIds.push(...this.getAllChildIds(child.id, allTasks));
    });

    return childIds;
  }

  /**
   * 检查是否存在循环引用
   */
  static hasCircularReference(
    taskId: number,
    newParentId: number,
    allTasks: TaskRow[]
  ): boolean {
    if (taskId === newParentId) return true;
    
    const childIds = this.getAllChildIds(taskId, allTasks);
    return childIds.includes(newParentId);
  }
}
