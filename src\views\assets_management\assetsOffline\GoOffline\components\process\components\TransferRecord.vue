<template>
  <el-dialog
    v-model="visible"
    title="流转记录"
    width="800px"
    :close-on-click-modal="false"
    append-to-body
    @open="getTransferRecord"
  >
    <!-- 使用表格替换时间线 -->
    <el-table :data="recordsList" style="width: 100%" border v-loading="loading">
      <!-- 序号列 -->
      <el-table-column type="index" label="序号" width="60" align="center" />
      
      <!-- 记录类型 -->
      <el-table-column prop="recordType" label="操作类型" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getTagType(row.name)">{{ row.name }}</el-tag>
        </template>
      </el-table-column>
      
      <!-- 处理人信息 -->
      <el-table-column label="处理人" width="150" align="center">
        <template #default="{ row }">
          <div class="handler-info">
            <span>{{ row.userName || '暂无' }}</span>
            <div class="handler-dept">{{ row.deptName || '暂无部门' }}</div>
          </div>
        </template>
      </el-table-column>
      
      <!-- 处理时间 -->
      <el-table-column prop="executeTime" label="处理时间" width="170" align="center" />
      
      <!-- 处理结果 -->
      <el-table-column prop="processingResults" label="处理结果" width="100" align="center">
        <template #default="{ row }">
          <el-tag v-if="row.processingResults" :type="getResultType(row.processingResults)">
            {{ row.processingResults }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <!-- 处理意见/内容 -->
      <el-table-column prop="commentContent" label="处理意见/内容" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip 
            :content="row.commentContent || row.msg" 
            placement="top" 
            :show-after="300"
            :hide-after="0"
          >
            <div class="content-cell">{{ row.commentContent || row.msg || '暂无内容' }}</div>
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 查看详情 -->
      <el-table-column label="操作" width="80" align="center">
        <template #default="{ row }">
          <el-button 
            type="primary" 
            link
            @click="showRecordDetail(row)"
          >
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 记录详情抽屉 -->
    <el-drawer
      v-model="drawer.visible"
      :title="drawer.title"
      direction="rtl"
      size="400px"
    >
      <div class="record-detail">
        <!-- 申请记录详情 -->
        <template v-if="drawer.currentRecord.name === '发起下线申请'">
          <div class="detail-item">
            <span class="detail-label">申请人：</span>
            <span class="detail-value">{{ drawer.currentRecord.userName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">申请部门：</span>
            <span class="detail-value">{{ drawer.currentRecord.deptName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">申请时间：</span>
            <span class="detail-value">{{ drawer.currentRecord.executeTime || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">申请内容：</span>
            <div class="detail-content">{{ drawer.currentRecord.commentContent || drawer.currentRecord.msg || '暂无' }}</div>
          </div>
        </template>
        
        <!-- 审核记录详情 -->
        <template v-else-if="drawer.currentRecord.name.includes('审核')">
          <div class="detail-item">
            <span class="detail-label">审核人：</span>
            <span class="detail-value">{{ drawer.currentRecord.userName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">审核部门：</span>
            <span class="detail-value">{{ drawer.currentRecord.deptName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">审核时间：</span>
            <span class="detail-value">{{ drawer.currentRecord.executeTime || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">审核结果：</span>
            <span class="detail-value">
              <el-tag :type="getResultType(drawer.currentRecord.processingResults)">
                {{ drawer.currentRecord.processingResults || '暂无' }}
              </el-tag>
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">审核意见：</span>
            <div class="detail-content">{{ drawer.currentRecord.commentContent || '暂无' }}</div>
          </div>
        </template>
        
        <!-- 评价记录详情 -->
        <template v-else-if="drawer.currentRecord.name === '评价结果'">
          <div class="detail-item">
            <span class="detail-label">评价人：</span>
            <span class="detail-value">{{ drawer.currentRecord.userName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">评价部门：</span>
            <span class="detail-value">{{ drawer.currentRecord.deptName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">评价时间：</span>
            <span class="detail-value">{{ drawer.currentRecord.executeTime || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">评价结果：</span>
            <span class="detail-value">
              <el-tag :type="getResultType(drawer.currentRecord.processingResults)">
                {{ drawer.currentRecord.processingResults || '暂无' }}
              </el-tag>
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">评价意见：</span>
            <div class="detail-content">{{ drawer.currentRecord.commentContent || '暂无' }}</div>
          </div>
        </template>

        <!-- 其他类型记录 -->
        <template v-else>
          <div class="detail-item">
            <span class="detail-label">处理人：</span>
            <span class="detail-value">{{ drawer.currentRecord.userName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">处理部门：</span>
            <span class="detail-value">{{ drawer.currentRecord.deptName || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">处理时间：</span>
            <span class="detail-value">{{ drawer.currentRecord.executeTime || '暂无' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">处理结果：</span>
            <span class="detail-value">
              <el-tag v-if="drawer.currentRecord.processingResults" :type="getResultType(drawer.currentRecord.processingResults)">
                {{ drawer.currentRecord.processingResults }}
              </el-tag>
              <span v-else>-</span>
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">处理意见：</span>
            <div class="detail-content">{{ drawer.currentRecord.commentContent || drawer.currentRecord.msg || '暂无' }}</div>
          </div>
        </template>
      </div>
    </el-drawer>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import offlineAPI from '@/api/assets_management/details/offline'

const props = defineProps({
  ticketId: {
    type: [String, Number],
    required: true
  }
})

const visible = ref(false)
const loading = ref(false)

// 记录列表数据
const recordsList = ref<any[]>([])

// 抽屉控制
const drawer = reactive({
  visible: false,
  title: '记录详情',
  currentRecord: {} as any
})

// 显示记录详情
const showRecordDetail = (record) => {
  drawer.currentRecord = { ...record }
  drawer.title = `${record.name}详情`
  drawer.visible = true
}

// 获取流转记录数据 - 直接使用getFlow接口
const getTransferRecord = async () => {
  try {
    loading.value = true
    // 直接使用getFlow接口获取流转记录
    const flowData = await offlineAPI.getFlowInfo(props.ticketId)

    if (flowData) {
      recordsList.value = flowData;
      console.log('流转记录获取成功:', recordsList.value)
    } else {
      recordsList.value = []
    }
  } catch (error) {
    console.error('获取流转记录失败:', error)
    ElMessage.error('获取流转记录失败')
    recordsList.value = []
  } finally {
    loading.value = false
  }
}

// 获取记录类型对应的标签类型
const getTagType = (recordType: string) => {
  if (recordType === '发起下线申请') return 'primary'
  if (recordType.includes('审核')) return 'success'
  if (recordType === '评价结果') return 'warning'
  return 'info'
}

// 获取处理结果对应的标签类型
const getResultType = (result: string) => {
  if (!result) return 'info'
  if (result.includes('通过') || result.includes('满意') || result.includes('已提交')) return 'success'
  if (result.includes('不通过') || result.includes('不满意')) return 'danger'
  return 'info'
}

defineExpose({
  visible
})
</script>

<style scoped>
.content-cell {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.handler-info {
  display: flex;
  flex-direction: column;
}

.handler-dept {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 记录详情样式 */
.record-detail {
  padding: 10px;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-label {
  font-weight: bold;
  color: #606266;
  display: block;
  margin-bottom: 8px;
}

.detail-value {
  color: #303133;
}

.detail-content {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  white-space: pre-wrap;
  margin-top: 4px;
  min-height: 60px;
}

.detail-rating {
  margin-top: 4px;
}
</style>
