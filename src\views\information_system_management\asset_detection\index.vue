<template>
  <div class="app-container">
    <!-- 三个统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <!-- 探测任务 -->
      <el-col :lg="8" :sm="12" :xs="24">
        <el-card shadow="hover" style="border-radius: 1rem;">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-gray-500 text-sm">探测任务</div>
              <div class="text-2xl font-bold mt-2 text-blue-600">120</div>
            </div>
            <el-icon style="font-size: 32px; color: #3b82f6;">
              <InfoFilled />
            </el-icon>
          </div>
        </el-card>
      </el-col>

      <!-- 执行中 -->
      <el-col :lg="8" :sm="12" :xs="24">
        <el-card shadow="hover" style="border-radius: 1rem;">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-gray-500 text-sm">执行中</div>
              <div class="text-2xl font-bold mt-2 text-amber-500">85</div>
            </div>
            <el-icon style="font-size: 32px; color: #f59e0b;">
              <Loading />
            </el-icon>
          </div>
        </el-card>
      </el-col>

      <!-- 已完成 -->
      <el-col :lg="8" :sm="12" :xs="24">
        <el-card shadow="hover" style="border-radius: 1rem;">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-gray-500 text-sm">已完成</div>
              <div class="text-2xl font-bold mt-2 text-green-500">15</div>
            </div>
            <el-icon style="font-size: 32px; color: #22c55e;">
              <CircleCheckFilled />
            </el-icon>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单部分 -->
    <div class="search-container p-4 rounded shadow-sm">
      <!-- 主搜索栏 -->
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="main-search-form" @submit.prevent>
        <el-form-item style="flex-grow: 1;">
          <el-input v-model="queryParams.sysname" clearable class="input-fixed-width-main" placeholder="搜索信息系统名称">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <!-- 搜索、重置按钮和高级筛选按钮 -->
        <el-form-item class="form-item buttons-group">
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search /> 搜索
          </el-button>
          <el-button @click="handleResetQuery()" class="search-btn">
            <i-ep-refresh /> 重置
          </el-button>
          <el-button type="text" @click="toggleAdvancedFilters" class="advanced-toggle-btn">
            {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
          </el-button>
        </el-form-item>
      </el-form>



      <!-- 高级筛选栏 -->
      <transition name="fade">
        <el-form v-if="showAdvancedFilters" :model="queryParams" :inline="true" class="advanced-search-form">
          <el-form-item label="探测ID" prop="detectionId">
            <el-input v-model="queryParams.detectionId" placeholder="探测ID" clearable class="!max-w-[130px]"
              @keyup.enter="handleQuery()" />
          </el-form-item>

          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="queryParams.taskName" placeholder="任务名称" clearable class="!max-w-[130px]"
              @keyup.enter="handleQuery()" />
          </el-form-item>

          <el-form-item label="探测状态" prop="detectionStatus">
            <el-select v-model="queryParams.detectionStatus" placeholder="请选择" clearable class="!w-[120px]">
              <el-option label="进行中" value="processing" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-form-item>

          <el-form-item label="探测时间" prop="detectionTime">
            <el-date-picker v-model="queryParams.detectionTime" type="datetimerange" range-separator="~"
              start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
          </el-form-item>

          <el-form-item label="定期探测" prop="regularDetectionStatus">
            <el-select v-model="queryParams.regularDetectionStatus" placeholder="请选择" clearable class="!w-[120px]">
              <el-option label="已启动" value="started" />
              <el-option label="未启动" value="not_started" />
            </el-select>
          </el-form-item>

          <el-form-item label="临时探测" prop="temporaryDetectionStatus">
            <el-select v-model="queryParams.temporaryDetectionStatus" placeholder="请选择" clearable class="!w-[120px]">
              <el-option label="已启动" value="started" />
              <el-option label="未启动" value="not_started" />
            </el-select>
          </el-form-item>
        </el-form>
      </transition>
    </div>


    <!-- 表格部分 -->
    <el-card shadow="never" class="table-container">
      <template #header>
        <div class="flex-x-between">
          <div>
            <el-button type="success" @click="handleOpenDialog()">
              <i-ep-plus />新增
            </el-button>
            <el-button type="danger" :disabled="ids.length === 0" @click="handleDelete()">
              <i-ep-delete />删除
            </el-button>
            <el-button type="primary" @click="handleBatchDetectionStrategy">
              <i-ep-edit />批量编辑探测策略
            </el-button>
          </div>

          <div>
          </div>

          <div>
            <!-- <el-button @click="handleImport">
              <template #icon><i-ep-upload /></template>
  导入
  </el-button>
  <el-button class="ml-3" @click="handleExport">
    <template #icon><i-ep-download /></template>
    导出
  </el-button> -->
          </div>
        </div>
      </template>

      <!-- 表格内容 -->
      <el-table ref="dataTableRef" v-loading="loading" :data="pageData" highlight-current-row border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column key="" label="序号" prop="" min-width="50" align="center" />
        <!-- <el-table-column key="id" label="探测ID" prop="id" min-width="100" align="center" /> -->
        <el-table-column key="" label="执行状态" prop="" min-width="60" align="center" />
        <el-table-column key="detectionName" label="任务名称" prop="detectionName" min-width="120" align="center" />
        <el-table-column label="探测记录" min-width="120" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="handleViewLog(scope.row)">
              查看日志
            </el-button>
          </template>
        </el-table-column>

        <el-table-column key="regularDetectionStatus" label="定期探测" prop="regularDetectionStatus" min-width="100"
          align="center">
          <template #default="scope">
            <!-- 根据定期探测状态值显示不同标签颜色和文本 -->
            <el-tag :type="(
              scope.row.regularDetectionStatus === 2 || scope.row.regularDetectionStatus === 'completed' ? 'success' :
                scope.row.regularDetectionStatus === 1 || scope.row.regularDetectionStatus === 'started' ? 'warning' :
                  'info'
            )">
              {{
                scope.row.regularDetectionStatus === 2 || scope.row.regularDetectionStatus === 'completed' ? '已完成' :
                  scope.row.regularDetectionStatus === 1 || scope.row.regularDetectionStatus === 'started' ? '已启动' :
                    '未启动'
              }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column key="immediateDetectionTime" label="最新探测时间" prop="immediateDetectionTime" min-width="160"
          align="center" />
        <el-table-column key="immediateDetectionTime" label="下一次探测时间" prop="immediateDetectionTime" min-width="160"
          align="center" />
        <!-- <el-table-column key="temporaryDetectionStatus" label="临时探测" prop="temporaryDetectionStatus" min-width="100"
          align="center">
          <template #default="scope">
   
            <el-tag :type="(
              scope.row.temporaryDetectionStatus === 2 || scope.row.temporaryDetectionStatus === 'completed' ? 'success' :
                scope.row.temporaryDetectionStatus === 1 || scope.row.temporaryDetectionStatus === 'started' ? 'warning' :
                  'info'
            )">
              {{
                scope.row.temporaryDetectionStatus === 2 || scope.row.temporaryDetectionStatus === 'completed' ? '已完成' :
                  scope.row.temporaryDetectionStatus === 1 || scope.row.temporaryDetectionStatus === 'started' ? '已启动' :
                    '未启动'
              }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column fixed="right" label="操作" width="300" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="goDetection(scope.row.id)">
              立即探测
            </el-button>
            <el-button v-hasPerm="['system:detection:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />编辑
            </el-button>
            <el-button v-hasPerm="['system:detection:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
    </el-card>

    <!-- 弹窗组件 -->
    <detection-dialog v-model:visible="dialog.visible" :title="dialog.title" :id="dialog.id" @submitted="handleQuery" />
    <detection-strategy-dialog v-model:visible="strategyDialog.visible" :title="strategyDialog.title"
      :id="strategyDialog.id" @submitted="handleQuery" />
    <batch-detection-strategy-dialog v-model:visible="batchStrategyDialog.visible" :title="batchStrategyDialog.title"
      :selected-ids="ids" @submitted="handleQuery" />
    <batch-notification-strategy-dialog v-model:visible="batchNotificationDialog.visible"
      :title="batchNotificationDialog.title" :selected-ids="ids" @submitted="handleQuery" />
    <detection-log-dialog v-model:visible="logDialog.visible" :detection-id="logDialog.detectionId"
      :task-name="logDialog.taskName" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Detection",
  inheritAttrs: false,
});

import DetectionAPI, { DetectionPageVO, DetectionPageQuery } from "@/api/assets_management/assets_detection/index";
import DetectionDialog from './components/DetectionAddDialog.vue'
import DetectionStrategyDialog from './components/DetectionStrategyDialog.vue'
import BatchDetectionStrategyDialog from './components/DetectionBatchDetectionStrategyDialog.vue'
import DetectionLogDialog from './components/DetectionLogDialog.vue' // 导入日志弹窗组件
import assetsAPI, { assetsPageVO } from '@/api/assets_management/details/assets'

const queryFormRef = ref(ElForm);
const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

// 高级筛选开关状态
const showAdvancedFilters = ref(false)

// 切换高级筛选显示
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

// 查询参数
const queryParams = reactive<DetectionPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 表格数据
const pageData = ref<DetectionPageVO[]>([]);

// 弹窗状态
const dialog = reactive({
  visible: false,
  title: "",
  id: undefined as number | undefined
});

// 策略编辑弹窗
const strategyDialog = reactive({
  visible: false,
  title: "",
  id: undefined as number | undefined
})

// 批量探测策略弹窗
const batchStrategyDialog = reactive({
  visible: false,
  title: "批量编辑探测策略"
})

// 批量通知策略弹窗
const batchNotificationDialog = reactive({
  visible: false,
  title: "批量编辑通知策略"
})

// 日志弹窗状态
const logDialog = reactive({
  visible: false,
  detectionId: undefined as number | undefined,
  taskName: '探测任务'
});

/** 查询资产探测 */
function handleQuery() {
  loading.value = true;
  DetectionAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

// 查看日志
function handleViewLog(row: any) {
  logDialog.visible = true;
  logDialog.detectionId = row.id;
  logDialog.taskName = row.detectionName || row.taskName || '探测任务';
}

function goDetection(id: any) {
  DetectionAPI.detectionById(id).then((res) => {
    handleQuery();
  });
}

/** 选择变更 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  dialog.id = id;
  dialog.title = id ? "修改资产探测" : "新增资产探测";
}

/** 删除方法 */
function handleDelete(id?: number) {
  const deleteIds = id ? [id] : [...ids.value];
  if (deleteIds.length === 0) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      DetectionAPI.deleteByIds(deleteIds.join(','))
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    }
  );
}

// 探测策略编辑
const handleDetectionStrategy = (id: number) => {
  strategyDialog.visible = true
  strategyDialog.id = id
  strategyDialog.title = "探测策略编辑"
}

// 批量探测策略
const handleBatchDetectionStrategy = () => {
  // if (ids.value.length === 0) {
  //   ElMessage.warning("请选择要编辑的资产")
  //   return
  // }
  batchStrategyDialog.visible = true
}

// 批量通知策略
const handleBatchNotificationStrategy = () => {
  // if (ids.value.length === 0) {
  //   ElMessage.warning("请选择要编辑的资产")
  //   return
  // }
  batchNotificationDialog.visible = true
}

// 导入导出方法
const handleImport = () => {
  console.log('导入')
}

const handleExport = () => {
  console.log('导出')
}

onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.flex-x-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ml-3 {
  margin-left: 12px;
}

search-container {
  max-width: 100%;
}

/* 主搜索栏样式 */
.main-search-form {
  display: flex;
  gap: 16px;
  align-items: center;
  width: 100%;
}

.input-fixed-width-main {
  flex-grow: 1;
  /* 占满剩余宽度 */
  min-width: 200px;
  /* 最小宽度，可调 */
  max-width: 100%;
  /* 最大不超出容器 */
  width: auto;
  /* 清除固定宽度 */
}


/* 高级筛选表单容器 */
.advanced-search-form {
  width: 1300px;
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
  align-items: flex-start;
}

/* 每个表单项固定宽度，防止拉伸 */
.form-item {
  width: 300px;
  flex: none;
}

/* 左对齐标签 */
.advanced-search-form .el-form-item__label {
  width: 80px !important;
  text-align: left !important;
}


.input-fixed-width {
  width: 180px;
}

.form-item.full-width {
  width: 100%;
}

.form-item.full-width .input-fixed-width {
  width: 100%;
}

.advanced-toggle-btn {
  background-color: rgb(34, 197, 94);
  width: 100px;
  color: #fff;
}

.search-btn {
  width: 100px;
  background-color: rgb(221, 151, 82);
  color: #fff;
  ;
}
</style>
