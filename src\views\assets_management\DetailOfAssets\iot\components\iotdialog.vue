<template>
  <el-dialog
    v-model="props.visible"
    :title="title"
    width="80%"
    :before-close="handleClose"
  >
    <!-- 默认插槽：包含所有主要内容 -->
    <template #default>
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="basicFormRef"
            :model="form.basic"
            :rules="rules.basic"
            label-width="150px"
            class="device-form"
          >
            <!-- 设备基本信息分组 -->
            <div class="form-section">
              <div class="section-title">设备基本信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备名称" prop="name">
                    <el-input
                      v-model="form.basic.name"
                      placeholder="设备的名称标识符"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备类型" prop="deviceType">
                    <el-select
                      v-model="form.basic.deviceType"
                      placeholder="如传感器、物联网设备、传感器等"
                      class="w-full"
                    >
                      <el-option label="传感器" value="传感器" />
                      <el-option label="物联网设备" value="物联网设备" />
                      <el-option label="智能终端" value="智能终端" />
                      <el-option label="控制器" value="控制器" />
                      <el-option label="监测设备" value="监测设备" />
                      <el-option label="其他" value="其他" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="系统名称及版本"
                    prop="systemnameAndVersion"
                  >
                    <el-input
                      v-model="form.basic.systemnameAndVersion"
                      placeholder="请输入系统名称及版本"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="操作系统（探测）" prop="os">
                    <el-input
                      v-model="form.basic.os"
                      placeholder="自动填充"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备型号" prop="model">
                    <el-input
                      v-model="form.basic.model"
                      placeholder="请输入设备型号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备序列号" prop="serialNumber">
                    <el-input
                      v-model="form.basic.serialNumber"
                      placeholder="请输入设备序列号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备厂家" prop="manufacturer">
                    <el-input
                      v-model="form.basic.manufacturer"
                      placeholder="请输入厂家"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="购买日期" prop="purchaseDate">
                    <el-date-picker
                      v-model="form.basic.purchaseDate"
                      type="date"
                      placeholder="请选择购买日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      class="w-full"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="维保截止日期" prop="warrantyEndDate">
                    <el-date-picker
                      v-model="form.basic.warrantyEndDate"
                      type="date"
                      placeholder="请选择维保截止日期"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      class="w-full"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备描述" prop="machineDesc">
                    <el-input
                      v-model="form.basic.machineDesc"
                      placeholder="请输入十字以内设备描述"
                      class="w-full"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <!-- IP配置与端口信息 -->
            <div class="form-section">
              <div class="section-title">IP配置与开放端口</div>

              <!-- 动态IP配置项 -->
              <div
                v-for="(ipConfig, configIndex) in form.ipConfigs"
                :key="configIndex"
                class="ip-config-item"
                :style="{
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  padding: '16px',
                  marginBottom: '16px',
                }"
              >
                <!-- IP配置表单项 -->
                <el-row :gutter="20" style="margin-bottom: 16px">
                  <el-col :span="12">
                    <el-form-item
                      label="IP地址标识"
                      :prop="`ipConfigs.${configIndex}.ipAddressLabel`"
                      :rules="ipConfigRules.ipAddressLabel"
                    >
                      <el-input
                        v-model="ipConfig.ipAddressLabel"
                        placeholder="请输入五个字以内IP地址标注"
                        class="w-full"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="IP地址"
                      :prop="`ipConfigs.${configIndex}.ipAddress`"
                      :rules="ipConfigRules.ipAddress"
                    >
                      <el-input
                        v-model="ipConfig.ipAddress"
                        placeholder="请输入IP地址"
                        class="w-full"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 端口操作按钮区域 -->
                <div class="table-operations" style="margin-bottom: 16px">
                  <el-button
                    type="primary"
                    icon="Plus"
                    size="small"
                    @click="openPortDialog(ipConfig)"
                  >
                    新增端口
                  </el-button>
                </div>

                <!-- 端口表格 -->
                <el-table
                  :data="ipConfig.ports"
                  border
                  style="width: 100%"
                  max-height="500px"
                >
                  <el-table-column
                    prop="ipAddress"
                    label="IP地址"
                    width="180"
                  />
                  <el-table-column prop="port" label="端口号" width="120" />
                  <el-table-column prop="protocol" label="协议" width="100" />
                  <el-table-column
                    prop="service"
                    label="服务名称"
                    width="180"
                  />
                  <el-table-column
                    prop="description"
                    label="描述"
                    min-width="200"
                  />
                  <el-table-column label="操作" width="180" fixed="right">
                    <template #default="scope">
                      <el-button
                        size="small"
                        type="primary"
                        text
                        @click="
                          handleEditPort(scope.row, scope.$index, ipConfig)
                        "
                      >
                        编辑
                      </el-button>
                      <el-button
                        size="small"
                        type="danger"
                        text
                        @click="handleDeletePort(scope.$index, ipConfig)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 按钮区域：删除IP配置 -->
                <div
                  style="
                    margin-top: 16px;
                    display: flex;
                    justify-content: flex-end;
                  "
                >
                  <el-button
                    type="danger"
                    icon="Delete"
                    @click="deleteIpConfig(configIndex)"
                    :disabled="form.ipConfigs.length <= 1"
                  >
                    删除IP配置
                  </el-button>
                </div>
              </div>

              <div>
                <!-- 添加IP配置按钮 -->
                <el-button
                  type="primary"
                  icon="Plus"
                  @click="addIpConfig"
                  style="margin-bottom: 16px"
                >
                  添加IP配置
                </el-button>
              </div>
            </div>
            <!-- 其他信息分组 -->
            <div class="form-section">
              <div class="section-title">其他信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="创建时间" prop="createTime">
                    <el-input
                      v-model="form.basic.createTime"
                      placeholder="自动填充"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="下线时间" prop="offTime">
                    <el-date-picker
                      v-model="form.basic.offTime"
                      type="datetime"
                      placeholder="选择时间"
                      value-format="YYYY/MM/日"
                      class="w-full"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="更新时间" prop="updateTime">
                    <el-input
                      v-model="form.basic.updateTime"
                      placeholder="自动填充"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="上线时间" prop="onTime">
                    <el-date-picker
                      v-model="form.basic.onTime"
                      type="datetime"
                      placeholder="选择时间"
                      value-format="YYYY/MM/日"
                      class="w-full"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="上线负责人">
                    <el-input
                      v-model="form.basic.OwnerName"
                      placeholder="请输入负责人"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系方式" prop="ownerPhone">
                    <el-input
                      v-model="form.basic.manufacturer"
                      placeholder="请输入负责人联系方式"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 设备位置信息 -->
            <div class="form-section">
              <div class="section-title">设备位置信息</div>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="安装位置" prop="installLocation">
                    <el-input
                      v-model="form.basic.installLocation"
                      placeholder="设备安装的具体位置，如楼层、房间号等"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="地理坐标" prop="gpsCoordinates">
                    <el-input
                      v-model="form.basic.gpsCoordinates"
                      placeholder="设备的GPS坐标"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </el-tab-pane>

        <!-- 管理单位基本信息 -->
        <el-tab-pane label="管理单位" name="management">
          <el-form
            ref="managementFormRef"
            :model="form.management"
            :rules="rules.management"
            label-width="150px"
            class="server-form"
          >
            <!-- 基本信息分组 -->
            <div class="form-section">
              <div class="section-title">基本信息</div>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="管理单位" prop="deptId">
                    <el-tree-select
                      v-model="form.management.deptId"
                      placeholder="请选择所属部门"
                      :data="deptOptions"
                      filterable
                      check-strictly
                      :render-after-expand="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="办公地址" prop="officeAddress">
                    <el-input
                      v-model="form.management.officeAddress"
                      placeholder="填写办公地址"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 领导信息部分 -->
            <div class="form-section">
              <div class="section-title">管理领导信息</div>

              <el-form-item label="选择管理领导" prop="manager">
                <el-button
                  type="primary"
                  @click="openSelectLeader"
                  :disabled="!form.management.deptId"
                >
                  <el-icon>
                    <User />
                  </el-icon>
                  选择部门领导
                </el-button>
              </el-form-item>

              <!-- 显示已选管理领导信息 -->
              <template v-if="peopleDialog.selectedLeaderId">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="领导姓名" :span="2">
                      {{ form.management.manager || "加载中..." }}
                    </el-descriptions-item>
                    <el-descriptions-item label="办公电话">
                      {{ form.management.officePhone || "未填写" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="手机号码">
                      {{ form.management.mobile || "未填写" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.management.email || "未填写" }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>

            <!-- 系统管理员信息部分 -->
            <div class="form-section">
              <div class="section-title">系统管理员信息</div>

              <el-form-item label="选择系统管理员" prop="sysAdmin">
                <el-button
                  type="primary"
                  @click="openSelectAdmin"
                  :disabled="!form.management.deptId"
                >
                  <el-icon>
                    <User />
                  </el-icon>
                  选择系统管理员
                </el-button>
              </el-form-item>

              <!-- 显示已选系统管理员信息 -->
              <template v-if="peopleDialog.selectedAdminId">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="管理员姓名" :span="2">
                      {{ form.management.sysAdmin || "加载中..." }}
                    </el-descriptions-item>
                    <el-descriptions-item label="办公电话">
                      {{ form.management.adminOfficePhone || "未填写" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="手机号码">
                      {{ form.management.adminMobile || "未填写" }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.management.adminEmail || "未填写" }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>
          </el-form>
        </el-tab-pane>

        <!-- 运维单位部分 -->
        <el-tab-pane label="运维单位" name="operations">
          <el-form
            ref="operationsFormRef"
            :model="form.operations"
            :rules="rules.operations"
            label-width="150px"
            class="server-form"
          >
            <div class="form-section">
              <div class="section-title">
                <el-icon>
                  <Office />
                </el-icon>
                运维服务商信息
              </div>

              <!-- 服务商选择组件 -->
              <el-form-item label="选择服务商" prop="providerId">
                <div class="service-provider-select">
                  <select-service-provider
                    v-model="form.operations.providerId"
                    @provider-selected="handleOperationsProviderSelected"
                  />
                </div>
              </el-form-item>

              <!-- 显示已选服务商信息 -->
              <template v-if="form.operations.providerDetail">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="公司名称" :span="2">
                      {{ form.operations.providerDetail.providerName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目负责人">
                      {{ form.operations.providerDetail.projectManager }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                      {{ form.operations.providerDetail.managerMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.operations.providerDetail.managerEmail }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术负责人">
                      {{ form.operations.providerDetail.techLeader }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术电话">
                      {{ form.operations.providerDetail.techMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术邮箱" :span="2">
                      {{ form.operations.providerDetail.techEmail }}
                    </el-descriptions-item>
                    <el-descriptions-item label="服务商描述" :span="2">
                      {{ form.operations.providerDetail.description || "-" }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- 人员选择组件：放在默认插槽内 -->
      <select-people
        v-model:visible="peopleDialog.leaderVisible"
        :title="'选择管理领导'"
        :department-id="form.management.deptId"
        :selected-user-id="peopleDialog.selectedLeaderId"
        @selected="handleLeaderSelected"
      />

      <select-people
        v-model:visible="peopleDialog.adminVisible"
        :title="'选择系统管理员'"
        :department-id="form.management.deptId"
        :selected-user-id="peopleDialog.selectedAdminId"
        @selected="handleAdminSelected"
      />

      <!-- 端口信息弹窗：放在默认插槽内 -->
      <el-dialog
        v-model="portDialogVisible"
        :title="portDialogTitle"
        width="500px"
      >
        <el-form
          ref="portFormRef"
          :model="currentPort"
          :rules="portRules"
          label-width="120px"
          class="port-form"
        >
          <el-form-item label="录入方式" prop="ipAddress">
            <el-input
              v-model="currentPort.loginWay"
              placeholder="自动填充"
              disabled
            />
          </el-form-item>
          <el-form-item label="录入时间" prop="loginTime">
            <el-date-picker
              v-model="form.basic.loginTime"
              type="datetime"
              placeholder="选择时间"
              value-format="YYYY/MM/日"
              class="w-full"
              disabled
            />
          </el-form-item>
          <el-form-item label="协议" prop="protocol">
            <el-select
              v-model="currentPort.protocol"
              placeholder="请选择协议类型"
            >
              <el-option label="TCP" value="TCP" />
              <el-option label="UDP" value="UDP" />
              <el-option label="HTTP" value="HTTP" />
              <el-option label="HTTPS" value="HTTPS" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务名称" prop="service">
            <el-input
              v-model="currentPort.service"
              placeholder="例如：Web服务"
            />
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="currentPort.description"
              type="textarea"
              :rows="3"
              placeholder="请输入端口用途描述"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="portDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePort">确定</el-button>
        </template>
      </el-dialog>
    </template>

    <!-- 底部按钮插槽 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          {{ props.id ? "更 新" : "创 建" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance } from "element-plus";
import DeptAPI from "@/api/dept";
import assetsAPI from "@/api/assets_management/details/assets";
import UserAPI from "@/api/user";
import SelectServiceProvider from "@/components/AssetsManage/SelectServiceProvider.vue";
import SelectPeople from "@/components/AssetsManage/SelectPeople.vue";
import { formatLocalDateTime } from "@/utils/dateUtils";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增物联网设备",
  },
  id: {
    type: Number,
  },
});

const emit = defineEmits(["update:visible", "submitted"]);

// 表单引用
const basicFormRef = ref<FormInstance>();
const managementFormRef = ref<FormInstance>();
const operationsFormRef = ref<FormInstance>();
const portFormRef = ref<FormInstance>(); // 端口表单引用

// 当前活动的标签页
const activeTab = ref("basic");
const loading = ref(false);

// 部门选项
const deptOptions = ref<any[]>([]);

// 人员选择相关的响应式数据
const peopleDialog = reactive({
  leaderVisible: false,
  adminVisible: false,
  selectedLeaderId: null,
  selectedAdminId: null,
});

// 端口弹窗相关
const portDialogVisible = ref(false);
const portDialogTitle = ref("新增端口信息");
const currentPortIndex = ref(-1); // 用于编辑时记录索引
const currentPortConfig = ref<any>(null); // 当前操作的IP配置项
const currentPort = reactive({
  ipAddress: "",
  port: null,
  protocol: "",
  service: "",
  description: "",
});

// IP配置验证规则
const ipConfigRules = reactive({
  ipAddressLabel: [
    { required: true, message: "请输入IP地址标识", trigger: "blur" },
    { max: 5, message: "IP地址标识不能超过5个字符", trigger: "blur" },
  ],
  ipAddress: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern:
        /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
      message: "请输入有效的IP地址",
      trigger: "blur",
    },
  ],
});

// 初始表单数据 - 包含多个IP配置项
const initialForm = {
  basic: {
    name: "",
    deviceType: "",
    systemnameAndVersion: "",
    os: "",
    model: "",
    serialNumber: "",
    manufacturer: "",
    purchaseDate: "",
    warrantyEndDate: "",
    registerTime: new Date(),
    hardwareConfig: "",
    networkInterfaceType: "",
    ipAddress: "",
    MACAddress: "",
    firmwareVersion: "",
    installLocation: "",
    gpsCoordinates: "",
    usageStatus: "使用中",
    relatedDevices: "",
    remark: "",
    machineDesc: "",
    type: 4, // 物联网设备类型
  },
  management: {
    deptId: undefined,
    officeAddress: "",
    manager: "",
    officePhone: "",
    mobile: "",
    email: "",
    sysAdmin: "",
    adminOfficePhone: "",
    adminMobile: "",
    adminEmail: "",
  },
  operations: {
    providerId: undefined,
    providerDetail: null,
  },
  // 多个IP配置项，每个包含标识、地址和端口列表
  ipConfigs: [
    {
      ipAddressLabel: "",
      ipAddress: "",
      ports: [],
    },
  ],
};

// 表单数据
const form = reactive(JSON.parse(JSON.stringify(initialForm)));

// 处理服务商选择
const handleOperationsProviderSelected = (provider: any) => {
  console.log("用户重新选择的服务提供商:", provider);
  form.operations.providerId = provider.id;
  form.operations.providerDetail = provider;
};

// 表单验证规则
const rules = reactive({
  basic: {
    name: [
      { required: true, message: "请输入设备名称", trigger: "blur" },
      {
        min: 2,
        max: 50,
        message: "设备名称长度应为2-50个字符",
        trigger: "blur",
      },
    ],
    deviceType: [
      { required: false, message: "请选择设备类型", trigger: "change" },
    ],
    registerTime: [
      { required: true, message: "请选择登记时间", trigger: "change" },
    ],
    usageStatus: [
      { required: false, message: "请选择使用状态", trigger: "change" },
    ],
  },
  management: {
    deptId: [{ required: false, message: "请选择管理单位", trigger: "change" }],
  },
  operations: {
    providerId: [
      { required: false, message: "请选择服务商", trigger: "change" },
    ],
  },
});

// 端口表单验证规则
const portRules = reactive({
  ipAddress: [
    { required: true, message: "请输入IP地址", trigger: "blur" },
    {
      pattern:
        /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
      message: "请输入有效的IP地址",
      trigger: "blur",
    },
  ],
  port: [
    { required: true, message: "请输入端口号", trigger: "blur" },
    {
      type: "number",
      min: 1,
      max: 65535,
      message: "端口号必须在1-65535之间",
      trigger: "blur",
    },
  ],
  protocol: [{ required: true, message: "请选择协议类型", trigger: "change" }],
  service: [{ required: true, message: "请输入服务名称", trigger: "blur" }],
});

// 打开选择领导的弹窗
const openSelectLeader = () => {
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.leaderVisible = true;
};

// 打开选择管理员的弹窗
const openSelectAdmin = () => {
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.adminVisible = true;
};

// 处理管理领导选择
const handleLeaderSelected = (user: any) => {
  console.log("选择的领导:", user);
  peopleDialog.selectedLeaderId = user.id;
  form.management.manager = user.nickname || user.username;
  form.management.mobile = user.mobile || "";
  form.management.email = user.email || "";
  form.management.officePhone = user.officePhone || "";
};

// 系统管理员选择处理函数
const handleAdminSelected = (user: any) => {
  console.log("选择的管理员:", user);
  peopleDialog.selectedAdminId = user.id;
  form.management.sysAdmin = user.nickname || user.username;
  form.management.adminMobile = user.mobile || "";
  form.management.adminEmail = user.email || "";
  form.management.adminOfficePhone = user.officePhone || "";
};

// 添加新的IP配置项
const addIpConfig = () => {
  form.ipConfigs.push({
    ipAddressLabel: "",
    ipAddress: "",
    ports: [],
  });
};

// 删除IP配置项
const deleteIpConfig = (index: number) => {
  if (form.ipConfigs.length <= 1) {
    ElMessage.warning("至少保留一个IP配置");
    return;
  }

  ElMessageBox.confirm(
    "确定要删除这个IP配置吗？相关的端口信息也会被删除",
    "确认删除",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      form.ipConfigs.splice(index, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除
    });
};

// 打开端口弹窗
const openPortDialog = (ipConfig: any) => {
  // 重置表单
  if (portFormRef.value) {
    portFormRef.value.resetFields();
  }
  // 清空当前端口数据
  Object.assign(currentPort, {
    ipAddress: ipConfig.ipAddress, // 默认使用当前IP配置的地址
    port: null,
    protocol: "",
    service: "",
    description: "",
  });
  currentPortIndex.value = -1;
  currentPortConfig.value = ipConfig;
  portDialogTitle.value = "新增端口信息";
  portDialogVisible.value = true;
};

// 编辑端口
const handleEditPort = (row: any, index: number, ipConfig: any) => {
  // 重置表单
  if (portFormRef.value) {
    portFormRef.value.resetFields();
  }
  // 复制数据到当前端口对象
  Object.assign(currentPort, { ...row });
  currentPortIndex.value = index;
  currentPortConfig.value = ipConfig;
  portDialogTitle.value = "编辑端口信息";
  portDialogVisible.value = true;
};

// 删除端口
const handleDeletePort = (index: number, ipConfig: any) => {
  ElMessageBox.confirm("确定要删除这条端口信息吗？", "确认删除", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      ipConfig.ports.splice(index, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除
    });
};

// 保存端口信息
const savePort = async () => {
  if (!portFormRef.value || !currentPortConfig.value) return;

  try {
    await portFormRef.value.validate();

    if (currentPortIndex.value === -1) {
      // 新增
      currentPortConfig.value.ports.push({ ...currentPort });
      ElMessage.success("新增成功");
    } else {
      // 编辑
      currentPortConfig.value.ports[currentPortIndex.value] = {
        ...currentPort,
      };
      ElMessage.success("修改成功");
    }

    portDialogVisible.value = false;
  } catch (error) {
    console.error("端口信息验证失败:", error);
  }
};

// 从API数据映射到表单结构
function mapApiDataToForm(apiData: any) {
  console.log("API返回的数据:", apiData);

  try {
    // 基本信息映射
    form.basic.name = apiData.name || "";
    form.basic.deviceType = apiData.deviceType || "";
    form.basic.model = apiData.model || "";
    form.basic.serialNumber = apiData.serialNumber || "";
    form.basic.manufacturer = apiData.factory || "";
    form.basic.purchaseDate = apiData.purchaseDate || "";
    form.basic.warrantyEndDate = apiData.warrantyEndDate || "";
    form.basic.registerTime = apiData.createTime
      ? new Date(apiData.createTime)
      : new Date();
    form.basic.hardwareConfig = apiData.hardwareConfig || "";
    form.basic.networkInterfaceType = apiData.networkInterfaceType || "";
    form.basic.ipAddress = apiData.ip || "";
    form.basic.MACAddress = apiData.macAddress || "";
    form.basic.firmwareVersion = apiData.firmwareVersion || "";
    form.basic.installLocation = apiData.local || "";
    form.basic.gpsCoordinates = apiData.gpsCoordinates || "";
    form.basic.usageStatus = apiData.usageStatus || "使用中";
    form.basic.relatedDevices = apiData.relatedDevices || "";
    form.basic.remark = apiData.notes || apiData.remark || "";
    form.basic.systemnameAndVersion = apiData.systemnameAndVersion || "";
    form.basic.os = apiData.os || "";
    form.basic.machineDesc = apiData.machineDesc || "";

    // 管理单位信息
    form.management.deptId = apiData.deptId;
    form.management.officeAddress = apiData.depAddress || "";

    // 管理领导信息
    form.management.manager = apiData.leader || "";
    peopleDialog.selectedLeaderId = apiData.managerId;
    form.management.officePhone = apiData.leaderPhone || "";
    form.management.mobile = apiData.leaderPhone1 || "";
    form.management.email = apiData.leaderEmail || "";

    // 系统管理员信息
    form.management.sysAdmin = apiData.ownerName || "";
    peopleDialog.selectedAdminId = apiData.sysManagerId;
    form.management.adminOfficePhone = apiData.ownerPhone1 || "";
    form.management.adminMobile = apiData.ownerPhone || "";
    form.management.adminEmail = apiData.ownerEmail || "";

    // 服务提供商信息
    if (apiData.provider) {
      form.operations.providerId = apiData.provider.id;
      form.operations.providerDetail = apiData.provider;
    } else {
      form.operations.providerId = null;
      form.operations.providerDetail = null;
    }

    // IP配置与端口信息映射
    // 假设API返回的格式是包含多个IP配置项的数组
    if (apiData.ipConfigs && Array.isArray(apiData.ipConfigs)) {
      form.ipConfigs = apiData.ipConfigs.map((config: any) => ({
        ipAddressLabel: config.ipAddressLabel || "",
        ipAddress: config.ipAddress || "",
        ports: config.ports || [],
      }));
    } else {
      // 兼容旧数据格式
      form.ipConfigs = [
        {
          ipAddressLabel: "",
          ipAddress: apiData.ip || "",
          ports: apiData.ports || [],
        },
      ];
    }

    console.log("成功映射API数据到表单");
  } catch (error) {
    console.error("映射API数据到表单时出错:", error);
    ElMessage.error("数据加载失败");
  }
}

// 获取用户详细信息
async function loadUserInformation() {
  try {
    // 加载系统管理员信息
    if (
      peopleDialog.selectedAdminId &&
      (!form.management.sysAdmin || !form.management.adminMobile)
    ) {
      const adminData = await UserAPI.getFormData(peopleDialog.selectedAdminId);
      if (adminData) {
        form.management.sysAdmin =
          form.management.sysAdmin || adminData.nickname || adminData.username;
        form.management.adminMobile =
          form.management.adminMobile || adminData.mobile;
        form.management.adminOfficePhone =
          form.management.adminOfficePhone || adminData.officePhone;
        form.management.adminEmail =
          form.management.adminEmail || adminData.email;
      }
    }

    // 加载管理领导信息
    if (
      peopleDialog.selectedLeaderId &&
      (!form.management.manager || !form.management.mobile)
    ) {
      const leaderData = await UserAPI.getFormData(
        peopleDialog.selectedLeaderId
      );
      if (leaderData) {
        form.management.manager =
          form.management.manager || leaderData.nickname || leaderData.username;
        form.management.officePhone =
          form.management.officePhone || leaderData.officePhone;
        form.management.mobile = form.management.mobile || leaderData.mobile;
        form.management.email = form.management.email || leaderData.email;
      }
    }
  } catch (error) {
    console.error("加载用户信息失败:", error);
  }
}

// 从表单数据映射到API需要的格式
function mapFormToApiData() {
  console.log("正在映射表单数据到API格式...");

  const apiData = {
    type: 4,
    name: form.basic.name,
    deviceType: form.basic.deviceType,
    model: form.basic.model,
    serialNumber: form.basic.serialNumber,
    factory: form.basic.manufacturer,
    purchaseDate: form.basic.purchaseDate,
    warrantyEndDate: form.basic.warrantyEndDate,
    createTime: formatLocalDateTime(form.basic.registerTime),
    hardwareConfig: form.basic.hardwareConfig,
    networkInterfaceType: form.basic.networkInterfaceType,
    ip: form.basic.ipAddress,
    macAddress: form.basic.MACAddress,
    firmwareVersion: form.basic.firmwareVersion,
    local: form.basic.installLocation,
    gpsCoordinates: form.basic.gpsCoordinates,
    usageStatus: form.basic.usageStatus,
    relatedDevices: form.basic.relatedDevices,
    notes: form.basic.remark,
    deptId: form.management.deptId,
    depAddress: form.management.officeAddress,
    // 系统管理员信息
    ownerName: form.management.sysAdmin,
    sysManagerId: peopleDialog.selectedAdminId,
    ownerPhone: form.management.adminMobile,
    ownerPhone1: form.management.adminOfficePhone,
    ownerEmail: form.management.adminEmail,
    // 管理领导信息
    leader: form.management.manager,
    managerId: peopleDialog.selectedLeaderId,
    leaderPhone: form.management.officePhone,
    leaderPhone1: form.management.mobile,
    leaderEmail: form.management.email,
    // 服务商信息
    providerId: form.operations.providerId,
    status: "1", // 默认正常状态
    // 端口信息 - 包含所有IP配置项
    ipConfigs: form.ipConfigs,
  };

  console.log("映射后的API数据:", apiData);
  return apiData;
}

// 重置表单方法
const resetForm = () => {
  const forms = [
    basicFormRef,
    managementFormRef,
    operationsFormRef,
    portFormRef,
  ];
  forms.forEach((formRef) => {
    if (formRef.value) {
      formRef.value.resetFields();
    }
  });

  Object.assign(form, JSON.parse(JSON.stringify(initialForm)));
  peopleDialog.selectedLeaderId = null;
  peopleDialog.selectedAdminId = null;
  activeTab.value = "basic";
  portDialogVisible.value = false;
  currentPortConfig.value = null;
};

// 关闭弹窗前的回调
const handleClose = () => {
  ElMessageBox.confirm("确认关闭？未保存的数据将丢失")
    .then(() => {
      emit("update:visible", false);
      resetForm();
    })
    .catch(() => {});
};

// 取消
const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

// 提交表单方法
const submitForm = async () => {
  const validateForms = [
    basicFormRef.value,
    managementFormRef.value,
    operationsFormRef.value,
  ].filter(Boolean);

  try {
    // 验证所有表单
    await Promise.all(validateForms.map((form) => form?.validate()));

    loading.value = true;
    const apiData = mapFormToApiData();

    if (props.id) {
      await assetsAPI.update(props.id, apiData);
      ElMessage.success("更新成功");
    } else {
      await assetsAPI.add(apiData);
      ElMessage.success("创建成功");
    }

    emit("submitted");
    handleCancel();
  } catch (error) {
    console.error("提交表单失败:", error);
    if (error !== false) {
      // 不是验证失败
      ElMessage.error("操作失败");
    }
  } finally {
    loading.value = false;
  }
};

// 监听ID变化加载表单数据
watch(
  () => [props.visible, props.id],
  async ([visible, id]) => {
    if (!visible) return;

    if (id) {
      try {
        loading.value = true;
        const data = await assetsAPI.getServerDetail(id);
        mapApiDataToForm(data);

        // 加载用户详细信息
        await loadUserInformation();
      } catch (error) {
        console.error("加载数据失败:", error);
        ElMessage.error("加载数据失败");
      } finally {
        loading.value = false;
      }
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

// 获取部门选项
onMounted(() => {
  DeptAPI.getOptions()
    .then((data) => {
      deptOptions.value = data;
    })
    .catch((error) => {
      console.error("加载部门数据失败:", error);
      ElMessage.error("加载部门数据失败");
    });
});
</script>

<style scoped>
.device-form {
  max-width: 900px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.provider-info-card {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-extra-light);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-divider__text) {
  font-weight: 500;
  color: var(--el-color-primary);
}

/* IP配置相关样式 */
.table-operations {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.empty-state {
  padding: 40px 0;
  text-align: center;
}

.port-form {
  margin-top: 20px;
}

/* 确保操作列固定效果正常 */
:deep(.el-table__fixed-right) {
  height: 100% !important;
}
</style>
