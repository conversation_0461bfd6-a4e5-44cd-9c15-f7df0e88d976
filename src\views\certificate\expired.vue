<template>
  <div class="cert-page" :class="getPageBackgroundClass()">
    <!-- 背景装饰图案 -->
    <div class="bg-pattern-1"></div>
    <div class="bg-pattern-2"></div>
    <div class="bg-pattern-3"></div>

    <!-- 返回登录页按钮 -->
    <div class="back-link-container">
      <el-button link @click="backToLogin" class="back-link">
        <i-ep-back />返回登录页
      </el-button>
    </div>

    <div class="cert-container" v-loading="loading">
      <!-- 头部 Logo 和标题 -->
      <div class="cert-header">
        <div class="logo-area">
          <h1 class="logo-title">网络安全工作管理平台</h1>
        </div>
      </div>

      <!-- 证书状态卡片 -->
      <div class="main-content">
        <!-- 状态提示横幅 -->
        <div class="status-banner" :class="getBannerClass()">
          <div class="banner-icon-wrapper" :class="getBannerIconClass()">
            <i-ep-warning v-if="certificateStatus.type === 'danger' || certificateStatus.type === 'warning'"
              class="banner-icon" />
            <i-ep-circle-check v-else-if="certificateStatus.type === 'success'" class="banner-icon" />
            <i-ep-question-filled v-else class="banner-icon" />
          </div>
          <div class="banner-content">
            <h3 class="banner-title" :class="{ 'text-center': !hasCertificate.value }">
              {{ getBannerTitle() }}
            </h3>
            <p class="banner-desc" :class="{ 'text-center': !hasCertificate.value }">
              {{ getBannerDescription() }}
            </p>
          </div>
        </div>

        <!-- 移除注释的状态卡片区域，简化界面 -->

        <!-- 证书详情与操作指引 - 并排布局 -->
        <div class="cert-details-section">
          <!-- 左侧 - 证书信息 -->
          <div class="details-column">
            <div class="section-header">
              <i-ep-info-filled class="header-icon" />
              <span>证书信息</span>
            </div>

            <div class="cert-info-grid">
              <div class="info-row">
                <span class="info-label">产品名称:</span>
                <span class="info-value">{{ certificateInfo.productName || '- -' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">产品序列号:</span>
                <span class="info-value">{{ certificateInfo.serialNumber || '- -' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">证书类型:</span>
                <span class="info-value">
                  <el-tag size="small" :type="certificateInfo.certType === 'official' ? 'success' : 'warning'">
                    {{ certificateInfo.certType === 'official' ? '正式证书' : '临时证书' }}
                  </el-tag>
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">用户名称:</span>
                <span class="info-value">{{ certificateInfo.userName || '- -' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">授权有效期:</span>
                <span class="info-value" v-if="certificateInfo.validFrom && certificateInfo.validTo">
                  {{ formatDate(certificateInfo.validFrom) }} 至
                  <span :class="certificateStatus.type === 'danger' ? 'text-danger font-bold' : ''">
                    {{ formatDate(certificateInfo.validTo) }}
                  </span>
                </span>
                <span class="info-value" v-else>- -</span>
              </div>
              <!-- 移动授权范围到这里 -->
              <div class="info-row">
                <span class="info-label">授权范围:</span>
                <span class="info-value">{{ certificateInfo.modules?.join(', ') || '全部模块' }}</span>
              </div>
            </div>
          </div>

          <!-- 右侧 - 操作指引 -->
          <div class="details-column">
            <div class="section-header">
              <i-ep-guide class="header-icon" />
              <span>操作指引</span>
            </div>

            <!-- 添加一个提示语句，增加内容区高度 -->
            <p class="guide-introduction">
              {{
                certificateStatus.type === 'danger' ?
                  '您的证书已过期，需要更新授权证书：' :
                  (!hasCertificate.value ?
                    '系统需要有效的授权证书才能正常工作：' :
              '请按照以下步骤操作：')
              }}
            </p>

            <ul class="guide-list">
              <li>
                <div class="guide-icon-wrapper">
                  <i-ep-right class="guide-icon" />
                </div>
                <span>{{ certificateStatus.type === 'danger' ? '请上传新的授权证书以恢复系统功能' : '请上传有效的授权证书' }}</span>
              </li>
              <li>
                <div class="guide-icon-wrapper">
                  <i-ep-right class="guide-icon" />
                </div>
                <span>支持标准格式(.p12)的证书文件</span>
              </li>
              <li>
                <div class="guide-icon-wrapper">
                  <i-ep-right class="guide-icon" />
                </div>
                <span>如需获取授权证书，请联系销售人员</span>
              </li>
            </ul>
            <div class="divider"></div>

            <div class="action-buttons">
              <el-button type="success" @click="openUploadDialog" class="action-button">
                <i-ep-upload-filled class="button-icon" />上传新证书
              </el-button>
              <el-button type="info" plain @click="refreshCertificate" :loading="loading" class="action-button">
                <i-ep-refresh class="button-icon" />刷新信息
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="cert-footer">
        <p>
          Copyright © 2021 - 2024 HOOK All Rights Reserved. 重庆虎克信息安全技术有限责任公司
          版权所有
        </p>
        <p>渝ICP备20006496号-3</p>
      </div>
    </div>

    <!-- 上传证书对话框 -->
    <CertificateUpload v-model:visible="uploadDialogVisible" @upload-success="handleUploadSuccess" />

    <!-- 联系支持对话框 -->
    <!-- <el-dialog v-model="contactDialogVisible" title="联系技术支持" width="500px" destroy-on-close
      custom-class="support-dialog">
      <div class="support-content">
        <div class="support-item">
          <div class="support-icon-wrapper">
            <i-ep-phone />
          </div>
          <div class="support-text">
            <h4>电话支持</h4>
            <p>************ (工作日 9:00 - 18:00)</p>
          </div>
        </div>
        <div class="support-item">
          <div class="support-icon-wrapper">
            <i-ep-message />
          </div>
          <div class="support-text">
            <h4>邮件支持</h4>
            <p><EMAIL></p>
          </div>
        </div>
      </div>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
// 脚本部分基本保持不变，仅添加一个新方法获取横幅图标的类名
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import { ElMessage } from 'element-plus';
import { format, differenceInDays, isValidDate, getCertificateStatus } from '@/utils/dateUtils';
import { useRouter } from 'vue-router';
import CertificateUpload from './components/CertificateUpload.vue';
import AuthAPI from "@/api/auth";
import { clearCertificateCache } from '@/utils/certificateChecker';

// 防止重复请求的标志
const isRequesting = ref(false);
const loading = ref(false);
const uploadDialogVisible = ref(false);
const contactDialogVisible = ref(false);

// 防止API请求后的路由跳转
const preventRedirect = ref(true);

// 证书信息，预设默认值避免未定义错误
const certificateInfo = reactive({
  productName: '虎克安全流程OA',
  serialNumber: '',
  userName: '',
  certType: 'temporary',
  validFrom: '',
  validTo: '',
  modules: ['全部模块']
});

// 状态缓存 - 防止重复计算
const statusCache = ref({
  key: '',
  status: { text: '未上传', type: 'warning' }
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  try {
    return format(new Date(dateString), 'yyyy-MM-dd');
  } catch (error) {
    return dateString;
  }
};

// 计算剩余天数
const remainingDays = computed(() => {
  if (!certificateInfo.validTo || !isValidDate(certificateInfo.validTo)) return -999;
  return differenceInDays(new Date(certificateInfo.validTo), new Date());
});

// 计算证书状态 - 使用缓存避免重复渲染
const certificateStatus = computed(() => {
  if (!certificateInfo.validTo) {
    return { text: '未上传', type: 'warning' };
  }

  // 使用缓存机制减少状态变化引起的重新渲染
  const key = certificateInfo.validTo;
  if (statusCache.value.key === key) {
    return statusCache.value.status;
  }

  const status = getCertificateStatus(certificateInfo.validTo);
  statusCache.value = { key, status };
  return status;
});

// 工具函数 - 获取样式类
const getIconClass = (type: string) => {
  switch (type) {
    case 'danger': return 'icon-danger';
    case 'warning': return 'icon-warning';
    case 'success': return 'icon-info';
    default: return 'icon-warning';
  }
};

const getTextClass = (type: string) => {
  switch (type) {
    case 'danger': return 'text-danger';
    case 'warning': return 'text-warning';
    default: '';
  }
};

const hasCertificate = computed(() => {
  return Boolean(certificateInfo.validTo);
});

// 获取横幅图标类
const getBannerIconClass = () => {
  if (!hasCertificate.value) {
    return 'icon-warning';
  } else if (certificateStatus.value.type === 'danger') {
    return 'icon-danger';
  } else if (certificateStatus.value.type === 'warning') {
    return 'icon-warning';
  } else {
    return 'icon-success';
  }
};

// 获取页面背景样式类
const getPageBackgroundClass = () => {
  if (!hasCertificate.value) {
    return 'bg-no-cert';
  } else if (certificateStatus.value.type === 'danger') {
    return 'bg-expired';
  } else if (certificateStatus.value.type === 'warning') {
    return 'bg-warning';
  } else {
    return 'bg-valid';
  }
};

// 获取横幅样式类
const getBannerClass = () => {
  if (!hasCertificate.value) {
    return 'no-cert';
  } else if (certificateStatus.value.type === 'danger') {
    return 'expired';
  } else if (certificateStatus.value.type === 'warning') {
    return 'warning';
  } else {
    return 'valid';
  }
};

// 获取横幅标题
const getBannerTitle = () => {
  if (!hasCertificate.value) {
    return '未检测到授权证书';
  } else if (certificateStatus.value.type === 'danger') {
    return '授权证书已过期';
  } else if (certificateStatus.value.type === 'warning') {
    return '授权证书即将过期';
  } else {
    return '授权证书有效';
  }
};

// 获取横幅描述
const getBannerDescription = () => {
  if (!hasCertificate.value) {
    return '系统未检测到有效的授权证书，请上传证书以正常使用系统。';
  } else if (certificateStatus.value.type === 'danger') {
    return `您的证书已过期 ${Math.abs(remainingDays.value)} 天，系统功能将受到限制。请更新授权证书以恢复全部功能。`;
  } else if (certificateStatus.value.type === 'warning') {
    return `您的证书将在 ${remainingDays.value} 天后过期，请尽快更新证书以确保系统正常使用。`;
  } else {
    return `您的证书状态正常，剩余有效期 ${remainingDays.value} 天。`;
  }
};

const router = useRouter();

// 打开上传对话框
const openUploadDialog = () => {
  uploadDialogVisible.value = true;
};

// 上传成功处理 - 避免循环刷新
const handleUploadSuccess = () => {
  ElMessage.success('证书上传成功，正在刷新授权状态...');
  clearCertificateCache();
  // 延迟执行刷新操作
  setTimeout(() => {
    // 先把状态设为false，再刷新
    preventRedirect.value = false;
    refreshCertificate();
  }, 500);
};

// 返回登录页
const backToLogin = () => {
  router.push('/login');
};

// 联系技术支持
const contactSupport = () => {
  contactDialogVisible.value = true;
};

// 检查证书是否过期
const isExpired = (dateString: string): boolean => {
  try {
    const validTo = new Date(dateString);
    const now = new Date();
    return validTo < now;
  } catch {
    return true; // 解析失败视为过期
  }
};

// 刷新证书信息 - 关键修改：处理API失败情况
const refreshCertificate = () => {
  // 防止重复请求
  if (isRequesting.value) return;

  console.log('刷新证书信息', new Date().toLocaleString());
  isRequesting.value = true;
  loading.value = true;

  AuthAPI.getCertificate()
    .then(response => {
      if (response && typeof response === 'object') {
        // 更新证书信息
        Object.assign(certificateInfo, response);

        // 检查是否需要重定向到首页
        if (!preventRedirect.value && response.validTo && !isExpired(response.validTo)) {
          console.log('证书有效，即将跳转到主页');
          setTimeout(() => {
            router.push('/');
          }, 1000);
        }
      }
    })
    .catch(error => {
      console.error('获取证书失败', error);
      // 错误处理：不做重定向，只显示提示
      ElMessage.warning({
        message: '获取证书信息失败，您可能需要上传有效证书',
        duration: 3000
      });
    })
    .finally(() => {
      isRequesting.value = false;
      loading.value = false;
    });
};

// 页面加载时获取证书信息 - 只请求一次
onMounted(() => {
  console.log('证书过期页面已挂载', new Date().toLocaleString());

  // 延迟获取证书信息，确保组件完全初始化
  setTimeout(() => {
    if (!isRequesting.value) {
      refreshCertificate();
    }
  }, 800); // 延长延迟时间，确保DOM完全渲染
});

// 组件卸载前清理
onBeforeUnmount(() => {
  console.log('证书过期页面已卸载', new Date().toLocaleString());

  // 重置所有状态，避免影响其他页面
  uploadDialogVisible.value = false;
  contactDialogVisible.value = false;
  loading.value = false;
  isRequesting.value = false;
});
</script>

<style scoped>
/* 基础样式 */
.cert-page {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 20px;
}

.bg-expired {
  background: linear-gradient(135deg, var(--el-color-danger-light-9) 0%, var(--el-bg-color) 100%);
}

.bg-no-cert {
  background: linear-gradient(135deg, var(--el-color-warning-light-9) 0%, var(--el-bg-color) 100%);
}

/* 高亮文字效果 */
.highlight {
  font-weight: 700;
  font-size: 16px;
  color: inherit;
}

.expired .highlight {
  color: var(--el-color-danger);
}

.no-cert .highlight {
  color: var(--el-color-warning);
}

.valid .highlight {
  color: var(--el-color-success);
}

/* 背景装饰图案 */
.bg-pattern-1,
.bg-pattern-2,
.bg-pattern-3 {
  position: absolute;
  border-radius: 50%;
  opacity: 0.04;
  z-index: 0;
}

.bg-pattern-1 {
  width: 600px;
  height: 600px;
  background: radial-gradient(circle, var(--el-color-primary) 0%, transparent 70%);
  top: -200px;
  right: -100px;
}

.bg-pattern-2 {
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, var(--el-color-success) 0%, transparent 70%);
  bottom: -100px;
  left: -100px;
}

.bg-pattern-3 {
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--el-color-info) 0%, transparent 70%);
  top: 50%;
  left: 10%;
}

.cert-container {
  position: relative;
  width: 100%;
  max-width: 1100px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 返回按钮 */
.back-link-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 5;
}

.back-link {
  display: flex;
  align-items: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  opacity: 0.7;
  transition: all 0.3s;
}

.back-link:hover {
  opacity: 1;
  color: var(--el-color-primary);
  transform: translateX(-3px);
}

/* 头部标题 */
.cert-header {
  margin-bottom: 15px;
  text-align: center;
}

.logo-area {
  display: inline-flex;
  align-items: center;
  padding: 8px 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
}

.logo-icon {
  font-size: 30px;
  color: var(--el-color-primary);
  margin-right: 12px;
}

.logo-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
  letter-spacing: 1px;
}

.bg-warning {
  background: linear-gradient(135deg, var(--el-color-warning-light-9) 0%, var(--el-bg-color) 100%);
}

.bg-valid {
  background: linear-gradient(135deg, var(--el-color-success-light-9) 0%, var(--el-bg-color) 100%);
}

.warning {
  background: linear-gradient(to right, rgba(var(--el-color-warning-rgb), 0.08), transparent);
  border-left: 4px solid var(--el-color-warning);
}

.valid {
  background: linear-gradient(to right, rgba(var(--el-color-success-rgb), 0.08), transparent);
  border-left: 4px solid var(--el-color-success);
}

/* 主内容区域 */
.main-content {
  background: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 0;
  transition: transform 0.3s, box-shadow 0.3s;
  width: 100%;
}

.main-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* 状态横幅 */
.status-banner {
  display: flex;
  align-items: center;
  padding: 24px 28px;
  position: relative;
}

.expired {
  background: linear-gradient(to right, rgba(var(--el-color-danger-rgb), 0.08), transparent);
  border-left: 4px solid var(--el-color-danger);
}

.no-cert {
  background: linear-gradient(to right, rgba(var(--el-color-warning-rgb), 0.08), transparent);
  border-left: 4px solid var(--el-color-warning);
}

.banner-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

/* 图标颜色 */
.icon-danger {
  background-color: rgba(var(--el-color-danger-rgb), 0.1);
  color: var(--el-color-danger);
}

.icon-warning {
  background-color: rgba(var(--el-color-warning-rgb), 0.1);
  color: var(--el-color-warning);
}

.icon-success {
  background-color: rgba(var(--el-color-success-rgb), 0.1);
  color: var(--el-color-success);
}

.banner-icon {
  font-size: 28px;
}

.banner-content {
  flex: 1;
}

.banner-title {
  font-size: 22px;
  margin: 0 0 10px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.banner-desc {
  font-size: 15px;
  margin: 0;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

/* 文本居中类 */
.text-center {
  text-align: center;
  width: 100%;
  padding: 0 30px;
}

/* 证书详情区域 - 新的并排布局 */
.cert-details-section {
  display: flex;
  padding: 0;
}

.details-column {
  flex: 1;
  padding: 25px 28px;
}

.details-column:first-child {
  border-right: 1px solid var(--el-border-color);
  background-color: rgba(var(--el-color-info-rgb), 0.02);
}

/* 证书信息网格布局 */
.cert-info-grid {
  display: grid;
  grid-gap: 12px;
  margin-top: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px dashed var(--el-border-color-light);
}

.header-icon {
  font-size: 20px;
  color: var(--el-color-primary);
  margin-right: 10px;
}

.section-header span {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.info-row {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px dashed var(--el-border-color-lighter);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  width: 95px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  flex-shrink: 0;
}

.info-value {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

/* 操作指引列表 */
.guide-list {
  list-style: none;
  padding: 0;
  margin: 16px 0 24px;
}

.guide-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 14px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.guide-icon-wrapper {
  width: 22px;
  height: 22px;
  background-color: rgba(var(--el-color-success-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.guide-icon {
  font-size: 12px;
  color: var(--el-color-success);
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.button-icon {
  margin-right: 6px;
  font-size: 16px;
}

/* 页脚 */
.cert-footer {
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.5;
  margin-top: 15px;
}

.cert-footer p {
  margin: 3px 0;
}

/* 联系支持对话框 */
:deep(.support-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

.support-content {
  padding: 10px 0;
}

.support-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-light);
}

.support-item:last-child {
  border-bottom: none;
}

.support-icon-wrapper {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
  color: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
}

.support-text {
  flex: 1;
}

.support-text h4 {
  font-size: 16px;
  margin: 0 0 5px;
  color: var(--el-text-color-primary);
}

.support-text p {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .cert-details-section {
    flex-direction: column;
  }

  .details-column:first-child {
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .cert-header {
    margin-bottom: 12px;
    margin-top: 30px;
  }

  .logo-title {
    font-size: 20px;
  }

  .status-banner {
    flex-direction: column;
    text-align: center;
    padding: 20px;
  }

  .banner-icon-wrapper {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .banner-title {
    font-size: 20px;
  }
}

/* 证书详情区域 - 优化布局 */
.details-column {
  flex: 1;
  padding: 25px 28px;
  display: flex;
  flex-direction: column;
}

.details-column:first-child {
  border-right: 1px solid var(--el-border-color);
  background-color: rgba(var(--el-color-info-rgb), 0.02);
}

/* 指引介绍 */
.guide-introduction {
  margin: 5px 0 20px;
  padding: 10px 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  line-height: 1.5;
  border-radius: 4px;
}

/* 操作指引列表 */
.guide-list {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
}

.guide-list li {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;
  margin-bottom: 5px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  border-bottom: 1px dashed var(--el-border-color-lighter);
}

.guide-list li:last-child {
  border-bottom: none;
}

.guide-icon-wrapper {
  width: 22px;
  height: 22px;
  background-color: rgba(var(--el-color-success-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.guide-icon {
  font-size: 12px;
  color: var(--el-color-success);
}

/* 分隔线 */
.divider {
  margin: 5px 0 15px;
  border-top: 1px dashed var(--el-border-color-light);
}

/* 联系方式区域 */
.contact-info {
  background-color: rgba(var(--el-color-info-rgb), 0.04);
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.contact-title {
  margin: 0 0 5px;
  font-size: 15px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.contact-desc {
  margin: 0 0 8px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.support-link {
  padding-left: 0;
  font-size: 13px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: auto;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.button-icon {
  margin-right: 6px;
  font-size: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .cert-details-section {
    flex-direction: column;
  }

  .details-column:first-child {
    border-right: none;
    border-bottom: 1px solid var(--el-border-color-light);
  }
}
</style>
