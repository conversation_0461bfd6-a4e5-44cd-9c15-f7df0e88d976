<template>
  <el-dialog v-model="dialogVisible" :title="title" width="1300px" :close-on-click-modal="false" destroy-on-close
    class="select-assets-dialog">
    <div class="dialog-content">
      <!-- 左侧资产列表 -->
      <div class="assets-container">
        <!-- 搜索区域 -->
        <div class="search-bar">
          <el-form :inline="true" :model="queryParams">
            <!-- 添加地址搜索 -->
            <el-form-item>
              <el-input v-model="queryParams.url" placeholder="请输入资产地址搜索" clearable @keyup.enter="handleSearch">
                <template #prefix>
                  <i-ep-location />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-input v-model="queryParams.name" placeholder="请输入资产名称搜索" clearable @keyup.enter="handleSearch">
                <template #prefix>
                  <i-ep-search />
                </template>
              </el-input>
            </el-form-item>

            <el-form-item>
              <el-select v-model="queryParams.type" placeholder="资产类型" clearable @change="handleSearch">
                <el-option v-if="props.assetsType === 'assetsAll'" label="信息系统" :value="10" />
                <el-option label="服务器" :value="1" />
                <el-option label="安全设备" :value="3" />
                <el-option label="网络设备" :value="2" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-tree-select v-model="queryParams.deptId" placeholder="请选择管理部门" :data="deptOptions" clearable
                filterable check-strictly :render-after-expand="false" @change="handleSearch" />
            </el-form-item>

            <el-form-item>
              <el-input v-model="queryParams.ownerName" placeholder="请输入管理员" clearable @keyup.enter="handleSearch" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
              <el-button @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 表格区域 -->
        <div class="table-wrapper">
          <el-table ref="tableRef" :data="assetsList" @selection-change="handleSelectionChange" v-loading="loading"
            :row-key="(row) => row.id" :reserve-selection="true" height="450px">
            <el-table-column type="selection" width="55" />

            <el-table-column prop="type" label="资产类型" width="100">
              <template #default="{ row }">
                <el-tag>{{ getAssetTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="name" label="资产名称" min-width="180">
              <template #default="{ row }">
                <div class="asset-name-cell">
                  {{ row.name }}
                  <el-tag size="small"
                    :type="row.status === '1' ? 'success' : (row.status === '0' ? 'danger' : 'info')">
                    {{ row.status === '1' ? '正常' : (row.status === '0' ? '异常' : '废弃') }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="address" label="地址" min-width="180">
              <template #default="{ row }">
                <el-tooltip :content="row.url || row.ip" placement="top" :show-after="500" :hide-after="0">
                  <span class="address-cell">{{ row.url || row.ip }}</span>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column label="管理员" width="100">
              <template #default="{ row }">
                {{ row.ownerName || row.managerName || '-' }}
              </template>
            </el-table-column>

            <el-table-column label="管理部门" width="180">
              <template #default="{ row }">
                <span>{{ getDeptName(row) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-container">
          <el-pagination v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]" :total="total" layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </div>

      <!-- 右侧已选择列表 -->
      <div class="selected-container">
        <div class="selected-header">
          <h3>已选择资产 ({{ persistentSelectedIds.length }})</h3>
          <el-button type="text" @click="clearSelected" :disabled="!persistentSelectedIds.length">
            清空全部
          </el-button>
        </div>

        <div class="selected-wrapper">
          <el-scrollbar height="510px" class="selected-scrollbar">
            <div class="selected-list">
              <el-empty v-if="!persistentSelectedIds.length" description="暂无选择资产" />
              <template v-else>
                <div v-for="id in persistentSelectedIds" :key="id" class="selected-item">
                  <div class="selected-info">
                    <div class="selected-name">
                      <span class="truncate-text">{{ getAssetName(id) }}</span>
                      <el-tag size="small"
                        :type="getAssetStatus(id) === '1' ? 'success' : (getAssetStatus(id) === '0' ? 'danger' : 'info')">
                        {{ getAssetStatus(id) === '1' ? '正常' : (getAssetStatus(id) === '0' ? '异常' : '废弃') }}
                      </el-tag>
                    </div>
                    <div class="selected-detail">
                      <div class="detail-row">
                        <span class="detail-label">类型:</span>
                        <el-tag size="small" class="asset-type-tag">
                          {{ getAssetTypeName(Number(getAssetType(id))) }}
                        </el-tag>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">管理员:</span>
                        <span class="detail-value truncate-text">{{ getAssetOwner(id) || '-' }}</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">地址:</span>
                        <span class="detail-value address-text">{{ getAssetAddress(id) || '-' }}</span>
                      </div>
                    </div>
                  </div>
                  <el-button type="danger" link class="delete-btn" @click="removeSelected(id)">
                    <i-ep-delete />
                  </el-button>
                </div>
              </template>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted, nextTick, PropType } from 'vue'
import { ElMessage } from 'element-plus'
import assetsAPI from "@/api/assets_management/details/assets"
import DeptAPI from "@/api/dept"
import { useDictStore } from '@/store/modules/dictStore'

const dictStore = useDictStore()

// 直接使用后端返回的完整资产接口，不做过多映射
interface Asset {
  id: number
  name: string
  type: number
  ip: string
  url: string
  port?: string | null
  managerName: string
  mobile: string
  ownerName?: string | null
  otherManager?: string | null
  otherContact?: string | null
  deptId: number
  deptName: string
  status: string
  offlineTime?: string | null
  address: string
  os?: string
  sysName?: string | null
  notes?: string | null
  survivalStatus?: string | null
  survivalStatusList?: any[] | null
  createTime: string
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '选择资产'
  },
  assetsType: {
    type: String,
    default: 'assetsAll'
  },
  selectedAssets: {
    type: Array as PropType<number[]>,
    default: () => []
  },
  selectedAssetsData: {
    type: Array as PropType<Asset[]>,
    default: () => []
  },
  // 是否单选模式
  singleSelect: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'selected'])

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 表格引用和基础数据
const tableRef = ref()
const dialogVisible = ref(false)
const persistentSelectedIds = ref<number[]>([])
const assetsList = ref<Asset[]>([])
const loading = ref(false)
const total = ref(0)
const deptOptions = ref<any[]>([])

// 禁用选择变化处理标志，避免重复触发
const disableSelectionChange = ref(false)

// 缓存所有看到过的资产数据，键为ID，值为资产对象
const assetsCache = ref<Map<number, Asset>>(new Map())

// 部门数据缓存
const deptMap = ref<Record<string | number, string>>({})
const deptLoading = ref(false)

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  status: 1,
  name: '',
  url: '',
  type: undefined,
  deptId: undefined,
  deptName: '',
  ownerName: '',
  assetsType: props.assetsType
})

// 获取部门名称的函数 - 优先使用资产自带的deptName
const getDeptName = (asset: Asset): string => {
  // 优先使用资产自带的部门名称
  if (asset.deptName) {
    return asset.deptName
  }
  // 如果没有，再从映射中查找
  if (asset.deptId && deptMap.value[asset.deptId]) {
    return deptMap.value[asset.deptId]
  }
  return asset.deptId ? `部门${asset.deptId}` : '-'
}

// 加载部门映射数据
const loadDeptMappings = async () => {
  if (deptLoading.value || Object.keys(deptMap.value).length > 0) return

  try {
    deptLoading.value = true
    const options = await dictStore.fetchOptions('dept0x0')

    const processDeptTree = (depts: any[]) => {
      depts.forEach(dept => {
        if (dept.value !== undefined && dept.label) {
          deptMap.value[dept.value] = dept.label
        }

        if (dept.children && dept.children.length > 0) {
          processDeptTree(dept.children)
        }
      })
    }

    if (Array.isArray(options)) {
      processDeptTree(options)
    }
  } catch (error) {
    console.error('加载部门映射数据失败:', error)
  } finally {
    deptLoading.value = false
  }
}

// 获取部门选项
const loadDeptOptions = async () => {
  try {
    const data = await DeptAPI.getOptions()
    deptOptions.value = data
    await loadDeptMappings()
  } catch (error) {
    console.error('加载部门列表失败:', error)
    ElMessage.error('加载部门列表失败')
  }
}

// 初始化资产缓存 - 直接使用传入的完整数据
const initAssetsCache = () => {
  try {
    if (props.selectedAssetsData && props.selectedAssetsData.length > 0) {
      props.selectedAssetsData.forEach(asset => {
        if (asset && asset.id) {
          // 直接使用完整的资产对象，不做映射
          assetsCache.value.set(asset.id, asset)
        }
      })
    }

    console.log('已初始化资产缓存，当前缓存数量:', assetsCache.value.size)
  } catch (error) {
    console.error('初始化资产缓存失败:', error)
  }
}

// 加载资产列表 - 直接使用后端返回的完整数据
const loadAssetsList = async () => {
  loading.value = true
  try {
    console.log('加载资产列表:', queryParams)
    const { list, total: totalCount } = await assetsAPI.getPageAll(queryParams)

    // 直接使用后端返回的数据，不做任何映射处理
    assetsList.value = list.map(asset => {
      // 更新资产缓存
      assetsCache.value.set(asset.id, asset)
      return asset
    })

    total.value = totalCount

    // 设置表格选中状态
    setTableSelection()
  } catch (error) {
    console.error('加载资产列表失败:', error)
    ElMessage.error('加载资产列表失败')
  } finally {
    loading.value = false
  }
}

// 设置表格选中状态
const setTableSelection = () => {
  nextTick(() => {
    if (!tableRef.value) return

    try {
      disableSelectionChange.value = true
      tableRef.value.clearSelection()

      assetsList.value.forEach(asset => {
        if (persistentSelectedIds.value.includes(asset.id)) {
          tableRef.value.toggleRowSelection(asset, true)
        }
      })
    } finally {
      disableSelectionChange.value = false
    }
  })
}

// 处理表格选择变化
const handleSelectionChange = (selection: Asset[]) => {
  if (disableSelectionChange.value) return

  // 如果是单选模式且选择了多个，只保留最后一个
  if (props.singleSelect && selection.length > 1) {
    const lastSelected = selection[selection.length - 1]
    
    // 清除所有选择，只保留最后一个
    disableSelectionChange.value = true
    try {
      tableRef.value.clearSelection()
      tableRef.value.toggleRowSelection(lastSelected, true)
    } finally {
      disableSelectionChange.value = false
    }
    
    // 更新选中列表
    persistentSelectedIds.value = [lastSelected.id]
    assetsCache.value.set(lastSelected.id, lastSelected)
    
    ElMessage.warning('只能选择一个资产')
    return
  }

  // 多选逻辑
  if (!props.singleSelect) {
    const currentPageIds = assetsList.value.map(asset => asset.id)
    const selectedIds = selection.map(item => item.id)

    const deselectedIds = currentPageIds.filter(id =>
      persistentSelectedIds.value.includes(id) && !selectedIds.includes(id)
    )

    const newSelectedIds = selectedIds.filter(id =>
      !persistentSelectedIds.value.includes(id)
    )

    if (deselectedIds.length > 0) {
      persistentSelectedIds.value = persistentSelectedIds.value.filter(id =>
        !deselectedIds.includes(id)
      )
    }

    if (newSelectedIds.length > 0) {
      persistentSelectedIds.value = [...persistentSelectedIds.value, ...newSelectedIds]

      // 直接使用完整的资产对象更新缓存
      selection
        .filter(asset => newSelectedIds.includes(asset.id))
        .forEach(asset => {
          assetsCache.value.set(asset.id, asset)
        })
    }
  } else {
    // 单选模式
    if (selection.length === 0) {
      persistentSelectedIds.value = []
    } else {
      const selectedAsset = selection[0]
      persistentSelectedIds.value = [selectedAsset.id]
      assetsCache.value.set(selectedAsset.id, selectedAsset)
    }
  }
}

// 获取资产信息的方法 - 直接从缓存获取完整字段
const getAssetInfo = (id: number, field: keyof Asset, defaultValue: any = '') => {
  const asset = assetsCache.value.get(id)
  return asset ? (asset[field] ?? defaultValue) : defaultValue
}

// 简化的获取方法
const getAssetName = (id: number) => getAssetInfo(id, 'name', `资产${id}`)
const getAssetStatus = (id: number) => getAssetInfo(id, 'status', '')
const getAssetType = (id: number) => getAssetInfo(id, 'type', -1)
const getAssetAddress = (id: number) => {
  const asset = assetsCache.value.get(id)
  if (!asset) return ''
  // 优先使用url，如果没有再使用ip
  return asset.url || asset.ip || ''
}
const getAssetOwner = (id: number) => {
  const asset = assetsCache.value.get(id)
  if (!asset) return ''
  // 优先使用ownerName，如果没有再使用managerName
  return asset.ownerName || asset.managerName || ''
}

// 清空选择
const clearSelected = () => {
  persistentSelectedIds.value = []
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 移除单个选择
const removeSelected = (id: number) => {
  persistentSelectedIds.value = persistentSelectedIds.value.filter(item => item !== id)

  const row = assetsList.value.find(asset => asset.id === id)
  if (row && tableRef.value) {
    disableSelectionChange.value = true
    try {
      tableRef.value.toggleRowSelection(row, false)
    } finally {
      disableSelectionChange.value = false
    }
  }
}

// 搜索处理
const handleSearch = async () => {
  queryParams.pageNum = 1
  await loadAssetsList()
}

// 分页处理
const handleSizeChange = async (val: number) => {
  queryParams.pageSize = val
  queryParams.pageNum = 1
  await loadAssetsList()
}

const handleCurrentChange = async (val: number) => {
  queryParams.pageNum = val
  await loadAssetsList()
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.url = ''
  queryParams.type = undefined
  queryParams.deptId = undefined
  queryParams.ownerName = ''
  handleSearch()
}

// 监听对话框显示状态
watch(() => props.visible, async (val) => {
  dialogVisible.value = val
  if (val) {
    console.log('对话框打开，初始化选择:', props.selectedAssets)

    if (Object.keys(deptMap.value).length === 0) {
      await loadDeptMappings()
    }

    persistentSelectedIds.value = [...props.selectedAssets]
    queryParams.pageNum = 1
    queryParams.pageSize = 10

    initAssetsCache()
    await loadAssetsList()
    // 移除这行，因为不需要额外获取详情
    // await fetchSelectedAssetDetails()
  }
})

// 监听持久化选择变化 - 简化逻辑，因为不需要额外获取详情
watch(() => persistentSelectedIds.value.length, () => {
  // 这里可以做一些其他逻辑，比如更新状态显示等
  console.log('选择数量变化:', persistentSelectedIds.value.length)
})

// 监听对话框关闭
watch(() => dialogVisible.value, (val) => {
  if (!val) {
    emit('update:visible', false)
  }
})

// 取消选择
const handleCancel = () => {
  dialogVisible.value = false
}

// 确认选择
const handleConfirm = () => {
  // 直接从缓存获取完整的资产数据
  const selectedAssetsData = persistentSelectedIds.value
    .map(id => assetsCache.value.get(id))
    .filter(Boolean) as Asset[]

  emit('selected', {
    selectedIds: persistentSelectedIds.value,
    selectedAssets: selectedAssetsData
  })

  console.log('已选择资产:', selectedAssetsData)
  console.log('确认选择:', persistentSelectedIds.value.length, '个资产')
  ElMessage.success(`已选择${persistentSelectedIds.value.length}个资产`)
  dialogVisible.value = false
}

onMounted(async () => {
  await loadDeptOptions()
})
</script>
<style scoped>
.select-assets-dialog :deep(.el-dialog__body) {
  height: 600px;
  padding: 20px;
}

.dialog-content {
  display: flex;
  gap: 24px;
  height: 100%;
}

/* 左侧容器 */
.assets-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.search-bar {
  margin-bottom: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
}

.search-bar :deep(.el-form--inline) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-right: 0;
}

.search-bar :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

.search-bar :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.search-bar :deep(.el-input),
.search-bar :deep(.el-select),
.search-bar :deep(.el-tree-select) {
  width: 200px;
}

.table-wrapper {
  flex: 1;
  overflow: auto;
}

.pagination-container {
  margin-top: 16px;
  padding: 10px 0;
  background-color: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 右侧容器 */
.selected-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--el-border-color-light);
  padding-left: 24px;
  min-width: 0;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.selected-header h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.selected-wrapper {
  flex: 1;
  overflow: hidden;
}

.selected-list {
  padding: 0 6px 6px 0;
}

/* 优化后的已选列表项样式 */
.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 6px 10px;
  border-radius: 4px;
  background-color: var(--el-fill-color-light);
  margin-bottom: 4px;
}

.selected-info {
  flex: 1;
  margin-right: 8px;
  min-width: 0;
}

.selected-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-weight: 500;
}

.truncate-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.selected-detail {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 2px;
  flex-wrap: nowrap;
}

.detail-label {
  color: var(--el-text-color-secondary);
  font-weight: normal;
  min-width: 45px;
}

.detail-value {
  color: var(--el-text-color-regular);
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.address-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  max-width: calc(100% - 45px);
}

.ml-auto {
  margin-left: auto;
}

.delete-btn {
  padding: 4px;
  margin-right: -4px;
  margin-top: -4px;
}

.asset-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.asset-type-tag {
  height: 20px;
  line-height: 20px;
}

:deep(.el-dialog__footer) {
  padding-top: 10px;
  margin-top: 0;
  border-top: 1px solid var(--el-border-color-light);
}

:deep(.el-table) {
  height: 100% !important;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

:deep(.el-tag) {
  border-radius: 4px;
}

.address-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}
</style>
