<template>
  <base-process-group ref="baseProcessGroupRef" :business-type="businessType" :departments="departments"
    :sms-templates="smsTemplates" :dept-type-map="deptTypeMap" @update:departments="handleDepartmentsUpdate" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseProcessGroup from './BaseProcessGroup.vue'

// 定义接口
interface Department {
  id: number
  name: string
  active: boolean
  execution?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
  notification?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
}

interface UserInfo {
  id: number | string
  username: string
  nickname: string
  deptName: string
  mobile: string
}

// 业务类型
const businessType = 'safety'
// 主管部门，运维部门，业务部门
// 部门类型映射
const deptTypeMap = {

  0: '2', // 工单审核
  1: '3', // 工单整改
  2: '4', // 工单复核
  3: '5'  // 工单评价
}

// 流程步骤部门数据
const departments = ref<Department[]>([
  { id:1, name: '工单审核部门', active: true },
  { id: 2, name: '事件通报部门', active: false },
  { id: 3, name: '事件处置部门', active: false },
  { id: 4, name: '工单评价部门', active: false }
])

// 短信模板数据
const smsTemplates = ref([
  // { id: 1, name: '安全评估通知模板', content: '尊敬的${name}，您有一项安全评估任务需要处理，请及时查看。' },
  { id: 1, name: '工单审核通知模板', content: '尊敬的${name}，您有一项工单审核任务需要处理，请及时查看。' },
  { id: 2, name: '安全整改通知模板', content: '尊敬的${name}，您有一项安全整改任务需要完成，请在规定时间内处理。' },
  { id: 3, name: '整改验收通知模板', content: '尊敬的${name}，您有一项整改验收任务需要确认，请及时复核。' },
  { id: 4, name: '工单评价通知模板', content: '尊敬的${name}，您有一项工单评价任务需要完成，请及时评价。' }
])

// 基础组件引用
const baseProcessGroupRef = ref()

// 处理部门更新
const handleDepartmentsUpdate = (updatedDepartments: Department[]) => {
  departments.value = updatedDepartments
}

// 获取流程数据
const getProcessData = () => {
  return baseProcessGroupRef.value?.getProcessData()
}

// 获取验证状态
const getValidationStatus = () => {
  return baseProcessGroupRef.value?.getValidationStatus()
}

// 初始化现有数据
const initFromExistingData = (data: any) => {
  baseProcessGroupRef.value?.initFromExistingData(data)
}

// 重置组件
const reset = () => {
  baseProcessGroupRef.value?.reset()
}

// 自动配置整改部门
const applyDefaultConfig = () => {
  return baseProcessGroupRef.value?.applyDefaultConfig()
}
// 
// 暴露方法
defineExpose({
  getProcessData,
  getValidationStatus,
  initFromExistingData,
  reset,
  applyDefaultConfig
})
</script>
