<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="重保事件名称" prop="eventName">
          <el-input v-model="queryParams.eventName" placeholder="重保事件名称" clearable class="!max-w-[130px]" />
        </el-form-item>
        <el-form-item label="重保创建人" prop="createdBy">
          <el-input v-model="queryParams.createdBy" placeholder="重保创建人" clearable class="!max-w-[130px]" />
        </el-form-item>
        <el-form-item label="重保创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="重保状态" prop="status">
          <dictionary v-model="queryParams.status" code="events_status" class="!w-[130px]" />
        </el-form-item>
        <el-form-item v-if="showAdvancedFilters" label="重保更新时间" prop="updateTime">
          <el-date-picker v-model="queryParams.updateTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item v-if="showAdvancedFilters" label="开始时间" prop="startTime">
          <el-date-picker v-model="queryParams.startTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item v-if="showAdvancedFilters" label="结束时间" prop="endTime">
          <el-date-picker v-model="queryParams.endTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item v-if="showAdvancedFilters" label="重保事件的备注信息" prop="remarks">
          <el-input v-model="queryParams.remarks" placeholder="重保事件的备注信息" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()"><i-ep-search />搜索</el-button>
          <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
        </el-form-item>
      </el-form>
      <el-button @click="toggleAdvancedFilters" class="mb-2">
        {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
      </el-button>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button v-hasPerm="['system:events:add']" type="success" @click="handleOpenDialog()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button v-hasPerm="['system:events:delete']" type="danger" :disabled="ids.length === 0"
          @click="handleDelete()"><i-ep-delete />
          删除
        </el-button>
      </template>

      <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading" :data="pageData" highlight-current-row border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column sortable key="id" label="重保id" prop="id" min-width="100" align="center" /> -->
        <el-table-column key="status" label="重保状态" prop="status" min-width="100" align="center">
          <template #default="{ row }">
            <!-- 0未开始 1进行中 2已结束 -->
            <el-tag :type="row.status == 0 ? 'info' : row.status == 1 ? 'success' : 'danger'">
              {{ row.status == 0 ? '未开始' : row.status == 1 ? '进行中' : '已结束' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column key="eventName" label="重保事件名称" prop="eventName" min-width="120" align="center" />
        <el-table-column key="assets" label="资产清单" prop="assets" min-width="100" align="center">
          <template #default="{ row }">
            <el-button type="success" @click="handleOpenDrawer(row.id)">查看资产</el-button>
          </template>
        </el-table-column>
        <el-table-column key="createdBy" label="重保创建人" prop="createdBy" min-width="100" align="center" />
        <el-table-column key="createTime" label="重保创建时间" prop="createTime" min-width="150" align="center" />
        <el-table-column key="updateTime" label="重保更新时间" prop="updateTime" min-width="150" align="center" />
        <el-table-column key="startTime" label="开始时间" prop="startTime" min-width="150" align="center" />
        <el-table-column key="endTime" label="结束时间" prop="endTime" min-width="150" align="center" />
        
        <el-table-column key="remarks" label="重保备注信息" prop="remarks" min-width="100" align="center"
          show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="220" align="center">
          <template #default="scope">
            <el-button v-hasPerm="['system:events:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />
              编辑
            </el-button>
            <el-button v-hasPerm="['system:events:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
    </el-card>

    <!-- 重保清单管理表单弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" @close="handleCloseDialog">
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="重保事件名称" prop="eventName">
          <el-input v-model="formData.eventName" placeholder="重保事件名称" clearable />
        </el-form-item>
        <el-form-item label="重保创建人" prop="createdBy">
          <el-input v-model="formData.createdBy" placeholder="重保创建人" clearable />
        </el-form-item>
        <el-form-item label="重保创建时间" prop="createTime">
          <el-date-picker v-model="formData.createTime" type="datetime" placeholder="重保创建时间"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item v-if="!formData.id" label="关联资产复用" prop="priority">
          <el-select v-model="formData.associationEventsId" placeholder="选择重保事件" style="width: 200px;">
            <el-option v-for="event in reinsuranceEvents" :key="event.id" :label="event.eventName" :value="event.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker v-model="formData.startTime" type="datetime" placeholder="开始时间"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker v-model="formData.endTime" type="datetime" placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="重保状态" prop="status">
          <dictionary v-model="formData.status" code="events_status" />
        </el-form-item>
        <el-form-item label="重保备注信息" prop="remarks">
          <el-input v-model="formData.remarks" placeholder="重保事件的备注信息" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联资产弹窗 -->
    <el-dialog
      v-model="drawer.visible"
      :title="drawer.title"
      width="80%"
      center
      append-to-body
      destroy-on-close
      @close="handleCloseDrawer"
    >
      <div class="dialog-header flex-x-between mb-4">
        <el-button type="primary" @click="handleEditAssets">编辑资产清单</el-button>
        <el-button class="ml-3" @click="handleExport">
          <template #icon><i-ep-download /></template>
          导出
        </el-button>
      </div>
      <el-table :data="systemInfoList" border>
        <el-table-column prop="sysname" label="信息系统名称" align="center" />
        <el-table-column prop="url" label="Web地址" align="center" />
        <!-- 新增管理单位字段 -->
        <el-table-column prop="managementUnit" label="管理单位" align="center" />
        <!-- 新增管理人员字段 -->
        <el-table-column prop="manager" label="管理人员" align="center" />
        <!-- 新增联系方式字段 -->
        <el-table-column prop="contactInfo" label="联系方式" align="center" />
        <el-table-column prop="status" label="资产状态" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">
              {{ scope.row.status == 1 ? '正常' : '异常' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDrawer">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <SelectAssets
      v-model:visible="selectAssetsVisible"
      :title="'选择关联资产'"
      :selected-assets="transferDialog.selectedAssets"
      @selected="handleAssetsSelected"
    />
  </div>
</template>

<script setup lang="ts">
import {formatLocalDateTime} from "@/utils/dateUtils";

defineOptions({
  name: "events",
  inheritAttrs: false,
});

import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import eventsAPI, { eventsPageVO, eventsForm, eventsPageQuery } from "@/api/work_management/critical"
import assetsAPI, { assetsPageVO } from "@/api/assets_management/details/assets"
import SelectAssets from './components/selectAssets.vue'
import {SystemInfo} from "@/api/assets_management/details/systems-entity";

// 表单引用
const queryFormRef = ref<InstanceType<typeof ElForm>>()
const dataFormRef = ref<InstanceType<typeof ElForm>>()

// 基础数据
const loading = ref(false)
const ids = ref<number[]>([])
const total = ref(0)
const selectAssetsVisible = ref(false)
const showAdvancedFilters = ref(false)

// 查询参数
const queryParams = reactive<eventsPageQuery>({
  pageNum: 1,
  pageSize: 10,
})

// 页面数据
const pageData = ref<eventsPageVO[]>([])

// 弹窗控制
const dialog = reactive({
  title: "",
  visible: false,
})

const drawer = reactive({
  title: "",
  visible: false,
})

// 表单数据
const formData = reactive<eventsForm>({})
const systemInfoList = ref<SystemInfo[]>([])

// 资产选择相关
const transferDialog = reactive({
  visible: false,
  id: undefined as number | undefined,
  allAssets: [] as { id: number; name: string }[],
  selectedAssets: [] as number[],
})

// 重保事件映射
const reinsuranceEvents = ref<any[]>([])

// 表单验证规则
const rules = reactive({
  eventName: [{ required: true, message: "请输入重保事件名称", trigger: "blur" }],
  createdBy: [{ required: true, message: "请输入重保创建人", trigger: "blur" }],
  createTime: [{ required: true, message: "请输入重保创建时间", trigger: "blur" }],
  updateTime: [{ required: true, message: "请输入重保更新时间", trigger: "blur" }],
  priority: [{ required: false, message: "请输入优先级", trigger: "blur" }],
  startTime: [{ required: true, message: "请输入开始时间", trigger: "blur" }],
  endTime: [{ required: true, message: "请输入结束时间", trigger: "blur" }],
  status: [{ required: true, message: "请输入重保状态", trigger: "blur" }],
})

// 方法定义
/** 切换高级筛选 */
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

/** 查询重保清单管理 */
function handleQuery() {
  loading.value = true
  eventsAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list
      total.value = data.total
    })
    .finally(() => {
      loading.value = false
    })
}

/** 查询重保清单列表 */
async function getEventsList() {
  const eventsList = await eventsAPI.getList();
  reinsuranceEvents.value = eventsList;
}

/** 重置查询 */
function handleResetQuery() {
  queryFormRef.value?.resetFields()
  queryParams.pageNum = 1
  handleQuery()
  getEventsList()
}

/** 选择资产处理 */
function handleAssetsSelected(result: {selectedIds: number[], selectedAssets: any[]}) {
  transferDialog.selectedAssets = result.selectedIds
  
  ElMessageBox.confirm("确认将选中的资产加入重保事件?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    loading.value = true
    eventsAPI.addEventsSystems(transferDialog.id, transferDialog.selectedAssets)
      .then(() => {
        ElMessage.success("成功将资产加入重保事件")
        drawer.visible = false
        handleQuery()
      })
      .catch((error) => {
        ElMessage.error("加入重保失败：" + error.message)
      })
      .finally(() => {
        loading.value = false
      })
  }).catch(() => {
    ElMessage.info("已取消操作")
  })
}

/** 表格选择变化 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id)
}

/** 打开编辑弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true
  if (id) {
    dialog.title = "修改重保清单管理"
    eventsAPI.getFormData(id).then((data) => {
      Object.assign(formData, data)
      formData.id = id
    })
  } else {
    dialog.title = "新增重保清单管理"
  }
}

/** 提交表单 */
function handleSubmit() {
  dataFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true
      const id = formData.id
      formData.updateTime = formatLocalDateTime(new Date())
      
      if (id) {
        eventsAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功")
            handleCloseDialog()
            handleResetQuery()
          })
          .finally(() => (loading.value = false))
      } else {
        eventsAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功")
            handleCloseDialog()
            handleResetQuery()
          })
          .finally(() => (loading.value = false))
      }
    }
  })
}

/** 关闭弹窗 */
function handleCloseDialog() {
  dialog.visible = false
  dataFormRef.value?.resetFields()
  dataFormRef.value?.clearValidate()
  formData.id = undefined
}

/** 删除处理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",")
  if (!removeId) {
    ElMessage.warning("请勾选删除项")
    return
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true
      eventsAPI.deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功")
          handleResetQuery()
        })
        .finally(() => (loading.value = false))
    },
    () => {
      ElMessage.info("已取消删除")
    }
  )
}

/** 编辑资产 */
function handleEditAssets() {
  selectAssetsVisible.value = true
}

/** 打开资产抽屉 */
async function handleOpenDrawer(id?: number) {
  drawer.visible = true
  drawer.title = "资产清单"
  const data = await eventsAPI.getAssets(id)
  systemInfoList.value = data
  eventsAPI.getFormData(id).then((data) => {
    Object.assign(formData, data)
    formData.id = id
  })

  transferDialog.id = id
  transferDialog.selectedAssets = systemInfoList.value.map((item: any) => item.id)
}

/** 关闭资产抽屉 */
function handleCloseDrawer() {
  drawer.visible = false
}

function handleExport() {
  assetsAPI.export(queryParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}


// 生命周期钩子
onMounted(() => {
  handleQuery()
  getEventsList()
})
</script>


