import request from "@/utils/request";

const PARAMETER_SET_BASE_URL = "/api/v1/parameterSets";

class parameter_setAPI {
    /** 获取参数组分页数据 */
    static getPage(queryParams?: parameter_setPageQuery) {
        return request<any, PageResult<parameter_setPageVO[]>>({
            url: `${PARAMETER_SET_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 获取参数组表单数据
     *
     * @param id parameter_setID
     * @returns parameter_set表单数据
     */
    static getFormData(id: number) {
        return request<any, parameter_setForm>({
            url: `${PARAMETER_SET_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /**
     * 分页查询列表
     * @param queryParams 查询参数
     */
    static getList(queryParams?: parameter_setPageQuery) {
        return request<any, PageResult<parameter_setPageVO[]>>({
            url: `${PARAMETER_SET_BASE_URL}/userPage`,
            method: "get",
            params: queryParams,
        });
    }

    /** 添加参数组*/
    static add(data: parameter_setForm) {
        return request({
            url: `${PARAMETER_SET_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新参数组
     *
     * @param id parameter_setID
     * @param data parameter_set表单数据
     */
    static update(id: number, data: parameter_setForm) {
        return request({
            url: `${PARAMETER_SET_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除参数组，多个以英文逗号(,)分割
     *
     * @param ids 参数组ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${PARAMETER_SET_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 设置对应人员
     */
    static setPeople(id: number, data: any) {
        return request({
            url:`${PARAMETER_SET_BASE_URL}/${id}/set`,
            method: "post",
            data: data,
        })
    }

    /**
     * 下拉菜单
     */
    static getOptions(id: number) {
        return request<any, parameter_setOptions[]>({
            url: `${PARAMETER_SET_BASE_URL}/${id}/list`,
            method: "get",
        });
    }

    /** 用户导出
     * @param queryParams 查询参数
     */
    static export(queryParams?: parameter_setPageQuery) {
        return request({
            url: `${PARAMETER_SET_BASE_URL}/exportUser`,
            method: "post",
            data: queryParams,
            responseType: "blob",
        });
    }

}

export default parameter_setAPI;

/** 参数组分页查询参数 */
export interface parameter_setPageQuery extends PageQuery {
    /** id */
    id?: number;
    /** 组名 */
    name?: string;
    /** 组类型（例：用户组，资产组） */
    type?: number;
    /** 关联id */
    concat?: string;
    /** 备注 */
    remark?: string;
}

/** 参数组表单对象 */
export interface parameter_setForm {
    /** id */
    id?:  number;
    /** 组名 */
    name?:  string;
    /** 组类型（例：用户组，资产组） */
    type?:  number;
    /** 关联id */
    concat?:  string;
    /** 备注 */
    remark?:  string;
}

/** 参数组分页对象 */
export interface parameter_setPageVO {
    /** id */
    id?: number;
    /** 组名 */
    name?: string;
    /** 组类型（例：用户组，资产组） */
    type?: number;
    /** 关联id */
    concat?: string;
    /** 备注 */
    remark?: string;
}

/** 下拉菜单 */
export interface parameter_setOptions {
    /** id */
    id?: number;
    /** 组名 */
    name?: string;
    /** 组类型（例：用户组，资产组） */
    type?: number;
    /** 关联id */
    concat?: string;
    /** 备注 */
    remark?: string;
    value?: number;
    label?: string;

}
