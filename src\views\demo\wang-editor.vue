<!-- wangEditor富文本编辑器示例 -->
<script setup lang="ts">
import { ref } from "vue";
import Editor from "@/components/WangEditor/index.vue";

const value = ref("<p>这是初始内容</p>");
const dialogVisible = ref(false);

const showDialog = () => {
  dialogVisible.value = !dialogVisible.value;
  console.log(dialogVisible.value);
  console.log(value.value);
};
</script>

<template>
  <div class="app-container">
    <el-link
      href="https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/wang-editor.vue"
      type="primary"
      target="_blank"
      class="mb-[20px]"
    >
      示例源码 请点击 >>>>
    </el-link>
    <el-button type="primary" @click="showDialog">查看内容</el-button>
    <editor v-model="value" style="height: calc(100vh - 180px)" />

    <!-- 富文本内容展示模态框 -->

    <el-dialog v-model="dialogVisible" title="富文本内容" append-to-body>
      <div v-html="value"></div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
