<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
  >
    <!-- 搜索区域 -->
    <div class="search-bar">
      <el-row :gutter="16">
        <el-col :span="18">
          <el-input
            v-model="searchQuery"
            placeholder="请输入姓名或用户名搜索"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <i-ep-search />
            </template>
          </el-input>
        </el-col>
        <el-col :span="6" v-if="!departmentId">
          <el-select v-model="selectedDept" placeholder="选择部门" clearable @change="handleDeptChange">
            <el-option 
              v-for="dept in departmentList" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id" 
            />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <el-table
      ref="tableRef"
      :data="filteredUsersList"
      border
      stripe
      highlight-current-row
      @current-change="handleCurrentChange"
      height="400px"
    >
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="nickname" label="姓名" width="120" />
      <el-table-column prop="deptName" label="所属部门" />
      <el-table-column prop="duty" label="职责分类">
        <template #default="scope">
          <dictmap 
            :model-value="scope.row.duty" 
            code="DUTY" 
            multiple 
            placeholder="未设置"
          />
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="联系电话" width="120" />
    </el-table>

    <!-- 已选用户展示区域 -->
    <div class="selected-user" v-if="selectedUser">
      <div class="selected-header">
        <span>已选择</span>
        <el-button type="text" @click="clearSelection">清空</el-button>
      </div>
      <div class="selected-tags">
        <el-tag closable @close="clearSelection">
          {{ selectedUser.nickname || selectedUser.username }} ({{ selectedUser.deptName }})
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import UserAPI, { UserPageVO } from '@/api/user'
import DeptAPI, { DeptVO } from '@/api/dept'
import { useDictStore } from '@/store/modules/dictStore'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '选择用户'
  },
  selectedUserId: {
    type: [Number, String],
    default: null
  },
  departmentId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'selected'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const tableRef = ref()
const searchQuery = ref('')
const usersList = ref<UserPageVO[]>([])
const selectedUser = ref<UserPageVO | null>(null)
const selectedDept = ref<number | null | undefined>(null)
const departmentList = ref<DeptVO[]>([])

// 获取字典存储
const dictStore = useDictStore()

// 需要预加载的字典类型
const requiredDictTypes = ref([
  'DUTY',      // 职责分类字典
])

// 记录字典是否已加载
const dictLoaded = ref(false)

/** 
 * 预加载字典数据 
 * 在表格数据加载前，预先加载所有可能用到的字典数据
 */
async function preloadDictData() {
  // 如果已加载，则跳过
  if (dictLoaded.value || requiredDictTypes.value.length === 0) {
    return
  }

  try {
    const dictPromises = requiredDictTypes.value.map(type => dictStore.fetchOptions(type))
    await Promise.all(dictPromises)
    
    // 标记字典已加载
    dictLoaded.value = true
  } catch (error) {
    console.error('字典数据预加载失败:', error)
  }
}

// 根据搜索条件过滤用户列表
const filteredUsersList = computed(() => {
  const query = searchQuery.value.toLowerCase()
  let result = [...usersList.value]
  
  if (query) {
    result = result.filter(user => 
      (user.username && user.username.toLowerCase().includes(query)) ||
      (user.nickname && user.nickname.toLowerCase().includes(query))
    )
  }
  
  return result
})

// 处理搜索
const handleSearch = () => {
  // 保持当前选择的用户
  if (selectedUser.value && tableRef.value) {
    nextTick(() => {
      const row = filteredUsersList.value.find(u => u.id === selectedUser.value?.id)
      if (row) {
        tableRef.value.setCurrentRow(row)
      } else {
        // 如果筛选后找不到当前选中的用户，清空选择
        tableRef.value.setCurrentRow(null)
        selectedUser.value = null
      }
    })
  }
}

// 处理部门变更 - 重新加载该部门的用户
const handleDeptChange = () => {
  loadUsers() // 重新加载用户数据
}

// 设置选中状态
const setTableSelection = () => {
  if (props.selectedUserId && usersList.value.length > 0) {
    const user = usersList.value.find(u => String(u.id) === String(props.selectedUserId))
    if (user) {
      selectedUser.value = user
      nextTick(() => {
        tableRef.value?.setCurrentRow(user)
      })
    }
  }
}

// 处理当前行变化
const handleCurrentChange = (currentRow: UserPageVO) => {
  selectedUser.value = currentRow || null
}

// 清空选择
const clearSelection = () => {
  tableRef.value?.setCurrentRow(null)
  selectedUser.value = null
}

// 取消选择
const handleCancel = () => {
  clearSelection()
  dialogVisible.value = false
}

// 确认选择
const handleConfirm = () => {
  if (!selectedUser.value) {
    ElMessage.warning('请选择一个用户')
    return
  }
  emit('selected', selectedUser.value)
  dialogVisible.value = false
}

// 加载部门列表
const loadDepts = async () => {
  try {
    // 如果不需要显示部门选择器，则不加载部门列表
    if (props.departmentId) return
    
    const res = await DeptAPI.getList()
    departmentList.value = res || []
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    // 首先预加载字典数据（只在第一次加载时执行）
    if (!dictLoaded.value) {
      await preloadDictData()
    }

    // 清空之前的数据，避免旧数据干扰
    usersList.value = []
    
    // 设置查询参数 - 使用传入的部门ID或选择的部门ID
    const deptIdParam = props.departmentId ? 
                       Number(props.departmentId) : 
                       selectedDept.value ? Number(selectedDept.value) : undefined
    
    const params: Record<string, any> = { 
      pageNum: 1, 
      pageSize: 999 
    }
    
    // 只有在有部门ID时才添加到参数中
    if (deptIdParam) {
      params.deptId = deptIdParam
    }
    
    const res = await UserAPI.getPage(params)
    usersList.value = res.list || []
    
    nextTick(() => {
      setTableSelection()
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 初始化数据
const initData = () => {
  // 如果传入了部门ID，设置为选中的部门
  if (props.departmentId) {
    selectedDept.value = Number(props.departmentId)
  } else {
    selectedDept.value = null
  }
  loadDepts()
  loadUsers()
}

// 监听弹窗显示
watch(() => props.visible, (val) => {
  if (val) {
    initData()
  }
})

// 监听已选用户变化
watch(() => props.selectedUserId, () => {
  if (dialogVisible.value && usersList.value.length > 0) {
    setTableSelection()
  }
})

// 监听部门ID变化
watch(() => props.departmentId, (val) => {
  if (val && dialogVisible.value) {
    selectedDept.value = Number(val)
    loadUsers()
  }
})
</script>

<style scoped>
.search-bar {
  margin-bottom: 16px;
}

.selected-user {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-footer {
  padding-top: 16px;
  text-align: right;
}
</style>
