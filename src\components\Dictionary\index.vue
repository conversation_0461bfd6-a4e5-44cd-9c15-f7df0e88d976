<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :multiple="multiple"
    :multiple-limit="multiple ? maxSelectCount : 0"
    :collapse-tags="multiple && collapseTagsTooltip"
    :collapse-tags-tooltip="multiple && collapseTagsTooltip"
    clearable
    filterable
    @change="handleChange"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
      @click="handleOptionClick(option)"
    />
  </el-select>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeMount, reactive, computed } from 'vue'
import DictAPI from '@/api/dict'
import DeptAPI from '@/api/dept'
import systemsEntityAPI from '@/api/assets_management/details/systems-entity'
import parameter_setAPI from "@/api/parameter_set";
import { useWatchModel } from '@/plugins/useWatchModel'

const props = defineProps({
  code: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [String, Number, Array],
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 新增：是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 新增：多选时是否折叠标签
  collapseTagsTooltip: {
    type: Boolean,
    default: false,
  },
  maxLimit: {
    type: Number,
    default: 0, 
  },
  onModelChange: {
    type: Function,
    default: null,
  },
  customOption: {
    type: Object,
    default: () => null,
  },
  onCustomOptionClick: {
    type: Function,
    default: null,
  },
})

const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 1000,
});

const emits = defineEmits(['update:modelValue'])

const options = ref<any[]>([])
const selectedValue = ref<string | number | string[] | number[] | undefined>()

// 计算最大选择数量
const maxSelectCount = computed(() => {
  if (!props.multiple) return 0;
  if (props.maxLimit > 0) return props.maxLimit;
  return options.value.length; // 默认为字典选项数量
});

// 统一值类型转换函数
function convertValueType(value: string | number, targetType: 'string' | 'number'): string | number {
  if (targetType === 'number') {
    return typeof value === 'number' ? value : Number(value);
  } else {
    return typeof value === 'string' ? value : String(value);
  }
}

// 获取字典选项中值的类型
function getOptionsValueType(): 'string' | 'number' {
  if (options.value.length === 0) return 'string';
  return typeof options.value[0].value === 'number' ? 'number' : 'string';
}

// 监听选项和外部值变化
watch([options, () => props.modelValue], ([newOptions, newModelValue]) => {
  if (newOptions.length === 0) {
    // 下拉数据源加载未完成不回显
    return
  }
  
  if (newModelValue == undefined || newModelValue === '') {
    selectedValue.value = props.multiple ? [] : undefined;
    return
  }

  const optionsValueType = getOptionsValueType();

  if (props.multiple) {
    // 多选模式处理
    let valueArray: (string | number)[] = [];
    
    if (Array.isArray(newModelValue)) {
      valueArray = newModelValue;
    } else if (typeof newModelValue === 'string') {
      // 字符串按逗号分割转数组
      valueArray = newModelValue.split(',').filter(item => item.trim() !== '');
    } else {
      valueArray = [newModelValue];
    }
    
    // 转换值类型以匹配字典选项
    valueArray = valueArray.map(val => convertValueType(val, optionsValueType));
    
    // 限制选择数量
    if (valueArray.length > maxSelectCount.value && maxSelectCount.value > 0) {
      valueArray = valueArray.slice(0, maxSelectCount.value);
    }
    
    selectedValue.value = valueArray;
  } else {
    // 单选模式处理
    selectedValue.value = convertValueType(newModelValue, optionsValueType);
  }
}, { immediate: true });

function handleChange(val?: any) {
  if (props.multiple) {
    // 多选模式：返回逗号分隔的字符串，便于后端处理
    const arrayValue = Array.isArray(val) ? val : [];
    
    // 再次检查选择数量限制（防止意外情况）
    const limitedArray = maxSelectCount.value > 0 && arrayValue.length > maxSelectCount.value 
      ? arrayValue.slice(0, maxSelectCount.value) 
      : arrayValue;
    
    const stringValue = limitedArray.join(',');
    emits('update:modelValue', stringValue);
  } else {
    // 单选模式：直接返回值
    emits('update:modelValue', val);
  }
}

// 使用自定义 hook 监听 selectedValue 的变化
useWatchModel(selectedValue, (newValue, oldValue) => {
  if (typeof props.onModelChange === 'function') {
    props.onModelChange(newValue, oldValue);
  }
})

function handleOptionClick(option: any) {
  if (option.value === props.customOption?.value && typeof props.onCustomOptionClick === 'function') {
    props.onCustomOptionClick(option);
  }
}

onBeforeMount(() => {
  if (props.code == 'system0x0') {
    systemsEntityAPI.getOptions().then((response) => {
      options.value = response;
      if (props.customOption) {
        options.value.push(props.customOption);
      }
    })
  } else if (props.code == 'dept0x0') {
    DeptAPI.getOptions().then((response) => {
      options.value = response;
      if (props.customOption) {
        options.value.push(props.customOption);
      }
    })
  } else if (props.code == 'parameterSet0x0') {
    parameter_setAPI.getPage(queryParams).then((response) => {
      options.value = response.list.map((item: any) => {
        return {
          label: item.name,
          value: item.id,
        }
      });
      if (props.customOption) {
        options.value.push(props.customOption);
      }
    })
  } else {
    // 通用字典API处理
    DictAPI.getOptions(props.code).then((response) => {
      options.value = response;
      if (props.customOption) {
        options.value.push(props.customOption);
      }
    }).catch((error) => {
      console.error(`获取字典 ${props.code} 失败:`, error);
    })
  }
})
</script>
