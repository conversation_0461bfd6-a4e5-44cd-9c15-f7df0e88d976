import request from "@/utils/request";

const INVENTORY_BASE_URL = "/api/v1/inventorys";

class InventoryAPI {
    /** 获取资产盘点分页数据 */
    static getPage(queryParams?: InventoryPageQuery) {
        return request<any, DataAssetInventoryVO>({
            url: `${INVENTORY_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }

    /** 更改盘点状态为进行中 */
    static startInventory(id: number) {
        return request({
            url: `${INVENTORY_BASE_URL}/${id}/status`,
            method: "put",
        });
    }

    /**
     * 获取资产盘点表单数据
     *
     * @param id 资产盘点ID
     * @returns 资产盘点表单数据
     */
    static getFormData(id: number) {
        return request<any,AssetInventoryForm>({
            url: `${INVENTORY_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加资产盘点 */
    static add(data: AssetInventoryForm) {
        return request({
            url: `${INVENTORY_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新资产盘点
     *
     * @param id 资产盘点ID
     * @param data 资产盘点表单对象
     */
    static update(id: number, data: AssetInventoryForm) {
        return request({
            url: `${INVENTORY_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除资产盘点，多个以英文逗号(,)分割
     *
     * @param ids 资产盘点ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${INVENTORY_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 获取资产盘点及资产列表
     * 
     * @param id 盘点任务ID
     */
    static getAssetInventory(id: number) {
        return request<any, AssetInventoryAVO>({
            url: `${INVENTORY_BASE_URL}/${id}/getAssetInventory`,
            method: "get",
        });
    }

    /**
     * 批量资产盘点
     * 
     * @param inventoryId 资产盘点ID
     * @param data 批量资产盘点表单对象
     */
    static batchAssetInventory(inventoryId: number, data: AssetInventoryBatchForm) {
        return request({
            url: `${INVENTORY_BASE_URL}/${inventoryId}/batchAssetInventory`,
            method: "post",
            data: data
        });
    }

    /**
     * 单个资产确认盘点
     * 
     * @param inventoryId 资产盘点ID
     * @param data 单个资产盘点表单对象
     */
    static singleAssetInventory(inventoryId: number, data: AssetInventorySingleForm) {
        return request({
            url: `${INVENTORY_BASE_URL}/${inventoryId}/singleAssetInventory`,
            method: "post",
            data: data
        });
    }

    /**
     * 导出资产盘点数据
     */
    static exportInventory(queryParams?: InventoryPageQuery) {
        return request({
            url: `${INVENTORY_BASE_URL}/export`,
            method: "get",
            params: queryParams,
            responseType: "blob"
        });
    }

    /**
     * 导入资产盘点数据
     */
    static importInventory(file: File) {
        const formData = new FormData();
        formData.append('file', file);

        return request({
            url: `${INVENTORY_BASE_URL}/import`,
            method: "post",
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }

    /**
     * 获取当前用户的盘点任务
     */
    static getMyInventoryTasks(queryParams?: InventoryPageQuery) {
        return request<any, DataAssetInventoryVO>({
            url: `${INVENTORY_BASE_URL}/myPage`,
            method: "get",
            params: queryParams
        });
    }
}

export default InventoryAPI;

/** 分页结果包装 */
export interface DataAssetInventoryVO {
    list: AssetInventoryVO[];
    total: number;
}

/** 资产盘点分页查询参数 */
export interface InventoryPageQuery extends PageQuery {
    /** ID */
    id?: string;
    /** 盘点名称 */
    inventoryName?: string;
    /** 任务状态 (0待进行 1进行中、2已完成、3逾期完成、4逾期未完成) */
    status?: string;
    /** 盘点类型 (1即时盘点 2定期盘点) */
    type?: string;
    /** 盘点周期 */
    inventoryCycle?: string;
    /** 截止时间 */
    deadline?: string | [string, string];
    /** 通知状态 */
    notificationStatus?: string;
    /** 通知策略ID */
    notificationStrategyId?: number;
    /** 通知时间 */
    notificationTime?: string | [string, string];
    /** 创建部门ID */
    createDeptId?: number;
    /** 创建人ID */
    createUserId?: number;
    /** 创建人名称 */
    createUserName?: string;
    /** 创建部门名称 */
    createDeptName?: string;
    /** 创建时间 */
    createTime?: string | [string, string];
}

/** 资产盘点列表项 */
export interface AssetInventoryVO {
    assetsNum: number;
    inventoryAssetsNum: number;
    /** 任务ID */
    id: number;
    /** 盘点名称 */
    inventoryName: string;
    /** 任务状态 (0待进行 1进行中、2已完成、3逾期完成、4逾期未完成) */
    status: string;
    /** 盘点类型 (1即时盘点 2定期盘点) */
    type: string;
    /** 盘点周期 */
    inventoryCycle: string;
    /** 截止时间 */
    deadline: string;
    /** 通知状态 */
    notificationStatus: string;
    /** 通知策略ID */
    notificationStrategyId: number;
    /** 通知时间 */
    notificationTime: string;
    /** 任务完成进度*100 */
    inventoryProgress: number;
    /** 待盘点资产数量 */
    treatInventoryNum: number;
    /** 创建人Id */
    createUserId: number;
    /** 创建人名称 */
    createUserName: string;
    /** 创建人部门Id */
    createDeptId: number;
    /** 创建人部门名称 */
    createDeptName: string;
    /** 创建时间 */
    createTime: string;
    /** 更新时间 */
    updateTime: string;
}

/** 资产盘点资产项 */
export interface AssetInventoryAssetsVO {
    inventoryUserName: string;
    /** 资产盘点资产id */
    inventoryAssetsId?: number;
    /** 资产盘点id */
    inventoryId?: number;
    /** 资产id */
    assetsId?: number;
    /** 盘点状态（0未盘点 1已盘点） */
    inventoryStatus?: string;
    /** 资产变动状态（1无变动 2已变动 3已下线） */
    changeStatus?: string;
    /** 盘点时间 */
    inventoryTime?: string;
    /** 资产名称 */
    name?: string;
    /** 设备种类 */
    type?: number;
    /** ip */
    ip?: string;
    /** 资产链接 */
    url?: string;
    /** 端口 */
    port?: string;
    /** 管理员名称 */
    managerName?: string;
    /** 管理员手机 */
    mobile?: string;
    /** 资产管理者 */
    ownerName?: string;
    /** 第三方运维人员 */
    otherManager?: string;
    /** 第三方运维人员联系方式 */
    otherContact?: string;
    /** 资产所属部门id */
    deptId?: number;
    /** 资产所属部门名称 */
    deptName?: string;
    /** 资产状态 */
    status?: string;
    /** ip配置 */
    address?: string;
    /** 系统名称及版本 */
    os?: string;
    /** 资产所属系统名称 */
    sysName?: string;
    /** 备注 */
    notes?: string;
    /** 登记时间 */
    createTime?: string;
}

/** 资产盘点表单对象 */
export interface AssetInventoryForm {
    startTime: string;
    createDeptName: string;
    createUserName: string;
    /** 任务ID */
    id?: number;
    /** 盘点名称 */
    inventoryName?: string;
    /** 资产ID */
    assetIds: number[];
    /** 盘点资产列表 */
    assetsList?: AssetInventoryAssetsVO[];
    /** 任务状态 (0待进行 1进行中、2已完成、3逾期完成、4逾期未完成) */
    status?: string;
    /** 盘点类型 (1即时盘点 2定期盘点) */
    type?: string;
    /** 盘点周期 */
    inventoryCycle?: string;
    /** 截止时间 */
    deadline?: string;
    /** 启用短信通知(0否 1是) */
    notificationStatus?: string;
    /** 通知策略ID */
    notificationStrategyId?: number;
    /** 通知时间 */
    notificationTime?: string;
    /** 创建人Id */
    createUserId?: number;
    /** 创建人部门Id */
    createDeptId?: number;
    /** 创建时间 */
    createTime?: string;
    /** 更新时间 */
    updateTime?: string;
}

/** 资产盘点及资产列表 */
export interface AssetInventoryAVO {
    status: string;
    createTime: string;
    createDeptName: string;
    inventoryAssetsNum: number;
    /** 盘点任务ID */
    id: number;
    /** 盘点名称 */
    inventoryName: string;
    /** 任务状态（0待进行 1进行中、2已完成、3逾期完成、4逾期未完成） */
    status: string;
    /** 截止时间 */
    deadline: string;
    /** 任务完成进度*100 */
    inventoryProgress: number;
    /** 资产数量 */
    assetsNum: number;
    /** 盘点资产列表 */
    assetsList: AssetInventoryAListVO[];
}

/** 资产盘点资产列表项 */
export interface AssetInventoryAListVO {
    inventoryTime: string;
    deptId: any;
    /** 资产盘点资产id */
    inventoryAssetsId: number;
    /** 资产id */
    assetsId: number;
    /** 盘点状态（0未盘点 1已盘点） */
    inventoryStatus: string;
    /** 资产变动状态（1无变动 2已变动 3已下线） */
    changeStatus: string;
    /** 资产名称 */
    name: string;
    /** 资产类型 */
    type: number;
    /** ip */
    ip: string;
    /** 资产链接 */
    url: string;
    /** 管理员名称 */
    managerName: string;
    /** 资产所属部门名称 */
    deptName: string;
    /** 创建人Id */
    createUserId: number;
    /** 创建人名称 */
    createUserName: string;
    /** 创建人部门Id */
    createDeptId: number;
    /** 创建人部门名称 */
    createDeptName: string;
    /** 创建时间 */
    createTime: string;
}

/** 批量资产盘点表单 */
export interface AssetInventoryBatchForm {
    /** 资产ID */
    assetIds: number[];
    /** 资产是否有继续使用(0否 1是) */
    isContinueUse: string;
    /** 资产信息是否有变动(0否) */
    isChange?: string;
}

/** 单个资产盘点表单 */
export interface AssetInventorySingleForm {
    /** 资产ID */
    assetId: number;
    /** 资产信息是否已变动(0否 1是) */
    isChange?: string;
}
