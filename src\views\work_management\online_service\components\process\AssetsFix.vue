<template>
  <div class="initiate-ticket">
    <div class="page-header">
      <h3>工单整改</h3>
    </div>

    <el-form :model="form" label-width="120px" class="form-container">
      <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="id" prop="id">
              <el-input v-model="form.id" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务名称" prop="name">
              <el-input v-model="form.name" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 工单类型显示 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单类型" prop="ticketType">
              <el-input v-model="form.ticketType" disabled/>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 关联资产按钮 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="关联资产" class="asset-buttons">
              <el-button type="primary" @click="openTransferDialog">
                <el-icon><view /></el-icon>查看关联资产
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 任务描述 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="任务描述" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="3" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 时间信息 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker 
                disabled 
                v-model="form.createTime" 
                type="datetime" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期" prop="deadline">
              <el-date-picker 
                disabled 
                v-model="form.deadline" 
                type="datetime" 
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 工单附件 -->
        <el-row :gutter="20" v-if="form.fileList && form.fileList.length">
          <el-col :span="24">
            <el-form-item label="工单附件">
              <div class="file-list">
                <el-tag 
                  v-for="file in form.fileList" 
                  :key="file.id"
                  class="file-item"
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 安全评估报告卡片 -->
      <el-card class="form-card" v-if="assessmentFiles.length || assessmentForm.commentContent">
        <template #header>
          <div class="card-header">
            <span>安全评估报告</span>
          </div>
        </template>

        <!-- 评估备注 -->
        <el-row :gutter="20" v-if="assessmentForm.commentContent">
          <el-col :span="24">
            <el-form-item label="评估说明">
              <el-input 
                v-model="assessmentForm.commentContent" 
                disabled 
                type="textarea" 
                :rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 安全评估报告文件 -->
        <el-row :gutter="20" v-if="assessmentFiles && assessmentFiles.length">
          <el-col :span="24">
            <el-form-item label="评估报告文件">
              <div class="file-list">
                <el-tag 
                  v-for="file in assessmentFiles" 
                  :key="file.id"
                  class="file-item"
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 整改信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>整改信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改时间" prop="createTime">
              <el-date-picker
                v-model="fixform.createTime"
                type="datetime"
                placeholder="选择时间"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="整改备注" prop="commentContent">
              <el-input 
                v-model="fixform.commentContent" 
                type="textarea" 
                :rows="3" 
                :disabled="!showStep"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="showStep">
          <el-col :span="24">
            <el-form-item label="上传整改附件">
              <file-upload 
                :upload-max-size="20 * 1024 * 1024" 
                v-model="fixFile"
                :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'" 
                :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'"
              >
              </file-upload>
              <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="!showStep && fixFile.length">
          <el-col :span="24">
            <el-form-item label="整改文件">
              <div class="file-list">
                <el-tag 
                  v-for="file in fixFile" 
                  :key="file.id"
                  class="file-item"
                  @click="downloadFile(file)"
                >
                  <el-icon><document /></el-icon>
                  {{ file.name }}
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions" v-if="showStep">
        <el-button type="primary" @click="submitForm" size="large">
          <el-icon><check /></el-icon>提交整改
        </el-button>
      </div>
    </el-form>

    <!-- 关联资产弹窗 -->
    <AssetsViewer
      v-model:visible="transferDialog.visible"
      :assets-list="transferDialog.allAssets"
      title="查看关联资产" 
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import businessAPI from '@/api/work_management/online_service/index'
import AssetsViewer from './components/AssetsViewer.vue'
import {formatLocalDateTime} from '@/utils/dateUtils';

interface TicketData {
  id: number;
  currentStep: string;
  isClick: boolean;
}

const props = defineProps<{
  ticketdata: TicketData,
}>();

const emit = defineEmits(['next'])

const form = reactive({
  createTime: "",
  name: "",
  updateTime: '',
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  id: '',
  comments: [],
  ticketType: "",
  deadline: "",
  fileList: [] as any[],
  assetsList: [] as any[],
})

const auditform = ref<{
  fixTime: string;
  createTime: string;
  updateTime: string;
  commentContent: string;
  commentBy: string;
  commentType: number;
  fileList: { id: string }[];
}>({
  fixTime: '',
  createTime: '',
  updateTime: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  fileList: [],
});

// 安全评估表单数据
const assessmentForm = ref<{
  createTime: string;
  updateTime: string;
  commentContent: string;
  commentBy: string;
  commentType: number;
  fileList: { id: string }[];
}>({
  createTime: '',
  updateTime: '',
  commentContent: '',
  commentBy: '',
  commentType: 1,
  fileList: [],
});

const fixform = reactive<any>({
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 1,
  fixTime: '',
  createTime: formatLocalDateTime(new Date()),
  fileList: [],
  ticketType: "",
  deadline: "",
})

// 安全评估报告文件列表
const assessmentFiles = ref([] as any[]);
const fixFile = ref([] as any[]);
const fixFileList = ref([] as any[]);

const currentStep = ref(props.ticketdata.currentStep);
const showStep = ref(true);
const nowStep = ref('');
const stepStatus = ref<any | null>(null);

// 是否为当前步骤
function isCurrentStep() {
  console.log(currentStep.value)
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
}

const submitForm = async () => {
  fixform.fileList = fixFile.value.map(file => file.id);
  await ElMessageBox.confirm(
      '确定要提交整改吗？',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    try {
      await businessAPI.rectification(fixform,props.ticketdata.id).then(() => {
        ElMessage.success('整改已提交')
        emit('next')
      })
    } catch (error) {
      console.error('Error submitting rectification:', error)
      ElMessage.error('提交失败，请重试')
    }
}

// 附件下载
const downloadFile = (row: any) => {
  const fileUrl = row.url ? row.url : null; // 添加 null check
  if (fileUrl) {
    window.open(fileUrl, '_blank');
  } else {
    ElMessage.error('附件不存在');
  }
}

// 添加新的响应式变量
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[]
})

//查看关联资产
const openTransferDialog = async () => {
  transferDialog.visible = true;
  transferDialog.allAssets = form.assetsList;
}

const handleQuery = async () => {
  if (props.ticketdata.id) {
    const statusRes: any = await businessAPI.getStepStatus(props.ticketdata.id);
    stepStatus.value = statusRes;
    for (const step in stepStatus.value) {
      if (stepStatus.value[step as keyof any] == 'process') {
        nowStep.value = step as string;
        break;
      }
    }
    await businessAPI.getFormData(props.ticketdata.id).then((data) => {
      Object.assign(form, data)
    })
    
    // 获取审核信息
    let filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 2) : [];
    auditform.value = filteredComments[filteredComments.length - 1];
    
    // 获取安全评估报告信息（步骤4）
    let assessComments = form.comments ? form.comments.filter((item: any) => item.step == 4) : [];
    if (assessComments.length > 0) {
      assessmentForm.value = assessComments[assessComments.length - 1];
      
      // 获取安全评估文件
      try {
        const res = await businessAPI.getAssessmentFiles(props.ticketdata.id);
        assessmentFiles.value = res.fileList || [];
      } catch (error) {
        console.error('Error getting assessment files:', error)
      }
    }
    
    try {
      await businessAPI.getRectification(props.ticketdata.id).then((res) => {
        fixFileList.value = res.fileList || [];
      })
    } catch (error) {
      console.error('Error getting rectification:', error)
    }

    isCurrentStep();
    if (!showStep.value) {
      let filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 3) : [];
      Object.assign(fixform, filteredComments[filteredComments.length - 1]);
      try {
        await businessAPI.getRectification(props.ticketdata.id).then((res) => {
          fixFile.value = res.fileList || [];
        })
      } catch (error) {
        console.error('Error getting rectification:', error)
      }
    }
  }
}

onMounted(() => {
  handleQuery()
})

</script>

<style scoped>
.initiate-ticket {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h3 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin: 0;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

.asset-buttons {
  display: flex;
  gap: 12px;
}

.asset-buttons .el-button {
  min-width: 120px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}

/* 输入框样式统一 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 卡片内容区域padding */
:deep(.el-card__body) {
  padding: 20px;
}

/* 资产弹窗样式 */
.asset-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}
</style>
