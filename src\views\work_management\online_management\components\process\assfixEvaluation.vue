<template>
  <div class="initiate-ticket">
    <div class="page-header">
      <h3>工单评价</h3>
    </div>
    <el-form :model="form" label-width="120px" class="form-container">
        <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
       <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="工号：" prop="employeeId">
              <el-input v-model="form.employeeId" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人信息：" prop="applicantName">
              <el-input v-model="form.applicantName" disabled/>
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label-width="100px" label="所在单位：" prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                    code="dept0x0"
                    v-model="form.deptId"
                  />
                </template>
              </el-input>
            </el-form-item>
           </el-col>
           <el-col :span="6">
            <el-form-item label-width="100px" label="联系方式：" prop="mobile">
              <el-input v-model="form.mobile" disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="12">
            <el-form-item label="工单创建时间：" prop="createTime">
              <el-date-picker 
                v-model="form.createTime" 
                type="datetime" 
                placeholder="选择时间"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item label="工单截止日期：" prop="deadline">
            <el-date-picker 
              disabled
              v-model="form.deadline" 
              type="datetime" 
              placeholder="选择截止时间"
              style="width: 100%"
              :min-date="form.deadline"
            />
          </el-form-item>
        </el-col>
        </el-row>
      </el-card>
       <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>被评估部门</span>
          </div>
        </template>
        <el-row :gutter="24">
              <el-col :span="8">
            <el-form-item label="责任部门：" >
             <el-input
                v-model="shstep.deptName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="责任人：">
              <el-input
                v-model="shstep.userName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式：" >
              <el-input
                v-model="shstep.userMobile"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
       </el-card>
     
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>网络安全评估工程师</span>
          </div>
        </template>
        <el-row :gutter="24">
           <!-- 安全工程师信息开始 -->
         <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
        <div style="display:flex; justify-content: space-between;">
              <el-form-item label-width="110px" label="工程师名称："   prop="engineerName" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerName"  ></el-input>
            </el-form-item>
            <el-form-item   label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input disabled  v-model="SafetyEngineerbox.engineerMobile"  > </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerWechat"  ></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerQq"  ></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="80px" prop="engineerEmail" style="width:20%">
              <el-input disabled v-model="SafetyEngineerbox.engineerEmail"  ></el-input>
            </el-form-item>
        </div>
        </el-form>
        </el-row>
      </el-card>
       <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>评估对象</span>
          </div>
        </template>
        <el-row :gutter="24">
         
          <el-col :span="12">
            <el-form-item label="信息系统名称："  label-width="120px" prop="sysname">
              <el-input v-model="form.sysname" readonly/>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="域名/IP："  label-width="120px" prop="domainIp">
              <el-input v-model="form.domainIp" readonly/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="评估描述："  label-width="120px" prop="reason">
              <el-input v-model="form.reason" readonly/>
            </el-form-item>
          </el-col>
           <el-col :span="24" v-if="form.evaluationObjectFile">
              <el-form-item  class="fontw"  label="评估对象清单：">
                <ul>
                  <li >
                    <span>{{ form.evaluationObjectFile.name }}</span>
                     <el-button type="primary" style="margin-left:20px" @click="downloadFile(form.evaluationObjectFile)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
           </el-col>
        </el-row>
      </el-card>
      <el-card class="form-card"  v-if="form.commentsAssessment">
        <template #header>
          <div class="card-header">
            <span>安全评估情况反馈</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="24">
           <el-form-item :label="typetext=='OnlineDetails'?`业务上线评估情况反馈：`:'安全评估情况反馈：'" label-width="170px">
              <el-input v-model="form.commentsAssessment.commentContent" disabled type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12"  v-if="form.commentsAssessment">
             <el-form-item  class="fontw" label-width="170px"  :label="typetext=='OnlineDetails'?`业务上线评估报告：`:'安全评估评估报告：'" >
                <ul  class="fileList_ul">
                  <li v-for="file in form.commentsAssessment.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    <el-card class="form-card" v-if="form.commentsReview">
        <template #header>
          <div class="card-header">
            <span>安全问题复核反馈</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="24">
           <el-form-item label="安全问题复核情况反馈："  label-width="170px">
              <el-input v-model="form.commentsReview.commentContent" disabled type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item  class="fontw" label-width="170px" label="安全问题复核报告：">
                <ul  class="fileList_ul">
                  <li v-for="file in form.commentsReview.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- 评估信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>评价信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="24" v-if="form.completeResult">
            <el-form-item label="工单完成状态：" prop="completeResult">
               <span>{{form.completeResult==1?'按时完成':'逾期完成'}}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="评分：" prop="rate">
              <el-rate v-model="evaluate.rate" :disabled="!showStep" show-score text-color="#ff9900"
                score-template="{value} points" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="评价备注：" prop="commentContent">
              <el-input v-model="evaluate.commentContent" :disabled="!showStep" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- 操作按钮 -->
      <div class="form-actions" v-if="showStep">
        <el-button type="primary" @click="submitForm" size="large">
          <el-icon>
            <check />
          </el-icon>提交评价
        </el-button>
         <el-button type="info" @click="concelForm" size="large">取消</el-button>
      </div>
    </el-form>
    <!-- 关联资产弹窗 -->
    <AssetsViewer v-model:visible="visible" :assets-list="assetsList" title="查看关联资产" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import businessAPI, { businessPageVO, businessForm, businessPageQuery } from '@/api/work_management/online_service/index'
import AssetsViewer from './components/AssetsViewer.vue'
import {formatLocalDateTime} from "@/utils/dateUtils";
import safetyAPI from "@/api/work_management/safety";
import UserAPI from "@/api/user";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
// 获取当前页面参数。判断是业务上线页面还是安全评估页面
const typetext = ref(route.query.type as string);
interface TicketData {
  id: number;
  currentStep: string;
  isClick: boolean;
}
const props = defineProps<{
  ticketdata: TicketData,
}>();
const emit = defineEmits(['next'])
const form = reactive({
  createTime: "",
  name: "",
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  id: '',
  comments: [],
  ticketType: "",  // 添加工单类型
  deadline: "",    // 添加截止日期
  fileList: [] as any[],  // 添加附件列表
  assetsList: [] as any[],  // 资产列表
})

const auditform = ref<{
  fixTime: string;
  createTime: string;
  updateTime: string;
  commentContent: string;
  commentBy: string;
  commentType: number;
  fileList: { id: string }[];
}>({
  fixTime: '',
  createTime: '',
  updateTime: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  fileList: [],
});

const fixform = reactive<any>({
  updateTime: '',
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  fixTime: '',
  createTime: '',
  fileList: [],
})

const recheck = reactive({
  updateTime: '',
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  fixTime: '',
  createTime: '',
  fileList: [],
})


const evaluate = reactive({
  updateTime: formatLocalDateTime(new Date()),
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 1,
  fixTime: '',
  createTime: formatLocalDateTime(new Date()),
  fileList: [],
  rate: 5
})

const fixFile = ref([] as any[]);

const fixFileList = ref([] as any[]);
const evaluateFileList = ref([] as any[]);

const currentStep = ref(props.ticketdata.currentStep);
const showStep = ref(true);
const nowStep = ref('');
const stepStatus = ref<any | null>(null);
// 是否为当前步骤
function isCurrentStep() {
  console.log(currentStep.value)
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = true;
  } else {
    showStep.value = false;
  }
}
// 取消按钮
const concelForm=()=>{
emit('next')
}
const submitForm = async () => {
  await ElMessageBox.confirm(
    '确定要提交评价吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  );
  try {
    evaluate.fileList = evaluateFileList.value.map(file => file.id);
    await businessAPI.evaluation(evaluate, props.ticketdata.id).then(() => {
      ElMessage.success('评价结果已提交')
    })
    emit('next')
  } catch (error) {
    console.error('Error submitting rectification:', error)
    ElMessage.error('提交失败，请重试')
  }
}

// 附件下载
const downloadFile = (row: any) => {
  // const fileUrl = row.url ? row.url : null; // 添加 null check
  if (row.url) {
      fetch(row.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = row.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  });
  } else {
    ElMessage.error('附件不存在');
  }
}

// 部门信息
const shstep=ref({})
 // 获取安全工程师信息
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}

const handleQuery = async () => {
  if (props.ticketdata.id) {
    const statusRes: any = await businessAPI.getStepStatus(props.ticketdata.id);
    stepStatus.value = statusRes;
    for (const step in stepStatus.value) {
      if (stepStatus.value[step as keyof any] == 'process') {
        nowStep.value = step as string;
        break;
      }
    }
    await businessAPI.getFormData(props.ticketdata.id).then((data) => {
      Object.assign(form, data)
    })
        // 整改部门信息
   const foundItem = form.reviewProcessForms?.find(item => item.executeDeptType ==4);
   shstep.value=foundItem
     // 获取审核信息

    let filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 2) : [];
    auditform.value = filteredComments[filteredComments.length - 1];
    try {
      await businessAPI.getRectification(props.ticketdata.id).then((res) => {
        fixFileList.value = res.fileList || [];
      })
    } catch (error) {
      console.error('Error getting rectification:', error)
    }

    //整改内容
    let filteredComments1 = form.comments ? form.comments.filter((item: any) => item.step == 3) : [];
    Object.assign(fixform, filteredComments1[filteredComments1.length - 1]);
    // try {
    //   await businessAPI.getEvaluationFiles(props.ticketdata.id).then((res) => {
    //     evaluateFileList.value = res.fileList || [];
    //   }).catch(() => {
    //     evaluateFileList.value = [];
    //   });
    // } catch (error) {
    //   console.error('Error getting evaluation files:', error)
    // }

    // 整改复核
    let filteredComments2 = form.comments ? form.comments.filter((item: any) => item.step == 4) : [];
    Object.assign(recheck, filteredComments2[filteredComments2.length - 1]);
    isCurrentStep();
    if (!showStep.value) {
      let filteredComments3 = form.comments ? form.comments.filter((item: any) => item.step == 5) : [];
      Object.assign(evaluate, filteredComments3[filteredComments3.length - 1]);
    }
  }
}

// 添加新的响应式变量
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[]
})

//查看关联资产
const openTransferDialog = async () => {
  transferDialog.visible = true;
  transferDialog.allAssets = form.assetsList;
}

onMounted(() => {
  handleQuery()
 SafetyEngineerConfig()
})


</script>

<style scoped>
.initiate-ticket {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h3 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin: 0;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

.asset-buttons {
  display: flex;
  gap: 12px;
}

.asset-buttons .el-button {
  min-width: 120px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}

/* 输入框样式统一 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 卡片内容区域padding */
:deep(.el-card__body) {
  padding: 20px;
}

/* 评分组件样式优化 */
:deep(.el-rate) {
  height: 32px;
  line-height: 32px;
}

/* 弹窗样式 */
.asset-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}
:deep(.el-form-item) {
  margin-right: 0;
}
</style>
