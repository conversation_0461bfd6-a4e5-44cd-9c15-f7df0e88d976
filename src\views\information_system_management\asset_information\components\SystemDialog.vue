<template>
  <el-dialog v-model="props.visible" :title="title" width="65%" :before-close="handleClose">
    <el-tabs type="border-card">
      <!-- 信息系统基本信息 -->
      <el-tab-pane label="基本信息">
        <el-form ref="basicFormRef" :model="form.basic" :rules="rules.basic" class="system-form">

          <!-- 基础信息 -->
          <div class="form-section">
            <div class="section-title">基础信息</div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="信息系统名称" prop="systemName">
                  <el-input v-model="form.basic.systemName" placeholder="填写系统全称或官方名称" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="信息系统版本" prop="systemVersion">
                  <el-input v-model="form.basic.systemVersion" placeholder="无版本填写无版本" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="域名/IP地址" prop="webAddresses.0.url">
                  <el-input v-model="form.basic.webAddresses[0].url" placeholder="http://www.example.com" />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="主要功能描述" prop="funcDes">
                  <el-input v-model="form.basic.funcDes" type="textarea" :rows="3" maxlength="100" show-word-limit
                    placeholder="填写信息系统主要功能（最多100字）" />
                </el-form-item>
              </el-col>
              <!-- 访问地址列表 -->
              <el-col :span="24">
                <AddressList v-model="form.basic.addressList" />
              </el-col>
            </el-row>
          </div>

          <!-- 其他信息 -->
          <div class="form-section mt-6">
            <div class="section-title">其他信息</div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开发框架" prop="framework">
                  <el-input v-model="form.basic.framework" placeholder="如: Vue, Spring Boot 等" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="等保等级" prop="securityLevel">
                  <el-select v-model="form.basic.securityLevel" placeholder="请选择等保等级">
                    <el-option label="未定级" value="0" />
                    <el-option label="等保一级" value="1" />
                    <el-option label="等保二级" value="2" />
                    <el-option label="等保三级" value="3" />
                    <el-option label="等保四级" value="4" />
                    <el-option label="等保五级" value="5" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="是否对公网开放" prop="isOpen">
                  <el-radio-group v-model="form.basic.isOpen">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="是否接入统一认证" prop="useUnifiedAuth">
                  <el-radio-group v-model="form.basic.useUnifiedAuth">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="是否加入重保" prop="joinCritical">
                  <el-radio-group v-model="form.basic.joinCritical">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col v-if="form.basic.joinCritical === '1'" :span="24">
                <el-form-item label="重保事件" prop="eventsIds">
                  <el-select v-model="form.basic.eventsIds" multiple placeholder="请选择重保事件" style="width: 100%"
                    clearable>
                    <el-option v-for="event in criticalEvents" :key="event.id" :label="event.eventName"
                      :value="event.id">
                      <div class="flex items-center justify-between">
                        <span>{{ event.eventName }}</span>
                        <el-tag size="small"
                          :type="event.status == 0 ? 'info' : event.status == 1 ? 'success' : 'danger'">
                          {{ event.status == 0 ? '未开始' : event.status == 1 ? '进行中' : '已结束' }}
                        </el-tag>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注" prop="description">
                  <el-input v-model="form.basic.description" type="textarea" :rows="3" placeholder="填写备注信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 时间信息 -->
          <div class="form-section mt-6">
            <div class="section-title">时间信息</div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间" prop="createTime">
                  <el-date-picker v-model="form.basic.createTime" type="date" placeholder="选择日期" style="width: 100%" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="上线日期" prop="onlineDate">
                  <el-date-picker v-model="form.basic.onlineDate" type="date" placeholder="选择日期" style="width: 100%" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="更新日期" prop="updateDate">
                  <el-date-picker v-model="form.basic.updateDate" type="date" placeholder="选择日期" style="width: 100%" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="下线日期" prop="offlineDate">
                  <el-date-picker v-model="form.basic.offlineDate" type="date" placeholder="选择日期" style="width: 100%" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="上线负责人" prop="launchLeader">
                  <el-input v-model="form.basic.launchLeader" placeholder="请输入负责人" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="联系方式" prop="contactPhone">
                  <el-input v-model="form.basic.contactPhone" placeholder="请输入联系方式" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

        </el-form>
      </el-tab-pane>

      <!-- 信息系统数据 -->
      <el-tab-pane label="数据基本信息">
        <el-form ref="dataFormRef" :model="form.data" :rules="rules.data" class="system-form">
          <!-- 基本数据信息 section -->
          <div class="form-section">
            <div class="section-title">基本数据信息</div>

            <!-- 数据描述 -->
            <el-form-item label="数据描述" prop="dataDescription">
              <el-input v-model="form.data.dataDescription" type="textarea" :rows="4" placeholder="请描述系统中的数据情况" />
            </el-form-item>

            <!-- 数据总量信息 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据量(单位:GB)" prop="totalDataSize">
                  <el-input v-model="form.data.totalDataSize" placeholder="请输入数据总量(如: 100GB)" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="数据数量(单位:条)" prop="totalDataCount">
                  <el-input v-model="form.data.totalDataCount" placeholder="请输入数据总条数(如: 1000万条)" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 数据安全 section -->
          <div class="form-section">
            <div class="section-title">数据安全</div>

            <!-- 第一行：是否有个人信息 & 是否在互联网中传输 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否有个人信息" prop="hasPersonalInfo">
                  <el-radio-group v-model="form.data.hasPersonalInfo">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否在互联网中传输" prop="hasInternetTransfer">
                  <el-radio-group v-model="form.data.hasInternetTransfer">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第二行：个人信息数据量和数量（仅在有个人信息时展示） -->
            <el-row v-if="form.data.hasPersonalInfo == '1'" :gutter="20">
              <el-col :span="12">
                <el-form-item label="个人信息数据量(GB)" prop="personalDataSize">
                  <el-input v-model="form.data.personalDataSize" placeholder="请输入数据量（如：50）" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="个人信息数据数量(条)" prop="personalDataCount">
                  <el-input v-model="form.data.personalDataCount" placeholder="请输入数据条数（如：500万）" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第三行：传输过程是否加密 & 存储过程是否加密 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="传输过程中是否加密重要信息" prop="hasTransferEncryption">
                  <el-radio-group v-model="form.data.hasTransferEncryption">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>

                  <div v-if="form.data.hasTransferEncryption == '1'"
                    style="margin-top: 8px; display: block; width: 100%;">
                    <el-input v-model="form.data.transferEncryptionAlgorithm" placeholder="请输入加密算法（如：AES）"
                      size="small" />
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="存储过程中是否加密重要信息" prop="hasStorageEncryption">
                  <el-radio-group v-model="form.data.hasStorageEncryption">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>

                  <div v-if="form.data.hasStorageEncryption == '1'"
                    style="margin-top: 8px; display: block; width: 100%;">
                    <el-input v-model="form.data.storageEncryptionAlgorithm" placeholder="请输入加密算法（如：SM4）"
                      size="small" />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

        </el-form>
      </el-tab-pane>

      <!-- 管理单位 -->
      <el-tab-pane label="管理单位">
        <el-form ref="managementFormRef" :model="form.management" :rules="rules.management"
          class="system-form management-form">
          <!-- 部门信息 -->
          <div class="form-section">
            <div class="section-title">部门基本信息</div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="管理部门" prop="deptId">
                  <el-tree-select v-model="form.management.deptId" placeholder="请选择所属部门" :data="deptOptions" filterable
                    check-strictly :render-after-expand="false" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="办公地址" prop="officeAddress">
                  <el-input v-model="form.management.officeAddress" placeholder="填写办公地址" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 领导信息 -->
          <div class="form-section">
            <div class="section-title">
              <el-icon>
                <User />
              </el-icon>
              部门领导信息
            </div>

            <el-form-item label="选择管理领导" prop="manager">
              <el-button type="primary" @click="openSelectLeader" :disabled="!form.management.deptId">
                <el-icon>
                  <User />
                </el-icon>选择部门领导
              </el-button>
            </el-form-item>

            <!-- 显示已选管理领导信息 -->
            <template v-if="peopleDialog.selectedLeaderId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="领导姓名" :span="2">
                    {{ form.management.manager }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.officePhone || '未填写' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.mobile || '未填写' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.email || '未填写' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>

          <!-- 管理员信息 -->
          <div class="form-section">
            <div class="section-title">
              <el-icon>
                <Avatar />
              </el-icon>
              系统管理员信息
            </div>

            <el-form-item label="选择系统管理员" prop="sysAdmin">
              <el-button type="primary" @click="openSelectAdmin" :disabled="!form.management.deptId">
                <el-icon>
                  <User />
                </el-icon>选择系统管理员
              </el-button>
            </el-form-item>

            <!-- 显示已选系统管理员信息 -->
            <template v-if="peopleDialog.selectedAdminId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="管理员姓名" :span="2">
                    {{ form.management.sysAdmin }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.adminOfficePhone || '未填写' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.adminMobile || '未填写' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.adminEmail || '未填写' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 服务商 tab 部分的修改 -->
      <el-tab-pane label="服务商">
        <el-form ref="developerFormRef" :model="form.developer" :rules="rules.developer" class="system-form">
          <!-- 基本选择 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-form-item label="开发与运维服务商是否相同" prop="isSameCompany">
              <el-radio-group v-model="form.developer.isSameCompany">
                <el-radio label="1">同一公司/团队</el-radio>
                <el-radio label="0">不同公司/团队</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>

          <!-- 同一家公司的情况 -->
          <template v-if="form.developer.isSameCompany === '1'">
            <div class="form-section">
              <div class="section-title">
                <el-icon>
                  <Office />
                </el-icon>
                开发与运维服务商信息
              </div>

              <!-- 集成服务商选择组件 -->
              <el-form-item label="选择服务商" prop="providerId">
                <select-service-provider v-model="form.developer.providerId"
                  @provider-selected="handleProviderSelected" />
              </el-form-item>

              <!-- 显示已选服务商信息 -->
              <template v-if="form.developer.providerDetail">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="公司名称" :span="2">
                      {{ form.developer.providerDetail.providerName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目负责人">
                      {{ form.developer.providerDetail.projectManager }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                      {{ form.developer.providerDetail.managerMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.developer.providerDetail.managerEmail }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术负责人">
                      {{ form.developer.providerDetail.techLeader }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术电话">
                      {{ form.developer.providerDetail.techMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术邮箱" :span="2">
                      {{ form.developer.providerDetail.techEmail }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>
          </template>

          <!-- 不同公司的情况 -->
          <template v-else>
            <div class="form-section">
              <div class="section-title">
                <el-icon>
                  <Monitor />
                </el-icon>
                开发商信息
              </div>

              <el-form-item label="选择开发商" prop="developerId">
                <select-service-provider v-model="form.developer.developerId"
                  @provider-selected="handleDeveloperSelected" />
              </el-form-item>

              <!-- 显示已选开发商信息 -->
              <template v-if="form.developer.developerDetail">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="公司名称" :span="2">
                      {{ form.developer.developerDetail.providerName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目负责人">
                      {{ form.developer.developerDetail.projectManager }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                      {{ form.developer.developerDetail.managerMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.developer.developerDetail.managerEmail }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术负责人">
                      {{ form.developer.developerDetail.techLeader }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术电话">
                      {{ form.developer.developerDetail.techMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术邮箱" :span="2">
                      {{ form.developer.developerDetail.techEmail }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>

            <!-- 运维商信息 -->
            <div class="form-section">
              <div class="section-title">
                <el-icon>
                  <SetUp />
                </el-icon>
                运维商信息
              </div>

              <el-form-item label="选择运维商" prop="operatorId">
                <select-service-provider v-model="form.developer.operatorId"
                  @provider-selected="handleOperatorSelected" />
              </el-form-item>

              <!-- 显示已选运维商信息 -->
              <template v-if="form.developer.operatorDetail">
                <div class="provider-info-card">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="公司名称" :span="2">
                      {{ form.developer.operatorDetail.providerName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="项目负责人">
                      {{ form.developer.operatorDetail.projectManager }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系电话">
                      {{ form.developer.operatorDetail.managerMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="联系邮箱" :span="2">
                      {{ form.developer.operatorDetail.managerEmail }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术负责人">
                      {{ form.developer.operatorDetail.techLeader }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术电话">
                      {{ form.developer.operatorDetail.techMobile }}
                    </el-descriptions-item>
                    <el-descriptions-item label="技术邮箱" :span="2">
                      {{ form.developer.operatorDetail.techEmail }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </template>
            </div>
          </template>
        </el-form>
      </el-tab-pane>


      <!-- 盘点策略 tab -->
      <!-- <el-tab-pane label="盘点策略">
        <el-form ref="inventoryFormRef" :model="form.inventory" :rules="rules.inventory" label-width="150px"
          class="system-form">
          <el-form-item label="是否进行资产盘点" prop="enableInventory">
            <el-radio-group v-model="form.inventory.enableInventory">
              <el-radio label="是">是</el-radio>
              <el-radio label="否">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="form.inventory.enableInventory === '是'">
            <el-form-item label="盘点任务名称" prop="inventoryTaskName">
              <el-input v-model="form.inventory.inventoryTaskName" placeholder="请输入盘点任务名称" />
            </el-form-item>

            <el-form-item label="是否定时盘点" prop="enableScheduledInventory">
              <el-radio-group v-model="form.inventory.enableScheduledInventory">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="form.inventory.enableScheduledInventory === '是'" label="盘点周期" prop="inventoryCycle">
              <el-select v-model="form.inventory.inventoryCycle" placeholder="请选择盘点周期">
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
                <el-option label="每季度" value="quarterly" />
                <el-option label="每年" value="yearly" />
              </el-select>
            </el-form-item>

            <el-form-item label="是否临时盘点" prop="enableTemporaryInventory">
              <el-radio-group v-model="form.inventory.enableTemporaryInventory">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="form.inventory.enableTemporaryInventory === '是'" label="临时盘点时间"
              prop="temporaryInventoryDate">
              <el-date-picker v-model="form.inventory.temporaryInventoryDate" type="datetime" placeholder="选择临时盘点时间" />
            </el-form-item>
          </template>
        </el-form>
      </el-tab-pane> -->

      <!-- 探测策略 tab -->
      <!-- <el-tab-pane label="探测策略">
        <el-form ref="detectionFormRef" :model="form.detection" :rules="rules.detection" label-width="150px"
          class="system-form">
          <el-form-item label="是否进行资产探测" prop="enableDetection">
            <el-radio-group v-model="form.detection.enableDetection">
              <el-radio label="是">是</el-radio>
              <el-radio label="否">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="form.detection.enableDetection === '是'">
            <el-form-item label="探测任务名称" prop="detectionTaskName">
              <el-input v-model="form.detection.detectionTaskName" placeholder="请输入探测任务名称" />
            </el-form-item>

            <el-form-item label="是否定时探测" prop="enableScheduledDetection">
              <el-radio-group v-model="form.detection.enableScheduledDetection">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="form.detection.enableScheduledDetection === '是'" label="探测周期" prop="detectionCycle">
              <el-select v-model="form.detection.detectionCycle" placeholder="请选择探测周期">
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
                <el-option label="每季度" value="quarterly" />
                <el-option label="每年" value="yearly" />
              </el-select>
            </el-form-item>

            <el-form-item label="是否临时探测" prop="enableTemporaryDetection">
              <el-radio-group v-model="form.detection.enableTemporaryDetection">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="form.detection.enableTemporaryDetection === '是'" label="临时探测时间"
              prop="temporaryDetectionDate">
              <el-date-picker v-model="form.detection.temporaryDetectionDate" type="datetime" placeholder="选择临时探测时间" />
            </el-form-item>
          </template>
        </el-form>
      </el-tab-pane> -->

      <!-- 关联资产 tab -->
      <el-tab-pane label="关联资产">
        <div class="assets-section">
          <el-button type="primary" @click="openSelectAssets" class="mb-4">选择关联资产</el-button>

          <!-- 使用表格显示已选资产 -->
          <el-table v-if="form.assets.selectedAssets?.length" :data="selectedAssetsDetails" border style="width: 100%">
            <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
            <el-table-column prop="name" label="资产名称" min-width="120" align="center" />
            <el-table-column prop="type" label="资产类型" min-width="100" align="center">
              <template #default="scope">
                <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="资产地址" min-width="200" align="center">
              <template #default="scope">
                <div>IP：{{ scope.row.ip }}</div>
                <div v-if="scope.row.url">URL：{{ scope.row.url }}</div>
                <div v-if="scope.row.port">端口：{{ scope.row.port }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="deptId" label="管理单位" min-width="120" align="center">
              <template #default="scope">
                <dictmap v-model="scope.row.deptId" code="dept0x0" />
              </template>
            </el-table-column>
            <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" />
            <el-table-column label="联系方式" min-width="150" align="center">
              <template #default="scope">
                <div>办公电话：{{ scope.row.otherContact }}</div>
                <div>手机号码：{{ scope.row.ownerPhone }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="资产状态" min-width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.status === '1' ? 'success' : 'danger'">
                  {{ scope.row.status === '1' ? '正常' : '异常' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" link @click="removeAsset(scope.row.id)">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div v-else class="empty-assets">
            <el-empty description="暂无关联资产" />
          </div>
        </div>

        <select-assets v-model:visible="transferDialog.visible" :title="'选择关联资产'"
          :selected-assets="transferDialog.selectedAssets" :assets-type="'assets'" @selected="handleAssetsSelected" />
      </el-tab-pane>

    </el-tabs>



    <!-- 添加人员选择组件 -->
    <select-people v-model:visible="peopleDialog.leaderVisible" :title="'选择管理领导'"
      :department-id="form.management.deptId" :selected-user-id="peopleDialog.selectedLeaderId"
      @selected="handleLeaderSelected" />

    <select-people v-model:visible="peopleDialog.adminVisible" :title="'选择管理领导'" :department-id="form.management.deptId"
      :selected-user-id="peopleDialog.selectedAdminId" @selected="handleAdminSelected" />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">已填写完成</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { formatLocalDateTime } from "@/utils/dateUtils";
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DeptAPI from "@/api/dept"
import systemAPI from '@/api/assets_management/details/systems-entity'
import SelectAssets from "@/components/AssetsManage/SelectAssets.vue"
import type { assetsPageVO } from '@/api/assets_management/details/assets'
import SelectServiceProvider from '@/components/AssetsManage/SelectServiceProvider.vue'
import eventsAPI from "@/api/work_management/critical"
import ProviderAPI, { ProviderPageVO, ProviderForm, ProviderPageQuery } from "@/api/work_management/serviceProvider/index";
import SelectPeople from "@/components/AssetsManage/SelectPeople.vue"
import UserAPI from '@/api/user';
import AddressList from './AddressList.vue'


const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增信息系统'
  },
  id: {
    type: [Number],
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'submitted'])

interface DeveloperForm {
  isSameCompany: '1' | '0'
  providerId?: number  // 同一家公司时的服务商ID
  developerId?: number // 不同公司时的开发商ID
  operatorId?: number  // 不同公司时的运维商ID
  providerDetail?: any // 同一家公司时的服务商详情
  developerDetail?: any // 不同公司时的开发商详情
  operatorDetail?: any  // 不同公司时的运维商详情
}

// 表单引用
const basicFormRef = ref()
const dataFormRef = ref()
const managementFormRef = ref()
const developerFormRef = ref()
const deptOptions = ref<any>([])
// 重保
const criticalEvents = ref([])
const getCriticalEvents = async () => {
  try {
    const res = await eventsAPI.getPage({ pageNum: 1, pageSize: 999 })
    criticalEvents.value = res.list || []
  } catch (error) {
    console.error('获取重保事件列表失败:', error)
    ElMessage.error('获取重保事件列表失败')
  }
}
// 添加人员选择相关的响应式数据
const peopleDialog = reactive({
  leaderVisible: false,
  adminVisible: false,
  selectedLeaderId: null,
  selectedAdminId: null
})

// 打开选择领导的弹窗
const openSelectLeader = () => {
  // 确保已经选择了部门
  if (!form.management.deptId) {
    ElMessage.warning('请先选择管理部门')
    return
  }
  peopleDialog.leaderVisible = true
}

// 打开选择管理员的弹窗
const openSelectAdmin = () => {
  // 确保已经选择了部门
  if (!form.management.deptId) {
    ElMessage.warning('请先选择管理部门')
    return
  }
  peopleDialog.adminVisible = true
}

// 处理管理领导选择
const handleLeaderSelected = (user) => {
  peopleDialog.selectedLeaderId = user.id
  form.management.manager = user.nickname || user.username
  form.management.mobile = user.mobile || ''
  form.management.email = user.email || ''
  form.management.officePhone = user.officePhone || ''
}

// 修改系统管理员选择处理函数，将信息存入表单
const handleAdminSelected = (user) => {
  peopleDialog.selectedAdminId = user.id
  form.management.sysAdmin = user.nickname || user.username
  form.management.adminMobile = user.mobile || ''
  form.management.adminEmail = user.email || ''
  form.management.adminOfficePhone = user.officePhone || ''
}


// 初始表单数据
const initialForm = {
  basic: {
    systemName: '',
    systemVersion: '',
    webAddresses: [{
      id: 1,
      url: ''
    }],
    framework: '',
    securityLevel: '',
    joinCritical: '0',
    eventsIds: undefined,
    isOpen: '0',
    openType: 'long', // 默认长期开放
    openDuration: '', // 长期开放时长
    openTimeRange: [], // 短期开放时间范围
    useUnifiedAuth: '0',
    registerTime: new Date(),
    description: ''
  },
  data: {
    dataDescription: '',
    totalDataSize: '',
    totalDataCount: '',
    hasPersonalInfo: '0',
    personalDataSize: '',
    personalDataCount: '',
    hasEncryption: '0',
    encryptionAlgorithm: '',
    hasInternetTransfer: '0'
  },
  management: {
    deptId: undefined,
    officeAddress: '',
    manager: '',
    officePhone: '',
    mobile: '',
    email: '',
    sysAdmin: '',
    adminOfficePhone: '',
    adminMobile: '',
    adminEmail: ''
  },
  developer: {
    isSameCompany: '0',
    developerId: undefined,
    operatorId: undefined,
    providerDetail: null, // 同一家公司时的服务商详情
    developerDetail: null, // 不同公司时的开发商详情
    operatorDetail: null  // 不同公司时的运维商详情
  },
  assets: {
    selectedAssets: []
  }
}

const form = reactive(JSON.parse(JSON.stringify(initialForm)))

// 添加开放类型变更处理方法
const handleOpenTypeChange = (value: string) => {
  // 清空另一种类型的值
  if (value === 'long') {
    // 设置长期开放时，将openDuration设为永久
    form.basic.openDuration = 'permanent';

    // 同时在后台设置开放时间范围为当前登记时间至30年后
    const startDate = form.basic.registerTime ? new Date(form.basic.registerTime) : new Date();
    const endDate = new Date(startDate);
    endDate.setFullYear(endDate.getFullYear() + 30); // 增加30年

    // 保存时间范围，但在UI上不显示
    form.basic.openTimeRange = [
      formatLocalDateTime(startDate, 'date'),
      formatLocalDateTime(endDate, 'date')
    ];
  } else {
    // 短期开放时清空长期开放的值
    form.basic.openDuration = '';
    // 确保openTimeRange是可编辑的空数组
    form.basic.openTimeRange = [];
  }
}

// 获取资产类型名称
const getAssetTypeName = (type: any) => {
  const typeMap = {
    1: '服务器',
    2: '网络设备',
    3: '安全设备',
    // 根据实际需要添加更多类型
  }
  return typeMap[type] || '未知类型'
}

// 资产选择相关的响应式数据
const transferDialog = reactive({
  visible: false,
  selectedAssets: [] as number[]
})

const selectedAssetsDetails = ref<assetsPageVO[]>([])

// 资产选择相关方法
const openSelectAssets = () => {
  transferDialog.visible = true
}

const handleAssetsSelected = ({ selectedIds, selectedAssets }) => {
  form.assets.selectedAssets = selectedIds
  transferDialog.selectedAssets = selectedIds
  selectedAssetsDetails.value = selectedAssets // 直接使用传递的资产详情数据
}

const removeAsset = (assetId: number) => {
  form.assets.selectedAssets = form.assets.selectedAssets.filter(id => id !== assetId)
  transferDialog.selectedAssets = transferDialog.selectedAssets.filter(id => id !== assetId)
  selectedAssetsDetails.value = selectedAssetsDetails.value.filter(asset => asset.id !== assetId)
}

// 处理服务商选择事件
const handleProviderSelected = (provider: any) => {
  form.developer.providerDetail = provider
}

const handleDeveloperSelected = (provider: any) => {
  form.developer.developerDetail = provider
}

const handleOperatorSelected = (provider: any) => {
  form.developer.operatorDetail = provider
}

// 验证规则
const rules = reactive({
  basic: {
    systemName: [{ required: true, message: '请输入信息系统名称', trigger: 'blur' }],
    // eventsIds: [
    //   {
    //     required: true,
    //     message: '请选择重保事件',
    //     trigger: 'change',
    //     validator: (rule: any, value: any, callback: Function) => {
    //       if (form.basic.joinCritical === '1' && !value) {
    //         callback(new Error('请选择重保事件'))
    //       } else {
    //         callback()
    //       }
    //     }
    //   }
    // ],
    // systemVersion: [{ required: true, message: '请输入信息系统版本', trigger: 'blur' }],
    'webAddresses.0.url': [{ required: true, message: '请输入web地址', trigger: 'blur' }],
    // framework: [{ required: true, message: '请输入开发框架', trigger: 'blur' }],
    // useUnifiedAuth: [{ required: true, message: '请选择是否使用统一身份认证', trigger: 'change' }],
    // securityLevel: [{ required: true, message: '请输入等保等级', trigger: 'blur' }],
    // isOpen: [{ required: true, message: '请选择是否对公网开放', trigger: 'change' }],
    // funcDes: [{ required: true, message: '请输入主要功能描述', trigger: 'blur' }],
    // openType: [
    //   { required: true, message: '请选择开放类型', trigger: 'change' }
    // ],
    // openDuration: [
    //   {
    //     required: true,
    //     message: '请选择开放时长',
    //     trigger: 'change',
    //     validator: (rule: any, value: string, callback: Function) => {
    //       if (form.basic.openType === 'long' && !value) {
    //         callback(new Error('请选择开放时长'))
    //       } else {
    //         callback()
    //       }
    //     }
    //   }
    // ],
    // openTimeRange: [
    //   {
    //     required: true,
    //     message: '请选择开放时间范围',
    //     trigger: 'change',
    //     validator: (rule: any, value: any[], callback: Function) => {
    //       if (form.basic.openType === 'short' && (!value || value.length !== 2)) {
    //         callback(new Error('请选择完整的开放时间范围'))
    //       } else {
    //         callback()
    //       }
    //     }
    //   }
    // ]
  },
  // data: {
  //   dataDescription: [
  //     { required: true, message: '请输入数据描述', trigger: 'blur' }
  //   ],
  //   totalDataSize: [
  //     { required: true, message: '请输入总数据量', trigger: 'blur' }
  //   ],
  //   totalDataCount: [
  //     { required: true, message: '请输入总数据条数', trigger: 'blur' }
  //   ],
  //   hasPersonalInfo: [
  //     { required: true, message: '请选择是否有个人信息', trigger: 'change' }
  //   ],
  //   personalDataSize: [
  //     {
  //       required: true,
  //       message: '请输入个人信息数据量',
  //       trigger: 'blur',
  //       validator: (rule: any, value: string, callback: Function) => {
  //         if (form.data.hasPersonalInfo === '1' && !value) {
  //           callback(new Error('请输入个人信息数据量'))
  //         } else {
  //           callback()
  //         }
  //       }
  //     }
  //   ],
  //   personalDataCount: [
  //     {
  //       required: true,
  //       message: '请输入个人信息数据条数',
  //       trigger: 'blur',
  //       validator: (rule: any, value: string, callback: Function) => {
  //         if (form.data.hasPersonalInfo === '1' && !value) {
  //           callback(new Error('请输入个人信息数据条数'))
  //         } else {
  //           callback()
  //         }
  //       }
  //     }
  //   ],
  //   hasEncryption: [
  //     { required: true, message: '请选择是否加密存储重要信息', trigger: 'change' }
  //   ],
  //   encryptionAlgorithm: [
  //     {
  //       required: true,
  //       message: '请输入加密算法',
  //       trigger: 'blur',
  //       validator: (rule: any, value: string, callback: Function) => {
  //         if (form.data.hasEncryption === '1' && !value) {
  //           callback(new Error('请输入加密算法'))
  //         } else {
  //           callback()
  //         }
  //       }
  //     }
  //   ],
  //   hasInternetTransfer: [
  //     { required: true, message: '请选择是否在互联网中传输', trigger: 'change' }
  //   ]
  // },
  // management: {
  //   deptId: [{ required: true, message: '请选择管理部门', trigger: 'change' }],
  //   manager: [{ required: true, message: '请输入管理领导', trigger: 'blur' }],
  //   mobile: [{ required: true, message: '请输入手机号码', trigger: 'blur' }],
  //   sysAdmin: [{ required: true, message: '请输入系统管理员', trigger: 'blur' }],
  //   adminMobile: [{ required: true, message: '请输入管理员电话', trigger: 'blur' }]
  // },
  // developer: {
  //   isSameCompany: [{ required: true, message: '请选择是否为同一公司', trigger: 'change' }],
  //   developer: [{ required: true, message: '请输入软件开发商', trigger: 'blur' }],
  //   operator: [{ required: true, message: '请输入运维服务商', trigger: 'blur' }]
  // }
})

// 重置表单
const resetForm = () => {
  const forms = [basicFormRef, dataFormRef, managementFormRef, developerFormRef]
  forms.forEach(formRef => {
    if (formRef.value) {
      formRef.value.resetFields()
      formRef.value.clearValidate()
    }
  })
  Object.assign(form, JSON.parse(JSON.stringify(initialForm)))
}

// 监听ID变化加载表单数据
watch(() => props.id, async (newId) => {
  if (newId) {
    try {
      const data = await systemAPI.getFormData(newId)

      // 填充表单数据（这里可以根据需要添加详细的映射）
      mapApiDataToForm(data)
    } catch (error) {
      console.error('加载数据失败:', error)
      ElMessage.error('加载数据失败')
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// 从API数据映射到表单结构 - 修改版本
function mapApiDataToForm(apiData) {
  // 基本信息
  form.basic.systemName = apiData.sysname || ''
  form.basic.systemVersion = apiData.sysVersion || ''
  form.basic.webAddresses = [{ id: 1, url: apiData.url || '' }]
  form.basic.framework = apiData.frame || ''
  form.basic.securityLevel = apiData.level || '-'
  form.basic.joinCritical = apiData.isProtect || ''
  form.basic.eventsIds = apiData.eventsIds
  form.basic.isOpen = apiData.isOpen || ''
  form.basic.openType = apiData.openType === 'short' ? 'short' : 'long'
  form.basic.openDuration = apiData.openTimeNum || ''
  if (apiData.openTime && apiData.closeTime) {
    form.basic.openTimeRange = [apiData.openTime, apiData.closeTime]
  }
  form.basic.useUnifiedAuth = apiData.isAuth || '0'
  form.basic.registerTime = apiData.createTime ? new Date(apiData.createTime) : new Date()
  form.basic.description = apiData.notes || ''
  form.basic.funcDes = apiData.funcDes || ''

  // 数据信息
  form.data.dataDescription = apiData.dataDescription || ''
  form.data.totalDataSize = apiData.dataTotal || ''
  form.data.totalDataCount = apiData.dataNum || ''
  form.data.hasPersonalInfo = apiData.hasPersonalInfo || '0'
  form.data.personalDataSize = apiData.personalInfoTotal || ''
  form.data.personalDataCount = apiData.personalInfoNum || ''
  form.data.hasEncryption = apiData.isEncrypt || '0'
  form.data.encryptionAlgorithm = apiData.encryptionAlgorithm || ''
  form.data.hasInternetTransfer = apiData.isTrans || '0'

  // 管理单位信息
  form.management.deptId = apiData.deptId || undefined;
  form.management.officeAddress = apiData.officeAddress || '';

  // 管理领导信息 - 重点修改
  peopleDialog.selectedLeaderId = apiData.managerId || null;
  form.management.manager = apiData.leader || '';
  form.management.officePhone = apiData.leaderPhone || '';
  form.management.mobile = apiData.leaderPhone1 || '';
  form.management.email = apiData.leaderEmail || '';

  // 系统管理员信息 - 重点修改
  peopleDialog.selectedAdminId = apiData.ownerId || apiData.sysManagerId || null;
  form.management.sysAdmin = apiData.ownerName || '';
  form.management.adminOfficePhone = apiData.ownerPhone1 || '';
  form.management.adminMobile = apiData.ownerPhone || '';
  form.management.adminEmail = apiData.ownerEmail || '';

  // 如果有ID但没有名称，加载用户信息
  Promise.all([
    loadUserIfNeeded('leader', peopleDialog.selectedLeaderId, form.management.manager),
    loadUserIfNeeded('admin', peopleDialog.selectedAdminId, form.management.sysAdmin)
  ]).catch(error => {
    console.error('加载用户信息出错:', error);
  });

  // 开发商信息
  form.developer.isSameCompany = apiData.isSame || '0'
  if (apiData.isSame === '1' && apiData.developer) {
    form.developer.providerId = apiData.developer
    // 获取服务商详情
    fetchProviderDetail(apiData.developer).then(detail => {
      form.developer.providerDetail = detail
    })
  } else {
    if (apiData.developer) {
      form.developer.developerId = apiData.developer
      fetchProviderDetail(apiData.developer).then(detail => {
        form.developer.developerDetail = detail
      })
    }
    if (apiData.operator) {
      form.developer.operatorId = apiData.operator
      fetchProviderDetail(apiData.operator).then(detail => {
        form.developer.operatorDetail = detail
      })
    }
  }

  // 关联资产
  if (apiData.assetIds && Array.isArray(apiData.assetIds)) {
    form.assets.selectedAssets = apiData.assetIds;
    transferDialog.selectedAssets = apiData.assetIds; // 同时设置transferDialog中的值

    // 如果有assetsList，则使用它作为详情数据
    if (apiData.assetsList && Array.isArray(apiData.assetsList)) {
      selectedAssetsDetails.value = apiData.assetsList;
    } else {
      // 如果没有assetsList，可能需要通过API根据ID获取详情
      // 这里可以添加获取资产详情的代码
      console.log('只有资产ID，没有详情数据');
    }
  } else if (apiData.assetsList && Array.isArray(apiData.assetsList)) {
    // 兼容旧数据，没有assetIds但有assetsList的情况
    const assetIds = apiData.assetsList.map(asset => asset.id);
    form.assets.selectedAssets = assetIds;
    transferDialog.selectedAssets = assetIds;
    selectedAssetsDetails.value = apiData.assetsList;
  } else {
    // 没有关联资产
    form.assets.selectedAssets = [];
    transferDialog.selectedAssets = [];
    selectedAssetsDetails.value = [];
  }
}

// 添加一个新函数，只在必要时加载用户信息
async function loadUserIfNeeded(type, userId, existingName) {
  if (!userId) return;

  // 如果已经有名称，则不需要再加载
  if (existingName) return;

  try {
    console.log(`加载${type}用户信息:`, userId);
    const userInfo = await UserAPI.getFormData(userId);

    // 根据类型更新不同的字段
    if (type === 'leader') {
      form.management.manager = userInfo.nickname || userInfo.username || '';
      form.management.officePhone = userInfo.mobile || form.management.officePhone;
      form.management.mobile = userInfo.mobile || form.management.mobile;
      form.management.email = userInfo.email || form.management.email;
    } else if (type === 'admin') {
      form.management.sysAdmin = userInfo.nickname || userInfo.username || '';
      form.management.adminOfficePhone = userInfo.mobile || form.management.adminOfficePhone;
      form.management.adminMobile = userInfo.mobile || form.management.adminMobile;
      form.management.adminEmail = userInfo.email || form.management.adminEmail;
    }
  } catch (error) {
    console.error(`加载${type}用户信息失败:`, error);
    ElMessage.warning(`无法加载${type === 'leader' ? '管理领导' : '系统管理员'}信息`);
  }
}

// 将表单数据映射到API需要的格式 - 修改版本
function mapFormToApiData() {
  const apiData = {
    id: props.id,
    sysname: form.basic.systemName,
    sysVersion: form.basic.systemVersion,
    url: form.basic.webAddresses[0]?.url || '',
    frame: form.basic.framework,
    level: form.basic.securityLevel,
    isProtect: form.basic.joinCritical,
    eventsIds: form.basic.joinCritical === '1' ? form.basic.eventsIds : [],
    isOpen: form.basic.isOpen,
    openType: form.basic.openType,
    openTimeNum: form.basic.openType === 'long' ? form.basic.openDuration : '',
    openTime: form.basic.openTimeRange?.[0] ? formatLocalDateTime(form.basic.openTimeRange[0], 'date') : undefined,
    closeTime: form.basic.openTimeRange?.[1] ? formatLocalDateTime(form.basic.openTimeRange[1], 'date') : undefined,
    isAuth: form.basic.useUnifiedAuth,
    function: form.basic.description,
    notes: form.basic.description, // 添加备注字段映射
    funcDes: form.basic.funcDes, // 添加功能描述字段映射

    // 数据信息
    dataDescription: form.data.dataDescription,
    dataTotal: form.data.totalDataSize,
    dataNum: form.data.totalDataCount,
    hasPersonalInfo: form.data.hasPersonalInfo,
    personalInfoTotal: form.data.personalDataSize,
    personalInfoNum: form.data.personalDataCount,
    isEncrypt: form.data.hasEncryption,
    encryptionAlgorithm: form.data.encryptionAlgorithm,
    isTrans: form.data.hasInternetTransfer,

    // 管理单位 - 确保字段正确映射
    deptId: form.management.deptId,
    officeAddress: form.management.officeAddress,

    // 管理领导
    leader: form.management.manager,
    managerId: peopleDialog.selectedLeaderId,
    officePhone: form.management.officePhone,
    phone: form.management.mobile,
    email: form.management.email,

    // 系统管理员
    sysManager: form.management.sysAdmin,
    sysManagerId: peopleDialog.selectedAdminId,
    sysManagerName: form.management.sysAdmin,
    sysOfficePhone: form.management.adminOfficePhone,
    sysPhone: form.management.adminMobile,
    sysEmail: form.management.adminEmail,

    // 开发商信息
    isSame: form.developer.isSameCompany,
    developer: form.developer.isSameCompany === '1'
      ? form.developer.providerId
      : form.developer.developerId,
    operator: form.developer.isSameCompany === '1'
      ? form.developer.providerId
      : form.developer.operatorId,

    // 关联资产
    assetIds: form.assets.selectedAssets
  }

  return apiData
}

// 假设需要获取服务商详情的函数
async function fetchProviderDetail(providerId) {
  // 根据实际API进行调整
  try {
    // 这里应该有一个获取服务商详情的API调用
    const detail = await ProviderAPI.getFormData(providerId)
    return detail
  } catch (error) {
    console.error('获取服务商详情失败:', error)
    return null
  }
}

// 关闭弹窗
const handleClose = (done: any) => {
  ElMessageBox.confirm('确认关闭？')
    .then(() => {
      handleCancel()
      done()
    })
    .catch(() => { })
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
  resetForm()

  // 重置人员选择相关状态
  peopleDialog.selectedLeaderId = null
  peopleDialog.selectedAdminId = null

  // 重置资产选择状态
  transferDialog.selectedAssets = []
  selectedAssetsDetails.value = []

  // 确保重置服务商详情
  form.developer.providerDetail = null
  form.developer.developerDetail = null
  form.developer.operatorDetail = null
}


// 提交表单
const submitForm = async () => {
  const validateForms = [
    basicFormRef.value,
    dataFormRef.value,
    managementFormRef.value,
    developerFormRef.value
  ]

  try {
    // 验证所有表单
    await Promise.all(validateForms.map(form => form.validate()))

    // 将表单数据映射到API需要的格式
    const systemsEntityForm = mapFormToApiData()

    // 发送请求
    if (props.id) {
      await systemAPI.update(props.id, systemsEntityForm)
      ElMessage.success('修改成功')
    } else {
      const res = await systemAPI.add(systemsEntityForm)
      ElMessage.success('新增成功')
    }

    emit('submitted')
    handleCancel()
  } catch (error) {
    console.error('提交表单错误:', error)
    ElMessage.error('请完善表单信息')
    return false
  }
}

// 获取部门选项
onMounted(() => {
  DeptAPI.getOptions().then((data) => {
    deptOptions.value = data
  })
  getCriticalEvents()
})
</script>

<style scoped>
/* 基础表单样式 */
.system-form {
  padding: 20px;
}

/* 页脚样式 */
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}

/* 弹窗主体样式 */
:deep(.el-dialog__body) {
  padding: 0;
}

/* Web地址部分样式 */
.web-address-section {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 4px;
}

/* 表单项内容样式 */
:deep(.el-form-item__content) {
  flex-wrap: wrap;
}

/* 间距工具类 */
.ml-2 {
  margin-left: 8px;
}

/* 输入组样式 */
.input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

/* URL输入框样式 */
.url-input {
  flex: 1;
  min-width: 0;
  /* 防止flex子项溢出 */
}

/* 删除按钮样式 */
.delete-btn {
  flex: 0 0 88px;
  /* 固定宽度且不缩放 */
}

/* 资产部分样式 */
.assets-section {
  padding: 20px;
}

/* 已选资产列表样式 */
.selected-assets-list {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 资产标签样式 */
.selected-asset-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

/* 管理表单样式 */
.management-form {
  padding: 20px;
}

/* 日期选择器样式 */
:deep(.el-date-picker) {
  width: 100%;
}

/* 下拉选择器样式 */
:deep(.el-select) {
  width: 100%;
}

/* 表单分区样式 */
.form-section {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

/* 分区标题样式 */
.section-title {
  margin-bottom: 16px;
  padding-bottom: 8px;
  font-size: 16px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

/* 行样式重置 */
.el-row {
  margin-bottom: 0 !important;
}

/* 表单项样式 */
:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  font-weight: normal;
}

/* 树形选择器样式 */
:deep(.el-tree-select) {
  width: 100%;
}


/* 服务商信息卡片样式 */
.provider-info-card {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

/* 表单分区标题样式优化 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
}

.section-title .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

/* 表单分区样式优化 */
.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

/* 描述列表样式优化 */
:deep(.el-descriptions) {
  padding: 16px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-descriptions__label) {
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
  font-weight: 500;
}
</style>
