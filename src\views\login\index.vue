<template>
  <div class="login-container">
    <!-- 顶部工具栏 -->
    <div class="top-bar">
      <el-tooltip content="证书状态" placement="bottom">
        <el-button link type="primary" class="cert-link" @click="toCertPage">
          <i-ep-key />
        </el-button>
      </el-tooltip>

      <el-switch
        v-model="isDark"
        inline-prompt
        active-icon="Moon"
        inactive-icon="Sunny"
        @change="toggleTheme"
      />
      <lang-select class="ml-2 cursor-pointer" />
    </div>
    
    <!-- 登录表单 -->
    <el-card class="login-card">
      <div class="text-center relative">
        <h2>{{ defaultSettings.title }}</h2>
        <el-tag class="ml-2 absolute-rt">{{ defaultSettings.version }}</el-tag>
      </div>
      
      <!-- 登录方式切换 -->
      <div class="login-type-selector">
        <div 
          class="login-type-item" 
          :class="{ active: loginType === 'password' }" 
          @click="loginType = 'password'"
        >
          <i-ep-lock class="login-type-icon" />
          <span>账号密码登录</span>
        </div>
        <div 
          class="login-type-item" 
          :class="{ active: loginType === 'sso' }" 
          @click="loginType = 'sso'"
        >
          <i-ep-connection class="login-type-icon" />
          <span>统一认证登录</span>
        </div>
      </div>

      <!-- 账号密码登录表单 -->
      <div v-if="loginType === 'password'">
        <el-form
          ref="loginFormRef"
          :model="loginData"
          :rules="loginRules"
          class="login-form"
        >
          <!-- 用户名 -->
          <el-form-item prop="username">
            <div class="input-wrapper">
              <i-ep-user class="mx-2" />
              <el-input
                ref="username"
                v-model="loginData.username"
                :placeholder="$t('login.username')"
                name="username"
                size="large"
                class="h-[48px]"
              />
            </div>
          </el-form-item>

          <!-- 密码 -->
          <el-tooltip
            :visible="isCapslock"
            :content="$t('login.capsLock')"
            placement="right"
          >
            <el-form-item prop="password">
              <div class="input-wrapper">
                <i-ep-lock class="mx-2" />
                <el-input
                  v-model="loginData.password"
                  :placeholder="$t('login.password')"
                  type="password"
                  name="password"
                  @keyup="checkCapslock"
                  @keyup.enter="handleLoginSubmit"
                  size="large"
                  class="h-[48px] pr-2"
                  show-password
                />
              </div>
            </el-form-item>
          </el-tooltip>

          <!-- 验证码 -->
          <el-form-item prop="captchaCode">
            <div class="input-wrapper">
              <svg-icon icon-class="captcha" class="mx-2" />
              <el-input
                v-model="loginData.captchaCode"
                auto-complete="off"
                size="large"
                class="flex-1"
                :placeholder="$t('login.captchaCode')"
                @keyup.enter="handleLoginSubmit"
              />

              <el-image
                @click="getCaptcha"
                :src="captchaBase64"
                class="captcha-image"
              />
            </div>
          </el-form-item>

          <!-- 登录按钮 -->
          <el-button
            :loading="loading"
            type="primary"
            size="large"
            class="w-full"
            @click.prevent="handleLoginSubmit"
          >
            {{ $t("login.login") }}
          </el-button>
        </el-form>
      </div>
      
      <!-- 统一认证登录 -->
      <div v-if="loginType === 'sso'" class="sso-login-container">
        <div class="sso-info">
          <i-ep-info-filled class="sso-info-icon" />
          <p>点击下方按钮将跳转到统一认证平台进行登录</p>
        </div>
        
        <el-button
          type="primary"
          size="large"
          class="w-full sso-button"
          :loading="ssoLoading"
          @click="handleSsoLogin"
        >
          <i-ep-connection class="mr-2" />
          统一认证登录
        </el-button>
        
        <div class="sso-help">
          <p class="text-sm text-gray">统一认证登录遇到问题？请联系系统管理员</p>
        </div>
      </div>
    </el-card>

    <!-- ICP备案 -->
    <div class="icp-info" v-show="icpVisible">
      <p>
        Copyright © 2021 - 2024 HOOK All Rights Reserved. 重庆虎克信息安全技术有限责任公司
        版权所有
      </p>
      <p>渝ICP备20006496号-3</p>
    </div>
  </div>
</template>

<script setup lang="ts">
// 外部库和依赖
import { LocationQuery, useRoute } from "vue-router";

// 内部依赖
import { useSettingsStore, useUserStore } from "@/store";
import AuthAPI, { LoginData } from "@/api/auth";
import router from "@/router";
import defaultSettings from "@/settings";
import { ThemeEnum } from "@/enums/ThemeEnum";
import { useRouter } from 'vue-router';

const _router = useRouter();

// 类型定义
import type { FormInstance } from "element-plus";

// 导入 login.scss 文件
import "@/styles/login.scss";

// 使用导入的依赖和库
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const route = useRoute();
// 窗口高度
const { height } = useWindowSize();
// 国际化 Internationalization
const { t } = useI18n();

const toCertPage = () => {
  _router.push('/certificate');
};
// 是否暗黑模式
const isDark = ref(settingsStore.theme === ThemeEnum.DARK);
// 是否显示 ICP 备案信息
const icpVisible = ref(true);
// 按钮 loading 状态
const loading = ref(false);
// SSO登录按钮loading状态
const ssoLoading = ref(false);
// 是否大写锁定
const isCapslock = ref(false);
// 验证码图片Base64字符串
const captchaBase64 = ref();
// 登录表单ref
const loginFormRef = ref<FormInstance>();
// 当前登录方式：password 或 sso
const loginType = ref('password');

const loginData = ref<LoginData>({
  username: "admin",
  password: "",
  captchaKey: "",
  captchaCode: "",
} as LoginData);

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
    captchaCode: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.captchaCode.required"),
      },
    ],
  };
});

/** 获取验证码 */
function getCaptcha() {
  AuthAPI.getCaptcha().then((data) => {
    loginData.value.captchaKey = data.captchaKey;
    captchaBase64.value = data.captchaBase64;
  });
}

/** 登录表单提交 */
function handleLoginSubmit() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .login(loginData.value)
        .then(() => {
          const { path, queryParams } = parseRedirect();
          router.push({ path: path, query: queryParams });
        })
        .catch(() => {
          getCaptcha();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/** 处理统一认证登录 */
function handleSsoLogin() {
  ssoLoading.value = true;
  try {
    // 获取当前地址作为回调地址
    const redirectUri = encodeURIComponent(window.location.origin + '/#/auth/callback');
    
    // 构建授权请求URL（后端在当前地址的8989端口）
    const baseUrl = window.location.origin.replace(/:\d+$/, '') + ':8989';
    
    // 跳转到后端的OAuth授权接口，后端会重定向到统一认证中心
    const authUrl = `${baseUrl}/oauth2.0/authorize?response_type=code&client_id=oauthtest&redirect_uri=${redirectUri}`;
    
    // 跳转到认证页面
    window.location.href = authUrl;
  } catch (error) {
    console.error('统一认证登录跳转失败', error);
    ElMessage.error('统一认证登录跳转失败');
    ssoLoading.value = false;
  }
}

/** 解析 redirect 字符串 为 path 和  queryParams */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

/** 主题切换 */
const toggleTheme = () => {
  const newTheme =
    settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
  settingsStore.changeTheme(newTheme);
};

/** 根据屏幕宽度切换设备模式 */
watchEffect(() => {
  if (height.value < 600) {
    icpVisible.value = false;
  } else {
    icpVisible.value = true;
  }
});

/** 检查输入大小写 */
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}

onMounted(() => {
  getCaptcha();
  
  // 保存重定向地址
  const { redirect } = route.query;
  if (redirect) {
    sessionStorage.setItem('redirect', redirect as string);
  }
});
</script>

<style lang="scss" scoped>
.cert-link {
  margin-right: 10px;
  opacity: 0.7;
  transition: opacity 0.2s;
  font-size: 16px;
  
  &:hover {
    opacity: 1;
  }
}

.login-type-selector {
  display: flex;
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
  
  .login-type-item {
    flex: 1;
    padding: 12px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-regular);
    
    &:not(:last-child) {
      border-right: 1px solid var(--el-border-color-light);
    }
    
    &:hover {
      background-color: var(--el-fill-color);
    }
    
    &.active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      font-weight: 500;
    }
    
    .login-type-icon {
      margin-right: 8px;
    }
  }
}

.sso-login-container {
  padding: 20px 0;
  
  .sso-info {
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    
    .sso-info-icon {
      font-size: 24px;
      color: var(--el-color-primary);
      margin-right: 10px;
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
    }
  }
  
  .sso-button {
    margin-top: 10px;
    margin-bottom: 15px;
    height: 48px;
    font-size: 16px;
  }
  
  .sso-help {
    text-align: center;
    margin-top: 15px;
    
    p {
      color: var(--el-text-color-secondary);
    }
  }
}
</style>
