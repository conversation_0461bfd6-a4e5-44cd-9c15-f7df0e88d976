<template>
  <div class="personal-announcements">
    <el-card class="announcement-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h2>我的公告</h2>
          <el-input
            v-model="searchQuery"
            placeholder="搜索公告"
            prefix-icon="el-icon-search"
            @input="handleSearch"
            class="search-input"
          />
        </div>
      </template>
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="custom-tabs">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="未读" name="unread"></el-tab-pane>
        <el-tab-pane label="已读" name="read"></el-tab-pane>
      </el-tabs>
      <el-timeline>
        <el-timeline-item
          v-for="announcement in filteredAnnouncements"
          :key="announcement.id"
          :timestamp="announcement.createTime"
          :type="getTimelineItemType(announcement)"
          class="timeline-item"
        >
          <el-card class="announcement-item" shadow="hover">
            <template #header>
              <div class="item-header">
                <h3>{{ announcement.title }}</h3>
                <div class="tag-container">
                  <el-tag :type="getTagType(announcement)" class="status-tag">
                    {{ announcement.isRead == 1 ? '已读' : '未读' }}
                  </el-tag>
                  <el-tag :type="announcement.type == 0 ? 'info' : 'warning'" class="type-tag">
                    {{ announcement.type == 0 ? '消息' : '通知' }}
                  </el-tag>
                </div>
              </div>
            </template>
            <p class="announcement-content" v-html="truncateContent(announcement.content)"></p>
            <div class="item-footer">
              <el-button type="primary" text @click="showAnnouncementDetails(announcement)">
                查看详情
              </el-button>
              <el-button v-if="announcement.isRead == 0" type="success" text @click="markAsRead(announcement)">
                标记为已读
              </el-button>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="selectedAnnouncement?.title"
      width="70%"
      :before-close="handleCloseDialog"
      class="announcement-dialog"
    >
    <div class="dialog-header">
      <el-tag :type="selectedAnnouncement?.type == 0 ? 'info' : 'warning'" class="type-tag">
        {{ selectedAnnouncement?.type == 0 ? '消息' : '通知' }}
      </el-tag>
    </div>
      <div v-html="selectedAnnouncement?.content" class="dialog-content"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseDialog">关闭</el-button>
          <el-button v-if="selectedAnnouncement?.isRead == 0" type="primary" @click="markAsReadAndClose">
            标记为已读并关闭
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>


<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import messagesAPI, { messagesPageVO } from "@/enums/MessageTypeEnum";

const activeTab = ref('all');
const searchQuery = ref('');
const announcements = ref<messagesPageVO[]>([]);
const dialogVisible = ref(false);
const selectedAnnouncement = ref<messagesPageVO | null>(null);

const filteredAnnouncements = computed(() => {
  let filtered = announcements.value;
  
  if (activeTab.value === 'unread') {
    filtered = filtered.filter(a => a.isRead == 0);
  } else if (activeTab.value === 'read') {
    filtered = filtered.filter(a => a.isRead == 1);
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(a => 
      a.title.toLowerCase().includes(query) || 
      a.content.toLowerCase().includes(query)
    );
  }
  
  return filtered;
});

const handleSearch = () => {
  // 搜索逻辑已经在 computed 属性中实现
};

const handleTabClick = () => {
  // Tab 切换逻辑已经在 computed 属性中实现
};

const getTimelineItemType = (announcement: messagesPageVO) => {
  return announcement.isRead == 1 ? 'primary' : 'warning';
};

const getTagType = (announcement: messagesPageVO) => {
  return announcement.isRead == 1 ? 'success' : 'warning';
};

const truncateContent = (content: string) => {
  return content.length > 100 ? content.slice(0, 100) + '...' : content;
};

const showAnnouncementDetails = (announcement: messagesPageVO) => {
  selectedAnnouncement.value = announcement;
  dialogVisible.value = true;
};

const handleCloseDialog = () => {
  dialogVisible.value = false;
  selectedAnnouncement.value = null;
};

const markAsRead = async (announcement: messagesPageVO) => {
  try {
    await messagesAPI.updateMessageRead(announcement.id);
    announcement.isRead = 1;
    ElMessage.success('已标记为已读');
  } catch (error) {
    ElMessage.error('操作失败，请重试');
  }
};

const markAsReadAndClose = async () => {
  if (selectedAnnouncement.value) {
    await markAsRead(selectedAnnouncement.value);
    handleCloseDialog();
  }
};

const fetchAnnouncements = async () => {
  try {
    const response = await messagesAPI.getPersonMessageList();
    announcements.value = response;
  } catch (error) {
    ElMessage.error('获取公告失败，请重试');
  }
};

onMounted(() => {
  fetchAnnouncements();
});
</script>
<style scoped>
.personal-announcements {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.announcement-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-input {
  width: 250px;
}

.custom-tabs :deep(.el-tabs__nav) {
  border-radius: 4px;
  padding: 5px;
}

.custom-tabs :deep(.el-tabs__item) {
  padding: 0 20px;
}

.custom-tabs :deep(.el-tabs__item.is-active) {
  border-radius: 4px;
}

.timeline-item {
  padding-bottom: 30px;
}

.announcement-item {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.announcement-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.status-tag {
  font-size: 12px;
  padding: 2px 10px;
}

.status-tag, .type-tag {
  font-size: 12px;
  padding: 2px 10px;
}

.announcement-content {
  margin-bottom: 15px;
  color: #606266;
  line-height: 1.6;
}

.item-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.announcement-dialog :deep(.el-dialog__header) {
  padding: 20px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.announcement-dialog :deep(.el-dialog__body) {
  padding: 30px;
}

.dialog-content {
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.8;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dialog-header {
  margin-bottom: 20px;
}

.tag-container {
  display: flex;
  gap: 10px;
}

</style>
