<template>
  <el-dialog
    v-model="props.visible"
    :title="title"
    width="65%"
    :before-close="handleClose"
  >
    <el-tabs type="border-card">
      <!-- 网络设备基本信息 -->
      <el-tab-pane label="基本信息">
        <el-form
          ref="basicFormRef"
          :model="form.basic"
          :rules="rules.basic"
          label-width="150px"
          class="device-form"
        >
          <!-- 基础信息分组 -->
          <div class="form-section">
            <div class="section-title">基础信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="网络设备名称" prop="deviceName">
                  <el-input
                    v-model="form.basic.deviceName"
                    placeholder="xxx网络设备"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统名称及版本" prop="osVersion">
                  <el-input
                    v-model="form.basic.osVersion"
                    placeholder="例如RouterOS 6.0"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作系统（探测）">
                  <el-input
                    v-model="form.basic.detectedOs"
                    placeholder="系统自动填充"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备厂家" prop="manufacturer">
                  <el-input
                    v-model="form.basic.manufacturer"
                    placeholder="请输入设备厂家"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="网络设备型号" prop="model">
                  <el-input
                    v-model="form.basic.model"
                    placeholder="请输入设备型号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="网络设备描述" prop="description">
                  <el-input
                    v-model="form.basic.description"
                    placeholder="请输入设备描述"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 网络配置分组 -->
          <div class="form-section">
            <div class="section-title">ip地址</div>
            <div
              v-for="(ipConfig, index) in form.basic.ipConfigs"
              :key="ipConfig.id"
              class="ip-config-section"
            >
              <div class="ip-header">
                <span class="ip-title">IP配置 #{{ index + 1 }}</span>
                <el-button
                  v-if="form.basic.ipConfigs.length > 1"
                  type="danger"
                  link
                  @click="removeIpConfig(index)"
                >
                  删除
                </el-button>
              </div>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    :label="'ip地址标识'"
                    :prop="`ipConfigs.${index}.flag`"
                  >
                    <el-input
                      v-model="ipConfig.flag"
                      placeholder="请输入五个字以内IP地址标注"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'ip地址'"
                    :prop="`ipConfigs.${index}.ip`"
                    :rules="[
                      {
                        required: true,
                        message: '请输入IP地址',
                        trigger: 'blur',
                      },
                      {
                        pattern:
                          /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                        message: '请输入正确的IP地址',
                        trigger: 'blur',
                      },
                    ]"
                  >
                    <el-input
                      v-model="ipConfig.ip"
                      placeholder="请输入ip地址"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 端口及服务列表 -->
              <el-card shadow="none">
                <div class="header">
                  <span>端口及服务列表</span>
                  <el-button
                    type="primary"
                    @click="openPortDialog(index)"
                    style="float: right"
                  >
                    新增端口及服务
                  </el-button>
                </div>

                <el-table
                  :data="ipConfig.ports"
                  border
                  style="margin-top: 16px"
                >
                  <el-table-column
                    prop="loginMethod"
                    label="录入方式"
                    width="100px"
                  />
                  <el-table-column
                    prop="loginTime"
                    label="录入时间"
                    width="200px"
                  />
                  <el-table-column
                    prop="updateTime"
                    label="更新时间"
                    width="200px"
                  />
                  <el-table-column
                    prop="description"
                    label="端口描述"
                    width="150px"
                  />
                  <el-table-column prop="port" label="端口" width="80px" />
                  <el-table-column prop="service" label="协议" width="110px" />
                  <el-table-column
                    prop="createTime"
                    label="探测时间"
                    width="200px"
                  />
                  <el-table-column prop="status" label="探测状态" width="150px">
                    <template #default="scope">
                      <el-tag type="success">{{ scope.row.status }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" fixed="right">
                    <template #default="scope">
                      <el-button
                        type="primary"
                        text
                        @click="handleEditPort(index, scope.$index)"
                        size="small"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="danger"
                        text
                        @click="handleRemovePort(index, scope.$index)"
                        size="small"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </div>

            <!-- 添加IP配置按钮 -->
            <el-button type="primary" @click="addIpConfig" class="add-ip-btn">
              <el-icon>
                <Plus />
              </el-icon>
              添加IP配置
            </el-button>
          </div>
          <!-- 其他信息分组 -->
          <div class="form-section">
            <div class="section-title">其他信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间" prop="createTime">
                  <el-input
                    v-model="form.basic.createTime"
                    placeholder="自动填充"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="下线时间" prop="offTime">
                  <el-date-picker
                    v-model="form.basic.offTime"
                    type="datetime"
                    placeholder="选择时间"
                    value-format="YYYY/MM/日"
                    class="w-full"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="更新时间" prop="updateTime">
                  <el-input
                    v-model="form.basic.updateTime"
                    placeholder="自动填充"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上线时间" prop="onTime">
                  <el-date-picker
                    v-model="form.basic.onTime"
                    type="datetime"
                    placeholder="选择时间"
                    value-format="YYYY/MM/日"
                    class="w-full"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上线负责人">
                  <el-input
                    v-model="form.basic.OwnerName"
                    placeholder="请输入负责人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式" prop="ownerPhone">
                  <el-input
                    v-model="form.basic.manufacturer"
                    placeholder="请输入负责人联系方式"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 设备位置信息 -->
          <div class="form-section">
            <div class="section-title">位置信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所在网络区域" prop="networkArea">
                  <el-input
                    v-model="form.basic.networkArea"
                    placeholder="核心区/汇聚区/接入区"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="机房名称" prop="location">
                  <el-input
                    v-model="form.basic.location"
                    placeholder="xxx机房"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="机柜编号及机柜u位" prop="machineBoxId">
                  <el-input v-model="form.basic.machineBoxId" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 管理单位基本信息 -->
      <el-tab-pane label="管理单位">
        <el-form
          ref="managementFormRef"
          :model="form.management"
          :rules="rules.management"
          label-width="150px"
          class="server-form"
        >
          <!-- 基本信息分组 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="管理单位" prop="deptId">
                  <el-tree-select
                    v-model="form.management.deptId"
                    placeholder="请选择所属部门"
                    :data="deptOptions"
                    filterable
                    check-strictly
                    :render-after-expand="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="办公地址" prop="officeAddress">
                  <el-input
                    v-model="form.management.officeAddress"
                    placeholder="填写办公地址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 领导信息部分 -->
          <div class="form-section">
            <div class="section-title">管理领导信息</div>

            <el-form-item label="选择管理领导" prop="manager">
              <el-button
                type="primary"
                @click="openSelectLeader"
                :disabled="!form.management.deptId"
              >
                <el-icon>
                  <User />
                </el-icon>
                选择部门领导
              </el-button>
            </el-form-item>

            <!-- 显示已选管理领导信息 -->
            <template v-if="peopleDialog.selectedLeaderId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="领导姓名" :span="2">
                    {{ form.management.manager || "加载中..." }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.officePhone || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.mobile || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.email || "未填写" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>

          <!-- 系统管理员信息部分 -->
          <div class="form-section">
            <div class="section-title">系统管理员信息</div>

            <el-form-item label="选择系统管理员" prop="sysAdmin">
              <el-button
                type="primary"
                @click="openSelectAdmin"
                :disabled="!form.management.deptId"
              >
                <el-icon>
                  <User />
                </el-icon>
                选择系统管理员
              </el-button>
            </el-form-item>

            <!-- 显示已选系统管理员信息 -->
            <template v-if="peopleDialog.selectedAdminId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="管理员姓名" :span="2">
                    {{ form.management.sysAdmin || "加载中..." }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.adminOfficePhone || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.adminMobile || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.adminEmail || "未填写" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 运维单位部分 -->
      <el-tab-pane label="运维单位">
        <el-form
          ref="operationsFormRef"
          :model="form.operations"
          :rules="rules.operations"
          label-width="150px"
          class="server-form"
        >
          <div class="form-section">
            <div class="section-title">
              <el-icon>
                <Office />
              </el-icon>
              运维服务商信息
            </div>

            <!-- 服务商选择组件 -->
            <el-form-item label="选择服务商" prop="providerId">
              <div class="service-provider-select">
                <select-service-provider
                  v-model="form.operations.providerId"
                  @provider-selected="handleOperationsProviderSelected"
                />
              </div>
            </el-form-item>

            <!-- 显示已选服务商信息 -->
            <template v-if="form.operations.providerDetail">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="公司名称" :span="2">
                    {{ form.operations.providerDetail.providerName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="项目负责人">
                    {{ form.operations.providerDetail.projectManager }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话">
                    {{ form.operations.providerDetail.managerMobile }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.operations.providerDetail.managerEmail }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术负责人">
                    {{ form.operations.providerDetail.techLeader }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术电话">
                    {{ form.operations.providerDetail.techMobile }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术邮箱" :span="2">
                    {{ form.operations.providerDetail.techEmail }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <!-- 人员选择组件 -->
    <select-people
      v-model:visible="peopleDialog.leaderVisible"
      :title="'选择管理领导'"
      :department-id="form.management.deptId"
      :selected-user-id="peopleDialog.selectedLeaderId"
      @selected="handleLeaderSelected"
    />

    <select-people
      v-model:visible="peopleDialog.adminVisible"
      :title="'选择系统管理员'"
      :department-id="form.management.deptId"
      :selected-user-id="peopleDialog.selectedAdminId"
      @selected="handleAdminSelected"
    />

    <!-- 端口及服务弹窗 -->
    <el-dialog
      :title="currentPortIndex === -1 ? '新增端口及服务' : '编辑端口及服务'"
      v-model="portDialogVisible"
      width="400px"
    >
      <el-form
        :model="currentPort"
        label-width="80px"
        ref="portFormRef"
        :rules="portRules"
      >
        <el-form-item label="录入方式" prop="loginMethod">
          <el-input
            v-model="currentPort.loginMethod"
            placeholder="手动录入"
            disabled
          />
        </el-form-item>
        <el-form-item label="录入时间" prop="loginTime" disabled>
          <el-date-picker
            v-model="currentPort.loginTime"
            type="datetime"
            placeholder="选择录入时间"
            class="w-full"
            disabled
          />
        </el-form-item>
        <el-form-item label="端口描述" prop="description">
          <el-input v-model="currentPort.description" placeholder="5个字以内" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input
            v-model="currentPort.port"
            type="number"
            placeholder="请输入端口号"
          />
        </el-form-item>
        <el-form-item label="协议" prop="service">
          <el-input v-model="currentPort.service" placeholder="只能是字母" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="portDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePortSave">保存</el-button>
      </template>
    </el-dialog>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">已填写完成</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance } from "element-plus";
import DeptAPI from "@/api/dept";
import assetsAPI from "@/api/assets_management/details/assets";
import UserAPI from "@/api/user";
import SelectServiceProvider from "@/components/AssetsManage/SelectServiceProvider.vue";
import SelectPeople from "@/components/AssetsManage/SelectPeople.vue";
import ProviderAPI from "@/api/work_management/serviceProvider/index";
import { formatLocalDateTime } from "@/utils/dateUtils";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增网络设备",
  },
  id: {
    type: [Number, String],
    default: "",
  },
});

const emit = defineEmits(["update:visible", "submitted"]);

// 表单引用
const basicFormRef = ref<FormInstance>();
const managementFormRef = ref<FormInstance>();
const operationsFormRef = ref<FormInstance>();
const portFormRef = ref<FormInstance>();

// 部门选项
const deptOptions = ref<any[]>([]);

// 人员选择相关的响应式数据
const peopleDialog = reactive({
  leaderVisible: false,
  adminVisible: false,
  selectedLeaderId: null,
  selectedAdminId: null,
});

// 端口服务弹窗状态
const portDialogVisible = ref(false);
const currentIpIndex = ref(-1);
const currentPortIndex = ref(-1);

// 端口表单数据
const currentPort = ref<{
  loginMethod: string;
  loginTime: string | Date | null;
  description: string;
  port: string;
  service: string;
  createTime: string;
  status: string;
}>({
  loginMethod: "",
  loginTime: new Date(),
  description: "",
  port: "",
  service: "",
  createTime: "",
  status: "存活",
});

// 端口表单验证规则
const portRules = reactive({
  loginMethod: [{ required: true, message: "请输入录入方式", trigger: "blur" }],
  loginTime: [{ required: true, message: "请选择录入时间", trigger: "change" }],
  description: [
    { required: true, message: "请输入端口描述", trigger: "blur" },
    { max: 5, message: "端口描述不能超过5个字符", trigger: "blur" },
  ],
  port: [
    { required: true, message: "请输入端口号", trigger: "blur" },
    {
      pattern: /^[0-9]+$/,
      message: "端口号必须是数字",
      trigger: "blur",
    },
    {
      validator: (rule: any, value: string, callback: any) => {
        const port = parseInt(value, 10);
        if (port < 1 || port > 65535) {
          callback(new Error("端口号必须在1-65535之间"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  service: [
    { required: true, message: "请输入协议名称", trigger: "blur" },
    {
      pattern: /^[A-Za-z]+$/,
      message: "协议名称只能包含字母",
      trigger: "blur",
    },
  ],
});

// 初始表单数据
const initialForm = {
  basic: {
    deviceName: "",
    osVersion: "",
    detectedOs: "", // 操作系统（探测）
    manufacturer: "",
    model: "",
    description: "", // 设备描述
    assetRemark: "",
    assetStatus: "1", // 默认正常状态
    plannedOfflineTime: null,
    ipConfigs: [
      {
        id: 1,
        flag: "", // IP地址标识
        ip: "",
        port: "",
        protocol: "",
        usage: "",
        ports: [] as any[], // 端口及服务列表
      },
    ],
    registerTime: new Date(),
    networkArea: "",
    location: "",
    type: 2, // 网络设备类型
  },
  management: {
    deptId: undefined,
    officeAddress: "",
    manager: "",
    officePhone: "",
    mobile: "",
    email: "",
    sysAdmin: "",
    adminOfficePhone: "",
    adminMobile: "",
    adminEmail: "",
  },
  operations: {
    providerId: undefined,
    providerDetail: null,
  },
};

// 表单数据
const form = reactive(JSON.parse(JSON.stringify(initialForm)));

// 处理服务商选择
const handleOperationsProviderSelected = (provider: any) => {
  console.log("选择的服务提供商:", provider);
  form.operations.providerId = provider.id;
  form.operations.providerDetail = provider;
};

// 表单验证规则
const rules = reactive({
  basic: {
    deviceName: [
      { required: true, message: "请输入网络设备名称", trigger: "blur" },
      {
        min: 2,
        max: 50,
        message: "设备名称长度应为2-50个字符",
        trigger: "blur",
      },
    ],
    osVersion: [
      { required: false, message: "请输入系统名称及版本", trigger: "blur" },
    ],
    manufacturer: [
      { required: false, message: "请输入设备厂家", trigger: "blur" },
    ],
    model: [
      { required: false, message: "请输入网络设备型号", trigger: "blur" },
    ],
    registerTime: [
      { required: true, message: "请选择登记时间", trigger: "change" },
    ],
    assetStatus: [
      { required: false, message: "请选择资产状态", trigger: "change" },
    ],
    networkArea: [
      { required: false, message: "请输入网络区域", trigger: "blur" },
    ],
    location: [{ required: false, message: "请输入物理位置", trigger: "blur" }],
  },
  management: {
    deptId: [{ required: false, message: "请选择管理单位", trigger: "change" }],
    officeAddress: [
      { required: false, message: "请输入办公地址", trigger: "blur" },
    ],
    manager: [{ required: false, message: "请选择管理领导", trigger: "blur" }],
    mobile: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    email: [
      { required: false, message: "请输入邮箱", trigger: "blur" },
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
    sysAdmin: [
      { required: false, message: "请选择系统管理员", trigger: "blur" },
    ],
    adminMobile: [
      { required: false, message: "请输入管理员手机号码", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    adminEmail: [
      { required: false, message: "请输入管理员邮箱", trigger: "blur" },
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
  },
  operations: {
    providerId: [
      { required: false, message: "请选择服务商", trigger: "change" },
    ],
  },
});

// 打开选择领导的弹窗
const openSelectLeader = () => {
  // 确保已经选择了部门
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.leaderVisible = true;
};

// 打开选择管理员的弹窗
const openSelectAdmin = () => {
  // 确保已经选择了部门
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.adminVisible = true;
};

// 处理管理领导选择
const handleLeaderSelected = (user: any) => {
  console.log("选择的领导:", user);
  peopleDialog.selectedLeaderId = user.id;
  form.management.manager = user.nickname || user.username;
  form.management.mobile = user.mobile || "";
  form.management.email = user.email || "";
  form.management.officePhone = user.officePhone || "";
};

// 系统管理员选择处理函数
const handleAdminSelected = (user: any) => {
  console.log("选择的管理员:", user);
  peopleDialog.selectedAdminId = user.id;
  form.management.sysAdmin = user.nickname || user.username;
  form.management.adminMobile = user.mobile || "";
  form.management.adminEmail = user.email || "";
  form.management.adminOfficePhone = user.officePhone || "";
};

// 加载用户信息函数
async function loadUserIfNeeded(
  type: string,
  userId: any,
  existingName: string
) {
  if (!userId) return;

  // 如果已经有名称，则不需要再加载
  if (existingName) return;

  try {
    console.log(`加载${type}用户信息:`, userId);
    const userInfo = await UserAPI.getFormData(userId);

    // 根据类型更新不同的字段
    if (type === "leader") {
      form.management.manager = userInfo.nickname || userInfo.username || "";
      form.management.officePhone =
        userInfo.mobile || form.management.officePhone;
      form.management.mobile = userInfo.mobile || form.management.mobile;
      form.management.email = userInfo.email || form.management.email;
    } else if (type === "admin") {
      form.management.sysAdmin = userInfo.nickname || userInfo.username || "";
      form.management.adminOfficePhone =
        userInfo.mobile || form.management.adminOfficePhone;
      form.management.adminMobile =
        userInfo.mobile || form.management.adminMobile;
      form.management.adminEmail = userInfo.email || form.management.adminEmail;
    }
  } catch (error) {
    console.error(`加载${type}用户信息失败:`, error);
  }
}

// 从API数据映射到表单结构
function mapApiDataToForm(apiData: any) {
  console.log("API返回的数据:", apiData);

  try {
    // 基础信息
    form.basic.deviceName = apiData.name || "";
    form.basic.osVersion = apiData.os || "";
    form.basic.detectedOs = apiData.detectedOs || ""; // 操作系统（探测）
    form.basic.manufacturer = apiData.factory || "";
    form.basic.model = apiData.model || "";
    form.basic.description = apiData.description || ""; // 设备描述
    form.basic.assetStatus = apiData.status || "1";
    form.basic.assetRemark = apiData.notes || apiData.remark || "";
    form.basic.plannedOfflineTime = apiData.plannedOfflineTime
      ? new Date(apiData.plannedOfflineTime)
      : null;
    form.basic.networkArea = apiData.netArea || "";
    form.basic.location = apiData.local || "";

    // 处理IP配置
    // 清空当前IP配置列表
    form.basic.ipConfigs = [];

    // 优先使用已解析好的addressList
    if (Array.isArray(apiData.addressList) && apiData.addressList.length > 0) {
      form.basic.ipConfigs = apiData.addressList.map(
        (addr: any, index: number) => ({
          id: index + 1,
          flag: addr.flag || "",
          ip: addr.ip || "",
          port: addr.port ? String(addr.port) : "",
          protocol: addr.protocol || "",
          usage: addr.usage || "",
          ports: addr.ports || [], // 端口服务列表
        })
      );
    }
    // 确保至少有一个IP配置
    if (form.basic.ipConfigs.length === 0) {
      form.basic.ipConfigs = [
        {
          id: 1,
          flag: "",
          ip: "",
          port: "",
          protocol: "",
          usage: "",
          ports: [],
        },
      ];
    }
    // 注册时间
    form.basic.registerTime = apiData.createTime
      ? new Date(apiData.createTime)
      : new Date();

    // 管理单位信息
    form.management.deptId = apiData.deptId;
    form.management.officeAddress = apiData.depAddress || "";

    // 管理领导信息
    form.management.manager = apiData.leader || "";
    peopleDialog.selectedLeaderId = apiData.managerId;
    form.management.officePhone = apiData.leaderPhone || "";
    form.management.mobile = apiData.leaderPhone1 || "";
    form.management.email = apiData.leaderEmail || "";

    // 系统管理员信息(资产管理者)
    form.management.sysAdmin = apiData.ownerName || "";
    peopleDialog.selectedAdminId = apiData.ownerId || apiData.sysManagerId;
    form.management.adminOfficePhone = apiData.ownerPhone1 || "";
    form.management.adminMobile = apiData.ownerPhone || "";
    form.management.adminEmail = apiData.ownerEmail || "";

    // 如果有ID但没有名称，加载用户信息
    Promise.all([
      loadUserIfNeeded(
        "leader",
        peopleDialog.selectedLeaderId,
        form.management.manager
      ),
      loadUserIfNeeded(
        "admin",
        peopleDialog.selectedAdminId,
        form.management.sysAdmin
      ),
    ]).catch((error) => {
      console.error("加载用户信息出错:", error);
    });

    // 服务提供商信息
    if (apiData.provider) {
      form.operations.providerId = apiData.provider.id;
      form.operations.providerDetail = apiData.provider;
    } else {
      form.operations.providerId = apiData.providerId;
      if (apiData.providerId) {
        // 获取服务商详情
        fetchProviderDetail(apiData.providerId);
      }
    }

    console.log("成功映射API数据到表单");
  } catch (error) {
    console.error("映射API数据到表单时出错:", error);
    ElMessage.error("表单数据加载异常");
  }
}

// 从表单数据映射到API需要的格式
function mapFormToApiData() {
  console.log("正在映射表单数据到API格式...");

  // 构建要提交的数据对象
  const apiData = {
    // 如果是编辑模式，添加ID
    ...(props.id ? { id: props.id } : {}),

    // 基础信息
    name: form.basic.deviceName,
    os: form.basic.osVersion,
    detectedOs: form.basic.detectedOs,
    systemId: "",
    factory: form.basic.manufacturer,
    model: form.basic.model,
    description: form.basic.description,
    ip: form.basic.ipConfigs[0]?.ip || "",
    port: form.basic.ipConfigs[0]?.port || "",
    url: form.basic.ipConfigs[0]?.protocol
      ? `${form.basic.ipConfigs[0].protocol.toLowerCase()}://${form.basic.ipConfigs[0].ip}`
      : "",

    // 管理信息
    deptId: form.management.deptId,
    depAddress: form.management.officeAddress,

    // 管理员信息(资产管理者)
    ownerName: form.management.sysAdmin,
    ownerId: peopleDialog.selectedAdminId,
    ownerPhone: form.management.adminMobile,
    ownerPhone1: form.management.adminOfficePhone,
    ownerEmail: form.management.adminEmail,
    sysManagerId: peopleDialog.selectedAdminId, // 冗余字段，同ownerId

    // 部门领导信息
    leader: form.management.manager,
    managerId: peopleDialog.selectedLeaderId,
    leaderPhone: form.management.officePhone,
    leaderPhone1: form.management.mobile,
    leaderEmail: form.management.email,

    // 设备信息
    status: form.basic.assetStatus || "1",
    createTime:
      formatLocalDateTime(form.basic.registerTime) ||
      formatLocalDateTime(new Date()),
    plannedOfflineTime: form.basic.plannedOfflineTime
      ? formatLocalDateTime(form.basic.plannedOfflineTime)
      : null,
    netArea: form.basic.networkArea,
    local: form.basic.location,
    notes: form.basic.assetRemark,

    // 类型标识
    type: 2, // 网络设备类型

    // 服务提供商信息
    providerId: form.operations.providerId,
    otherFactory: form.operations.providerDetail?.name,
    otherManager: form.operations.providerDetail?.projectManager,
    otherContact: form.operations.providerDetail?.managerMobile,
  };

  // 处理所有IP配置，将它们转换为统一的格式
  if (form.basic.ipConfigs.length > 0) {
    apiData.address = JSON.stringify(
      form.basic.ipConfigs.map((cfg) => ({
        id: cfg.id,
        flag: cfg.flag || "",
        ip: cfg.ip || "",
        port: cfg.port || "",
        protocol: cfg.protocol || "",
        usage: cfg.usage || "",
        ports: cfg.ports || [], // 端口服务信息
      }))
    );
  }

  console.log("映射后的API数据:", apiData);
  return apiData;
}

// 获取服务商详情
async function fetchProviderDetail(providerId: any) {
  try {
    const detail = await ProviderAPI.getFormData(providerId);
    form.operations.providerDetail = detail;
  } catch (error) {
    console.error("获取服务商详情失败:", error);
  }
}

// IP配置相关方法
const addIpConfig = () => {
  const newId =
    form.basic.ipConfigs.length > 0
      ? Math.max(...form.basic.ipConfigs.map((ip) => ip.id)) + 1
      : 1;
  form.basic.ipConfigs.push({
    id: newId,
    flag: "",
    ip: "",
    port: "",
    protocol: "",
    usage: "",
    ports: [],
  });
};

const removeIpConfig = (index: number) => {
  if (form.basic.ipConfigs.length > 1) {
    form.basic.ipConfigs.splice(index, 1);
  } else {
    ElMessage.warning("至少保留一个IP配置");
  }
};

// 端口服务相关方法
const openPortDialog = (ipIndex: number, portIndex: number = -1) => {
  currentIpIndex.value = ipIndex;

  // 如果是编辑模式
  if (portIndex !== -1) {
    currentPortIndex.value = portIndex;
    // 深拷贝选中的端口数据，避免直接修改表格数据
    const portData = form.basic.ipConfigs[ipIndex].ports[portIndex];
    currentPort.value = { ...portData };
    // 转换日期格式
    if (
      currentPort.value.loginTime &&
      typeof currentPort.value.loginTime === "string"
    ) {
      currentPort.value.loginTime = new Date(currentPort.value.loginTime);
    }
  } else {
    // 新增模式
    currentPortIndex.value = -1;
    currentPort.value = {
      loginMethod: "手动录入",
      loginTime: new Date(),
      description: "",
      port: "",
      service: "",
      createTime: "",
      status: "存活",
    };
  }

  portDialogVisible.value = true;
};

const handlePortSave = async () => {
  if (!portFormRef.value) return;

  try {
    // 验证表单
    await portFormRef.value.validate();

    // 处理时间格式
    const portData = {
      ...currentPort.value,
      createTime: new Date().toISOString().slice(0, 19).replace("T", " "),
      // 确保时间是字符串格式
      loginTime: currentPort.value.loginTime
        ? formatLocalDateTime(currentPort.value.loginTime)
        : "",
    };

    // 添加或更新端口数据
    if (currentPortIndex.value === -1) {
      // 新增
      form.basic.ipConfigs[currentIpIndex.value].ports.push(portData);
      ElMessage.success("端口信息添加成功");
    } else {
      // 更新
      form.basic.ipConfigs[currentIpIndex.value].ports[currentPortIndex.value] =
        portData;
      ElMessage.success("端口信息更新成功");
    }

    portDialogVisible.value = false;
  } catch (error) {
    console.error("保存端口信息失败:", error);
    return false;
  }
};

// 编辑端口
const handleEditPort = (ipIndex: number, portIndex: number) => {
  // 验证索引有效性
  if (
    ipIndex < 0 ||
    ipIndex >= form.basic.ipConfigs.length ||
    portIndex < 0 ||
    portIndex >= form.basic.ipConfigs[ipIndex].ports.length
  ) {
    ElMessage.error("无效的端口数据");
    return;
  }

  openPortDialog(ipIndex, portIndex);
};

// 删除端口
const handleRemovePort = (ipIndex: number, portIndex: number) => {
  // 验证索引有效性
  if (
    ipIndex < 0 ||
    ipIndex >= form.basic.ipConfigs.length ||
    portIndex < 0 ||
    portIndex >= form.basic.ipConfigs[ipIndex].ports.length
  ) {
    ElMessage.error("无效的端口数据");
    return;
  }

  const port = form.basic.ipConfigs[ipIndex].ports[portIndex];
  const portInfo = `端口 ${port.port} (${port.service})`;

  ElMessageBox.confirm(
    `确定要删除${portInfo}吗？此操作不可恢复。`,
    "删除确认",
    {
      confirmButtonText: "确认删除",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      form.basic.ipConfigs[ipIndex].ports.splice(portIndex, 1);
      ElMessage.success(`${portInfo}已删除`);
    })
    .catch(() => {
      // 取消删除，不做任何操作
    });
};

// 重置表单方法
const resetForm = () => {
  const forms = [
    basicFormRef,
    managementFormRef,
    operationsFormRef,
    portFormRef,
  ];
  forms.forEach((formRef) => {
    if (formRef.value) {
      formRef.value.resetFields();
      formRef.value.clearValidate();
    }
  });

  Object.assign(form, JSON.parse(JSON.stringify(initialForm)));
  // 重置人员选择状态
  peopleDialog.selectedLeaderId = null;
  peopleDialog.selectedAdminId = null;
};

// 关闭弹窗前的回调
const handleClose = () => {
  ElMessageBox.confirm("确认关闭？未保存的数据将丢失")
    .then(() => {
      handleCancel();
    })
    .catch(() => {});
};

// 取消
const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

// 提交表单方法
const submitForm = async () => {
  const validateForms = [
    basicFormRef.value,
    managementFormRef.value,
    operationsFormRef.value,
  ].filter(Boolean);

  try {
    // 验证所有表单
    await Promise.all(validateForms.map((form) => form?.validate()));

    // 检查人员ID是否已选择
    if (!peopleDialog.selectedLeaderId && form.management.manager) {
      ElMessage.warning('请通过"选择部门领导"按钮选择有效的管理领导');
      return false;
    }

    if (!peopleDialog.selectedAdminId && form.management.sysAdmin) {
      ElMessage.warning('请通过"选择系统管理员"按钮选择有效的系统管理员');
      return false;
    }

    // 将表单数据映射到API需要的格式
    const apiData = mapFormToApiData();

    if (props.id) {
      await assetsAPI.update(props.id, apiData);
      ElMessage.success("修改成功");
    } else {
      await assetsAPI.add(apiData);
      ElMessage.success("新增成功");
    }

    emit("submitted");
    handleCancel();
  } catch (error) {
    console.error("提交表单出错:", error);
    ElMessage.error("请完善表单信息");
    return false;
  }
};

// 监听ID变化加载表单数据
watch(
  () => [props.visible, props.id],
  async ([visible, id]) => {
    if (visible && id) {
      try {
        const data = await assetsAPI.getServerDetail(id);
        mapApiDataToForm(data);
      } catch (error) {
        console.error("加载数据失败:", error);
        ElMessage.error("加载数据失败");
      }
    } else if (!visible) {
      // 当弹窗关闭时重置表单
      resetForm();
    } else if (visible && !id) {
      // 新增模式下重置表单
      resetForm();
    }
  },
  { immediate: true }
);

// 获取部门选项
onMounted(() => {
  DeptAPI.getOptions()
    .then((data) => {
      deptOptions.value = data;
    })
    .catch((error) => {
      console.error("加载部门数据失败:", error);
      ElMessage.error("加载部门数据失败");
    });
});
</script>

<style scoped>
/* 基础组件样式 */
.service-provider-select {
  display: inline-block;
  position: relative;
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.selected-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 弹窗层级控制 */
:deep(.el-dialog__wrapper) {
  position: fixed !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 3000 !important;
}

:deep(.el-overlay) {
  position: fixed !important;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2999 !important;
  height: 100%;
  background-color: var(--el-overlay-color-lighter);
}

.provider-dialog {
  :deep(.el-dialog) {
    position: relative;
    margin: 15vh auto 50px !important;
    z-index: 3001 !important;
  }
}

/* 弹窗内容样式 */
.dialog-content {
  display: flex;
  height: 600px;
  background-color: var(--el-bg-color-page);
  position: relative;
  z-index: 3002;
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
  position: relative;
}

/* 左侧列表样式 */
.provider-list {
  width: 300px;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.header-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.list-content {
  padding: 12px;
}

/* 服务商列表项样式 */
.provider-item {
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.provider-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

.provider-item.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.provider-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.item-sub {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 右侧内容区样式 */
.provider-detail {
  flex: 1;
  padding: 20px;
  background-color: var(--el-bg-color);
  overflow-y: auto;
}

/* 弹窗基础样式 */
:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-dialog__footer) {
  margin: 0;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 表单样式 */
.provider-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* 详情展示样式 */
.detail-content {
  max-width: 800px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.detail-header h3 {
  margin: 0;
  font-size: 20px;
  color: var(--el-text-color-primary);
}

.info-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item .label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-item .value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.empty-tip {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 动画效果 */
.dialog-enter-active,
.dialog-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-enter-from,
.dialog-leave-to {
  opacity: 0;
}

/* IP配置区域样式 */
.ip-config-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
}

.ip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--el-border-color);
}

.ip-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.add-ip-btn {
  margin-top: 10px;
}

/* 端口服务表格样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
