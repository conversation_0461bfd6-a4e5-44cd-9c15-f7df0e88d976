<template>
  <div>{{ getLabelByValue(props.code, props.modelValue) }}</div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue';
import { useDictStore } from '@/store/modules/dictStoreFunc';

const props = defineProps({
  code: {
    type: String,
    required: true,
  },
  modelValue: {
    type: [String, Number],
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const dictStore = useDictStore();
const options = ref<{ label: string, value: string | number, children?: any[] }[]>([]);

onBeforeMount(async () => {
  console.log(`Fetching options for code: ${props.code}`);
  options.value = await dictStore.fetchOptions(props.code);  // 确保调用的是 fetchOptions 方法
  console.log(`Options fetched:`, options.value);
});

function getLabelByValue(code: string, value: string | number | undefined): string {
  function findLabel(options: any[], value: string | number): string | undefined {
    for (const option of options) {
      if (option.value == value) {
        return option.label;
      }
      if (option.children) {
        const childLabel = findLabel(option.children, value);
        if (childLabel) {
          return childLabel;
        }
      }
    }
    return undefined;
  }

  const label = value !== undefined ? findLabel(options.value, value) : undefined;
  return label || '';
}
</script>
