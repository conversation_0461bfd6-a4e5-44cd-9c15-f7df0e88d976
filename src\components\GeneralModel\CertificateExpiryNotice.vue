<template>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ElNotification, NotificationHandle } from 'element-plus';
import AuthAPI from '@/api/auth';
import { differenceInDays, format, isExpiring, isValidDate } from '@/utils/dateUtils';
import { useUserStore } from '@/store'; // 导入用户状态管理

// 通知显示状态控制
const noticeShown = ref(false);
// 维护全局通知实例引用，确保只有一个通知显示
let notificationInstance: NotificationHandle | null = null;
const userStore = useUserStore();

// 获取当前用户ID (用于区分不同用户的提醒状态)
const getUserId = () => {
  return userStore.user?.userId || userStore.user?.username || 'anonymous';
};

// 生成用户特定的存储键
const getUserSpecificKey = () => {
  const userId = getUserId();
  return `certificate_notice_closed_date_${userId}`;
};

// 检查证书到期情况
const checkCertificateExpiry = async () => {
  // 如果已经显示通知，则不再重复显示
  if (noticeShown.value) return;
  
  // 如果没有用户信息或不是登录状态，则不显示提醒
  if (!userStore.user || Object.keys(userStore.user).length === 0) {
    return;
  }
  
  // 获取用户特定的存储键
  const userSpecificKey = getUserSpecificKey();
  
  // 检查今天该用户是否已经关闭过提醒
  const today = format(new Date(), 'yyyy-MM-dd');
  const lastClosedDate = localStorage.getItem(userSpecificKey);
  
  // 如果今天已经关闭过提醒，则不再显示
  if (lastClosedDate === today) {
    return;
  }
  
  try {
    const certificateInfo = await AuthAPI.getCertificate();
    
    if (!certificateInfo || !certificateInfo.validTo || !isValidDate(certificateInfo.validTo)) {
      return;
    }
    
    // 使用dateUtils计算剩余天数
    const remainingDays = differenceInDays(new Date(certificateInfo.validTo), new Date());
    
    // 使用isExpiring函数检查是否在30天内过期
    const expiring = isExpiring(certificateInfo.validTo);
    
    // 如果在30天内过期且未显示通知
    if (expiring && remainingDays > 0 && !noticeShown.value) {
      const expiryDateFormatted = format(certificateInfo.validTo, 'yyyy 年 MM 月 dd 日');
      
      // 先关闭可能存在的旧通知
      if (notificationInstance) {
        notificationInstance.close();
      }

      // 创建通知内容HTML - 更加简洁美观的设计
      const message = `
        <div class="cert-notice-content">
          <div class="cert-notice-days-container">
            <div class="cert-notice-days">
              <span class="cert-days-number">${remainingDays}</span>
              <span class="cert-days-text">天</span>
            </div>
          </div>
          <div class="cert-notice-divider"></div>
          <div class="cert-notice-message">
            <p>您好！您所使用的网络安全工作管理平台将于<span class="cert-date">${expiryDateFormatted}</span>到期，目前剩余有效期不足一个月。为保障您的正常使用，建议您尽快与我公司销售人员联系续费事宜，感谢您的支持与配合！</p>
          </div>
        </div>
      `;
      
      // 创建新通知并保存引用
      notificationInstance = ElNotification({
        title: '证书即将到期提醒',
        message,
        type: 'warning',
        position: 'bottom-right',
        duration: 0, // 不自动关闭
        showClose: true,
        dangerouslyUseHTMLString: true, // 允许使用HTML内容
        customClass: 'certificate-expiry-notice',
        onClose: () => {
          // 使用用户特定的键记录今天已关闭过提醒
          localStorage.setItem(userSpecificKey, today);
          noticeShown.value = false;
          notificationInstance = null;
        }
      });
      
      // 设置通知已显示状态
      noticeShown.value = true;
    }
  } catch (error) {
    console.error('检查证书状态出错:', error);
  }
};

// 监听登录状态变化
watch(
  () => userStore.user, 
  (newUserInfo) => {
    if (newUserInfo && Object.keys(newUserInfo).length > 0) {
      // 只在用户登录时检查一次
      if (!noticeShown.value) {
        checkCertificateExpiry();
      }
    }
  },
  { deep: true, immediate: true }
);

// 确保组件挂载时也执行检查
onMounted(() => {
  // 延迟执行，确保用户信息已加载
  setTimeout(() => {
    if (Object.keys(userStore.user || {}).length > 0 && !noticeShown.value) {
      checkCertificateExpiry();
    }
  }, 2000);
});
</script>

<style>
.certificate-expiry-notice {
  min-width: 360px;
  max-width: 400px;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  background: #fff;
  border: none;
  overflow: hidden;
}

.certificate-expiry-notice .el-notification__title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 6px;
}

.certificate-expiry-notice .el-notification__title::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #ff9900;
  border-radius: 2px;
  margin-right: 6px;
}

.certificate-expiry-notice .el-notification__closeBtn {
  font-size: 18px;
  color: #909399;
  top: 16px;
  right: 16px;
  opacity: 0.8;
  transition: all 0.2s;
}

.certificate-expiry-notice .el-notification__closeBtn:hover {
  opacity: 1;
  color: #606266;
}

.certificate-expiry-notice .el-notification__content {
  padding: 0;
  margin: 0;
}

/* 自定义内容样式 */
.cert-notice-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cert-notice-days-container {
  display: flex;
  justify-content: center;
  margin: 4px 0 8px;
}

.cert-notice-days {
  background: rgba(255, 153, 0, 0.1);
  border-radius: 30px;
  padding: 8px 22px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.cert-days-number {
  font-size: 18px;
  font-weight: 700;
  color: #ff9900;
  margin-right: 2px;
  line-height: 1;
}

.cert-days-text {
  font-size: 16px;
  font-weight: 500;
  color: #ff9900;
}

.cert-notice-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 0 -20px;
}

.cert-notice-message {
  color: #606266;
  font-size: 14px;
  line-height: 1.8;
}

.cert-notice-message p {
  margin: 6px 0;
  text-align: center;
}

.cert-date {
  font-weight: 600;
  color: #f56c6c;
  font-size: 18px;
  margin: 10px 0 !important;
  letter-spacing: 1px;
}
</style>
