<template>
  <div class="app-container">
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-blue-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-blue-500 text-3xl"><Tickets /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">服务器总数</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">128</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">同比增长 5.2%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-green-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-green-500 text-3xl"><Check /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">正常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">105</div>
            </div>
          </div>
          <div class="text-green-600 text-sm flex justify-start items-center">
            <el-icon><Top /></el-icon>
            <span class="ml-1">占比 82.03%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-yellow-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-yellow-500 text-3xl"><Warning /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">部分存活</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">17</div>
            </div>
          </div>
          <div class="text-yellow-600 text-sm flex justify-start items-center">
            <el-icon><Bottom /></el-icon>
            <span class="ml-1">占比 13.28%</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          class="asset-card text-center shadow-md rounded-lg p-4 bg-white border border-gray-200"
        >
          <div class="flex items-center justify-start mb-2">
            <div
              class="w-16 h-16 rounded-full flex items-center justify-center bg-red-100 mr-4 flex-shrink-0"
            >
              <el-icon class="text-red-500 text-3xl"><Close /></el-icon>
            </div>
            <div class="flex flex-col items-start text-left">
              <span class="text-gray-700 font-medium text-lg">异常</span>
              <div class="text-4xl font-bold text-gray-900 mt-1">6</div>
            </div>
          </div>
          <div class="text-red-600 text-sm flex justify-start items-center">
            <el-icon><Bell /></el-icon>
            <span class="ml-1">需要立即处理</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree
          v-model="queryParams.deptId"
          @node-click="handleQuery"
          class="mb-2"
        />
        <system-tree
          v-model="queryParams.systemId"
          @system-click="handleQuery"
        />
      </el-col>

      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <FuzzySearch
          v-model="queryParams"
          placeholder="搜索资产名称、IP地址..."
          search-field="keyword"
          @search="handleSearch"
          @reset="handleResetQuery"
        >
          <template #filters="{ form }">
            <el-form-item label="资产名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="资产名称"
                clearable
                class="!w-[150px]"
              />
            </el-form-item>
            <el-form-item label="资产IP" prop="ip">
              <el-input
                v-model="form.ip"
                placeholder="资产IP"
                clearable
                class="!w-[150px]"
              />
            </el-form-item>
            <el-form-item label="资产管理员" prop="ownerName">
              <el-input
                v-model="form.ownerName"
                placeholder="管理人员"
                clearable
                class="!w-[150px]"
              />
            </el-form-item>

            <el-form-item label="管理部门" prop="deptId">
              <el-select
                v-model="form.deptId"
                placeholder="选择部门"
                clearable
                class="!w-[150px]"
              >
                <el-option
                  v-for="dept in flatDeptOptions"
                  :key="dept.value"
                  :label="dept.label"
                  :value="dept.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="所属系统" prop="systemId">
              <el-select
                v-model="form.systemId"
                placeholder="选择系统"
                clearable
                class="!w-[150px]"
              >
                <el-option
                  v-for="system in dictCache.system0x0"
                  :key="system.value"
                  :label="system.label"
                  :value="system.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="资产链接" prop="url">
              <el-input
                v-model="form.url"
                placeholder="资产链接"
                clearable
                class="!w-[180px]"
              />
            </el-form-item>
            <el-form-item label="资产端口" prop="port">
              <el-input
                v-model="form.port"
                placeholder="资产端口"
                clearable
                class="!w-[120px]"
              />
            </el-form-item>
            <el-form-item label="联系方式" prop="ownerPhone">
              <el-input
                v-model="form.ownerPhone"
                placeholder="管理者手机号"
                clearable
                class="!w-[150px]"
              />
            </el-form-item>
            <el-form-item label="操作系统" prop="os">
              <el-select
                v-model="form.os"
                placeholder="操作系统"
                clearable
                class="!w-[150px]"
              >
                <el-option
                  v-for="item in dictCache.os"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="登记时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="daterange"
                range-separator="~"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="!w-[300px]"
              />
            </el-form-item>
          </template>
        </FuzzySearch>
        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <el-button
                  v-hasPerm="['system:assets:add']"
                  type="success"
                  class="ml-3"
                  @click="handleOpenDialog()"
                >
                  <i-ep-plus />
                  新增
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  :disabled="ids.length === 0"
                  class="ml-3"
                  @click="handleDelete()"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </div>
              <div>
                <span v-if="ids.length > 0" class="selection-info">
                  已选择
                  <el-tag type="info">{{ ids.length }}</el-tag>
                  项
                  <el-button type="primary" link @click="clearSelection">
                    清除选择
                  </el-button>
                </span>
              </div>
              <div>
                <el-button class="ml-3" @click="handleOpenImportDialog">
                  <template #icon><i-ep-upload /></template>
                  导入
                </el-button>

                <el-button
                  class="ml-3"
                  @click="handleExport"
                  :loading="exportLoading"
                >
                  <template #icon><i-ep-download /></template>
                  导出{{ ids.length > 0 ? "选中" : "全部" }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            ref="dataTableRef"
            :default-sort="{ prop: 'id', order: 'descending' }"
            v-loading="loading"
            :data="pageData"
            highlight-current-row
            border
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              align="center"
              key="id"
              label="序号"
              prop="id"
              min-width="90"
            />
            <el-table-column
              align="center"
              key="name"
              label="资产名称"
              prop="name"
              min-width="200"
            />
            <el-table-column
              align="center"
              key="os"
              label="操作系统"
              prop="os"
              min-width="200"
            />
            <el-table-column
              align="center"
              key="ip"
              label="ip地址"
              prop="ip"
              min-width="100"
            />
            <el-table-column
              align="center"
              key="deptName"
              label="管理部门"
              prop="deptName"
              min-width="120"
            />
            <el-table-column
              align="center"
              key="ownerInfo"
              label="管理人员及联系方式"
              min-width="160"
            >
              <template #default="scope">
                <div>
                  <span>{{ scope.row.ownerName }}</span>
                  <br />
                  <span>{{ scope.row.ownerPhone }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              key="systemId"
              label="关联系统"
              prop="systemId"
              min-width="100"
            >
              <template #default="scope">
                <span>{{ getSystemName(scope.row.systemId) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              key="status"
              label="资产状态"
              prop="status"
              min-width="100"
            >
              <template #default="scope">
                <el-tag
                  :type="
                    scope.row.status == '1'
                      ? 'success'
                      : scope.row.status == '2'
                        ? 'danger'
                        : 'info'
                  "
                >
                  {{ getAssetStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" label="探测状态" width="150">
              <template #default="scope">
                <asset-detection-status
                  :survival-status-list="scope.row.survivalStatusList"
                  :asset-name="scope.row.name"
                />
              </template>
            </el-table-column>

            <el-table-column
              align="center"
              fixed="right"
              label="操作"
              width="240"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  size="small"
                  link
                  @click="handleViewDetail(scope.row.id)"
                >
                  <el-icon class="text-sm">
                    <View />
                  </el-icon>
                  查看详情
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:edit']"
                  type="primary"
                  size="small"
                  link
                  @click="handleOpenDialog(scope.row.id)"
                >
                  <i-ep-edit />
                  编辑
                </el-button>
                <el-button
                  v-hasPerm="['system:assets:delete']"
                  type="danger"
                  size="small"
                  link
                  @click="handleDelete(scope.row.id)"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery()"
          />
        </el-card>
      </el-col>
    </el-row>
    <AssetsDialog
      v-model:visible="dialog.visible"
      :title="dialog.title"
      :id="dialog.id"
      @submitted="handleQuery"
    />

    <detailAssetsImport
      v-model:visible="importDialogVisible"
      @import-success="handleOpenImportDialogSuccess"
    />
    <asset-detail-view
      v-model:visible="detailDialog.visible"
      :asset-id="detailDialog.id"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 引入 Element Plus 的图标组件
import {
  Top,
  Bottom,
  Bell,
  Tickets, // 服务器总数图标
  Check, // 正常图标
  Warning, // 部分存活图标
  Close, // 异常图标
  View, // 查看详情图标
  Edit, // 编辑图标 (对应 i-ep-edit)
  Delete, // 删除图标 (对应 i-ep-delete)
  Plus, // 新增图标 (对应 i-ep-plus)
  Upload, // 导入图标 (对应 i-ep-upload)
  Download, // 导出图标 (对应 i-ep-download)
} from "@element-plus/icons-vue";

defineOptions({
  name: "assets",
  inheritAttrs: false,
});
import AssetDetailView from "./components/AssetDetailView.vue";
import detailAssetsImport from "./components/detail-assets-import.vue";
import assetsAPI, {
  assetsPageVO,
  assetsForm,
  assetsPageQuery,
} from "@/api/assets_management/details/assets";
// import eventsAPI, { // 如果不需要，可以注释掉或删除
//   eventsPageVO,
//   eventsForm,
//   eventsPageQuery,
// } from "@/api/work_management/critical";
// import DeptAPI from "@/api/dept"; // 如果不需要，可以注释掉或删除
import AssetsDialog from "./components/assetsDialog.vue";
import { useDictStore } from "@/store/modules/dictStore";
import AssetDetectionStatus from "../components/assetDetectionStatus.vue";

const dictStore = useDictStore();
const queryFormRef = ref(null);
const dataFormRef = ref(null);
const dataTableRef = ref(); // 添加表格引用
const importDialogVisible = ref(false);
const loading = ref(false);
const exportLoading = ref(false); // 添加导出加载状态
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const queryParams = reactive<assetsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  type: 1, //服务器类型
  status: 1,
});

const flatDeptOptions = ref<{ value: string | number; label: string }[]>([]);

// 缓存字典数据
const dictCache = reactive<{
  asset_status: any[];
  os: any[];
  dept0x0: any[];
  system0x0: any[];
}>({
  asset_status: [],
  os: [],
  dept0x0: [],
  system0x0: [],
});

// 各字典映射缓存
const deptMap = ref<Record<string | number, string>>({});
const systemMap = ref<Record<string | number, string>>({});
const statusMap = ref<Record<string | number, string>>({});
const osMap = ref<Record<string | number, string>>({});

const showAdvancedFilters = ref(false);

// 资产管理表格数据
const pageData = ref<assetsPageVO[]>([]);
const deptOptions = ref<any>([]);
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined as number | undefined,
});

// 资产管理表单数据
const formData = reactive<assetsForm>({});

/** 查询资产管理 */
function handleQuery() {
  loading.value = true;
  assetsAPI
    .getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 搜索处理方法
function handleSearch(params: Record<string, any>) {
  console.log("搜索参数:", params);

  // // 处理模糊搜索逻辑
  // if (params.keyword) {
  //   // 如果有关键词，可以同时搜索名称、IP和管理员
  //   queryParams.name = params.keyword;
  //   queryParams.ip = params.keyword;
  //   queryParams.ownerName = params.keyword;
  // }

  // 更新其他查询参数
  Object.assign(queryParams, params);
  // queryParams.pageNum = 1; // 重置页码
  handleQuery();
}

/** 重置资产管理查询 */
function handleResetQuery() {
  Object.keys(queryParams).forEach((key) => {
    (queryParams as any)[key] = undefined;
  });
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.status = 1;
  queryParams.type = 1;
  handleQuery();
}

// 资产详情对话框
const detailDialog = reactive({
  visible: false,
  id: undefined as number | undefined,
});

/** 打开资产详情查看弹窗 */
function handleViewDetail(id?: number) {
  if (!id) return;
  detailDialog.id = id;
  detailDialog.visible = true;
}

// 预加载字典数据
async function preloadDictData() {
  try {
    loading.value = true;

    // 并行加载所有需要的字典数据
    const [statusOptions, osOptions, deptOptions, systemOptions] =
      await Promise.all([
        dictStore.fetchOptions("asset_status"),
        dictStore.fetchOptions("os"),
        dictStore.fetchOptions("dept0x0"),
        dictStore.fetchOptions("system0x0"),
      ]);

    // 更新缓存
    dictCache.asset_status = statusOptions || [];
    dictCache.os = osOptions || [];
    dictCache.dept0x0 = deptOptions || [];
    dictCache.system0x0 = systemOptions || [];

    processOptions(dictCache.asset_status, statusMap);
    processOptions(dictCache.os, osMap);
    processDeptTree(dictCache.dept0x0);
    processOptions(dictCache.system0x0, systemMap.value);
  } catch (error) {
    console.error("获取字典数据失败:", error);
  } finally {
    loading.value = false; // 确保加载状态在字典数据加载完成后关闭
  }
}

// 处理部门树数据
function processDeptTree(depts: any[]) {
  if (!Array.isArray(depts)) return;

  const flatOptions: { value: string | number; label: string }[] = [];

  function flattenDepts(deptArray: any[], prefix = "") {
    deptArray.forEach((dept) => {
      if (dept.value !== undefined && dept.label) {
        const label = prefix ? `${prefix} / ${dept.label}` : dept.label;
        deptMap.value[dept.value] = dept.label;
        flatOptions.push({
          value: dept.value,
          label: label,
        });
      }

      if (dept.children && dept.children.length > 0) {
        const currentPrefix = prefix ? `${prefix} / ${dept.label}` : dept.label;
        flattenDepts(dept.children, currentPrefix);
      }
    });
  }

  flattenDepts(depts);
  flatDeptOptions.value = flatOptions;
}

// 处理选项数据
function processOptions(options: any[], targetMap: any) {
  if (!Array.isArray(options)) return;

  options.forEach((option) => {
    if (option.value !== undefined && option.label) {
      targetMap[option.value] = option.label;
    }
  });
}

// 获取系统名称
function getSystemName(systemId: string | number): string {
  if (!systemId) return "-";
  return systemMap.value[systemId] || `-`;
}

// 资产状态名称
function getAssetStatusName(status: string | number): string {
  const item = dictCache.asset_status.find((item) => item.value == status);
  return item ? item.label : `未知状态${status}`;
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 清除所有选择 */
function clearSelection() {
  ids.value = [];
  // 清除表格选择状态
  if (dataTableRef.value) {
    dataTableRef.value.clearSelection();
  }
}

/** 打开资产管理弹窗 */
async function handleOpenDialog(id?: number) {
  dialog.visible = true;
  dialog.id = undefined;
  dialog.title = id ? "修改服务器" : "新增服务器";
  nextTick(() => {
    dialog.id = id;
  });
}

/** 检测存活 */
function handleCheck(row?: any) {
  ElMessageBox.confirm("确认检测存活?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    loading.value = true;
    // assetsAPI.check(id || ids.value)
    //   .then(() => {
    //      ElMessage.success("检测成功");
    //      handleQuery();
    //   })
    console.log(row);
    assetsAPI
      .update(row.id, row)
      .then(() => {
        ElMessage.success("设置检测成功");
        handleQuery();
      })
      .finally(() => (loading.value = false));
  });
}

/** 删除资产管理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      assetsAPI
        .deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  // 构建导出参数
  const exportParams: any = {
    ...queryParams,
  };

  // 如果当前页有选中项，则传入assetIds参数
  if (ids.value.length > 0) {
    exportParams.assetIds = ids.value;
  }

  exportLoading.value = true;

  assetsAPI
    .exportServer(exportParams)
    .then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success(
        `成功导出${ids.value.length > 0 ? "选中" : "全部"}服务器`
      );
    })
    .catch((error) => {
      console.error("导出失败:", error);
      ElMessage.error("导出失败，请重试");
    })
    .finally(() => {
      handleResetQuery();
      exportLoading.value = false;
    });
}

onMounted(async () => {
  await preloadDictData();
  handleQuery();
});
</script>
<style scoped lang="scss">
.app-container {
  background-color: var(--app-bg-color);
  min-height: 100vh;
  // 其他样式...
}
</style>
