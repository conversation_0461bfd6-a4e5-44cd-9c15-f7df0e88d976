<template>
  <div class="initiate-ticket">
    <div class="page-header">
      <h3>工单审核</h3>
    </div>
    <el-form :model="form" :rules="rules" ref="dataFormRef" label-width="100px" class="form-container">
      <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label-width="60px" label="工号：" prop="employeeId">
              <el-input v-model="form.employeeId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label-width="80px"  label="创建人:" prop="applicantName">
              <el-input v-model="form.applicantName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="6">
             <el-form-item label-width="100px" label="所在单位：" prop="deptName">
              <el-input disabled v-model="form.deptName">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
           <el-form-item label-width="100px" label="联系方式：" prop="mobile">
              <el-input
                v-model="form.mobile"
                :disabled="!showStep"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <el-row :gutter="24">
         <el-col :span="12">
            <el-form-item label="创建时间：" prop="createTime">
              <el-date-picker disabled v-model="form.createTime" type="datetime" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期：" prop="deadline">
              <el-date-picker disabled v-model="form.deadline" type="datetime" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
       <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>事件对象</span>
          </div>
        </template>
        <el-row :gutter="24">
              <el-col :span="12">
            <el-form-item label-width="120px" label="信息系统名称：">
              <el-input
                v-model="form.sysname"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="域名/IP：" prop="domainIp">
              <el-input
                v-model="form.domainIp"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
       </el-card>
         <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>责任单位</span>
          </div>
        </template>
        <el-row :gutter="24">
              <el-col :span="8">
            <el-form-item label="责任部门：" >
             <el-input
                v-model="shstep.deptName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="责任人：">
              <el-input
                v-model="shstep.userName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式：" >
              <el-input
                v-model="shstep.userMobile"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
       </el-card>
        <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>事件信息</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="8">
             <el-form-item label="事件名称：" prop="name">
              <el-input v-model="form.name" disabled/>
            </el-form-item>
           </el-col>
            <el-col :span="8">
           <el-form-item  disabled label="事件来源：" prop="incidentSource">
              <Dictionary
                v-model="form.incidentSource"
                code="incidentsResouce"
               disabled
              ></Dictionary>
            </el-form-item>
           </el-col>
           <el-col :span="8">
            <el-form-item  disabled label="事件类型：" prop="incidentType">
              <Dictionary
                v-model="form.incidentType"
                code="incidentsType"
                disabled
              ></Dictionary>
            </el-form-item>
          </el-col>
            <el-col :span="24">
           <el-form-item label="事件描述：" prop="reason">
              <el-input v-model="form.reason" type="textarea" :rows="3" disabled />
            </el-form-item>
           </el-col>
        </el-row>
           </el-card>
             <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>网络安全应急工程师</span>
          </div>
        </template>
       <!-- 安全工程师信息开始 -->
        <el-form  :model="SafetyEngineerbox" :inline="true" >
        <div style="display:flex; justify-content: space-between;">
              <el-form-item label-width="110px" label="工程师名称：" prop="engineerName" style="width:20%">
              <el-input   disabled v-model="SafetyEngineerbox.engineerName"></el-input>
            </el-form-item>
            <el-form-item label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
              <el-input  disabled  v-model="SafetyEngineerbox.engineerMobile"> </el-input>
            </el-form-item>
            <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
              <el-input  disabled v-model="SafetyEngineerbox.engineerWechat"></el-input>
            </el-form-item>
            <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
              <el-input  disabled v-model="SafetyEngineerbox.engineerQq"></el-input>
            </el-form-item>
            <el-form-item label="邮箱：" label-width="70px" prop="engineerEmail" style="width:20%">
              <el-input  disabled v-model="SafetyEngineerbox.engineerEmail"></el-input>
            </el-form-item>
        </div>
        </el-form>
        <!-- 已上传文件展示 -->
        <el-row :gutter="20" v-if="form.fileList && form.fileList.length">
          <el-col :span="24">
             <el-form-item  class="fontw" label-width="110px" label="安全事件报告：">
                <ul>
                  <li v-for="file in form.fileList" :key="file.id">
                    <span>{{ file.name }}</span>
                    <el-button type="primary" style="margin-left:20px" @click="downloadFile(file)">下载</el-button>
                  </li>
                </ul>
              </el-form-item>
          </el-col>
        </el-row>
        <!-- 安全工程师信息结束 -->
            </el-card>
      <!-- 审核信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单审批</span>
          </div>
        </template>
          <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="审批结果：" prop="commentType">
              <el-select v-model="auditform.commentType" :disabled="!showStep" placeholder="请选择" style="width: 100%">
                <el-option label="通过" :value="1" />
                <el-option label="通过并关闭工单" :value="2" />
                <el-option label="不通过" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批时间：" prop="createTime">
              <el-date-picker v-model="auditform.createTime" disabled type="datetime" placeholder="选择时间"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="审核内容：" prop="commentContent">
              <el-input v-model="auditform.commentContent" :disabled="!showStep" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="审核报告：" >
               <file-upload uploadBtnText="上传审核报告" :upload-max-size="20 * 1024 * 1024"  v-model="fixFileList"
                  :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'"  :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'">
                </file-upload>
                <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <!-- 操作按钮 -->
      <div class="form-actions" >
        <el-button type="primary" @click="submitForm" size="large">
          <el-icon>
            <check />
          </el-icon>提交审核
        </el-button>
         <el-button type="info" @click="concelForm" size="large">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import UserAPI from "@/api/user";
import safetyIncidentAPI from '@/api/work_management/online_service/new_index'
import { formatLocalDateTime } from "@/utils/dateUtils";
import safetyAPI from "@/api/work_management/safety";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const emit = defineEmits(['next'])
interface TicketData {
  id: number;
  currentStep: string;
  isClick: boolean;
}


const props = defineProps<{
  ticketdata: TicketData,
}>();

// 添加新的响应式变量
const transferDialog = reactive({
  visible: false,
  allAssets: [] as any[]
})

const form = reactive({
  createTime: "",
  name: "",
  updateTime: '',
  reason: '',
  commentContent: '',
  commentBy: '',
  commentType: 0,
  id: '',
  comments: [],
  ticketType: "",
  deadline: "",
  fileList: [] as any[],
  assetsList: [] as any[],
});

const auditform = ref<{
  fixTime: string;
  createTime: string;
  updateTime: string;
  commentContent: string;
  commentBy: string;
  commentType: number;
  fileList: { id: string }[];
}>({
  fixTime: '',
  createTime: formatLocalDateTime(new Date()),
  updateTime: formatLocalDateTime(new Date()),
  commentContent: '',
  commentBy: '',
  commentType: 0,
  fileList: [],
  assetsList: [] as any[]
})
const fixFileList = ref([] as any[]);
const currentStep = ref(props.ticketdata.currentStep);
const showStep = ref(true);
const nowStep = ref('');
const stepStatus = ref<any | null>(null);
// 是否为当前步骤
function isCurrentStep() {
  if (currentStep.value == nowStep.value) { //是当前步骤
    showStep.value = false;
  } else {
    showStep.value = true;
  }
}
// 取消按钮
const concelForm=()=>{
emit('next')
}
const submitForm = async () => {
  auditform.value.fileList = fixFileList.value.map(file => file.id);
  console.log(auditform.value.fileList)
  await ElMessageBox.confirm(
    '确定要提交审核吗？',
    '确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  );
  try {
    
    await safetyIncidentAPI.audit(auditform.value, form.id);
    // 显示成功消息
    ElMessage.success('审核已提交')
    // 发送成功消息
    emit('next')
  } catch (error) {
    console.error('Error submitting vulnerability ticket:', error)
    ElMessage.error('提交失败，请重试')
  }
}
// 附件下载
const downloadFile = (row: any) => {
  if (row.url) {
  fetch(row.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = row.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  });
  } else {
    ElMessage.error('附件不存在');
  }
}
const shstep=ref({})
const steparr={
  1:"InitiateTicket",
  2:"AssetsAudit",
  3:"AssetsFix",
  4:"FixVerification",
  5:"fixEvaluation",
  6:"CloseTicket"
}
// 获取安全工程师信息
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}
const handleQuery = async () => {
  if (props.ticketdata.id) {
    await safetyIncidentAPI.getFormData(props.ticketdata.id).then((data) => {
      Object.assign(form, data)
    })
    console.log(form)
 // 整改部门信息
   const foundItem = form.reviewProcessForms?.find(item => item.executeDeptType ==3);
  shstep.value=foundItem
  // auditform.value.step=form.step
  nowStep.value=steparr[form.step]
    isCurrentStep();
    if (!showStep.value) {
      let filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 2) : [];
      auditform.value = filteredComments[filteredComments.length - 1];
    }
  }
}

// 添加验证规则
const rules = {
  reason: [
    { required: true, message: '请填写上线原因', trigger: 'blur' }
  ],
  createTime: [
    { required: true, message: '请选择创建时间', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入上线业务名称', trigger: 'change' }
  ],
}

  
onMounted(() => {
  handleQuery()
  SafetyEngineerConfig()
})

</script>
<style scoped>
.initiate-ticket {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h3 {
  color: var(--el-color-primary);
  font-size: 20px;
  margin: 0;
}

.form-container {
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-card :deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 500;
}

.asset-buttons {
  display: flex;
  gap: 12px;
}

.asset-buttons .el-button {
  min-width: 120px;
}

.form-actions {
  margin-top: 32px;
  text-align: center;
}

.form-actions .el-button {
  min-width: 120px;
  margin: 0 8px;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.file-item:hover {
  color: var(--el-color-primary);
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: var(--el-fill-color-light);
}

/* 输入框样式统一 */
:deep(.el-input__inner) {
  border-radius: 4px;
}

/* 卡片内容区域padding */
:deep(.el-card__body) {
  padding: 20px;
}

/* 资产弹窗样式 */
.asset-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}
:deep(.el-form-item) {
  margin-right: 0;
}
</style>
