import request from "@/utils/request";

const PROCESSCONFIG_BASE_URL = "/api/v1/processConfig";

class ProcessConfigAPI {
  /**
   * 获取审核流程配置
   * 
   * @param businessType 业务类型（safety安全漏洞 business安全评估与问题 offline资产下线）
   * @returns 审核流程配置列表
   */
  static getConfig(businessType: string) {
    return request<any, ReviewProcessConfigResult>({
      url: `${PROCESSCONFIG_BASE_URL}/${businessType}`,
      method: "get",
    });
  }

  /**
   * 新增修改审核流程配置
   * 
   * @param businessType 业务类型（safety安全漏洞 business安全评估与问题 offline资产下线）
   * @param data 审核流程配置表单对象数组
   */
  static saveConfig(businessType: string, data: ReviewProcessConfigForm[]) {
    return request({
      url: `${PROCESSCONFIG_BASE_URL}/${businessType}`,
      method: "post",
      data: data,
    });
  }
}

export default ProcessConfigAPI;

/** 审核流程配置结果 */
export interface ReviewProcessConfigResult {
  code: string;
  data: ReviewProcessConfig[];
  msg: string;
}

/** 审核流程配置 */
export interface ReviewProcessConfig {
  id: number;
  createTime: string;
  updateTime: string;
  businessType: string;
  executeDeptType: string;
  executeType: string;
  deptId: string;
  deptName: string;
  userId: string;
  userName: string;
  enableSms: string;
  smsTemplateId: number;
  notifyType: string;
  notifyPeriod: string;
}

/** 审核流程配置表单对象 */
export interface ReviewProcessConfigForm {
  id?: number;
  businessType?: string;
  businessId?: number;
  executeDeptType: string;
  executeType: string;
  deptId?: string;
  userId: string;
  enableSms: string;
  smsTemplateId?: number;
  notifyType?: string;
  notifyPeriod?: string;
  createTime?: string;
  updateTime?: string;
}
