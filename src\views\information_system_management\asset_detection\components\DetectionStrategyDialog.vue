<template>
    <el-dialog 
      v-model="props.visible" 
      title="探测历史记录" 
      width="900px"
    >
      <div class="flex-x-between mb-3">
        <div></div>
        <el-button @click="handleExport">
          <template #icon><i-ep-download /></template>
          导出
        </el-button>
      </div>
  
      <el-table :data="tableData" border stripe>
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="detectionTime" label="探测时间" width="180" />
        <el-table-column prop="detectionCycle" label="探测周期">
          <template #default="{ row }">
            {{ formatDetectionCycle(row.detectionCycle) }}
          </template>
        </el-table-column>
        <el-table-column prop="regularDetectionStatus" label="定期探测状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.regularDetectionStatus)">
              {{ formatDetectionStatus(row.regularDetectionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="immediateDetectionTime" label="即时探测时间" width="180" />
        <el-table-column prop="immediateDetectionStatus" label="即时探测状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.immediateDetectionStatus)">
              {{ formatDetectionStatus(row.immediateDetectionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
  
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive } from 'vue'
  
  const props = defineProps({
    visible: {
      type: Boolean,
      required: true
    }
  })
  
  const emit = defineEmits(['update:visible'])
  
  const total = ref(0)
  const tableData = ref([])
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10
  })
  
  const formatDetectionCycle = (cycle: string) => {
    const cycleMap: Record<string, string> = {
      day: '天',
      month: '月',
      quarter: '季度',
      year: '年',
      custom: '自定义'
    }
    return cycleMap[cycle] || cycle
  }
  
  const formatDetectionStatus = (status: number) => {
    const statusMap: Record<number, string> = {
      0: '未开始',
      1: '进行中',
      2: '已完成'
    }
    return statusMap[status] || '未知'
  }
  
  const getStatusType = (status: number): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
      const typeMap: Record<number, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
        0: 'info',
        1: 'warning',
        2: 'success'
      }
      return typeMap[status] || 'info'
  }
  
  const handleSizeChange = (val: number) => {
    queryParams.pageSize = val
    loadData()
  }
  
  const handleCurrentChange = (val: number) => {
    queryParams.pageNum = val
    loadData()
  }
  
  const loadData = () => {
    // TODO: 调用API加载数据
  }
  
  const handleExport = () => {
    // TODO: 实现导出功能
  }
  </script>
  
  <style scoped>
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  </style>