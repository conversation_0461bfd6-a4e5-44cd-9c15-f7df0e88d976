<template>
  <el-dialog 
    :model-value="visible"
    :title="title || (id ? '编辑证书' : '生成证书')" 
    width="500px"
    destroy-on-close
    @close="handleClose"
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="form.productName" placeholder="请输入产品名称" />
      </el-form-item>
      <el-form-item label="序列号" prop="serialNumber">
        <el-input v-model="form.serialNumber" placeholder="请输入序列号" />
      </el-form-item>
      <el-form-item label="证书类型" prop="certType">
        <el-select v-model="form.certType" placeholder="请选择证书类型" class="w-full">
          <el-option label="正式证书" value="official" />
          <el-option label="临时证书" value="temporary" />
        </el-select>
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="form.userName" placeholder="请输入用户名称" />
      </el-form-item>
      <el-form-item label="开始日期" prop="validFrom">
        <el-date-picker
          v-model="form.validFrom"
          type="date"
          placeholder="选择开始日期"
          value-format="YYYY-MM-DD"
          :disabledDate="disabledStartDate"
          @change="validateDates"
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="validTo">
        <el-date-picker
          v-model="form.validTo"
          type="date"
          placeholder="选择结束日期"
          value-format="YYYY-MM-DD"
          :disabledDate="disabledEndDate"
          @change="validateDates"
          style="width: 100%;"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="loading">
          {{ id ? '保存' : '生成' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import CertificateManageAPI, { CertificateForm } from '@/api/certificateManage';
import { format, isValidDate, differenceInDays, isExpired } from '@/utils/dateUtils';

const props = defineProps({
  visible: Boolean,
  id: String,
  title: String
});
const emit = defineEmits(['update:visible', 'submitted']);

const formRef = ref<FormInstance>();
const loading = ref(false);

const form = reactive<any>({
  productName: '',
  serialNumber: '',
  userName: '',
  certType: 'official',
  validFrom: '',
  validTo: '',
});

// 禁用开始日期 - 不能选择未来日期
const disabledStartDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const timeDate = new Date(time);
  timeDate.setHours(0, 0, 0, 0);
  
  // 禁用大于今天的日期
  if (differenceInDays(timeDate, today) > 0) {
    console.log('禁用开始日期:', format(timeDate, 'yyyy-MM-dd'), '大于今天');
    return true;
  }
  
  return false;
};

// 禁用结束日期 - 不能选择今天及以前的日期
const disabledEndDate = (time: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const timeDate = new Date(time);
  timeDate.setHours(0, 0, 0, 0);
  
  // 禁用小于等于今天的日期
  if (differenceInDays(timeDate, today) <= 0) {
    console.log('禁用结束日期:', format(timeDate, 'yyyy-MM-dd'), '不大于今天');
    return true;
  }
  
  // 如果已选择开始日期，则结束日期不能小于等于开始日期
  if (form.validFrom) {
    const startDate = new Date(form.validFrom);
    startDate.setHours(0, 0, 0, 0);
    
    if (differenceInDays(timeDate, startDate) <= 0) {
      console.log('禁用结束日期:', format(timeDate, 'yyyy-MM-dd'), '不大于开始日期:', format(startDate, 'yyyy-MM-dd'));
      return true;
    }
  }
  
  return false;
};

// 验证日期
const validateDates = () => {
  console.log('验证日期:', form.validFrom, form.validTo);
  nextTick(() => {
    formRef.value?.validateField(['validFrom', 'validTo']);
  });
};

// 表单验证规则
const rules = {
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  serialNumber: [{ required: true, message: '请输入序列号', trigger: 'blur' }],
  userName: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
  validFrom: [
    { required: true, message: '请选择开始日期', trigger: 'change' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          return callback(new Error('请选择开始日期'));
        }
        
        const startDate = new Date(value);
        const today = new Date();
        
        startDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);
        
        // 开始日期不能大于今天
        if (differenceInDays(startDate, today) > 0) {
          return callback(new Error('开始日期不能大于当前日期'));
        }
        
        callback();
      },
      trigger: ['change', 'blur']
    }
  ],
  validTo: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value) {
          return callback(new Error('请选择结束日期'));
        }
        
        const endDate = new Date(value);
        const today = new Date();
        
        endDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);
        
        // 结束日期必须大于今天
        if (differenceInDays(endDate, today) <= 0) {
          return callback(new Error('结束日期必须大于当前日期'));
        }
        
        // 如果已选择开始日期，则结束日期必须大于开始日期
        if (form.validFrom) {
          const startDate = new Date(form.validFrom);
          startDate.setHours(0, 0, 0, 0);
          
          if (differenceInDays(endDate, startDate) <= 0) {
            return callback(new Error('结束日期必须大于开始日期'));
          }
        }
        
        callback();
      },
      trigger: ['change', 'blur']
    }
  ]
};

// 监听日期变化
watch(
  () => [form.validFrom, form.validTo],
  ([newValidFrom, newValidTo]) => {
    console.log('日期变更:', newValidFrom, newValidTo);
    if (newValidFrom && newValidTo) {
      // 使用setTimeout确保在值更新后再触发验证
      setTimeout(() => {
        console.log('watch触发验证');
        formRef.value?.validateField(['validFrom', 'validTo']);
      }, 100);
    }
  }
);

// 监听id和visible变化，编辑时拉取详情
watch(
  () => [props.id, props.visible],
  ([id, visible]) => {
    if (id && visible) {
      fetchDetail(id);
    } else if (visible && !id) {
      resetForm();
    }
  },
  { immediate: true }
);

function resetForm() {
  formRef.value?.resetFields();
  Object.assign(form, {
    productName: '',
    serialNumber: '',
    userName: '',
    certType: 'official',
    validFrom: '',
    validTo: ''
  });
}

async function fetchDetail(id: string) {
  loading.value = true;
  try {
    const data = await CertificateManageAPI.getCertificateDetail(id);
    Object.assign(form, {
      productName: data.productName,
      serialNumber: data.serialNumber,
      userName: data.userName,
      certType: data.certType,
      validFrom: data.validFrom,
      validTo: data.validTo
    });
    
    // 加载数据后主动验证一次
    nextTick(() => {
      formRef.value?.validateField(['validFrom', 'validTo']);
    });
  } catch {
    ElMessage.error('获取证书详情失败');
    handleClose();
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  emit('update:visible', false);
  resetForm();
}

function submitForm() {
  formRef.value?.validate(async (valid) => {
    if (!valid) {
      console.log('表单验证失败');
      return;
    }
    
    // 最后一次验证确认
    const { validFrom, validTo } = form;
    if (validFrom && validTo) {
      const startDate = new Date(validFrom);
      const endDate = new Date(validTo);
      const today = new Date();
      
      // 重置时间部分
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0);
      today.setHours(0, 0, 0, 0);
      
      // 再次验证规则
      if (differenceInDays(startDate, today) > 0) {
        ElMessage.error('开始日期不能大于当前日期');
        return;
      }
      
      if (differenceInDays(endDate, today) <= 0) {
        ElMessage.error('结束日期必须大于当前日期');
        return;
      }
      
      if (differenceInDays(endDate, startDate) <= 0) {
        ElMessage.error('结束日期必须大于开始日期');
        return;
      }
    }
    
    loading.value = true;
    try {
      if (props.id) {
        await CertificateManageAPI.updateCertificate(props.id,{ ...form, id: props.id });
        ElMessage.success('证书更新成功');
      } else {
        await CertificateManageAPI.generateCertificate(form);
        ElMessage.success('证书生成成功');
      }
      emit('update:visible', false);
      emit('submitted');
      resetForm();
    } catch {
      ElMessage.error(props.id ? '证书更新失败' : '证书生成失败');
    } finally {
      loading.value = false;
    }
  });
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
