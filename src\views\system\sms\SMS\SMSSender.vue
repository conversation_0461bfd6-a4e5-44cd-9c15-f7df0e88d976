<template>
  <el-card class="sms-sender">
    <template #header>
      <div class="card-header">
        <span>发送短信</span>
      </div>
    </template>
    <el-form :model="form" label-width="120px" @submit.prevent="sendSMS">
      <el-form-item label="接收手机号" required>
        <el-input v-model="form.phoneNumber" placeholder="请输入手机号">
          <template #prefix>
            <el-icon><Phone /></el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="短信模板" required>
        <el-select v-model="form.templateId" placeholder="请选择短信模板" style="width: 100%;">
          <el-option
            v-for="item in templates"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="短信内容" required>
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入短信内容"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" native-type="submit" :loading="sending">
          {{ sending ? '发送中...' : '发送短信' }}
        </el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Phone } from '@element-plus/icons-vue';

const form = ref({
  phoneNumber: '',
  templateId: '',
  content: '',
});

const templates = ref([
  { id: '1', name: '模板1' },
  { id: '2', name: '模板2' },
]);

const sending = ref(false);

const sendSMS = () => {
  sending.value = true;
  // 模拟发送过程
  setTimeout(() => {
    console.log('发送短信:', form.value);
    ElMessage.success('短信发送成功');
    sending.value = false;
  }, 2000);
};
</script>

<style scoped>
.sms-sender {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
