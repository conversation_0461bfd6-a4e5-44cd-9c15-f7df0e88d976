<template>
  <div class="asset-dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1 class="dashboard-title">资产仪表盘</h1>
      </el-col>
    </el-row>

    <!-- 资产总览卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="(count, type) in assetCounts" :key="type">
        <el-card shadow="hover" class="asset-card">
          <div class="asset-content">
            <div class="asset-icon">
              <svg-icon :icon-class="getAssetIcon(type)" size="2em" />
              <el-icon :size="40">
                <component :is="getAssetIcon(type)"></component>
              </el-icon>
            </div>
            <div class="asset-info">
              <div class="asset-type">{{ type }}</div>
              <div class="asset-count">{{ count }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产趋势图表 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产增长趋势</span>
              <el-radio-group v-model="trendTimeRange" size="small">
                <!-- <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="quarter">季度</el-radio-button> -->
                <el-radio-button label="year">年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产分布饼图 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产类型分布</span>
            </div>
          </template>
          <div class="chart-container" ref="distributionChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产状态分布</span>
            </div>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import DashboardAPI from "@/api/dashboard";

// 添加主题检测
const isDark = ref(document.documentElement.classList.contains('dark'));

// 创建 MutationObserver 监听主题变化
let themeObserver: MutationObserver;

const assetCounts = reactive({
  '服务器': 0,
  '安全设备': 0,
  '网络设备': 0,
  '信息系统': 0,
});

const trendTimeRange = ref('month');
const trendChartRef = ref<HTMLElement | null>(null);
const distributionChartRef = ref<HTMLElement | null>(null);
const statusChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;
let distributionChart: echarts.ECharts | null = null;
let statusChart: echarts.ECharts | null = null;

// 保存最后一次获取的数据，用于主题切换时重新渲染
const lastData = reactive({
  trendMonths: [] as string[],
  assetTrends: {},
  assetCounts: {} as any,
  assetStatuses: [] as any[]
});

const getAssetIcon = (type: string) => {
  const icons = {
    '信息系统': 'el-icon-cpu',
    '服务器': 'el-icon-cpu',
    '安全设备': 'el-icon-lock',
    '网络设备': 'el-icon-connection',
  };
  return icons[type as keyof typeof icons] || 'el-icon-box';
};

// 更新图表初始化函数，添加主题支持
const initTrendChart = (trendMonths: string[], assetTrends: any) => {
  if (trendChartRef.value) {
    // 销毁旧图表
    if (trendChart) {
      trendChart.dispose();
    }
    
    // 使用当前主题初始化
    trendChart = echarts.init(trendChartRef.value, isDark.value ? 'dark' : undefined);
    
    // 获取主题颜色
    const textColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-text-color-primary').trim() || '#303133';
    const borderColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-border-color').trim() || '#DCDFE6';
    
    const option = {
      backgroundColor: 'transparent', // 透明背景适配主题
      title: {
        text: '资产增长趋势',
        textStyle: {
          color: textColor
        }
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['信息系统', '服务器', '安全设备', '网络设备'],
        textStyle: {
          color: textColor
        }
      },
      xAxis: {
        type: 'category',
        data: trendMonths,
        axisLine: {
          lineStyle: {
            color: borderColor
          }
        },
        axisLabel: {
          color: textColor
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: borderColor
          }
        },
        axisLabel: {
          color: textColor
        },
        splitLine: {
          lineStyle: {
            color: borderColor,
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '信息系统',
          type: 'line',
          data: trendMonths.map(month => assetTrends['10']?.[month] || 0)
        },
        {
          name: '服务器',
          type: 'line',
          data: trendMonths.map(month => assetTrends['1']?.[month] || 0)
        },
        {
          name: '安全设备',
          type: 'line',
          data: trendMonths.map(month => assetTrends['3']?.[month] || 0)
        },
        {
          name: '网络设备',
          type: 'line',
          data: trendMonths.map(month => assetTrends['2']?.[month] || 0)
        }
      ]
    };
    trendChart.setOption(option);
  }
};

const initDistributionChart = (assetCounts: any) => {
  if (distributionChartRef.value) {
    // 销毁旧图表
    if (distributionChart) {
      distributionChart.dispose();
    }
    
    // 使用当前主题初始化
    distributionChart = echarts.init(distributionChartRef.value, isDark.value ? 'dark' : undefined);
    
    // 获取主题颜色
    const textColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-text-color-primary').trim() || '#303133';
    
    const option = {
      backgroundColor: 'transparent', // 透明背景适配主题
      title: {
        text: '资产类型分布',
        left: 'center',
        textStyle: {
          color: textColor
        }
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: textColor
        }
      },
      series: [
        {
          name: '资产类型',
          type: 'pie',
          radius: '50%',
          data: Object.keys(assetCounts).map(type => ({
            value: assetCounts[type],
            name: type
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: isDark.value ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: textColor
          }
        }
      ]
    };
    distributionChart.setOption(option);
  }
};

const initStatusChart = (assetStatuses: any) => {
  if (statusChartRef.value) {
    // 销毁旧图表
    if (statusChart) {
      statusChart.dispose();
    }
    
    // 使用当前主题初始化
    statusChart = echarts.init(statusChartRef.value, isDark.value ? 'dark' : undefined);
    
    // 获取主题颜色
    const textColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-text-color-primary').trim() || '#303133';
    
    const option = {
      backgroundColor: 'transparent', // 透明背景适配主题
      title: {
        text: '资产状态分布',
        left: 'center',
        textStyle: {
          color: textColor
        }
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        textStyle: {
          color: textColor
        }
      },
      series: [
        {
          name: '资产状态',
          type: 'pie',
          radius: '50%',
          data: assetStatuses.map((status: any) => ({
            value: status.count,
            name: status.status
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: isDark.value ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            color: textColor
          }
        }
      ]
    };
    statusChart.setOption(option);
  }
};

// 刷新所有图表
const refreshCharts = () => {
  if (Object.keys(lastData.assetCounts).length > 0) {
    initTrendChart(lastData.trendMonths, lastData.assetTrends);
    initDistributionChart(lastData.assetCounts);
    initStatusChart(lastData.assetStatuses);
  }
};

function handleQuery() {
  DashboardAPI.getAssetDashboard()
    .then((data) => {
      Object.assign(assetCounts, data.assetCounts);
      
      // 保存数据用于主题切换时重新渲染
      lastData.trendMonths = data.trendMonths;
      lastData.assetTrends = data.assetTrends;
      lastData.assetCounts = data.assetCounts;
      lastData.assetStatuses = data.assetStatuses;
      
      initTrendChart(data.trendMonths, data.assetTrends);
      initDistributionChart(data.assetCounts);
      initStatusChart(data.assetStatuses);
    });
}

// 监听时间范围变化
watch(trendTimeRange, () => {
  handleQuery();
});

// 监听主题变化
watch(isDark, () => {
  refreshCharts();
});

onMounted(() => {
  // 设置主题观察器
  themeObserver = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains('dark');
  });
  
  // 开始观察
  themeObserver.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class'] 
  });
  
  // 加载数据
  handleQuery();

  // 监听窗口大小变化，调整图表尺寸
  window.addEventListener('resize', () => {
    trendChart?.resize();
    distributionChart?.resize();
    statusChart?.resize();
  });
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 停止主题观察
  themeObserver?.disconnect();
  
  // 销毁图表实例
  trendChart?.dispose();
  distributionChart?.dispose();
  statusChart?.dispose();
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {
    trendChart?.resize();
    distributionChart?.resize();
    statusChart?.resize();
  });
});
</script>

<style scoped>
.asset-dashboard {
  padding: 20px;
  background-color: var(--el-bg-color);  /* 使用 Element Plus 变量 */
}

.dashboard-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量 */
}

.asset-card {
  height: 120px;
  overflow: hidden;
  background-color: var(--el-bg-color);  /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color-light);  /* 使用 Element Plus 变量 */
  transition: all 0.3s;
}

.asset-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px var(--el-box-shadow-lighter);
}

.asset-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.asset-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 16px;
}

.asset-info {
  flex-grow: 1;
}

.asset-type {
  font-size: 16px;
  color: var(--el-text-color-secondary);  /* 使用 Element Plus 变量 */
  margin-bottom: 8px;
}

.asset-count {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量 */
}

.chart-container {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

/* 为每种资产类型设置不同的背景色 - 这些颜色在暗色主题中也能保持可见 */
.el-col:nth-child(1) .asset-icon { background-color: var(--el-color-primary); }
.el-col:nth-child(2) .asset-icon { background-color: var(--el-color-success); }
.el-col:nth-child(3) .asset-icon { background-color: var(--el-color-warning); }
.el-col:nth-child(4) .asset-icon { background-color: var(--el-color-danger); }

.asset-icon :deep(svg) {
  color: #ffffff;  /* 图标颜色保持白色，在彩色背景上 */
}

/* 深度选择器适配 Element Plus 组件在暗模式下的样式 */
:deep(.el-card__header) {
  border-bottom: 1px solid var(--el-border-color);  /* 使用 Element Plus 变量 */
}

:deep(.el-radio-button__inner) {
  color: var(--el-text-color-regular);  /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color);  /* 使用 Element Plus 变量 */
}
</style>
