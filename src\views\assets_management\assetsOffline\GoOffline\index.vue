<template>
  <div :key="componentKey" class="asset-offline-management">
    <!-- 动态绑定class：只在list页签时隐藏标签栏，在process页签时显示 -->
    <el-tabs v-model="activeTab" :class="{'hide-tabs-header': activeTab === 'list'}">
      <el-tab-pane label="详情" name="list">
        <offline-details @navigateToProcess="navigateToProcess" :id="getId" />
      </el-tab-pane>
      <el-tab-pane label="流程" name="process">
        <template v-if="activeTab === 'process'">
          <offline-process :ticketId="processParams" @init="init" />
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import OfflineProcess from "./components/offlineAssprocess.vue";
import OfflineDetails from "./components/offlineDetails.vue";

const activeTab = ref("list");
const processParams = ref<number | undefined>();
const router = useRouter();
const route = useRoute();
const componentKey = ref(0);
// 从get请求获取id
const getId = Number(route.query.id);

const navigateToProcess = (ticketId: number | undefined) => {
  console.log("ticketId", ticketId);
  processParams.value = ticketId;
  activeTab.value = "process";
};

const init = async (id: number) => {
  console.log("init with id:", id);
  activeTab.value = "list";
  // 强制重新渲染
  componentKey.value++;
  // 如果需要刷新页面
  if (route.path == "/assets_management/assetsOffline/GoOffline") {
    router.push({
      path: '/assets_management/assetsOffline/GoOffline',
      query: { refresh: Date.now() }
    });
  } else {
    router.push({
      path: '/assets_management/assetsOffline/GoOffline',
      query: { refresh: Date.now() }
    });
  }
};
</script>

<style scoped>
.asset-offline-management {
  padding: 20px;
}

/* 隐藏标签栏 */
.hide-tabs-header :deep(.el-tabs__header) {
  display: none;
}

/* 美化标签栏 */
:deep(.el-tabs__header) {
  margin-bottom: 20px;
  border-bottom: 2px solid var(--el-border-color-light);
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__item.is-active) {
  font-weight: bold;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 2px;
}
</style>
