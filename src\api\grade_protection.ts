import request from "@/utils/request";

const GRADE_PROTECTION_BASE_URL = "/api/v1/grade_protection";

class GradeProtectionAPI {
  // 获取所有等保数据数量（支持筛选参数）
  static getTotalCount(queryParams?: Record<string, any>) {
    return request<ApiResponse<number>>({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/number`,
      method: "get",
      params: queryParams,
    });
  }

  //获取等级卡片对应等级数量（支持筛选参数）
  static getLevelCounts(queryParams?: Record<string, any>) {
    return request<ApiResponse<LevelCountVO[]>>({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/level-count`,
      method: "get",
      params: queryParams,
    });
  }

  // 获取等保定级表单列表（支持参数）
  static getGradingList(queryParams?: Record<string, any>) {
    return request<
      ApiResponse<{ records: GradingVO[]; total: number }>,
      { records: GradingVO[]; total: number }
    >({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/index`,
      method: "get",
      params: queryParams,
    });
  }
  //获取所有部门列表
  static getDepartmentList() {
    return request<ApiResponse<string[]>>({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/department-list`,
      method: "get",
    });
  }

  // 更新等保定级系统信息
  static updateGradingSystem(id: string | number, data: Partial<GradingVO>) {
    return request<ApiResponse<GradingVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/update/${id}`,
      method: "put",
      data,
    });
  }

  // 删除等保定级某个数据
  static deleteGradingSystem(id: string | number) {
    return request<ApiResponse<void>>({
      url: `${GRADE_PROTECTION_BASE_URL}/grading/delete/${id}`,
      method: "delete",
    });
  }

  // 获取所有备案数据数量（支持筛选参数）
  static getRecordTotalCount(queryParams?: Record<string, any>) {
    return request<ApiResponse<number>>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/number`,
      method: "get",
      params: queryParams,
    });
  }

  //获取备案卡片对应数量（支持筛选参数）
  static getRecordlevelCounts(queryParams?: Record<string, any>) {
    return request<ApiResponse<LevelCountVO[]>>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/level-count`,
      method: "get",
      params: queryParams,
    });
  }

  // 获取备案记录列表（支持参数）
  static getRecordList(queryParams?: Record<string, any>) {
    return request<ApiResponse<RecordVO[]>, RecordVO[]>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/index`,
      method: "get",
      params: queryParams,
    });
  }

  //获取新增备案时系统ID和名称和等保级别
  static getRecordSystemAndLevel() {
    return request<
      ApiResponse<{ systemId: string; systemNames: string[]; levels: string[] }>
    >({
      url: `${GRADE_PROTECTION_BASE_URL}/record/system-list`,
      method: "get",
    });
  }

  // 新增备案
  static addRecord(data: FormData) {
    return request<ApiResponse<RecordVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/add`,
      method: "post",
      data,
      headers: { "Content-Type": "multipart/form-data" },
    });
  }

  // 编辑备案
  static updateRecord(systemId: string | number, data: FormData) {
    return request<ApiResponse<RecordVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/update/${systemId}`,
      method: "put",
      data,
      headers: { "Content-Type": "multipart/form-data" },
    });
  }

  // 删除备案记录
  static deleteRecord(systemId: string | number) {
    return request<ApiResponse<void>>({
      url: `${GRADE_PROTECTION_BASE_URL}/record/delete/${systemId}`,
      method: "delete",
    });
  }

  // 获取所有测评报告数据数量（支持筛选参数）
  static getReportTotalCount(queryParams?: Record<string, any>) {
    return request<ApiResponse<number>>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/number`,
      method: "get",
      params: queryParams,
    });
  }
  // 获取测评报告列表（支持查询参数）
  static getReportList(queryParams?: {
    systemName?: string;
    level?: string;
    assessmentAgency?: string;
    assessmentStatus?: string;
    beginTime?: string;
    endTime?: string;
    pageNum?: number;
    pageSize?: number;
  }) {
    return request<ApiResponse<ReportVO[]>, ReportVO[]>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/index`,
      method: "get",
      params: queryParams,
    });
  }

  // 新增测评报告
  static addReport(data: FormData) {
    return request<ApiResponse<ReportVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/add`,
      method: "post",
      data,
      headers:
        data instanceof FormData
          ? { "Content-Type": "multipart/form-data" }
          : {},
    });
  }

  // 更新测评报告
  static updateReport(id: string | number, data: FormData) {
    return request<ApiResponse<ReportVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/update/${id}`,
      method: "put",
      data,
      headers:
        data instanceof FormData
          ? { "Content-Type": "multipart/form-data" }
          : {},
    });
  }

  // 删除测评报告
  static deleteReport(id: string | number) {
    return request<ApiResponse<void>>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/delete/${id}`,
      method: "delete",
    });
  }
  //获取定级系统测评报告各状态数量统计
  static getReportStatusCounts(queryParams?: Record<string, any>) {
    return request<ApiResponse<ReportStatusCountVO[]>>({
      url: `${GRADE_PROTECTION_BASE_URL}/report/status-count`,
      method: "get",
      params: queryParams,
    });
  }

  //获取测评时 测评机构	 测评人	测评人联系方式
  static getReportLoadProviderList() {
    return request<
      ApiResponse<{ name: string; manager: string[]; managerPhone: string[] }>
    >({
      url: `${GRADE_PROTECTION_BASE_URL}/report/provider-list`,
      method: "get",
    });
  }

  //新增文章/文档
  static addCyberkb(data: FormData) {
    return request<ApiResponse<CyberkbVO>>({
      url: `${GRADE_PROTECTION_BASE_URL}/article/add`,
      method: "post",
      data,
      headers:
        data instanceof FormData
          ? { "Content-Type": "multipart/form-data" }
          : {},
    });
  }

  //获取文章/文档列表
  static getCyberkbList(queryParams?: Record<string, any>) {
    return request<ApiResponse<CyberkbVO[]>, CyberkbVO[]>({
      url: `${GRADE_PROTECTION_BASE_URL}/article/index`,
      method: "get",
      params: queryParams,
    });
  }

  //获取文档分类
  static getCategoryCounts() {
    return request<ApiResponse<string[]>>({
      url: `${GRADE_PROTECTION_BASE_URL}/article/group`,
      method: "get",
    });
  }

  // 获取文章总数
  static getArticleCount(queryParams?: Record<string, any>) {
    return request<ApiResponse<number>, number>({
      url: `${GRADE_PROTECTION_BASE_URL}/article/number`,
      method: "get",
      params: queryParams,
    });
  }
}

export default GradeProtectionAPI;

// 类型定义
export interface GradingVO {
  systemId: string;
  name: string;
  department: string;
  level: string;
  isOpen: string;
  responsiblePerson: string;
  respPersonPhone: string;
}

export interface RecordVO {
  id: string; // 备案记录ID
  systemId: long | string; // 系统ID
  name: string;
  recordNumber: string;
  level: string;
  recordStatus: string; //判断是否撤销
  date: string;
  recordDepartment: string;
  certificateFile?: File; // 仅前端上传时用
  certificateFileUrl?: string; // 仅后端返回时用
}

export interface ReportVO {
  id: string; // 报告ID
  systemId: string;
  reportName: string;
  name: string;
  level: string;
  assessmentAgency: string;
  assessmentPerson: string;
  assessmentPersonPhone: string;
  date: string;
  assessmentStatus: string;
  reportFile?: File; // 仅前端上传时用
  reportFileUrl: string; // 仅后端返回时用
}

export interface CyberkbVO {
  title: string; // 标题
  introduction: string; // 简介
  category: string; // 类型
  document: File; // 仅前端上传时用
  documentUrl: string; // 仅后端返回时用
  createTime: string; // 创建时间
}

// 通用接口响应格式
interface ApiResponse<T> {
  code: number | string;
  msg: string;
  data: T;
}
