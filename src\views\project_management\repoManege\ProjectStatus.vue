<template>
  <div>
    <!-- 筛选栏 -->
    <div class="filter-bar">
      <el-select
        v-model="selectedYear"
        placeholder="选择年份"
        style="width: 120px"
      >
        <el-option v-for="y in years" :key="y" :label="y + '年'" :value="y" />
      </el-select>

      <el-select
        v-model="selectedQuarter"
        placeholder="选择季度"
        style="width: 140px; margin-left: 10px"
      >
        <el-option label="全部季度" value="" />
        <el-option label="第1季度" value="1" />
        <el-option label="第2季度" value="2" />
        <el-option label="第3季度" value="3" />
        <el-option label="第4季度" value="4" />
      </el-select>

      <el-button
        type="primary"
        size="small"
        @click="fetchOverviewData"
        :loading="loading"
        style="margin-left: auto; margin-right: 10px"
      >
        <el-icon><i-ep-refresh /></el-icon>
        刷新数据
      </el-button>

      <el-button
        type="success"
        size="small"
        @click="generateWordReport"
        :loading="reportLoading"
      >
        <el-icon><i-ep-document /></el-icon>
        生成报告
      </el-button>
    </div>
    <!-- 概述-->
    <div class="overview" style="margin-bottom: 16px">
      <el-card
        shadow="never"
        class="overview-card"
        :body-style="{ backgroundColor: 'var(--el-bg-color)' }"
      >
        <div class="overview-content">
          <span class="overview-year">{{ selectedYear }}年</span>
          <span v-if="selectedQuarter" class="overview-quarter">
            第{{ selectedQuarter }}季度
          </span>
          <span class="overview-text">
            期间，累计项目数
            <span class="overview-num">{{ stats[0].value || 0 }}</span>
            个， 完成项目数
            <span class="overview-num success">{{ stats[1].value || 0 }}</span>
            个， 待完成项目数
            <span class="overview-num warning">{{ stats[2].value || 0 }}</span>
            个， 总体完成率
            <span class="overview-num rate">{{ stats[3].value || 0 }}</span>
          </span>
        </div>
      </el-card>
    </div>

    <!-- 数据卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="item in stats" :key="item.label">
        <el-card
          shadow="hover"
          class="data-card"
          :body-style="{ padding: '0px' }"
        >
          <div class="card-content">
            <div class="card-info">
              <div class="card-type">
                {{ item.label }}
              </div>
              <div :style="{ color: item.color }" class="card-count">
                {{ item.value }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表容器 -->
    <div ref="chartRef" class="chart-container"></div>

    <!-- 项目展示列表 -->
    <el-row :gutter="20">
      <el-card shadow="hover" class="project-card">
        <template #header>
          <div class="card-header">
            <span class="chart-title">项目状态</span>
          </div>
        </template>
        <el-table :data="projectList" style="" v-loading="loading">
          <el-table-column prop="name" label="项目名称" />
          <el-table-column prop="owner" label="负责人" />
          <el-table-column prop="startDate" label="开始日期">
            <template #default="{ row }">
              {{ formatDate(row.startDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="endDate" label="结束日期">
            <template #default="{ row }">
              {{
                formatDate(new Date(new Date(row.endDate).getTime() - 86400000))
              }}
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status) as any"
                disable-transitions
              >
                {{ row.status }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container" v-if="pagination.total > 0">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </el-row>

    <!-- 饼图 -->
    <CompletionPieChart :data="chartData" />
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  computed,
  reactive,
  nextTick,
} from "vue";
import * as echarts from "echarts";
import { ProjectAPI } from "@/api/progress_management/createProject";
import { Project } from "@/types/project";
import CompletionPieChart from "./components/CompletionPieChart.vue";
import { ElMessage } from "element-plus";
import html2canvas from "html2canvas";
import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  ImageRun,
  Table,
  TableRow,
  TableCell,
  WidthType,
  AlignmentType,
  HeadingLevel,
} from "docx";
import { saveAs } from "file-saver";

// 监听暗黑模式q
const isDark = ref<boolean>(
  document.documentElement.classList.contains("dark")
);
let themeObserver: MutationObserver | null = null;

interface Props {
  defaultYear?: number; // 默认选中的年份
  title?: string; // 图表标题
}

const props = defineProps<Props>();

const projectData = ref();
const projectList = ref<Project[]>([]);
const years = ref<number[]>([]);

// 状态
const loading = ref(false);

// 报告生成状态
const reportLoading = ref(false);

// 年份/季度选择状态
const selectedYear = ref<number>(props.defaultYear || years.value[0]);
const selectedQuarter = ref<number>();

// 分页数据
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 5,
  totalPages: 0,
});

// 获取项目列表
const fetchProjects = async (params: {
  year: number;
  quarter: number;
  pageNum: number;
  pageSize: number;
}) => {
  try {
    loading.value = true;
    const response = await ProjectAPI.getListByCondition(params);
    const data = response.data || response; // 兼容不同的响应格式

    if (data && data.items) {
      projectList.value = data.items;
      pagination.total = data.total;
      pagination.page = data.page;
      pagination.pageSize = data.pageSize;
      pagination.totalPages = data.totalPages;
    } else {
      projectList.value = [];
      pagination.total = 0;
      pagination.page = 1;
      pagination.totalPages = 0;
    }
  } catch (error) {
    console.error("获取项目列表失败:", error);
    ElMessage.error("获取项目列表失败");
    projectList.value = [];
    pagination.total = 0;
    pagination.page = 1;
    pagination.totalPages = 0;
  } finally {
    loading.value = false;
  }
};

// 图表实例
const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

// 渲染图表
const renderChart = (months: string[], totals: number[]) => {
  if (!chartRef.value) return;

  // 如果已有实例，先销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 使用当前主题初始化
  chartInstance = echarts.init(
    chartRef.value,
    isDark.value ? "dark" : undefined
  );

  // 获取主题颜色
  const textColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-text-color-primary")
      .trim() || "#303133";

  chartInstance.setOption({
    backgroundColor: "transparent", // 透明背景适配主题
    title: {
      text: selectedQuarter.value
        ? `${selectedYear.value}年 第${selectedQuarter.value}季度 项目数量趋势`
        : props.title || `${selectedYear.value}年 项目数量趋势`,
      left: "center",
      textStyle: {
        color: textColor, // 使用主题文本颜色
      },
    },
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: months,
      axisLabel: {
        color: textColor, // 使用主题文本颜色
      },
      axisLine: {
        lineStyle: {
          color: isDark.value
            ? "rgba(255, 255, 255, 0.3)"
            : "rgba(0, 0, 0, 0.2)",
        },
      },
    },
    yAxis: {
      type: "value",
      name: "项目数",
      nameTextStyle: {
        color: textColor, // 使用主题文本颜色
      },
      axisLabel: {
        color: textColor, // 使用主题文本颜色
      },
      splitLine: {
        lineStyle: {
          color: isDark.value
            ? "rgba(255, 255, 255, 0.1)"
            : "rgba(0, 0, 0, 0.1)",
        },
      },
    },
    series: [
      {
        name: "项目数",
        type: "line",
        smooth: true,
        data: totals,
        itemStyle: {
          color: isDark.value ? "#409EFF" : "#409EFF", // 保持主色调一致
        },
        lineStyle: {
          width: 3,
          color: isDark.value ? "#409EFF" : "#409EFF", // 保持主色调一致
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: isDark.value
                ? "rgba(64, 158, 255, 0.5)"
                : "rgba(64, 158, 255, 0.5)",
            },
            {
              offset: 1,
              color: isDark.value
                ? "rgba(64, 158, 255, 0.05)"
                : "rgba(64, 158, 255, 0.05)",
            },
          ]),
        },
      },
    ],
  });
};

// 请求接口获取数据并处理
const fetchData = async () => {
  try {
    // 构建参数
    const params: { year: number; quarter?: number } = {
      year: selectedYear.value,
    };
    if (selectedQuarter.value) {
      params.quarter = Number(selectedQuarter.value);
    }

    const res = await ProjectAPI.getReportOverview(params);
    fetchProjects({
      year: selectedYear.value,
      quarter: selectedQuarter.value || 0,
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
    }); // 获取项目列表

    const data = Array.isArray(res.data)
      ? res.data
      : Array.isArray(res)
        ? res
        : [];
    projectData.value = data;

    // 补全月份
    let months: string[] = [];
    let totals: number[] = [];

    if (!selectedQuarter.value) {
      // 全年 1-12 月
      for (let m = 1; m <= 12; m++) {
        months.push(`${m}月`);
        const item = data.find((d: any) => d.month === m);
        totals.push(item ? item.total : 0);
      }
    } else {
      // 单季度
      const quarter = parseInt(String(selectedQuarter.value));
      const start = (quarter - 1) * 3 + 1;
      const end = quarter * 3;
      for (let m = start; m <= end; m++) {
        months.push(`${m}月`);
        const item = data.find((d: any) => d.month === m);
        totals.push(item ? item.total : 0);
      }
    }

    renderChart(months, totals);
  } catch (err) {
    console.error("获取项目数据失败", err);
    renderChart([], []);
  }
};

// 监听年份/季度变化触发请求
watch(
  [selectedYear, selectedQuarter],
  () => {
    pagination.page = 1; // 重置分页
    fetchData();
  },
  { immediate: true }
);

// 监听主题变化
watch(isDark, () => {
  // 重新渲染图表以适应新主题
  if (projectData.value) {
    const months: string[] = [];
    const totals: number[] = [];

    if (!selectedQuarter.value) {
      // 全年 1-12 月
      for (let m = 1; m <= 12; m++) {
        months.push(`${m}月`);
        const item = projectData.value.find((d: any) => d.month === m);
        totals.push(item ? item.total : 0);
      }
    } else {
      // 单季度
      const quarter = parseInt(String(selectedQuarter.value));
      const start = (quarter - 1) * 3 + 1;
      const end = quarter * 3;
      for (let m = start; m <= end; m++) {
        months.push(`${m}月`);
        const item = projectData.value.find((d: any) => d.month === m);
        totals.push(item ? item.total : 0);
      }
    }

    renderChart(months, totals);
  }
});
// 获取年份列表
const getYears = async () => {
  try {
    const res = await ProjectAPI.getYears();
    const data = Array.isArray(res.data)
      ? res.data
      : Array.isArray(res)
        ? res
        : [];
    years.value = data;
  } catch (err) {
    throw new Error("获取年份列表失败");
  }
};
// 窗口大小变化处理函数
const handleResize = () => {
  chartInstance?.resize();
};

// 窗口自适应
onMounted(() => {
  getYears();
  // 设置主题观察器
  themeObserver = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains("dark");
  });

  // 开始观察
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["class"],
  });

  window.addEventListener("resize", handleResize);
});

onBeforeUnmount(() => {
  // 停止主题观察
  themeObserver?.disconnect();

  // 销毁图表实例
  chartInstance?.dispose();

  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);
});

// 计算完成率
const calculateRate = (completed: number, total: number) => {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
};

// 统计卡片数据 - 基于新的响应数据计算
const stats = computed(() => {
  if (!projectData.value) {
    return [
      { label: "总项目数", value: 0, color: "blue" },
      { label: "已完成项目", value: 0, color: "green" },
      { label: "待完成项目", value: 0, color: "orange" },
      { label: "项目完成率", value: "0%", color: "purple" },
    ];
  }

  // 定义每项数据的类型
  type ProjectItem = { total: number; completed: number };

  const { total, completed } = (projectData.value as ProjectItem[]).reduce(
    (acc: { total: number; completed: number }, item: ProjectItem) => {
      acc.total += item.total;
      acc.completed += item.completed;
      return acc;
    },
    { total: 0, completed: 0 }
  );
  const pendingCount = total - completed;
  const completionRate = calculateRate(completed, total);

  return [
    { label: "总项目数", value: total, color: "blue" },
    { label: "已完成项目", value: completed, color: "green" },
    { label: "待完成项目", value: pendingCount, color: "orange" },
    { label: "总体完成率", value: `${completionRate}%`, color: "purple" },
  ];
});

// 饼图数据
const chartData = computed(() => ({
  completed: (stats.value[1].value as number) || 0,
  uncompleted: (stats.value[2].value as number) || 0,
}));

// 格式化日期显示
const formatDate = (date: string | Date | null): string => {
  if (!date) return "未设置";
  try {
    const d = new Date(date);
    return d.toLocaleDateString("zh-CN");
  } catch (error) {
    console.error("日期格式化错误:", error);
    return "格式错误";
  }
};

// 分页处理函数
const handleSizeChange = (newSize: number) => {
  pagination.pageSize = newSize;
  pagination.page = 1; // 重置到第一页
  refreshProjectList();
};

const handleCurrentChange = (newPage: number) => {
  pagination.page = newPage;
  refreshProjectList();
};

// 刷新项目列表
const refreshProjectList = () => {
  fetchProjects({
    year: selectedYear.value,
    quarter: selectedQuarter.value || 0,
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
  });
};

// 刷新概览数据
const fetchOverviewData = () => {
  fetchData();
};

// 根据状态获取标签类型
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    未开始: "info",
    已开始: "warning",
    已完成: "success",
    已逾期: "danger",
    进行中: "warning",
  };
  return statusMap[status] || "default";
};

// 捕获图表为图片
const captureChart = async (
  element: HTMLElement | null
): Promise<Uint8Array | null> => {
  if (!element) return null;

  try {
    const canvas = await html2canvas(element, {
      backgroundColor: null,
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
    });

    // 将 canvas 转换为 Blob，然后转换为 Uint8Array
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          const reader = new FileReader();
          reader.onload = () => {
            const arrayBuffer = reader.result as ArrayBuffer;
            resolve(new Uint8Array(arrayBuffer));
          };
          reader.readAsArrayBuffer(blob);
        } else {
          resolve(null);
        }
      }, "image/png");
    });
  } catch (error) {
    console.error("捕获图表失败:", error);
    return null;
  }
};

// 创建项目表格
const createProjectTable = (): Table => {
  const tableRows = [
    // 表头
    new TableRow({
      children: [
        new TableCell({
          children: [
            new Paragraph({
              children: [new TextRun({ text: "项目名称", bold: true })],
            }),
          ],
          width: { size: 25, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [
            new Paragraph({
              children: [new TextRun({ text: "负责人", bold: true })],
            }),
          ],
          width: { size: 15, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [
            new Paragraph({
              children: [new TextRun({ text: "开始日期", bold: true })],
            }),
          ],
          width: { size: 20, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [
            new Paragraph({
              children: [new TextRun({ text: "结束日期", bold: true })],
            }),
          ],
          width: { size: 20, type: WidthType.PERCENTAGE },
        }),
        new TableCell({
          children: [
            new Paragraph({
              children: [new TextRun({ text: "状态", bold: true })],
            }),
          ],
          width: { size: 20, type: WidthType.PERCENTAGE },
        }),
      ],
    }),
  ];

  // 添加数据行
  projectList.value.forEach((project) => {
    tableRows.push(
      new TableRow({
        children: [
          new TableCell({
            children: [
              new Paragraph({ children: [new TextRun(project.name || "")] }),
            ],
          }),
          new TableCell({
            children: [
              new Paragraph({ children: [new TextRun(project.owner || "")] }),
            ],
          }),
          new TableCell({
            children: [
              new Paragraph({
                children: [new TextRun(formatDate(project.startDate))],
              }),
            ],
          }),
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun(
                    project.endDate
                      ? formatDate(
                          new Date(
                            new Date(project.endDate).getTime() - 86400000
                          )
                        )
                      : "未设置"
                  ),
                ],
              }),
            ],
          }),
          new TableCell({
            children: [
              new Paragraph({ children: [new TextRun(project.status || "")] }),
            ],
          }),
        ],
      })
    );
  });

  return new Table({
    rows: tableRows,
    width: {
      size: 100,
      type: WidthType.PERCENTAGE,
    },
  });
};

// 生成 Word 报告
const generateWordReport = async () => {
  try {
    reportLoading.value = true;
    ElMessage.info("正在生成报告，请稍候...");

    // 等待 DOM 更新
    await nextTick();

    // 创建文档内容数组
    const docChildren = [];

    // 标题
    docChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `项目状态报告 - ${selectedYear.value}年${selectedQuarter.value ? `第${selectedQuarter.value}季度` : ""}`,
            bold: true,
            size: 32,
          }),
        ],
        alignment: AlignmentType.CENTER,
        heading: HeadingLevel.HEADING_1,
      })
    );

    // 空行
    docChildren.push(new Paragraph({ children: [new TextRun("")] }));

    // 概述信息
    docChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: "一、项目概述",
            bold: true,
            size: 24,
          }),
        ],
        heading: HeadingLevel.HEADING_2,
      })
    );

    docChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `${selectedYear.value}年${selectedQuarter.value ? `第${selectedQuarter.value}季度` : ""}期间，累计项目数 ${stats.value[0].value} 个，完成项目数 ${stats.value[1].value} 个，待完成项目数 ${stats.value[2].value} 个，总体完成率 ${stats.value[3].value}。`,
            size: 20,
          }),
        ],
      })
    );

    // 空行
    docChildren.push(new Paragraph({ children: [new TextRun("")] }));

    // 尝试捕获并添加图表
    try {
      // 1. 捕获折线图
      const lineChartImage = await captureChart(chartRef.value || null);

      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "二、项目数量趋势",
              bold: true,
              size: 24,
            }),
          ],
          heading: HeadingLevel.HEADING_2,
        })
      );

      if (lineChartImage) {
        docChildren.push(
          new Paragraph({
            children: [
              new ImageRun({
                data: lineChartImage,
                transformation: {
                  width: 600,
                  height: 300,
                },
              } as any),
            ],
            alignment: AlignmentType.CENTER,
          })
        );
      } else {
        docChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "（图表捕获失败，请查看系统界面获取图表信息）",
                italics: true,
                size: 16,
              }),
            ],
          })
        );
      }

      docChildren.push(new Paragraph({ children: [new TextRun("")] }));
    } catch (error) {
      console.warn("折线图处理失败:", error);
      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "项目数量趋势",
              bold: true,
              size: 24,
            }),
          ],
          heading: HeadingLevel.HEADING_2,
        })
      );
      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "（图表处理失败，请查看系统界面获取图表信息）",
              italics: true,
              size: 16,
            }),
          ],
        })
      );
      docChildren.push(new Paragraph({ children: [new TextRun("")] }));
    }

    try {
      // 2. 捕获饼图
      const pieChartElement = document.querySelector(".chart") as HTMLElement;
      const pieChartImage = await captureChart(pieChartElement);

      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "三、项目完成状态分布",
              bold: true,
              size: 24,
            }),
          ],
          heading: HeadingLevel.HEADING_2,
        })
      );

      if (pieChartImage) {
        docChildren.push(
          new Paragraph({
            children: [
              new ImageRun({
                data: pieChartImage,
                transformation: {
                  width: 400,
                  height: 300,
                },
              } as any),
            ],
            alignment: AlignmentType.CENTER,
          })
        );
      } else {
        docChildren.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "（图表捕获失败，请查看系统界面获取图表信息）",
                italics: true,
                size: 16,
              }),
            ],
          })
        );
      }

      docChildren.push(new Paragraph({ children: [new TextRun("")] }));
    } catch (error) {
      console.warn("饼图处理失败:", error);
      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "四、项目完成状态分布",
              bold: true,
              size: 24,
            }),
          ],
          heading: HeadingLevel.HEADING_2,
        })
      );
      docChildren.push(
        new Paragraph({
          children: [
            new TextRun({
              text: "（图表处理失败，请查看系统界面获取图表信息）",
              italics: true,
              size: 16,
            }),
          ],
        })
      );
      docChildren.push(new Paragraph({ children: [new TextRun("")] }));
    }

    // 项目详细列表
    docChildren.push(
      new Paragraph({
        children: [
          new TextRun({
            text: "项目详细列表",
            bold: true,
            size: 24,
          }),
        ],
        heading: HeadingLevel.HEADING_2,
      })
    );

    // 创建表格
    docChildren.push(createProjectTable());

    // 3. 创建 Word 文档
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: docChildren,
        },
      ],
    });

    // 4. 生成并下载文档
    const blob = await Packer.toBlob(doc);
    const fileName = `项目状态报告_${selectedYear.value}年${selectedQuarter.value ? `第${selectedQuarter.value}季度` : ""}_${new Date().toISOString().slice(0, 10)}.docx`;
    saveAs(blob, fileName);

    ElMessage.success("报告生成成功！");
  } catch (error) {
    console.error("生成报告失败:", error);
    ElMessage.error("生成报告失败，请重试");
  } finally {
    reportLoading.value = false;
  }
};
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 400px;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s;
}

.filter-bar {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  padding: 16px;
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
}

/* 概述卡片样式 */
.overview-card {
  width: 100%;
  transition: all 0.3s;
}

.overview-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 10px 0;
}

.overview-year {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-right: 10px;
}

.overview-quarter {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-right: 10px;
}

.overview-text {
  font-size: 16px;
  color: var(--el-text-color-regular);
}

.overview-num {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
  margin: 0 5px;
}

.overview-num.success {
  color: var(--el-color-success);
}

.overview-num.warning {
  color: var(--el-color-warning);
}

.overview-num.rate {
  color: var(--el-color-danger);
}

.card-content {
  display: flex;
  padding: 20px;
  height: 100%;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  transition: all 0.3s;
}

.card-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-type {
  font-size: 16px;
  color: var(--el-text-color-secondary);
}

.card-count {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 5px 0;
}

.project-card {
  width: 100%;
  margin: 10px;
  background-color: var(--el-bg-color); /* 使用 Element Plus 变量 */
  border: 1px solid var(--el-border-color-light); /* 使用 Element Plus 变量 */
  transition: all 0.3s;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--el-border-color-lighter); /* 使用 Element Plus 变量 */
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary); /* 使用 Element Plus 变量 */
}

/* 适配暗色主题下的表格 */
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
  --el-table-row-hover-bg-color: var(--el-fill-color);
}

/* 适配暗色主题下的卡片 */
:deep(.el-card) {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-card__body) {
  padding: 20px;
  background-color: var(--el-bg-color);
}

/* 分页组件样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
  background-color: var(--el-bg-color);
}

:deep(.el-pagination) {
  --el-pagination-bg-color: var(--el-bg-color);
  --el-pagination-text-color: var(--el-text-color-primary);
}
</style>
