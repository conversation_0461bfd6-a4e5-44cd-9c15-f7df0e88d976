<template>
  <el-dialog v-model="props.visible" title="物联网设备详情" width="75%" :before-close="handleClose">
    <el-tabs type="border-card">
      <!-- 物联网设备基本信息 -->
      <el-tab-pane label="基本信息">
        <div class="detail-section">
          <div class="section-title">设备基本信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="设备名称">
              {{ assetDetail.name || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备类型">
              {{ assetDetail.deviceType || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备型号">
              {{ assetDetail.model || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="设备序列号">
              {{ assetDetail.serialNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="制造商">
              {{ assetDetail.factory || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="购买日期">
              {{ formatDate(assetDetail.purchaseDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="维保截止日期">
              {{ formatDate(assetDetail.warrantyEndDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="登记时间">
              {{ formatDate(assetDetail.createTime) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <div class="section-title">设备技术参数</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="硬件配置">
              {{ assetDetail.hardwareConfig || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="网络接口类型">
              {{ assetDetail.networkInterfaceType || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
              {{ assetDetail.ip || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="MAC地址">
              {{ assetDetail.macAddress || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="固件版本" :span="2">
              {{ assetDetail.firmwareVersion || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <div class="section-title">设备位置信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="安装位置">
              {{ assetDetail.local || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="地理坐标">
              {{ assetDetail.gpsCoordinates || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <div class="section-title">设备使用信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="使用状态">
              <el-tag 
                :type="getStatusTagType(assetDetail.usageStatus)"
                effect="light"
              >
                {{ assetDetail.usageStatus || '-' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="关联设备">
              {{ assetDetail.relatedDevices || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ assetDetail.notes || assetDetail.remark || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>

      <!-- 管理单位信息 -->
      <el-tab-pane label="管理单位">
        <div class="detail-section">
          <div class="section-title">管理单位信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="管理单位">{{ getDeptName(assetDetail.deptId) }}</el-descriptions-item>
            <el-descriptions-item label="办公地址">{{ assetDetail.depAddress || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 管理领导信息 -->
        <div class="detail-section">
          <div class="section-title">管理领导信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="领导姓名">{{ assetDetail.leader || '-' }}</el-descriptions-item>
            <el-descriptions-item label="办公电话">{{ assetDetail.leaderPhone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ assetDetail.leaderPhone1 || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ assetDetail.leaderEmail || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 系统管理员信息 -->
        <div class="detail-section">
          <div class="section-title">系统管理员信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="管理员姓名">{{ assetDetail.ownerName || '-' }}</el-descriptions-item>
            <el-descriptions-item label="办公电话">{{ assetDetail.ownerPhone1 || '-' }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ assetDetail.ownerPhone || '-' }}</el-descriptions-item>
            <el-descriptions-item label="联系邮箱">{{ assetDetail.ownerEmail || '-' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>

      <!-- 运维单位信息 -->
      <el-tab-pane label="运维单位">
        <div class="detail-section">
          <div class="section-title">运维服务商信息</div>
          <div v-if="assetDetail.provider">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="公司名称" :span="2">
                {{ assetDetail.provider.providerName || assetDetail.otherFactory || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="项目负责人">
                {{ assetDetail.provider.projectManager || assetDetail.otherManager || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="联系电话">
                {{ assetDetail.provider.managerMobile || assetDetail.otherContact || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="联系邮箱" :span="2">
                {{ assetDetail.provider.managerEmail || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="技术负责人">
                {{ assetDetail.provider.techLeader || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="技术电话">
                {{ assetDetail.provider.techMobile || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="技术邮箱" :span="2">
                {{ assetDetail.provider.techEmail || '-' }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <el-empty v-else description="暂无运维服务商信息" />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watchEffect } from 'vue'
import { ElMessage } from 'element-plus'
import assetsAPI from '@/api/assets_management/details/assets'
import { useDictStore } from '@/store/modules/dictStore'
import UserAPI from '@/api/user'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  assetId: {
    type: Number
  }
})

const emit = defineEmits(['update:visible'])

// 获取字典Store
const dictStore = useDictStore()

// 资产详情
const assetDetail = ref<any>({})
const loading = ref(false)

// 字典映射
const deptMap = ref<Record<string | number, string>>({})

// 获取资产详情
async function fetchAssetDetail(id: number) {
  if (!id) return
  
  try {
    loading.value = true
    const data = await assetsAPI.getServerDetail(id)
    assetDetail.value = data
    
    // 加载字典映射
    await loadDictMappings()
    
    // 加载用户信息
    await loadUserInformation()
    
  } catch (error) {
    console.error('获取物联网设备详情失败:', error)
    ElMessage.error('获取物联网设备详情失败')
  } finally {
    loading.value = false
  }
}

// 获取用户详细信息
async function loadUserInformation() {
  try {
    if (assetDetail.value) {
      // 加载系统管理员信息
      if (assetDetail.value.sysManagerId && (!assetDetail.value.ownerName || !assetDetail.value.ownerPhone)) {
        const adminData = await UserAPI.getFormData(assetDetail.value.sysManagerId)
        if (adminData) {
          assetDetail.value.ownerName = assetDetail.value.ownerName || adminData.nickname || adminData.username
          assetDetail.value.ownerPhone = assetDetail.value.ownerPhone || adminData.mobile
          assetDetail.value.ownerPhone1 = assetDetail.value.ownerPhone1 || adminData.officePhone
          assetDetail.value.ownerEmail = assetDetail.value.ownerEmail || adminData.email
        }
      }
      
      // 加载管理领导信息
      if (assetDetail.value.managerId && (!assetDetail.value.leader || !assetDetail.value.leaderPhone)) {
        const leaderData = await UserAPI.getFormData(assetDetail.value.managerId)
        if (leaderData) {
          assetDetail.value.leader = assetDetail.value.leader || leaderData.nickname || leaderData.username
          assetDetail.value.leaderPhone = assetDetail.value.leaderPhone || leaderData.officePhone
          assetDetail.value.leaderPhone1 = assetDetail.value.leaderPhone1 || leaderData.mobile
          assetDetail.value.leaderEmail = assetDetail.value.leaderEmail || leaderData.email
        }
      }

      //服务商
      if (assetDetail.value.provider) {
          assetDetail.value.provider = assetDetail.value.provider
      }
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载字典映射
async function loadDictMappings() {
  try {
    const deptOptions = await dictStore.fetchOptions('dept0x0')
    processDeptTree(deptOptions || [])
  } catch (error) {
    console.error('加载字典映射失败:', error)
  }
}

// 处理部门树数据
function processDeptTree(depts: any[]) {
  if (!Array.isArray(depts)) return
  
  depts.forEach(dept => {
    if (dept.value !== undefined && dept.label) {
      deptMap.value[dept.value] = dept.label
      if (typeof dept.value === 'number') {
        deptMap.value[String(dept.value)] = dept.label
      }
    }
    
    if (dept.children && dept.children.length > 0) {
      processDeptTree(dept.children)
    }
  })
}

// 获取部门名称
function getDeptName(deptId: string | number): string {
  if (!deptId) return '-'
  return deptMap.value[deptId] || `部门${deptId}`
}

// 格式化日期
function formatDate(dateStr: string | null | undefined): string {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString()
  } catch (e) {
    return dateStr
  }
}

// 获取状态标签类型
function getStatusTagType(status: string): string {
  const statusMap: Record<string, string> = {
    '使用中': 'success',
    '在线使用': 'success',
    '闲置': 'info',
    '离线': 'warning',
    '维护中': 'warning',
    '维修': 'warning',
    '停用': 'danger',
    '故障': 'danger',
    '报废': 'danger'
  }
  return statusMap[status] || 'info'
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}

// 监听ID变化，加载资产详情
watchEffect(() => {
  if (props.visible && props.assetId) {
    fetchAssetDetail(props.assetId)
  }
})
</script>

<style scoped>
.detail-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-light);
}

.section-title {
  margin: 0 0 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-color-primary);
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
}

:deep(.el-descriptions) {
  --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
}

:deep(.el-descriptions__body) {
  background-color: var(--el-bg-color);
}

:deep(.el-descriptions__label) {
  width: 140px;
  color: var(--el-text-color-regular);
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
}

:deep(.el-tabs__content) {
  padding: 20px;
  background-color: var(--el-bg-color);
}

:deep(.el-tab-pane) {
  max-height: 65vh;
  overflow-y: auto;
}
</style>
