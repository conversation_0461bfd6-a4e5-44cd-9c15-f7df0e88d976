<template>
  <div class="asset-dashboard">
    <el-row :gutter="20">
      <el-col :span="24">
        <h1 class="dashboard-title">资产仪表盘</h1>
      </el-col>
    </el-row>

    <!-- 资产总览卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6" v-for="(count, type) in assetCounts" :key="type">
        <el-card shadow="hover" class="asset-card">
          <div class="asset-content">
            <div class="asset-icon">
              <svg-icon :icon-class="getAssetIcon(type)" size="2em" />
              <el-icon :size="40">
                <component :is="getAssetIcon(type)"></component>
              </el-icon>
            </div>
            <div class="asset-info">
              <div class="asset-type">{{ type }}</div>
              <div class="asset-count">{{ count }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产趋势图表 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产增长趋势</span>
              <el-radio-group v-model="trendTimeRange" size="small">
                <!-- <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="quarter">季度</el-radio-button> -->
                <el-radio-button label="year">年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产分布饼图 -->
    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产类型分布</span>
            </div>
          </template>
          <div class="chart-container" ref="distributionChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>资产状态分布</span>
            </div>
          </template>
          <div class="chart-container" ref="statusChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch } from 'vue';
import * as echarts from 'echarts';
import DashboardAPI from "@/api/dashboard";

const assetCounts = reactive({
  '服务器': 0,
  '安全设备': 0,
  '网络设备': 0,
});

const trendTimeRange = ref('month');
const trendChartRef = ref<HTMLElement | null>(null);
const distributionChartRef = ref<HTMLElement | null>(null);
const statusChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;
let distributionChart: echarts.ECharts | null = null;
let statusChart: echarts.ECharts | null = null;

const getAssetIcon = (type: string) => {
  const icons = {
    '信息系统': 'el-icon-cpu',
    '服务器': 'el-icon-cpu',
    '安全设备': 'el-icon-lock',
    '网络设备': 'el-icon-connection',
  };
  return icons[type as keyof typeof icons] || 'el-icon-box';
};

const initTrendChart = (trendMonths: string[], assetTrends: any) => {
  if (trendChartRef.value) {
    trendChart = echarts.init(trendChartRef.value);
    const option = {
      title: {
        text: '资产增长趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['信息系统', '服务器', '安全设备', '网络设备']
      },
      xAxis: {
        type: 'category',
        data: trendMonths
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '信息系统',
          type: 'line',
          data: trendMonths.map(month => assetTrends['10'][month] || 0)
        },
        {
          name: '服务器',
          type: 'line',
          data: trendMonths.map(month => assetTrends['1'][month] || 0)
        },
        {
          name: '安全设备',
          type: 'line',
          data: trendMonths.map(month => assetTrends['3'][month] || 0)
        },
        {
          name: '网络设备',
          type: 'line',
          data: trendMonths.map(month => assetTrends['2'][month] || 0)
        }
      ]
    };
    trendChart.setOption(option);
  }
};

const initDistributionChart = (assetCounts: any) => {
  if (distributionChartRef.value) {
    distributionChart = echarts.init(distributionChartRef.value);
    const option = {
      title: {
        text: '资产类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '资产类型',
          type: 'pie',
          radius: '50%',
          data: Object.keys(assetCounts).map(type => ({
            value: assetCounts[type],
            name: type
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    distributionChart.setOption(option);
  }
};

const initStatusChart = (assetStatuses: any) => {
  if (statusChartRef.value) {
    statusChart = echarts.init(statusChartRef.value);
    const option = {
      title: {
        text: '资产状态分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '资产状态',
          type: 'pie',
          radius: '50%',
          data: assetStatuses.map((status: any) => ({
            value: status.count,
            name: status.status
          })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    statusChart.setOption(option);
  }
};

function handleQuery() {
  DashboardAPI.getAssetDashboard()
    .then((data) => {
      Object.assign(assetCounts, data.assetCounts);
      initTrendChart(data.trendMonths, data.assetTrends);
      initDistributionChart(data.assetCounts);
      initStatusChart(data.assetStatuses);
    });
}

watch(trendTimeRange, () => {
  // 这里可以根据选择的时间范围更新趋势图的数据
  // 为演示目的，我们只是重新初始化图表
  handleQuery();
});

onMounted(() => {
  handleQuery();

  window.addEventListener('resize', () => {
    trendChart?.resize();
    distributionChart?.resize();
    statusChart?.resize();
  });
});
</script>

<style scoped>
.asset-dashboard {
  padding: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #303133;
}

.asset-card {
  height: 120px;
  overflow: hidden;
}

.asset-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.asset-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 16px;
}

.asset-info {
  flex-grow: 1;
}

.asset-type {
  font-size: 16px;
  color: #909399;
  margin-bottom: 8px;
}

.asset-count {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.chart-container {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 为每种资产类型设置不同的背景色 */
.el-col:nth-child(1) .asset-icon { background-color: #409EFF; }
.el-col:nth-child(2) .asset-icon { background-color: #67C23A; }
.el-col:nth-child(3) .asset-icon { background-color: #E6A23C; }
.el-col:nth-child(4) .asset-icon { background-color: #F56C6C; }

.asset-icon :deep(svg) {
  color: #ffffff;
}
</style>
