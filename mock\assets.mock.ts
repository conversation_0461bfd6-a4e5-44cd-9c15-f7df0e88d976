import { defineMock } from "./base";

const assets: Asset[] = [
  {
    id: 1,
    name: "资产1",
    type: "服务器",
    IP: "***********",
    url: "http://example.com",
    port: 8080,
    ownerName: "张三",
    deptId: "IT部门", //注意我传过来的是部门ID，请把ID改成部门名称存数据库
    systemId: 1,
    status: 1, //注意这里的状态是数字，1表示正常，0表示异常
    createTime: "2023-01-01",
    ownerphone: "123456789",
    os: "Windows",
    notes: "备注信息1",
  },
  {
    id: 2,
    name: "资产2",
    type: "数据库",
    IP: "***********",
    url: "http://example2.com",
    port: 3306,
    ownerName: "李四",
    deptId: "运维部门", //注意我传过来的是部门ID，请把ID改成部门名称存数据库
    systemId: 2,
    status: 0, //注意这里的状态是数字，1表示正常，0表示异常
    createTime: "2023-02-01",  //注意实时生成一下创建资产的时间
    ownerphone: "123456789",
    os: "Linux",
    notes: "备注信息2",
  },
];

interface Asset {
  id: number;
  name: string;
  type: string;
  IP: string;
  url: string;
  keywords?: string;
  port: number;
  ownerName: string;
  deptId: string;
  ownerphone: string;
  systemId: number;
  status: number;
  createTime: string; //注意实时生成一下创建资产的时间
  os: string;
  notes: string;
}

export default defineMock([
  {
    url: "assets/assetsAll",
    method: ["GET"],
    body: {
      code: "00000",
      data: assets,
      msg: "一切ok",
    },
  },
  {
    url: "assets",
    method: ["POST"],
    body({ body }: { body: Asset }) {
      const newAsset = { ...body, id: assets.length + 1 };
      assets.push(newAsset);
      return {
        code: "00000",
        data: newAsset,
        msg: "新增资产成功",
      };
    },
  },
  {
    url: "assets/:id",
    method: ["PUT"],
    body({ params, body }: { params: { id: number }; body: Partial<Asset> }) {
      const { id } = params;
      const index = assets.findIndex((asset) => asset.id == id);
      if (index !== -1) {
        assets[index] = { ...assets[index], ...body };
        return {
          code: "00000",
          data: assets[index],
          msg: "修改资产成功",
        };
      } else {
        return {
          code: "404",
          msg: "资产未找到",
        };
      }
    },
  },
  {
    url: "assets/:id",
    method: ["DELETE"],
    body({ params }: { params: { id: number } }) {
      const { id } = params;
      const index = assets.findIndex((asset) => asset.id == id);
      if (index !== -1) {
        assets.splice(index, 1);
        return {
          code: "00000",
          data: null,
          msg: "删除资产成功",
        };
      } else {
        return {
          code: "404",
          msg: "资产未找到",
        };
      }
    },
  },
  {
    url: "assets/:id/form",
    method: ["GET"],
    body({ params }: { params: { id: number } }) {
      const { id } = params;
      const asset = assets.find((asset) => asset.id == id);
      if (asset) {
        return {
          code: "00000",
          data: asset,
          msg: "一切ok",
        };
      } else {
        return {
          assets:asset,
          code: "404",
          msg: "资产未找到",
        };
      }
    },
  },
  {
    url: "assets/:ids",
    method: ["DELETE"],
    body({ params }: { params: { ids: string } }) {
      const { ids } = params;
      const idArray = ids.split(",").map(Number);
      idArray.forEach((id) => {
        const index = assets.findIndex((asset) => asset.id == id);
        if (index !== -1) {
          assets.splice(index, 1);
        }
      });
      return {
        code: "00000",
        data: null,
        msg: "批量删除成功",
      };
    },
  },
  {
    url: "assets/template",
    method: ["GET"],
    body: {
      code: "00000",
      data: "模板文件内容",
      msg: "一切ok",
    },
  },
  {
    url: "assets/import",
    method: ["POST"],
    body: {
      code: "00000",
      data: null,
      msg: "导入成功",
    },
  },
  {
    url: "assets/export",
    method: ["GET"],
    body: {
      code: "00000",
      data: "导出文件内容",
      msg: "一切ok",
    },
  },
  {
    url: "assets",
    method: ["GET"],
    body({ query }: { query: Partial<Asset> }) {
      const { keywords, type, systemId, status, port, os, createTime } = query;
      let filteredAssets = assets;
  
      if (keywords) {
        filteredAssets = filteredAssets.filter((asset) =>
          asset.name.includes(keywords) ||
          asset.ownerName.includes(keywords) ||
          asset.ownerphone.includes(keywords) ||
          asset.IP.includes(keywords)
        );
      }
  
      if (type) {
        filteredAssets = filteredAssets.filter((asset) => asset.type.includes(type));
      }
  
      if (status !== undefined) {
        filteredAssets = filteredAssets.filter((asset) => asset.status == status);
      }
  
      if (port) {
        filteredAssets = filteredAssets.filter((asset) => asset.port == Number(port));
      }
  
      if (os) {
        filteredAssets = filteredAssets.filter((asset) => asset.os.includes(os));
      }
      
      if (systemId) {
        filteredAssets = filteredAssets.filter((asset) => asset.systemId == systemId);
      }

      if (createTime) {
        const [start, end] = createTime;
        filteredAssets = filteredAssets.filter((asset) => {
          const assetTime = new Date(asset.createTime);
          return assetTime >= new Date(start) && assetTime <= new Date(end);
        });
      }
  
      return {
        code: "00000",
        data: {
          total: filteredAssets.length,
          list: filteredAssets,
        },
        msg: "一切ok",
      };
    },
  }
]);
