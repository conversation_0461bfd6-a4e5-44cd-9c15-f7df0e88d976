<template>
  <base-process-group ref="baseProcessGroupRef" :business-type="businessType" :departments="departments"
    :sms-templates="smsTemplates" :dept-type-map="deptTypeMap" @update:departments="handleDepartmentsUpdate" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import BaseProcessGroup from './BaseProcessGroup.vue'

// 定义接口
interface Department {
  id: number
  name: string
  active: boolean
  execution?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
  notification?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
}

interface UserInfo {
  id: number | string
  username: string
  nickname: string
  deptName: string
  mobile: string
}

// 业务类型
const businessType = 'offline'

// 部门类型映射
const deptTypeMap = {
  0: '1', // 下线审核
  1: '2'  // 工单评价
}

// 流程步骤部门数据
const departments = ref<Department[]>([
  { id: 1, name: '下线审核', active: true },
  { id: 2, name: '工单评价', active: false }
])

// 短信模板数据
const smsTemplates = ref([
  { id: 1, name: '下线申请通知模板', content: '尊敬的${name}，您有一项资产下线申请需要处理，请及时查看。' },
  { id: 2, name: '下线审核通知模板', content: '尊敬的${name}，您有一项资产下线审核任务需要完成，请及时处理。' }
])

// 基础组件引用
const baseProcessGroupRef = ref()

// 处理部门更新
const handleDepartmentsUpdate = (updatedDepartments: Department[]) => {
  departments.value = updatedDepartments
}

// 获取流程数据
const getProcessData = () => {
  return baseProcessGroupRef.value?.getProcessData()
}

// 获取验证状态
const getValidationStatus = () => {
  return baseProcessGroupRef.value?.getValidationStatus()
}

// 初始化现有数据
const initFromExistingData = (data: any) => {
  baseProcessGroupRef.value?.initFromExistingData(data)
}

// 重置组件
const reset = () => {
  baseProcessGroupRef.value?.reset()
}

const applyDefaultConfig = () => {
  return baseProcessGroupRef.value?.applyDefaultConfig()
}

// 暴露方法
defineExpose({
  getProcessData,
  getValidationStatus,
  initFromExistingData,
  reset,
  applyDefaultConfig
})
</script>
