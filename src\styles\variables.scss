/** 全局SCSS变量 */

:root {
  --menu-background: #ffffff;
  --menu-text: #0f0e0e;
  --menu-active-text: var(--el-menu-active-color);
  --menu-hover: #edf1fe;
  --sidebar-logo-background: #ffffff;
  --app-bg-color: #f7f7f7;
  // 全局字体设置
  --font-family:
    "Alibaba PuHuiTi", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei",
    "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
  // 修复表格 fixed 列被选中后由于透明色导致叠字的 bug
  .el-table {
    --el-table-current-row-bg-color: rgb(235 243 250);
  }
}

/** 暗黑主题 */
html.dark {
  --menu-background: var(--el-bg-color-overlay);
  --menu-text: #ffffff;
  --menu-active-text: var(--el-menu-active-color);
  --menu-hover: rgb(0 0 0 / 20%);
  --sidebar-logo-background: rgb(0 0 0 / 20%);
  --app-bg-color: #181a20; // 暗色主题下的背景色
}

$menu-background: var(--menu-background); // 菜单背景色
$menu-text: var(--menu-text); // 菜单文字颜色
$menu-active-text: var(--menu-active-text); // 菜单激活文字颜色
$menu-hover: var(--menu-hover); // 菜单悬停背景色
$sidebar-logo-background: var(--sidebar-logo-background); // 侧边栏 Logo 背景色

$sidebar-width: 210px; // 侧边栏宽度
$sidebar-width-collapsed: 50px; // 侧边栏收缩宽度
$navbar-height: 50px; // 导航栏高度
$tags-view-height: 34px; // TagsView 高度
