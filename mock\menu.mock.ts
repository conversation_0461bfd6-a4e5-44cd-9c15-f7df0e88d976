import { defineMock } from "./base";

export default defineMock([
  {
    url: "menus/routes",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          path: "/system",
          component: "Layout",
          redirect: "/system/user",
          name: "/system",
          meta: {
            title: "系统管理",
            icon: "system",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            // {
            //   path: "sms",
            //   component: "sms/index",
            //   name: "Sms",
            //   meta: {
            //     title: "短信管理",
            //     icon: "el-icon-User",
            //     hidden: false,
            //     keepAlive: true,
            //     alwaysShow: false,
            //     params: null,
            //   },
            // },
            {
              path: "user",
              component: "system/user/index",
              name: "User",
              meta: {
                title: "用户管理",
                icon: "el-icon-User",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "role",
              component: "system/role/index",
              name: "Role",
              meta: {
                title: "角色管理",
                icon: "role",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "menu",
              component: "system/menu/index",
              name: "Menu",
              meta: {
                title: "菜单管理",
                icon: "menu",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dept",
              component: "system/dept/index",
              name: "Dept",
              meta: {
                title: "部门管理",
                icon: "tree",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict",
              component: "system/dict/index",
              name: "Dict",
              meta: {
                title: "字典管理",
                icon: "dict",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "log",
              component: "system/log/index",
              name: "Log",
              meta: {
                title: "日志管理",
                icon: "log",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "sms",
              component: "system/sms/index",
              name: "Sms",
              meta: {
                title: "短信管理",
                icon: "el-icon-Message",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          //new
          path: "/assets_management",
          component: "Layout",
          name: "/assetsManagement",
          meta: {
            title: "资产管理",
            icon: "cascader",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "details",
              component: "assets_management/details/index",
              name: "Details",
              meta: {
                title: "资产详情",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "system",
              component: "assets_management/system/index",
              name: "System",
              meta: {
                title: "系统管理",
                icon: "el-icon-ElemeFilled",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "online-service",
              component: "assets_management/assetsOffiline/leve1",
              name: "assetsOffline",
              meta: {
                title: "资产下线",
                icon: "el-icon-ElemeFilled",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          //new
          path: "/work_management",
          component: "Layout",
          name: "/workManagement",
          meta: {
            title: "日常工作管理",
            icon: "el-icon-Sunrise",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "safety",
              component: "work_management/safety/index",
              name: "safety",
              meta: {
                title: "安全问题管理 ",
                icon: "el-icon-Discount",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "online-service",
              component: "work_management/online-service/index",
              name: "onlineService",
              meta: {
                title: "业务上线管理",
                icon: "el-icon-Briefcase",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "critical",
              component: "work_management/critical/index",
              name: "critical",
              meta: {
                title: "重保开放申请管理",
                icon: "el-icon-TrophyBase",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "announcement",
              component: "work_management/announcement/index",
              name: "announcement",
              meta: {
                title: "安全公告管理",
                icon: "el-icon-AlarmClock",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "evaluate",
              component: "work_management/evaluate/index",
              name: "evaluate",
              meta: {
                title: "安全评价管理",
                icon: "el-icon-CoffeeCup",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          //new
          path: "/report_management",
          component: "Layout",
          name: "/reportManagement",
          meta: {
            title: "报表管理",
            icon: "el-icon-Document",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "details",
              component: "report_management/details/index",
              name: "details",
              meta: {
                title: "报表详情",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        // 等级保护定级管理
        {
          path: "/grade_protection",
          component: "Layout",
          name: "gradeProtection",
          meta: {
            title: "等保定级管理",
            icon: "menu",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "grading",
              component: "grade_protection/grading/index",
              name: "Grading",
              meta: {
                title: "等级保护定级",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "record",
              component: "grade_protection/record/index",
              name: "Record",
              meta: {
                title: "定级备案管理",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "report",
              component: "grade_protection/report/index",
              name: "Report",
              meta: {
                title: "测评报告管理",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "cyberkb",
              component: "grade_protection/cyberkb/index",
              name: "Cyberkb",
              meta: {
                title: "网络安全知识库",
                icon: "el-icon-Reading",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/api",
          component: "Layout",
          name: "/api",
          meta: {
            title: "接口文档",
            icon: "api",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "apifox",
              component: "demo/api/apifox",
              name: "Apifox",
              meta: {
                title: "Apifox",
                icon: "api",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/doc",
          component: "Layout",
          redirect: "https://juejin.cn/post/7228990409909108793",
          name: "/doc",
          meta: {
            title: "平台文档",
            icon: "document",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "internal-doc",
              component: "demo/internal-doc",
              name: "InternalDoc",
              meta: {
                title: "平台文档(内嵌)",
                icon: "document",
                hidden: false,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "https://juejin.cn/post/7228990409909108793",
              name: "Https://juejin.cn/post/7228990409909108793",
              meta: {
                title: "平台文档(外链)",
                icon: "el-icon-Link",
                hidden: false,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/multi-level",
          component: "Layout",
          name: "/multiLevel",
          meta: {
            title: "多级菜单",
            icon: "cascader",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "multi-level1",
              component: "demo/multi-level/level1",
              name: "MultiLevel1",
              meta: {
                title: "菜单一级",
                icon: "",
                hidden: false,
                alwaysShow: true,
                params: null,
              },
              children: [
                {
                  path: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  name: "MultiLevel2",
                  meta: {
                    title: "菜单二级",
                    icon: "",
                    hidden: false,
                    alwaysShow: false,
                    params: null,
                  },
                  children: [
                    {
                      path: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      name: "MultiLevel31",
                      meta: {
                        title: "菜单三级-1",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                    {
                      path: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      name: "MultiLevel32",
                      meta: {
                        title: "菜单三级-2",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          path: "/component",
          component: "Layout",
          name: "/component",
          meta: {
            title: "组件封装",
            icon: "menu",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "curd",
              component: "demo/curd/index",
              name: "Curd",
              meta: {
                title: "增删改查",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "table-select",
              component: "demo/table-select/index",
              name: "TableSelect",
              meta: {
                title: "列表选择器",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "wang-editor",
              component: "demo/wang-editor",
              name: "WangEditor",
              meta: {
                title: "富文本编辑器",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "upload",
              component: "demo/upload",
              name: "Upload",
              meta: {
                title: "图片上传",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "icon-selector",
              component: "demo/icon-selector",
              name: "IconSelector",
              meta: {
                title: "图标选择器",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict-demo",
              component: "demo/dict",
              name: "DictDemo",
              meta: {
                title: "字典组件",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/route-param",
          component: "Layout",
          name: "/routeParam",
          meta: {
            title: "路由参数",
            icon: "el-icon-ElementPlus",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "route-param-type1",
              component: "demo/route-param",
              name: "RouteParamType1",
              meta: {
                title: "参数(type=1)",
                icon: "el-icon-Star",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "1",
                },
              },
            },
            {
              path: "route-param-type2",
              component: "demo/route-param",
              name: "RouteParamType2",
              meta: {
                title: "参数(type=2)",
                icon: "el-icon-StarFilled",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: {
                  type: "2",
                },
              },
            },
          ],
        },
        {
          path: "/function",
          component: "Layout",
          name: "/function",
          meta: {
            title: "功能演示",
            icon: "menu",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "icon-demo",
              component: "demo/icons",
              name: "IconDemo",
              meta: {
                title: "Icons",
                icon: "el-icon-Notification",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "/function/websocket",
              component: "demo/websocket",
              name: "/function/websocket",
              meta: {
                title: "Websocket",
                icon: "",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "other/:id",
              component: "demo/other",
              name: "Other/:id",
              meta: {
                title: "敬请期待...",
                icon: "",
                hidden: false,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  {
    url: "menus",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: 1, // 菜单项的唯一标识符
          parentId: 0, // 父菜单项的标识符，0 表示这是一个顶级菜单项
          name: "系统管理", // 菜单项的名称
          type: "CATALOG", // 菜单项的类型，这里是目录类型
          routeName: "", // 路由名称，通常用于 Vue Router 的命名路由
          routePath: "/system", // 路由路径，表示菜单项对应的 URL 路径
          component: "Layout", // 组件名称，表示菜单项对应的 Vue 组件
          sort: 1, // 排序字段，表示菜单项在同级菜单中的显示顺序
          visible: 1, // 可见性字段，1 表示菜单项是可见的
          icon: "system", // 图标名称，表示菜单项的图标
          redirect: "/system/user", // 重定向路径，表示访问该菜单项时自动重定向到的路径
          perm: null, // 权限字段，表示菜单项的权限标识，null 表示没有特定权限要求
          children: [
            // 子菜单项数组，表示该菜单项的子菜单
            {
              id: 2,
              parentId: 1,
              name: "用户管理",
              type: "MENU",
              routeName: "User",
              routePath: "user",
              component: "system/user/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-User",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 105,
                  parentId: 2,
                  name: "用户查询",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 0,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:query",
                  children: [],
                },
                {
                  id: 31,
                  parentId: 2,
                  name: "用户新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:add",
                  children: [],
                },
                {
                  id: 32,
                  parentId: 2,
                  name: "用户编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:edit",
                  children: [],
                },
                {
                  id: 33,
                  parentId: 2,
                  name: "用户删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:delete",
                  children: [],
                },
                {
                  id: 88,
                  parentId: 2,
                  name: "重置密码",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:password:reset",
                  children: [],
                },
                {
                  id: 106,
                  parentId: 2,
                  name: "用户导入",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:import",
                  children: [],
                },
                {
                  id: 107,
                  parentId: 2,
                  name: "用户导出",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:export",
                  children: [],
                },
              ],
            },
            {
              id: 3,
              parentId: 1,
              name: "角色管理",
              type: "MENU",
              routeName: "Role",
              routePath: "role",
              component: "system/role/index",
              sort: 2,
              visible: 1,
              icon: "role",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 70,
                  parentId: 3,
                  name: "角色新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:add",
                  children: [],
                },
                {
                  id: 71,
                  parentId: 3,
                  name: "角色编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:edit",
                  children: [],
                },
                {
                  id: 72,
                  parentId: 3,
                  name: "角色删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:delete",
                  children: [],
                },
              ],
            },
            {
              id: 4,
              parentId: 1,
              name: "菜单管理",
              type: "MENU",
              routeName: "Menu",
              routePath: "menu",
              component: "system/menu/index",
              sort: 3,
              visible: 1,
              icon: "menu",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 73,
                  parentId: 4,
                  name: "菜单新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:add",
                  children: [],
                },
                {
                  id: 75,
                  parentId: 4,
                  name: "菜单删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:delete",
                  children: [],
                },
                {
                  id: 74,
                  parentId: 4,
                  name: "菜单编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:edit",
                  children: [],
                },
              ],
            },
            {
              id: 5,
              parentId: 1,
              name: "部门管理",
              type: "MENU",
              routeName: "Dept",
              routePath: "dept",
              component: "system/dept/index",
              sort: 4,
              visible: 1,
              icon: "tree",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 76,
                  parentId: 5,
                  name: "部门新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:add",
                  children: [],
                },
                {
                  id: 77,
                  parentId: 5,
                  name: "部门编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:edit",
                  children: [],
                },
                {
                  id: 78,
                  parentId: 5,
                  name: "部门删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:delete",
                  children: [],
                },
              ],
            },
            {
              id: 6,
              parentId: 1,
              name: "字典管理",
              type: "MENU",
              routeName: "Dict",
              routePath: "dict",
              component: "system/dict/index",
              sort: 5,
              visible: 1,
              icon: "dict",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 79,
                  parentId: 6,
                  name: "字典类型新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:add",
                  children: [],
                },
                {
                  id: 81,
                  parentId: 6,
                  name: "字典类型编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:edit",
                  children: [],
                },
                {
                  id: 84,
                  parentId: 6,
                  name: "字典类型删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:delete",
                  children: [],
                },
                {
                  id: 85,
                  parentId: 6,
                  name: "字典数据新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:add",
                  children: [],
                },
                {
                  id: 86,
                  parentId: 6,
                  name: "字典数据编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:edit",
                  children: [],
                },
                {
                  id: 87,
                  parentId: 6,
                  name: "字典数据删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:delete",
                  children: [],
                },
              ],
            },
            {
              id: 7,
              parentId: 1,
              name: "短信管理",
              type: "MENU",
              routeName: "Sms",
              routePath: "sms",
              component: "system/sms/index",
              sort: 6,
              visible: 1,
              icon: "el-icon-Message",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 92,
                  parentId: 7,
                  name: "短信发送",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:sms:send",
                  children: [],
                },
              ],
            },
          ],
        },
        {
          //new
          id: 150,
          parentId: 0,
          name: "资产管理",
          type: "CATALOG",
          routeName: null,
          routePath: "/assets_management",
          component: "Layout",
          sort: 2,
          visible: 1,
          icon: "cascader",
          redirect: "",
          perm: null, //权限
          children: [
            {
              id: 151,
              parentId: 150,
              name: "资产详情",
              type: "MENU",
              routeName: null,
              routePath: "details",
              component: "assets_management/details/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-Reading",
              redirect: "",
              perm: null, //权限
              children: [],
            },
            {
              id: 152,
              parentId: 150,
              name: "系统管理",
              type: "MENU",
              routeName: null,
              routePath: "system",
              component: "assets_management/system/index",
              sort: 2,
              visible: 1,
              icon: "el-icon-ElemeFilled",
              redirect: "",
              perm: null, //权限
              children: [],
            },
          ],
        },
        {
          //new
          id: 153,
          parentId: 0,
          name: "日常工作管理",
          type: "CATALOG",
          routeName: null,
          routePath: "/work_management",
          component: "Layout",
          sort: 3,
          visible: 1,
          icon: "el-icon-Sunrise",
          redirect: "",
          perm: null, //权限
          children: [
            {
              id: 154,
              parentId: 153,
              name: "安全问题管理",
              type: "MENU",
              routeName: null,
              routePath: "safety",
              component: "work_management/safety/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-Discount",
              redirect: "",
              perm: null, //权限
              children: [],
            },
            {
              id: 155,
              parentId: 153,
              name: "业务上线管理",
              type: "MENU",
              routeName: null,
              routePath: "online-service",
              component: "work_management/online-service/index",
              sort: 2,
              visible: 1,
              icon: "el-icon-Briefcase",
              redirect: "",
              perm: null, //权限
              children: [],
            },
            {
              id: 156,
              parentId: 153,
              name: "重保开放申请管理",
              type: "MENU",
              routeName: null,
              routePath: "critical",
              component: "work_management/critical/index",
              sort: 3,
              visible: 1,
              icon: "el-icon-TrophyBase",
              redirect: "",
              perm: null, //权限
              children: [],
            },
            {
              id: 157,
              parentId: 153,
              name: "安全公告管理",
              type: "MENU",
              routeName: null,
              routePath: "announcement",
              component: "work_management/announcement/index",
              sort: 4,
              visible: 1,
              icon: "el-icon-AlarmClock",
              redirect: "",
              perm: null, //权限
              children: [],
            },
            {
              id: 158,
              parentId: 153,
              name: "安全评价管理",
              type: "MENU",
              route: null,
              routePath: "evaluate",
              component: "work_management/evaluate/index",
              sort: 5,
              visible: 1,
              icon: "el-icon-CoffeeCup",
              redirect: "",
              perm: null, //权限
              children: [],
            },
          ],
        },
        {
          //new
          id: 159,
          parentId: 0,
          name: "报表管理",
          type: "CATALOG",
          routeName: null,
          routePath: "/report_management",
          component: "Layout",
          sort: 4,
          visible: 1,
          icon: "el-icon-Document",
          redirect: "",
          perm: null, //权限
          children: [
            {
              id: 160,
              parentId: 159,
              name: "报表详情",
              type: "MENU",
              routeName: null,
              routePath: "details",
              component: "report_management/details/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-Reading",
              redirect: "",
              perm: null, //权限
              children: [],
            },
          ],
        },
        {
          id: 40,
          parentId: 0,
          name: "接口文档",
          type: "CATALOG",
          routeName: null,
          routePath: "/api",
          component: "Layout",
          sort: 7,
          visible: 1,
          icon: "api",
          redirect: "",
          perm: null,
          children: [
            {
              id: 41,
              parentId: 40,
              name: "Apifox",
              type: "MENU",
              routeName: null,
              routePath: "apifox",
              component: "demo/api/apifox",
              sort: 1,
              visible: 1,
              icon: "api",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 26,
          parentId: 0,
          name: "平台文档",
          type: "CATALOG",
          routeName: null,
          routePath: "/doc",
          component: "Layout",
          sort: 8,
          visible: 1,
          icon: "document",
          redirect: "https://juejin.cn/post/7228990409909108793",
          perm: null,
          children: [
            {
              id: 102,
              parentId: 26,
              name: "平台文档(内嵌)",
              type: "EXTLINK",
              routeName: null,
              routePath: "internal-doc",
              component: "demo/internal-doc",
              sort: 1,
              visible: 1,
              icon: "document",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 30,
              parentId: 26,
              name: "平台文档(外链)",
              type: "EXTLINK",
              routeName: null,
              routePath: "https://juejin.cn/post/7228990409909108793",
              component: "",
              sort: 2,
              visible: 1,
              icon: "link",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 20,
          parentId: 0,
          name: "多级菜单",
          type: "CATALOG",
          routeName: null,
          routePath: "/multi-level",
          component: "Layout",
          sort: 9,
          visible: 1,
          icon: "cascader",
          redirect: "",
          perm: null,
          children: [
            {
              id: 21,
              parentId: 20,
              name: "菜单一级",
              type: "MENU",
              routeName: null,
              routePath: "multi-level1",
              component: "demo/multi-level/level1",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [
                {
                  id: 22,
                  parentId: 21,
                  name: "菜单二级",
                  type: "MENU",
                  routeName: null,
                  routePath: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: null,
                  children: [
                    {
                      id: 23,
                      parentId: 22,
                      name: "菜单三级-1",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      sort: 1,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                    {
                      id: 24,
                      parentId: 22,
                      name: "菜单三级-2",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      sort: 2,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 36,
          parentId: 0,
          name: "组件封装",
          type: "CATALOG",
          routeName: null,
          routePath: "/component",
          component: "Layout",
          sort: 10,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 108,
              parentId: 36,
              name: "增删改查",
              type: "MENU",
              routeName: null,
              routePath: "curd",
              component: "demo/curd/index",
              sort: 0,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 109,
              parentId: 36,
              name: "列表选择器",
              type: "MENU",
              routeName: null,
              routePath: "table-select",
              component: "demo/table-select/index",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 37,
              parentId: 36,
              name: "富文本编辑器",
              type: "MENU",
              routeName: null,
              routePath: "wang-editor",
              component: "demo/wang-editor",
              sort: 2,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 38,
              parentId: 36,
              name: "图片上传",
              type: "MENU",
              routeName: null,
              routePath: "upload",
              component: "demo/upload",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 95,
              parentId: 36,
              name: "字典组件",
              type: "MENU",
              routeName: null,
              routePath: "dict-demo",
              component: "demo/dict",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 39,
              parentId: 36,
              name: "图标选择器",
              type: "MENU",
              routeName: null,
              routePath: "icon-selector",
              component: "demo/icon-selector",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 110,
          parentId: 0,
          name: "路由参数",
          type: "CATALOG",
          routeName: null,
          routePath: "/route-param",
          component: "Layout",
          sort: 11,
          visible: 1,
          icon: "el-icon-ElementPlus",
          redirect: null,
          perm: null,
          children: [
            {
              id: 111,
              parentId: 110,
              name: "参数(type=1)",
              type: "MENU",
              routeName: null,
              routePath: "route-param-type1",
              component: "demo/route-param",
              sort: 1,
              visible: 1,
              icon: "el-icon-Star",
              redirect: null,
              perm: null,
              children: [],
            },
            {
              id: 112,
              parentId: 110,
              name: "参数(type=2)",
              type: "MENU",
              routeName: null,
              routePath: "route-param-type2",
              component: "demo/route-param",
              sort: 2,
              visible: 1,
              icon: "el-icon-StarFilled",
              redirect: null,
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 89,
          parentId: 0,
          name: "功能演示",
          type: "CATALOG",
          routeName: null,
          routePath: "/function",
          component: "Layout",
          sort: 12,
          visible: 1,
          icon: "menu",
          redirect: "",
          perm: null,
          children: [
            {
              id: 97,
              parentId: 89,
              name: "Icons",
              type: "MENU",
              routeName: null,
              routePath: "icon-demo",
              component: "demo/icons",
              sort: 2,
              visible: 1,
              icon: "el-icon-Notification",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 90,
              parentId: 89,
              name: "Websocket",
              type: "MENU",
              routeName: null,
              routePath: "/function/websocket",
              component: "demo/websocket",
              sort: 3,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 91,
              parentId: 89,
              name: "敬请期待...",
              type: "CATALOG",
              routeName: null,
              routePath: "other/:id",
              component: "demo/other",
              sort: 4,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  //
  {
    url: "menus/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: 1,
          label: "系统管理",
          children: [
            {
              value: 2,
              label: "用户管理",
              children: [
                {
                  value: 105,
                  label: "用户查询",
                },
                {
                  value: 31,
                  label: "用户新增",
                },
                {
                  value: 32,
                  label: "用户编辑",
                },
                {
                  value: 33,
                  label: "用户删除",
                },
                {
                  value: 88,
                  label: "重置密码",
                },
                {
                  value: 106,
                  label: "用户导入",
                },
                {
                  value: 107,
                  label: "用户导出",
                },
              ],
            },
            {
              value: 3,
              label: "角色管理",
              children: [
                {
                  value: 70,
                  label: "角色新增",
                },
                {
                  value: 71,
                  label: "角色编辑",
                },
                {
                  value: 72,
                  label: "角色删除",
                },
              ],
            },
            {
              value: 4,
              label: "菜单管理",
              children: [
                {
                  value: 73,
                  label: "菜单新增",
                },
                {
                  value: 75,
                  label: "菜单删除",
                },
                {
                  value: 74,
                  label: "菜单编辑",
                },
              ],
            },
            {
              value: 5,
              label: "部门管理",
              children: [
                {
                  value: 76,
                  label: "部门新增",
                },
                {
                  value: 77,
                  label: "部门编辑",
                },
                {
                  value: 78,
                  label: "部门删除",
                },
              ],
            },
            {
              value: 6,
              label: "字典管理",
              children: [
                {
                  value: 79,
                  label: "字典类型新增",
                },
                {
                  value: 81,
                  label: "字典类型编辑",
                },
                {
                  value: 84,
                  label: "字典类型删除",
                },
                {
                  value: 85,
                  label: "字典数据新增",
                },
                {
                  value: 86,
                  label: "字典数据编辑",
                },
                {
                  value: 87,
                  label: "字典数据删除",
                },
              ],
            },
            {
              value: 7,
              label: "短信管理",
              children: [
                {
                  value: 92,
                  label: "短信发送",
                },
              ],
            },
          ],
        },
        {
          value: 150,
          label: "资产管理",
          children: [
            {
              value: 151,
              label: "资产详情",
            },
            {
              value: 152,
              label: "系统管理",
            },
          ],
        },
        {
          value: 153,
          label: "日常工作管理",
          children: [
            {
              value: 154,
              label: "安全问题管理",
            },
            {
              value: 155,
              label: "业务上线管理",
            },
            {
              value: 156,
              label: "重保开放申请管理",
            },
            {
              value: 157,
              label: "安全公告管理",
            },
            {
              value: 158,
              label: "安全评价管理",
            },
          ],
        },
        {
          value: 159,
          label: "报表管理",
          children: [
            {
              value: 160,
              label: "报表详情",
            },
          ],
        },
        {
          value: 40,
          label: "接口文档",
          children: [
            {
              value: 41,
              label: "Apifox",
            },
          ],
        },
        {
          value: 26,
          label: "平台文档",
          children: [
            {
              value: 102,
              label: "平台文档(内嵌)",
            },
            {
              value: 30,
              label: "平台文档(外链)",
            },
          ],
        },
        {
          value: 20,
          label: "多级菜单",
          children: [
            {
              value: 21,
              label: "菜单一级",
              children: [
                {
                  value: 22,
                  label: "菜单二级",
                  children: [
                    {
                      value: 23,
                      label: "菜单三级-1",
                    },
                    {
                      value: 24,
                      label: "菜单三级-2",
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          value: 36,
          label: "组件封装",
          children: [
            {
              value: 108,
              label: "增删改查",
            },
            {
              value: 109,
              label: "列表选择器",
            },
            {
              value: 37,
              label: "富文本编辑器",
            },
            {
              value: 38,
              label: "图片上传",
            },
            {
              value: 95,
              label: "字典组件",
            },
            {
              value: 39,
              label: "图标选择器",
            },
          ],
        },
        {
          value: 110,
          label: "路由参数",
          children: [
            {
              value: 111,
              label: "参数(type=1)",
            },
            {
              value: 112,
              label: "参数(type=2)",
            },
          ],
        },
        {
          value: 89,
          label: "功能演示",
          children: [
            {
              value: 97,
              label: "Icons",
            },
            {
              value: 90,
              label: "Websocket",
            },
            {
              value: 91,
              label: "敬请期待...",
            },
          ],
        },
      ],
      msg: "一切ok",
    },
  },

  // 新增菜单
  {
    url: "menus",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "新增菜单" + body.name + "成功",
      };
    },
  },

  // 获取菜单表单数据
  {
    url: "menus/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: menuMap[params.id],
        msg: "一切ok",
      };
    },
  },

  // 修改菜单
  {
    url: "menus/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "修改菜单" + body.name + "成功",
      };
    },
  },

  // 删除菜单
  {
    url: "menus/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "删除菜单" + params.id + "成功",
      };
    },
  },
]);

// 菜单映射表数据
const menuMap: Record<string, any> = {
  1: {
    id: 1,
    parentId: 0,
    name: "系统管理",
    type: "CATALOG",
    routeName: "",
    routePath: "/system",
    component: "Layout",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "system",
    redirect: "/system/user",
    keepAlive: null,
    alwaysShow: null,
    params: null,
  },
  2: {
    id: 2,
    parentId: 1,
    name: "用户管理",
    type: "MENU",
    routeName: "User",
    routePath: "user",
    component: "system/user/index",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "user",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  3: {
    id: 3,
    parentId: 1,
    name: "角色管理",
    type: "MENU",
    routeName: "Role",
    routePath: "role",
    component: "system/role/index",
    perm: null,
    visible: 1,
    sort: 2,
    icon: "role",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  4: {
    id: 4,
    parentId: 1,
    name: "菜单管理",
    type: "MENU",
    routeName: "Menu",
    routePath: "menu",
    component: "system/menu/index",
    perm: null,
    visible: 1,
    sort: 3,
    icon: "menu",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  5: {
    id: 5,
    parentId: 1,
    name: "部门管理",
    type: "MENU",
    routeName: "Dept",
    routePath: "dept",
    component: "system/dept/index",
    perm: null,
    visible: 1,
    sort: 4,
    icon: "tree",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  6: {
    id: 6,
    parentId: 1,
    name: "字典管理",
    type: "MENU",
    routeName: "Dict",
    routePath: "dict",
    component: "system/dict/index",
    perm: null,
    visible: 1,
    sort: 5,
    icon: "dict",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
};
