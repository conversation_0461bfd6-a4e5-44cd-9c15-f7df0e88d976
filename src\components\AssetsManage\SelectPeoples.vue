<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="800px"
  >
    <!-- 搜索区域 -->
    <div class="search-bar">
      <el-row :gutter="16">
        <el-col :span="18">
          <el-input
            v-model="searchQuery"
            placeholder="请输入姓名或用户名搜索"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <i-ep-search />
            </template>
          </el-input>
        </el-col>
        <el-col :span="6" v-if="!departmentId">
          <el-select v-model="selectedDept" placeholder="选择部门" clearable @change="handleDeptChange">
            <el-option 
              v-for="dept in departmentList" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id" 
            />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <el-table
      ref="tableRef"
      :data="filteredUsersList"
      border
      stripe
      @selection-change="handleSelectionChange"
      height="400px"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="nickname" label="姓名" width="120" />
      <el-table-column prop="deptName" label="所属部门" />
      <el-table-column prop="mobile" label="联系电话" width="120" />
    </el-table>

    <!-- 已选用户展示区域 -->
    <div class="selected-users" v-if="selectedUsers.length">
      <div class="selected-header">
        <span>已选择 ({{ selectedUsers.length }}{{ maxSelectCount ? '/' + maxSelectCount : '' }})</span>
        <el-button type="text" @click="clearSelection">清空</el-button>
      </div>
      <div class="selected-tags">
        <el-tag
          v-for="user in selectedUsers"
          :key="user.id"
          closable
          @close="removeSelection(user)"
        >
          {{ user.nickname || user.username }} ({{ user.deptName }})
        </el-tag>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import UserAPI, { UserPageVO } from '@/api/user'
import DeptAPI, { DeptVO } from '@/api/dept'

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: '选择用户'
  },
  selectedUserIds: {
    type: Array as () => (number | string)[],
    default: () => []
  },
  departmentId: {
    type: [Number, String],
    default: null
  },
  maxSelectCount: {
    type: Number,
    default: 5
  }
})

const emit = defineEmits(['update:visible', 'selected'])

const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

const tableRef = ref()
const searchQuery = ref('')
const usersList = ref<UserPageVO[]>([])
const selectedUsers = ref<UserPageVO[]>([])
const selectedDept = ref<number | null | undefined>(null)
const departmentList = ref<DeptVO[]>([])

// 根据搜索条件过滤用户列表
const filteredUsersList = computed(() => {
  const query = searchQuery.value.toLowerCase()
  let result = [...usersList.value]
  
  // 只按搜索条件筛选，不再在前端按部门筛选（因为接口已经支持）
  if (query) {
    result = result.filter(user => 
      (user.username && user.username.toLowerCase().includes(query)) ||
      (user.nickname && user.nickname.toLowerCase().includes(query))
    )
  }
  
  return result
})

// 处理搜索
const handleSearch = () => {
  // 在筛选后的结果中保持当前选择的用户
  if (selectedUsers.value.length > 0 && tableRef.value) {
    nextTick(() => {
      tableRef.value.clearSelection()
      selectedUsers.value.forEach(selectedUser => {
        const row = filteredUsersList.value.find(u => u.id === selectedUser.id)
        if (row) {
          tableRef.value.toggleRowSelection(row, true)
        }
      })
    })
  }
}

// 处理部门变更 - 重新加载该部门的用户
const handleDeptChange = () => {
  loadUsers() // 重新加载用户，而不是在前端过滤
}

// 设置选中状态
const setTableSelection = () => {
  if (props.selectedUserIds.length > 0 && usersList.value.length > 0) {
    // 清空当前选择
    tableRef.value?.clearSelection()
    
    // 根据传入的ID找到对应用户并选中
    selectedUsers.value = []
    props.selectedUserIds.forEach(id => {
      const user = usersList.value.find(u => String(u.id) === String(id))
      if (user) {
        selectedUsers.value.push(user)
        nextTick(() => {
          const rowEl = filteredUsersList.value.find(u => String(u.id) === String(id))
          if (rowEl) {
            tableRef.value?.toggleRowSelection(rowEl, true)
          }
        })
      }
    })
  }
}

// 处理选择变化
const handleSelectionChange = (selection: UserPageVO[]) => {
  // 检查是否超过最大选择数量
  if (props.maxSelectCount && selection.length > props.maxSelectCount) {
    ElMessage.warning(`最多只能选择 ${props.maxSelectCount} 个用户`)
    
    // 保持之前的选择状态
    nextTick(() => {
      tableRef.value?.clearSelection()
      selectedUsers.value.forEach(user => {
        const row = filteredUsersList.value.find(u => u.id === user.id)
        if (row) {
          tableRef.value?.toggleRowSelection(row, true)
        }
      })
    })
    return
  }
  selectedUsers.value = selection
}

// 移除单个选择
const removeSelection = (user: UserPageVO) => {
  const row = usersList.value.find(u => u.id === user.id)
  if (row) {
    tableRef.value?.toggleRowSelection(row, false)
  }
  selectedUsers.value = selectedUsers.value.filter(u => u.id !== user.id)
}

// 清空选择
const clearSelection = () => {
  tableRef.value?.clearSelection()
  selectedUsers.value = []
}

// 取消选择
const handleCancel = () => {
  clearSelection()
  dialogVisible.value = false
}

// 确认选择
const handleConfirm = () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户')
    return
  }
  emit('selected', selectedUsers.value)
  dialogVisible.value = false
}

// 加载部门列表
const loadDepts = async () => {
  try {
    // 如果不需要显示部门选择器，则不加载部门列表
    if (props.departmentId) return
    
    const res = await DeptAPI.getList()
    departmentList.value = res || []
  } catch (error) {
    console.error('获取部门列表失败:', error)
  }
}

// 加载用户列表
const loadUsers = async () => {
  try {
    // 清空之前的数据，避免旧数据干扰
    usersList.value = [];
    
    // 设置查询参数 - 使用传入的部门ID或选择的部门ID
    const deptIdParam = props.departmentId ? 
                       Number(props.departmentId) : 
                       selectedDept.value ? Number(selectedDept.value) : undefined;
    
    const params: Record<string, any> = { 
      pageNum: 1, 
      pageSize: 999 
    };
    
    // 只有在有部门ID时才添加到参数中
    if (deptIdParam) {
      params.deptId = deptIdParam;
    }
    
    console.log('加载用户列表，参数:', params);
    
    const res = await UserAPI.getPage(params)
    usersList.value = res.list || []
    
    console.log('加载到用户数量:', usersList.value.length);
    
    nextTick(() => {
      setTableSelection()
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

// 初始化数据
const initData = () => {
  // 如果传入了部门ID，设置为选中的部门
  if (props.departmentId) {
    selectedDept.value = Number(props.departmentId)
  } else {
    selectedDept.value = null
  }
  loadDepts()
  loadUsers()
}

// 监听弹窗显示
watch(() => props.visible, (val) => {
  if (val) {
    initData()
  }
})

// 监听已选用户变化
watch(() => props.selectedUserIds, () => {
  if (dialogVisible.value && usersList.value.length > 0) {
    setTableSelection()
  }
})

// 监听部门ID变化
watch(() => props.departmentId, (val) => {
  if (val && dialogVisible.value) {
    selectedDept.value = Number(val)
    loadUsers()
  }
})
</script>

<style scoped>
.search-bar {
  margin-bottom: 16px;
}

.selected-users {
  margin-top: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.selected-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.dialog-footer {
  padding-top: 16px;
  text-align: right;
}
</style>
