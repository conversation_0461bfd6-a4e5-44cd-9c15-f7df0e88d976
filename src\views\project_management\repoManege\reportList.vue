<template>
  <div class="report-layout">
    <div class="report-sidebar">
      <div class="sidebar-title">报表</div>
      <!-- 报表概览 -->
      <el-menu
        :default-active="activeView"
        class="report-menu"
        @select="handleViewSelect"
      >
        <el-menu-item index="overview">报表概览</el-menu-item>
      </el-menu>
      <!-- 报表 -->
      <el-menu
        :default-active="activeGroup"
        class="report-menu"
        @select="handleGroupSelect"
      >
        <el-menu-item
          v-for="group in allGroups"
          :key="group.key"
          :index="group.key"
        >
          {{ group.label }}
          <span v-if="group.count !== undefined" class="group-count">
            ({{ group.count }})
          </span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="report-main">
      <!-- 报表概览视图 -->
      <div v-if="activeView === 'overview'" class="report-overview">
        <div class="overview-content">
          <ProjectStatus
            base-url="/api/projects/report"
            :default-year="2025"
          />
        </div>
      </div>
      <!-- 报表列表视图 -->
      <div v-else class="report-list-view">
        <div class="report-main-header">
          <el-input
            v-model="search"
            placeholder="搜索报表名称"
            size="small"
            class="main-search"
            clearable
          />
          <el-button
            type="primary"
            icon="el-icon-plus"
            size="small"
            class="create-btn"
            @click="createDialogVisible = true"
          >
            新建报表
          </el-button>
        </div>
        <el-table :data="filteredReports" border stripe class="report-table">
          <el-table-column prop="name" label="报表名称" width="620">
            <template #default="scope">
              <el-icon v-if="scope.row.icon === 'bar'" style="color: #409eff">
                <Histogram />
              </el-icon>
              <el-icon
                v-else-if="scope.row.icon === 'pie'"
                style="color: #67c23a"
              >
                <PieChart />
              </el-icon>
              <el-icon v-else style="color: #888"><Document /></el-icon>
              <span style="margin-left: 8px">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="group" label="所属分组" width="100" />
          <el-table-column prop="creator" label="创建者" width="90" />
          <el-table-column prop="updated" label="最后更新时间" width="140" />
          <el-table-column label="操作" width="80px" align="center">
            <template #default="scope">
              <el-dropdown trigger="click">
                <span class="el-dropdown-link">···</span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handlePreview(scope.row)">
                      预览
                    </el-dropdown-item>
                    <el-dropdown-item>重命名</el-dropdown-item>
                    <el-dropdown-item>移动分组</el-dropdown-item>
                    <el-dropdown-item divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog v-model="previewDialogVisible" title="报表预览" width="680px">
      <div
        style="
          min-height: 320px;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <div v-if="previewReport">
          <div style="font-size: 18px; font-weight: bold; margin-bottom: 12px">
            {{ previewReport.name }}
          </div>
          <div style="color: #888; font-size: 13px; margin-bottom: 8px">
            所属分组：{{ previewReport.group }} | 创建者：{{
              previewReport.creator
            }}
            | 更新时间：{{ previewReport.updated }}
          </div>
          <div
            style="
              background: #f8f8f8;
              padding: 24px 0;
              text-align: center;
              border-radius: 8px;
            "
          >
            <span style="color: #aaa">
              （此处为报表内容预览，后续可对接接口）
            </span>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="createDialogVisible" title="新建报表" width="400px">
      <el-form :model="createForm" label-width="80px">
        <el-form-item label="报表名称" required>
          <el-input v-model="createForm.name" placeholder="请输入报表名称" />
        </el-form-item>
        <el-form-item label="分组" required>
          <el-select v-model="createForm.group" placeholder="请选择分组">
            <el-option label="工时分析" value="工时分析" />
            <el-option label="任务分析" value="任务分析" />
            <el-option label="其他文件" value="其他文件" />
          </el-select>
        </el-form-item>
        <el-form-item label="图标类型" required>
          <el-radio-group v-model="createForm.icon">
            <el-radio label="bar">柱状图</el-radio>
            <el-radio label="pie">饼图</el-radio>
            <el-radio label="document">文档</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上传报表">
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :file-list="createForm.fileList"
            :on-change="handleFileChange"
            :limit="1"
            :on-remove="handleFileRemove"
            :show-file-list="true"
            accept=".xls,.xlsx,.csv,.pdf,.doc,.docx,.txt"
          >
            <el-button size="small" type="primary">选择文件</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleCreateReport">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Histogram, PieChart, Document } from "@element-plus/icons-vue";
import ProjectStatus from "./ProjectStatus.vue";

const search = ref("");
const activeView = ref("overview"); // 默认显示报表概览
const activeGroup = ref("all");

// 合并所有分组为同级单选
const groupCounts = computed(() => {
  const mine = reports.value.filter((r) => r.creator === "hooke").length;
  const all = reports.value.length;
  const group1 = reports.value.filter((r) => r.group === "工时分析").length;
  const group2 = reports.value.filter((r) => r.group === "任务分析").length;
  const group0 = reports.value.filter(
    (r) =>
      !r.group ||
      (r.group !== "工时分析" &&
        r.group !== "任务分析" &&
        r.group !== "其他文件")
  ).length;
  const groupOther = reports.value.filter((r) => r.group === "其他文件").length;
  return {
    mine,
    all,
    group1,
    group2,
    groupOther,
  };
});
const allGroups = computed(() => [
  { key: "mine", label: "我创建的", count: groupCounts.value.mine },
  { key: "all", label: "所有报表", count: groupCounts.value.all },
  { key: "group1", label: "工时分析", count: groupCounts.value.group1 },
  { key: "group2", label: "任务分析", count: groupCounts.value.group2 },
  { key: "groupOther", label: "其他文件", count: groupCounts.value.groupOther },
]);
const activeSubGroup = ref("");
const previewDialogVisible = ref(false);
const previewReport = ref<any>(null);
const createDialogVisible = ref(false);
const createForm = ref({
  name: "",
  group: "",
  icon: "bar",
  fileList: [] as any[],
});

const reports = ref<
  Array<{
    name: string;
    icon: string;
    group: string;
    creator: string;
    updated: string;
    file?: any | null;
  }>
>([
  {
    name: "员工工时日志报表",
    icon: "bar",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "成员(负责人)-每日工时总览",
    icon: "bar",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "成员(负责人)-每周工时总览",
    icon: "bar",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "成员(负责人)-状态类型工时总览",
    icon: "bar",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "送代工时日志报表",
    icon: "document",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "每日工时日志报表",
    icon: "document",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "每月工时日志报表",
    icon: "document",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "每周工时日志报表",
    icon: "document",
    group: "工时分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "任务创建者分布",
    icon: "pie",
    group: "任务分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "任务分析分布",
    icon: "pie",
    group: "任务分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "任务累计趋势",
    icon: "pie",
    group: "任务分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "任务新增趋势",
    icon: "pie",
    group: "任务分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
  {
    name: "任务状态分布",
    icon: "pie",
    group: "任务分析",
    creator: "hooke",
    updated: "2025-07-14 09:14",
  },
]);

const filteredReports = computed(() => {
  let list = reports.value;
  if (activeGroup.value === "mine") {
    list = list.filter((r) => r.creator === "hooke");
  } else if (activeGroup.value === "all") {
    // 不筛选，显示全部
  } else if (activeGroup.value === "group1") {
    list = list.filter((r) => r.group === "工时分析");
  } else if (activeGroup.value === "group2") {
    list = list.filter((r) => r.group === "任务分析");
  } else if (activeGroup.value === "groupOther") {
    list = list.filter((r) => r.group === "其他文件");
  }
  if (search.value.trim()) {
    list = list.filter((r) => r.name.includes(search.value.trim()));
  }
  return list;
});

function handleViewSelect(key: string) {
  activeView.value = key;
}
function handleGroupSelect(key: string) {
  activeGroup.value = key;
  // 当选择分组时，自动切换到报表列表视图
  activeView.value = "";
}
function handleSubGroupSelect(key: string) {
  activeSubGroup.value = key;
}
function handlePreview(row: any) {
  previewReport.value = row;
  previewDialogVisible.value = true;
}
function handleFileChange(file: any, fileList: any[]) {
  createForm.value.fileList = fileList.slice(-1); // 只保留最新一个
}
function handleFileRemove(file: any, fileList: any[]) {
  createForm.value.fileList = fileList;
}
function handleCreateReport() {
  if (
    !createForm.value.name ||
    !createForm.value.group ||
    !createForm.value.icon
  )
    return;
  let groupValue = createForm.value.group;
  reports.value.unshift({
    name: createForm.value.name,
    icon: createForm.value.icon,
    group: groupValue,
    creator: "hooke",
    updated: new Date().toISOString().slice(0, 16).replace("T", " "),
    file: createForm.value.fileList[0] || null,
  });
  createDialogVisible.value = false;
  createForm.value = { name: "", group: "", icon: "bar", fileList: [] };
}
</script>

<style scoped>
.report-layout {
  display: flex;
  height: 100%;
  background-color: var(--el-bg-color);
}
.report-sidebar {
  background-color: var(--el-bg-color);
  border-right: 1.5px solid var(--el-bg-color);
  padding: 18px 0 0 0;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}
.sidebar-title {
  font-size: 18px;
  font-weight: bold;
  color: #222;
  margin-left: 24px;
  margin-bottom: 12px;
}
.sidebar-search {
  margin: 0 16px 10px 16px;
  border-radius: 8px;
}
.report-menu,
.report-menu-group {
  width: 15 0px;
  border: none;
  background: transparent;
  margin-bottom: 8px;
}

/* 报表概览样式 */
.report-overview {
  /* padding: 20px; */
  width: 100%;
}
.report-overview h2 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
}
.overview-content {
  background-color: var(--el-bg-color);

  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 报表列表视图样式 */
.report-list-view {
  width: 100%;
}
.group-count {
  color: #aaa;
  font-size: 12px;
  margin-left: 4px;
}
.sidebar-group-title {
  font-size: 13px;
  color: #888;
  margin: 18px 0 6px 24px;
}
.report-main {
  flex: 1;
  background-color: var(--el-bg-color);
}
.report-main-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.main-search {
  width: 220px;
  margin-right: 18px;
}
.create-btn {
  margin-left: auto;
}
.report-table {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  font-size: 14px;
}
.el-dropdown-link {
  cursor: pointer;
  color: #888;
  font-size: 18px;
  margin-left: 8px;
}


</style>
