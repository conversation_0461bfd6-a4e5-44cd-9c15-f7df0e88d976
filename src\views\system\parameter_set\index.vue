<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="组名" prop="name">
          <el-input v-model="queryParams.name" placeholder="组名" clearable @keyup.enter="handleQuery()" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleQuery()"><i-ep-search />搜索</el-button>
          <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button v-hasPerm="['system:parameterSet:add']" type="success" @click="handleOpenDialog()">
          <i-ep-plus />
          新增
        </el-button>
        <el-button v-hasPerm="['system:parameterSet:delete']" type="danger" :disabled="ids.length === 0"
          @click="handleDelete()"><i-ep-delete />
          删除
        </el-button>
      </template>

      <el-table ref="dataTableRef" v-loading="loading" :data="pageData" highlight-current-row border
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column key="name" label="组名" prop="name" min-width="100" />
        <el-table-column key="concat" label="用户清单" prop="concat" min-width="100" align="center">
          <template #default="{ row }">
            <el-button type="success" @click="handleOpenUserDialog(row.id, row.concat)">查看用户</el-button>
          </template>
        </el-table-column>
        <el-table-column key="remark" label="备注" prop="remark" min-width="100" align="center" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button v-hasPerm="['system:parameterSet:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />
              编辑
            </el-button>
            <el-button v-hasPerm="['system:parameterSet:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
    </el-card>

    <!-- 参数组表单弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" @close="handleCloseDialog">
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="组名" prop="name">
          <el-input v-model="formData.name" placeholder="组名" clearable />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="4" v-model="formData.remark" placeholder="备注" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户关联弹窗组件 -->
    <UserAssociationDialog
      v-model:visible="userDialogVisible"
      :group-id="selectedGroupId"
      :selected-user-ids="selectedUserIds"
      @refresh="handleQuery"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parameter_setAPI, { parameter_setPageVO, parameter_setForm, parameter_setPageQuery } from "@/api/parameter_set";
import UserAssociationDialog from './components/UserAssociationDialog.vue';

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<parameter_setPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 参数组表格数据
const pageData = ref<parameter_setPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 用户关联弹窗
const userDialogVisible = ref(false);
const selectedGroupId = ref<number | null>(null);
const selectedUserIds = ref<string>('');

// 参数组表单数据
const formData = reactive<parameter_setForm>({
  id: undefined,
});

// 参数组表单校验规则
const rules = reactive({
  name: [{ required: true, message: "请输入组名", trigger: "blur" }],
  remark: [{ required: false, message: "请输入备注", trigger: "blur" }],
});

/** 查询参数组 */
function handleQuery() {
  loading.value = true;
  parameter_setAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置参数组查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开参数组弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改参数组";
    parameter_setAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增参数组";
  }
}

/** 打开用户关联弹窗 */
function handleOpenUserDialog(groupId: number, concat: string) {
  selectedGroupId.value = groupId;
  selectedUserIds.value = concat || '';
  userDialogVisible.value = true;
}

// 生成基于当前时间的id
const generateTimeBasedId = () => {
  const now = new Date();
  return `${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;
}

/** 提交参数组表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      let id = formData.id;
      formData.concat = '';
      if (id) {
        parameter_setAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        formData.id = Number(generateTimeBasedId());
        parameter_setAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭参数组弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除参数组 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      parameter_setAPI.deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
