<template>
  <el-dialog :model-value="visible" :title="title" append-to-body @close="handleClose"
    @update:model-value="updateVisible">
    <el-form ref="userFormRef" :model="formData" :rules="rules" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="formData.username" :readonly="!!formData.id" placeholder="请输入用户名" />
      </el-form-item>

      <el-form-item label="用户姓名" prop="nickname">
        <el-input v-model="formData.nickname" placeholder="请输入用户姓名" />
      </el-form-item>

      <el-form-item label="所属部门" prop="deptId">
        <el-tree-select v-model="formData.deptId" placeholder="请选择所属部门" :data="deptOptions" filterable check-strictly
          :render-after-expand="false" />
      </el-form-item>

      <el-form-item label="职责分类" prop="duty">
        <dictionary 
          v-model="formData.duty" 
          code="DUTY" 
          multiple
          placeholder="请选择职责分类"
        />
      </el-form-item>

      <el-form-item label="性别" prop="gender">
        <dictionary v-model="formData.gender" code="gender" />
      </el-form-item>

      <el-form-item label="角色" prop="roleIds">
        <el-select v-model="formData.roleIds" multiple placeholder="请选择">
          <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="请输入手机号码" maxlength="11" />
      </el-form-item>

      <el-form-item label="办公地址" prop="address">
        <el-input v-model="formData.address" placeholder="请输入办公地址：XX楼XX层XX号房间" maxlength="50" />
      </el-form-item>

      <el-form-item label="邮箱" prop="email">
        <el-input v-model="formData.email" placeholder="请输入邮箱" maxlength="50" />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-switch v-model="formData.status" inline-prompt active-text="正常" inactive-text="禁用" :active-value="1"
          :inactive-value="0" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import UserAPI, { UserForm } from "@/api/user";
import DeptAPI from "@/api/dept";
import RoleAPI from "@/api/role";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  userId: {
    type: Number,
    default: null,
  },
});

const emits = defineEmits(['update:visible', 'refresh']);

const userFormRef = ref();
const formData = reactive<UserForm>({
  status: 1,
});
const deptOptions = ref<any>([]);
const roleOptions = ref<any>([]);
const title = ref('新增用户');

const rules = reactive({
  username: [
    { required: true, message: "用户名不能为空", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9][a-zA-Z0-9]*$/,
      message: "用户名只能包含字母和数字",
      trigger: "blur"
    }
  ],
  nickname: [{ required: true, message: "用户昵称不能为空", trigger: "blur" }],
  deptId: [{ required: true, message: "所属部门不能为空", trigger: "blur" }],
  roleIds: [{ required: true, message: "用户角色不能为空", trigger: "blur" }],
  duty: [{ required: false, message: "请选择职责分类", trigger: "change" }],
  email: [
    {
      pattern: /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,
      message: "请输入正确的邮箱地址",
      trigger: "blur",
    },
  ],
  mobile: [
    {
      required: true,
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
});

watch(() => props.visible, (newVal) => {
  if (newVal) {
    openDialog();
  }
});

async function openDialog() {
  // 加载角色下拉数据源
  roleOptions.value = await RoleAPI.getOptions();
  // 加载部门下拉数据源
  deptOptions.value = await DeptAPI.getOptions();

  if (props.userId) {
    title.value = "修改用户";
    UserAPI.getFormData(props.userId).then((data) => {
      // 修改：直接赋值，字典组件会自动处理数据格式
      Object.assign(formData, data);
    });
  } else {
    title.value = "新增用户";
    resetForm();
  }
}

function resetForm() {
  Object.assign(formData, {
    id: undefined,
    username: '',
    nickname: '',
    deptId: null,
    gender: null,
    roleIds: [],
    mobile: '',
    email: '',
    address: '',
    duty: '', // 修改：重置为空字符串，字典组件会处理
    status: 1,
  });
}

function handleClose() {
  emits('update:visible', false);
  resetForm();
}

function handleSubmit() {
  userFormRef.value.validate((valid: any) => {
    if (valid) {
      const userId = formData.id;
      // 修改：直接提交，字典组件已经处理成字符串格式

      if (userId) {
        UserAPI.update(userId, formData).then(() => {
          ElMessage.success("修改用户成功");
          handleClose();
          emits('refresh');
        });
      } else {
        UserAPI.add(formData).then(() => {
          ElMessage.success("新增用户成功");
          handleClose();
          emits('refresh');
        });
      }
    }
  });
}

function updateVisible(value: boolean) {
  emits('update:visible', value);
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.form-tip {
  margin-top: 4px;
}
</style>
