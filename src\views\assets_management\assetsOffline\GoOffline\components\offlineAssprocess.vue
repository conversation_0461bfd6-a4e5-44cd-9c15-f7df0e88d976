<template>
  <div class="asset-online-process" :key="componentKey">
    <div class="process-header">
      <h2>资产下线流程</h2>
      <el-button 
          type="primary" 
          link
          @click="showTransferRecord"
        >
          <el-icon><Document /></el-icon>
          查看流转记录
      </el-button>
    </div>

    <el-steps :active="getStepIndex" finish-status="success" align-center>
      <el-step 
        v-for="(step, index) in steps" 
        :key="index" 
        :title="step.title" 
        :icon="step.icon" 
        :description="step.description" 
        @click="handleStepClick(getStepKey(index))"
      />
    </el-steps>
    <transfer-record-dialog
      v-model:visible="showRecordsDialog"
      :ticket-id="ticketdata.id"
      :api-type="'offline'"
      :show-attachments="true"
      ref="transferRecordRef"
    />
    <div class="step-content">
      <component 
        :is="steps[clickIndex].component" 
        v-model:ticketdata="ticketdata"
        @next="nextStep"
      />
    </div>

    
  </div>
</template>

<script lang="ts" setup>
import { Edit, Upload, Connection, Check, CloseBold } from '@element-plus/icons-vue'
import InitiateTicket from './process/assInitiateTicket.vue'
import AssetsAudit from './process/AssetsAudit.vue'
import fixEvaluation from './process/assfixEvaluation.vue'
import CloseTicket from './process/assCloseTicket.vue'
import { hasAuth } from "@/plugins/permission"
import offlineAPI from "@/api/assets_management/details/offline";
import TransferRecord from './process/components/TransferRecord.vue'
import {formatLocalDateTime} from "@/utils/dateUtils";
import TransferRecordDialog from '@/components/GeneralModel/TransferRecord.vue'

const props = defineProps({
  ticketId: [String, Number],
  currentStatus: Number,
})

const componentKey = ref(0)

const emit = defineEmits(['init'])

const steps = [
  { title: '申请下线', icon: Edit, description: '发起资产下线申请', component: InitiateTicket },
  { title: '下线审核', icon: Upload, description: '审核资产下线申请', component: AssetsAudit },
  { title: '工单评价', icon: Check, description: '评价整改结果', component: fixEvaluation },
  { title: '关闭工单', icon: CloseBold, description: '完成下线流程', component: CloseTicket }
]

// 流转记录相关
const transferRecordRef = ref()
const showRecordsDialog = ref(false)

// 修改显示流转记录的方法
const showTransferRecord = () => {
  showRecordsDialog.value = true
}

const stepStatus = ref({
  initiateTicket: 'process',
  assetsAudit: 'wait',
  assetsFix: 'wait',
  closeComments: 'wait'
})

const stepPermission = ref({
  initiateTicket: "system:offline:add",
  assetsAudit: "system:offline:audit",
  fixEvaluation: "system:offline:evaluate",
  closeTicket: "system:offline:close"
})

const currentStep = ref<keyof typeof stepStatus.value>('initiateTicket')
const ticketdata = ref({
  id: props.ticketId,
  currentStep: currentStep.value,
  isClick: false
})
const nowStatus = ref<keyof typeof stepStatus.value>('initiateTicket') // 当前步骤步骤条状态
const isClick = ref<boolean>(false) // 是否是点击事件

const getStepKey = (index: number): keyof typeof stepStatus.value => {
  return Object.keys(stepStatus.value)[index] as keyof typeof stepStatus.value
}

const getStepIndex = computed(() => {
  return Object.keys(stepStatus.value).indexOf(nowStatus.value)
})

const clickIndex = computed(() => {
  return Object.keys(stepStatus.value).indexOf(currentStep.value)
})

const hasPermission = (requiredPerms: string): boolean => {
  return hasAuth(requiredPerms, 'button')
}

const handleStepClick = async (step: keyof typeof stepStatus.value) => {
  console.log('step', step)
  console.log('currentStep', currentStep.value)
  console.log('nowStatus', nowStatus.value)
  // if (step === 'closeComments' && nowStatus.value !== 'closeComments') {
  //     await finishProcess();
  // } else if (stepStatus.value[step] === 'wait') {
  //   ElMessage.warning('当前步骤未开始')
  // } else {
  //   ticketdata.value.isClick = true
  //   currentStep.value = step
  //   ticketdata.value.currentStep = step
  // }
}

const nextStep = async (id: number) => {
  console.log('id', id)
  emit('init', id)
  
  // 如果流转记录对话框已打开，则刷新数据
  if (showRecordsDialog.value && transferRecordRef.value) {
    transferRecordRef.value.loadData()
  }
}

const finishProcess = async () => {
  emit('init', props.ticketId)
}

const transformRecord = (id: any) => {
  // API调用处理
}

const handleQuery = async () => {
  console.log('props.ticketId', props.ticketId, typeof props.ticketId)

  if (props.ticketId) {
    try {
      const statusRes:any = await offlineAPI.status(props.ticketId);
      stepStatus.value = statusRes;
      for (const step in stepStatus.value) {
        if (stepStatus.value[step as keyof typeof stepStatus.value] === 'process') {
          currentStep.value = step as keyof typeof stepStatus.value
          nowStatus.value = step as keyof typeof stepStatus.value;
          break
        }
      }
      ticketdata.value.currentStep = currentStep.value
      transformRecord(props.ticketId)
    } catch (error) {
      console.error('Error fetching step status:', error)
    }
  } else {
    currentStep.value = 'initiateTicket'
    nowStatus.value = 'initiateTicket'
  }
}

onMounted(() => {
  console.log('组件挂载，执行查询')
  handleQuery()
})

const forceRefresh = () => {
  componentKey.value++ // 增加key值，强制组件重新渲染
  console.log('强制刷新组件，执行查询')
  handleQuery() // 重新执行数据加载
}
// 监听 ticketId 的变化
watch(
  () => [props.ticketId, props.currentStatus], 
  () => {
    console.log('属性变化，强制重新渲染和查询')
    forceRefresh()
  },
  { immediate: true }
)



// watchEffect 确保组件渲染时自动执行
watchEffect(() => {
  console.log('组件渲染，执行查询，ticketId:', props.ticketId)
  handleQuery()
})

</script>

<style scoped>
.asset-online-process {
  padding: 20px;
}

.step-content {
  margin-top: 30px;
}

:deep(.el-step) {
  cursor: pointer;
}

:deep(.el-step__title) {
  font-size: 14px;
}

:deep(.el-step__description) {
  font-size: 12px;
}

:deep(.el-step.is-process .el-step__icon) {
  background-color: #409EFF;
  border-color: #409EFF;
}

:deep(.el-step.is-finish .el-step__icon) {
  background-color: #67C23A;
  border-color: #67C23A;
}

:deep(.el-step.is-wait .el-step__icon) {
  background-color: #909399;
  border-color: #909399;
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}


</style>
