<template>
  <div class="service-provider-select">
    <!-- 选择按钮 -->
    <el-button :type="selectedProvider ? 'default' : 'primary'" @click="openDialog" class="select-btn">
      <template v-if="selectedProvider">
        <span class="selected-name">{{ selectedProvider.providerName }}</span>
        <el-icon>
          <EditPen />
        </el-icon>
      </template>
      <template v-else>
        <el-icon>
          <Plus />
        </el-icon>
        <span>选择服务商</span>
      </template>
    </el-button>

    <!-- 服务商选择/新增弹窗 -->
    <el-dialog v-model="dialogVisible" title="服务商管理" width="1000px" :close-on-click-modal="false"
      :before-close="handleClose" class="provider-dialog" destroy-on-close>
      <div class="dialog-content">
        <!-- 左侧列表 -->
        <div class="provider-list">
          <div class="list-header">
            <h3 class="header-title">服务商列表</h3>
            <el-button type="primary" @click="handleAddNew" plain size="small">
              <el-icon>
                <Plus />
              </el-icon>新增服务商
            </el-button>
          </div>
          <el-scrollbar>
            <div class="list-content">
              <div v-for="item in providerList" :key="String(item.id)" class="provider-item"
                :class="{ active: currentProvider?.id === item.id }" @click="selectProvider(item)">
                <div class="item-main">
                  <span class="provider-name" :title="item.providerName">{{ item.providerName }}</span>

                </div>
                <div class="item-sub">
                  <el-icon>
                    <User />
                  </el-icon>
                  <span>{{ item.projectManager }}</span>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <!-- 右侧内容区 -->
        <div class="provider-detail">
          <template v-if="isAdding">
            <!-- 新增表单 -->
            <el-form ref="providerFormRef" :model="providerForm" :rules="rules" label-width="120px"
              class="provider-form" scroll-to-error> <!-- 基本信息 -->
              <div class="form-section">
                <h4 class="section-title">基本信息</h4>
                <el-form-item label="公司名称" prop="providerName">
                  <el-input v-model="providerForm.providerName" placeholder="请输入公司全称" clearable />
                </el-form-item>
                <el-form-item label="服务商描述" prop="description">
                  <el-input v-model="providerForm.description" type="textarea" :rows="2" placeholder="请输入服务商描述"
                    clearable />
                </el-form-item>
              </div>

              <!-- 联系人信息 -->
              <div class="form-section">
                <h4 class="section-title">联系人信息</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="项目负责人" prop="projectManager">
                      <el-input v-model="providerForm.projectManager" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="managerMobile">
                      <el-input v-model="providerForm.managerMobile" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="联系邮箱" prop="managerEmail">
                  <el-input v-model="providerForm.managerEmail" clearable />
                </el-form-item>
              </div>

              <!-- 技术联系人 -->
              <div class="form-section">
                <h4 class="section-title">技术联系人</h4>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="技术负责人" prop="techLeader">
                      <el-input v-model="providerForm.techLeader" clearable />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="techMobile">
                      <el-input v-model="providerForm.techMobile" clearable />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="联系邮箱" prop="techEmail">
                  <el-input v-model="providerForm.techEmail" clearable />
                </el-form-item>
              </div>
            </el-form>
          </template>

          <template v-else-if="currentProvider">
            <!-- 详情展示 -->
            <div class="detail-content">
              <div class="detail-header">
                <h3>{{ currentProvider.providerName }}</h3>
              </div>

              <!-- 联系人信息 -->
              <div class="info-section">
                <h4 class="section-title">
                  <el-icon>
                    <UserFilled />
                  </el-icon>
                  联系人信息
                </h4>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">项目负责人</span>
                    <span class="value">{{ currentProvider.projectManager }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系电话</span>
                    <span class="value">{{ currentProvider.managerMobile }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系邮箱</span>
                    <span class="value">{{ currentProvider.managerEmail }}</span>
                  </div>
                </div>
              </div>

              <!-- 技术联系人 -->
              <div class="info-section">
                <h4 class="section-title">
                  <el-icon>
                    <Tools />
                  </el-icon>
                  技术联系人
                </h4>
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">技术负责人</span>
                    <span class="value">{{ currentProvider.techLeader }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系电话</span>
                    <span class="value">{{ currentProvider.techMobile }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">联系邮箱</span>
                    <span class="value">{{ currentProvider.techEmail }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>

          <template v-else>
            <div class="empty-tip">
              <el-empty description="请选择或新增服务商" />
            </div>
          </template>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button v-if="isAdding" type="primary" @click="submitProvider" :loading="submitting">
            保 存
          </el-button>
          <el-button v-else-if="currentProvider" type="primary" @click="confirmSelect">
            确 定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import { Plus, EditPen, User, UserFilled, Tools } from '@element-plus/icons-vue'
import ProviderAPI, { ProviderPageVO, ProviderForm } from "@/api/work_management/serviceProvider/index";

// Props 定义
const props = defineProps({
  modelValue: {
    type: [String, Number, Object] as any,
    default: ''
  }
})

// Emits 定义
const emit = defineEmits(['update:modelValue', 'provider-selected'])

// 响应式状态
const dialogVisible = ref(false)
const isAdding = ref(false)
const submitting = ref(false)
const providerFormRef = ref<FormInstance>()
const currentProvider = ref<ProviderPageVO | null>(null)
const selectedProvider = ref<ProviderPageVO | null>(null)
const providerList = ref<ProviderPageVO[]>([])

// 服务商查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 999 // 获取所有服务商
})

// 服务商表单数据
const providerForm = ref<ProviderForm>({
  providerName: '',
  projectManager: '',
  managerMobile: '',
  managerEmail: '',
  techLeader: '',
  techMobile: '',
  techEmail: '',
  status: 1 // 默认启用
})

// 表单验证规则
const rules = {
  providerName: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
  projectManager: [{ required: true, message: '请输入项目负责人', trigger: 'blur' }],
  managerMobile: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ], managerEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  techLeader: [{ required: true, message: '请输入技术负责人', trigger: 'blur' }],
  techMobile: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ], techEmail: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email' as const, message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 获取服务商类型标签
const getProviderTypeLabel = (type: string) => {
  const types = {
    dev: '开发商',
    ops: '运维商',
    both: '开发运维一体'
  }
  return types[type as keyof typeof types] || ''
}

// 获取服务商类型对应的标签类型
const getProviderTypeTag = (type: string) => {
  const tags = {
    dev: 'success',
    ops: 'warning',
    both: 'primary'
  }
  return tags[type as keyof typeof tags] || 'info'
}

// 获取服务商列表
const getProviderList = async () => {
  try {
    const response = await ProviderAPI.getPage(queryParams.value)
    providerList.value = response.list || []
  } catch (error) {
    console.error('获取服务商列表失败:', error)
    ElMessage.error('获取服务商列表失败')
  }
}

// 选择服务商
const selectProvider = (provider: ProviderPageVO) => {
  currentProvider.value = provider
  isAdding.value = false
}

// 打开弹窗
const openDialog = () => {
  dialogVisible.value = true
  if (selectedProvider.value) {
    currentProvider.value = selectedProvider.value
  }
}

// 处理新增
const handleAddNew = () => {
  isAdding.value = true
  currentProvider.value = null
  providerForm.value = {
    providerName: '',
    projectManager: '',
    managerMobile: '',
    managerEmail: '',
    techLeader: '',
    techMobile: '',
    techEmail: '',
    status: 1
  }
}

// 确认选择
const confirmSelect = () => {
  if (!currentProvider.value) {
    ElMessage.warning('请选择服务商')
    return
  }
  selectedProvider.value = currentProvider.value
  emit('update:modelValue', currentProvider.value.id)
  emit('provider-selected', currentProvider.value)
  dialogVisible.value = false
}

// 提交新增服务商
const submitProvider = async () => {
  if (!providerFormRef.value) return

  try {
    await providerFormRef.value.validate()
    submitting.value = true

    // 提交服务商数据到API
    await ProviderAPI.add(providerForm.value)

    // 刷新服务商列表
    await getProviderList()

    // 提交成功后，切换到列表视图，让用户手动选择新创建的服务商
    isAdding.value = false
    currentProvider.value = null

    ElMessage.success('添加服务商成功，请从列表中选择新创建的服务商')

  } catch (error) {
    console.error('提交服务商数据失败:', error)
    ElMessage.error('提交服务商数据失败')
  } finally {
    submitting.value = false
  }
}

// 获取服务商详情
const getProviderDetail = async (id: number | string | bigint) => {
  if (!id) return
  try {
    // 将id转换为bigint类型
    const bigintId = typeof id === 'bigint' ? id : BigInt(id)
    const data = await ProviderAPI.getFormData(bigintId)
    selectedProvider.value = data
    currentProvider.value = data
  } catch (error) {
    console.error('获取服务商详情失败:', error)
  }
}

// 处理弹窗关闭
const handleClose = () => {
  isAdding.value = false
  currentProvider.value = selectedProvider.value
  dialogVisible.value = false
}

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal && (!selectedProvider.value || String(selectedProvider.value.id) !== String(newVal))) {
    getProviderDetail(newVal)
  } else if (!newVal) {
    selectedProvider.value = null
    currentProvider.value = null
  }
}, { immediate: true })

// 初始化
onMounted(async () => {
  await getProviderList()
  if (props.modelValue) {
    await getProviderDetail(props.modelValue)
  }
})
</script>

<style scoped>
.service-provider-select {
  display: inline-block;
}

.select-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.selected-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.provider-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
}

.dialog-content {
  display: flex;
  height: 600px;
  background-color: var(--el-bg-color-page);
}

/* 左侧列表样式 */
.provider-list {
  width: 300px;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.header-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.list-content {
  padding: 12px;
}

.provider-item {
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.provider-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

.provider-item.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.provider-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.item-sub {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 右侧内容区样式 */
.provider-detail {
  flex: 1;
  padding: 20px;
  background-color: var(--el-bg-color);
  overflow-y: auto;
}

/* 表单样式 */
.provider-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.section-title .el-icon {
  color: var(--el-color-primary);
}

/* 详情展示样式 */
.detail-content {
  max-width: 800px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.detail-header h3 {
  margin: 0;
  font-size: 20px;
  color: var(--el-text-color-primary);
}

.info-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item .label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-item .value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.empty-tip {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
