<template>
  <div class="detection-status">
    <!-- 状态标签 -->
    <el-tag :type="getStatusType(latestStatus?.status)" v-if="hasStatus" size="small">
      {{ latestStatus?.status || '未知' }}
    </el-tag>
    <el-tag type="info" size="small" v-else>未探测</el-tag>
    
    <!-- 查看状态按钮 -->
    <el-button type="primary" link size="small" @click="showStatusDialog">
      查看状况
    </el-button>
    
    <!-- 状态详情弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      title="探测状态详情" 
      width="700px" 
      destroy-on-close 
      append-to-body
      :z-index="2050"
      :modal-append-to-body="true"
    >
      <div class="detection-details">
        <!-- 基本信息 -->
        <div class="info-section" v-if="assetName">
          <div class="info-item">
            <span class="info-label">资产名称:</span>
            <span class="info-value">{{ assetName }}</span>
          </div>
        </div>

        <!-- 筛选项 -->
        <!-- <div class="filter-section" v-if="statusList.length > 0">
          <el-radio-group v-model="statusFilter" size="small">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="success">成功</el-radio-button>
            <el-radio-button label="fail">失败</el-radio-button>
          </el-radio-group>
        </div> -->
        
        <!-- 探测结果表格 -->
        <div v-if="filteredStatusList.length > 0">
          <el-table :data="filteredStatusList" border stripe style="width: 100%">
            <el-table-column label="探测地址" min-width="180" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ getDisplayAddress(scope.row) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="message" label="探测消息" min-width="200" show-overflow-tooltip />
            <el-table-column prop="ip" label="IP地址" width="130" v-if="showIpColumn" show-overflow-tooltip />
            <el-table-column prop="port" label="端口" width="80" align="center" v-if="showPortColumn" />
          </el-table>
        </div>
        <el-empty v-else :description="getEmptyText()" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

interface StatusItem {
  status?: string;
  url?: string;
  ip?: string;
  port?: string | number;
  message?: string;
}

const props = defineProps({
  // 直接使用页面分页数据中的survivalStatusList字段
  survivalStatusList: {
    type: Array as () => StatusItem[],
    default: () => []
  },
  // 资产名称用于显示在详情弹窗中
  assetName: {
    type: String,
    default: ''
  }
});

const dialogVisible = ref(false);
const statusFilter = ref('all');

// 状态列表
const statusList = computed(() => {
  // 处理survivalStatusList可能为空或非数组的情况
  if (!props.survivalStatusList || !Array.isArray(props.survivalStatusList)) {
    return [];
  }
  return props.survivalStatusList;
});

// 是否有状态数据
const hasStatus = computed(() => {
  return statusList.value.length > 0;
});

// 最新状态（取第一个）
const latestStatus = computed(() => {
  if (hasStatus.value) {
    return statusList.value[0];
  }
  return null;
});

// 根据状态获取标签类型
const getStatusType = (status: string | undefined): string => {
  if (!status) return 'info';
  const lowerStatus = String(status).toLowerCase();
  
  if (lowerStatus.includes('success') || lowerStatus === '正常' || lowerStatus === '成功' || lowerStatus === '存活') {
    return 'success';
  }
  if (lowerStatus.includes('warn')) return 'warning';
  if (lowerStatus.includes('fail') || lowerStatus.includes('error') || lowerStatus === '不存活' || lowerStatus === '异常') {
    return 'danger';
  }
  
  return 'info';
};

// 获取显示地址
const getDisplayAddress = (item: any): string => {
  if (item.url) return item.url;
  if (item.ip) {
    return item.port ? `${item.ip}:${item.port}` : item.ip;
  }
  return '未知地址';
};

// 获取空状态文本
const getEmptyText = (): string => {
  if (statusFilter.value === 'all') return '暂无探测数据';
  if (statusFilter.value === 'success') return '暂无成功探测记录';
  return '暂无失败探测记录';
};

// 根据筛选条件过滤状态列表
const filteredStatusList = computed(() => {
  if (!statusList.value.length) return [];
  
  if (statusFilter.value === 'all') {
    return statusList.value;
  } else if (statusFilter.value === 'success') {
    return statusList.value.filter(item => {
      const status = String(item.status || '').toLowerCase();
      return status.includes('success') || status === '正常' || status === '成功' || status === '存活';
    });
  } else {
    return statusList.value.filter(item => {
      const status = String(item.status || '').toLowerCase();
      return status.includes('fail') || status.includes('error') || status === '失败' || status === '异常';
    });
  }
});

// 是否显示IP列
const showIpColumn = computed(() => {
  return statusList.value.some(item => item.ip);
});

// 是否显示端口列
const showPortColumn = computed(() => {
  return statusList.value.some(item => item.port);
});

// 显示状态详情对话框
const showStatusDialog = () => {
  dialogVisible.value = true;
};
</script>

<style scoped>
.detection-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.detection-details {
  padding: 10px 0;
}

.info-section {
  margin-bottom: 16px;
  padding: 12px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
  width: 100px;
  color: var(--el-text-color-regular);
}

.info-value {
  color: var(--el-text-color-primary);
}

.filter-section {
  margin-bottom: 16px;
}
</style>
