<template>
    <div class="security-assessment">
      <div class="page-header">
        <h3>安全评估</h3>
      </div>
      <el-form :model="form"   label-width="120px" class="form-container">
         <!-- 基本信息卡片 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单创建人信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="工号：" prop="employeeId">
              <el-input v-model="form.employeeId" disabled/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人信息：" prop="applicantName">
              <el-input v-model="form.applicantName" disabled/>
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label-width="100px" label="所在单位：" prop="deptId">
              <el-input disabled>
                <template v-slot:prepend>
                  <Dictmap
                    code="dept0x0"
                    v-model="form.deptId"
                  />
                </template>
              </el-input>
            </el-form-item>
           </el-col>
           <el-col :span="6">
            <el-form-item label-width="100px" label="联系方式：" prop="mobile">
              <el-input v-model="form.mobile" disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>工单基本信息</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="12">
            <el-form-item label="工单创建时间：" prop="createTime">
              <el-date-picker 
                v-model="form.createTime" 
                type="datetime" 
                placeholder="选择时间"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
        <el-col :span="12">
          <el-form-item disabled label="工单截止日期：" prop="deadline">
            <el-date-picker 
             disabled
              v-model="form.deadline" 
              type="datetime" 
              placeholder="选择截止时间"
              style="width: 100%"
              :min-date="form.deadline"
            />
          </el-form-item>
        </el-col>
        </el-row>
      </el-card>
       <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>被评估部门</span>
          </div>
        </template>
        <el-row :gutter="24">
              <el-col :span="8">
            <el-form-item label="责任部门：" >
             <el-input
                v-model="shstep.deptName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="责任人：">
              <el-input
                v-model="shstep.userName"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式：" >
              <el-input
                v-model="shstep.userMobile"
               disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
       </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>网络安全评估工程师</span>
          </div>
        </template>
        <el-row :gutter="24">
          <!-- 安全工程师信息开始 -->
          <el-form :rules="rules2" :model="SafetyEngineerbox" :inline="true" >
            <div style="display:flex; justify-content: space-between;">
                  <el-form-item label-width="110px" label="工程师名称："   prop="engineerName" style="width:20%">
                  <el-input disabled v-model="SafetyEngineerbox.engineerName"  ></el-input>
                </el-form-item>
                <el-form-item   label="联系方式：" label-width="90px" prop="engineerMobile" style="width:20%">
                  <el-input disabled  v-model="SafetyEngineerbox.engineerMobile"  > </el-input>
                </el-form-item>
                <el-form-item label="微信号：" label-width="80px" prop="engineerWechat" style="width:20%">
                  <el-input disabled v-model="SafetyEngineerbox.engineerWechat"  ></el-input>
                </el-form-item>
                <el-form-item label="QQ号：" label-width="80px" prop="engineerQq" style="width:20%">
                  <el-input disabled v-model="SafetyEngineerbox.engineerQq"  ></el-input>
                </el-form-item>
                <el-form-item label="邮箱：" label-width="80px" prop="engineerEmail" style="width:20%">
                  <el-input disabled v-model="SafetyEngineerbox.engineerEmail"  ></el-input>
                </el-form-item>
            </div>
          </el-form>
        </el-row>
      </el-card>
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <span>评估对象</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="信息系统名称："  label-width="120px" prop="sysname">
              <el-input v-model="form.sysname" readonly/>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="域名/IP："  label-width="120px" prop="domainIp">
              <el-input v-model="form.domainIp" readonly/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="评估描述："  label-width="120px" prop="reason">
              <el-input v-model="form.reason" readonly  type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.evaluationObjectFile">
              <el-form-item  class="fontw"  label="评估对象清单：">
                  <ul>
                    <li >
                      <span>{{ form.evaluationObjectFile.name }}</span>
                        <el-button type="primary" style="margin-left:20px" @click="downloadFile(form.evaluationObjectFile)">下载</el-button>
                    </li>
                  </ul>
               </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card class="form-card" v-if="form.commentsExamine">
        <template #header>
          <div class="card-header">
            <span>审核情况反馈</span>
          </div>
        </template>
        <el-row :gutter="24">
           <el-col :span="24">
            <el-form-item label="审核结果："  label-width="140px" >
              <el-select v-model="form.commentsExamine.commentType" disabled placeholder="请选择" style="width: 100%">
                <el-option label="通过" :value="1" />
                <el-option label="通过并关闭工单" :value="2" />
                <el-option label="不通过" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核情况反馈内容："  label-width="140px">
              <el-input v-model="form.commentsExamine.commentContent" disabled  type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-form :model="assessform" :rules="rules" ref="dataFormRef" label-width="170px" class="form-container">
        <!-- 评估信息卡片 -->
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>安全评估情况反馈</span>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="评估时间："  label-width="180px" prop="createTime">
                <el-date-picker 
                  v-model="assessform.createTime" 
                  disabled
                  type="datetime" 
                  placeholder="选择时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item :label="typetext=='OnlineDetails'?`业务上线评估情况反馈：`:'安全评估情况反馈：'"  label-width="180px" prop="commentContent">
                <el-input 
                  v-model="assessform.commentContent" 
                  :disabled="!showStep" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
          <el-col :span="18">
            <el-form-item label-width="180px" :label="typetext=='OnlineDetails'?`业务上线评估报告：`:'安全评估报告：'" >
              <file-upload 
              :uploadBtnText="typetext=='OnlineDetails'?'上传业务上线评估报告':'上传安全评估报告'"
                v-model="assessFileList"
                :upload-max-size="20 * 1024 * 1024"
                :accept="'.pdf,.xls,.doc,.docx,.txt,.csv,.xlsx'"
                :tip="'仅支持pdf，excel,word格式的文件，且大小不超过20MB'"
                :disabled="!showStep"
              />
              <div class="upload-tip">仅支持pdf，excel,word格式的文件，且大小不超过20MB</div>
            </el-form-item>
          </el-col>
        </el-row>
          <!-- 已上传文件列表 -->
          <el-row :gutter="20" v-if="!showStep && assessFileList.length">
            <el-col :span="24">
              <el-form-item label="文件列表：">
                <div class="file-list">
                  <el-tag 
                    v-for="file in assessFileList" 
                    :key="file.id"
                    class="file-item"
                    @click="downloadFile(file)"
                  >
                    <el-icon><document /></el-icon>
                    {{ file.name }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
        <!-- 操作按钮 -->
        <div class="form-actions" v-if="showStep">
          <el-button type="primary" @click="submitForm" size="large">
            <el-icon><check /></el-icon>提交评估
          </el-button>
            <el-button type="info" @click="concelForm" size="large">取消</el-button>
        </div>
      </el-form>
    </el-form>
    </div>
  </template>
  
  <script lang="ts" setup>
  import { ref, reactive, onMounted, watch } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import businessAPI from '@/api/work_management/online_service/index'
  import type { FormInstance } from 'element-plus'
  import {formatLocalDateTime} from "@/utils/dateUtils";
  import safetyAPI from "@/api/work_management/safety";
  import UserAPI from "@/api/user";
  import { useRouter, useRoute } from "vue-router";
  const router = useRouter();
  const route = useRoute();
   // 获取当前页面参数。判断是业务上线页面还是安全评估页面
  const typetext = ref(route.query.type as string);
  const emit = defineEmits(['next'])
  interface TicketData {
    id: number;
    currentStep: string;
    isClick: boolean;
  }
  
  const props = defineProps<{
    ticketdata: TicketData,
  }>();
  // 表单引用
  const dataFormRef = ref<FormInstance | null>(null);
  
  // 添加新的响应式变量
  const transferDialog = reactive({
    visible: false,
    allAssets: [] as any[]
  })

  const form = reactive({
    createTime: "",
    name: "",
    updateTime: '',
    reason: '',
    commentContent: '',
    commentBy: '',
    commentType: 0,
    id: '',
    comments: [],
    ticketType: "",
    deadline: "",
    fileList: [] as any[],
    assetsList: [] as any[],
  });
  
  // 审核表单数据（来自前一步骤）
  const auditform = ref<{
    fixTime: string;
    createTime: string;
    updateTime: string;
    commentContent: string;
    commentBy: string;
    commentType: number;
    fileList: { id: string }[];
  }>({
    fixTime: '',
    createTime: '',
    updateTime: '',
    commentContent: '',
    commentBy: '',
    commentType: undefined,
    fileList: [],
  })
  
  // 评估表单数据
  const assessform = ref<{
    fixTime: string;
    createTime: string;
    updateTime: string;
    commentContent: string;
    commentBy: string;
    commentType: number;
    fileList: { id: string }[];
  }>({
    fixTime: '',
    createTime: formatLocalDateTime(new Date()),
    updateTime: formatLocalDateTime(new Date()),
    commentContent: '',
    commentBy: '',
    commentType: 1,
    fileList: [],
  })
  
  const assessFileList = ref([] as any[]);
  
  const currentStep = ref(props.ticketdata.currentStep);
  const showStep = ref(true);
  const nowStep = ref('');
  const stepStatus = ref<any | null>(null);
  // 部门信息
  const shstep=ref({})
  // 是否为当前步骤
  function isCurrentStep() {
    console.log(currentStep.value)
    if (currentStep.value == nowStep.value) { //是当前步骤
      showStep.value = true;
    } else {
      showStep.value = false;
    }
  }
  
  // 表单验证规则
  const rules = {
    commentType: [
      { required: true, message: '请选择评估结果', trigger: 'change' }
    ],
    commentContent: [
      { required: true, message: '请填写评估意见', trigger: 'blur' }
    ],
    createTime: [
      { required: true, message: '请选择评估时间', trigger: 'change' }
    ],
  }
  const concelForm=()=>{
  emit('next')
  }
  const submitForm = async () => {
    if (!dataFormRef.value) return;
    try {
      await dataFormRef.value.validate();
      assessform.value.fileList = assessFileList.value.map(file => file.id);
      await ElMessageBox.confirm(
        '确定要提交评估吗？',
        '确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
      
      try {
        // 调用评估API
        await businessAPI.assess(assessform.value, form.id);
        ElMessage.success('评估已提交')
        emit('next')
      } catch (error) {
        console.error('Error submitting assessment:', error)
        ElMessage.error('提交失败，请重试')
      }
    } catch (error) {
      console.error('Validation error:', error);
      ElMessage.error('表单验证失败，请检查输入');
    }
  }
  
  //查看关联资产
  const openTransferDialog = async () => {
    transferDialog.visible = true;
    transferDialog.allAssets = form.assetsList;
  }
  
  // 附件下载
  const downloadFile = (row: any) => {
    if (row.url) {
      fetch(row.url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = row.name; // 设置自定义文件名
    link.style.display = 'none'; // 隐藏链接
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  })
  .catch(error => {
      ElMessage.error('附件不存在');
  });
    } else {
      ElMessage.error('附件不存在');
    }
  }
  // 获取安全工程师信息
interface  SafetyEngineer{
  id?:  any;
  engineerName?: string;
  engineerMobile: string;
  engineerWechat: string;
  engineerQq:string;
  engineerEmail: string;
}
const SafetyEngineerbox:any = ref<SafetyEngineer>(
  {
    id:null,
    engineerName:'',
    engineerMobile: '',
    engineerWechat: '',
    engineerQq: '',
    engineerEmail: '',
  },
);
const SafetyEngineerConfig = async () => {
  const statusRes = await safetyAPI.getSafetyEngineerConfig({})
  SafetyEngineerbox.value=statusRes[0] ||{}
}
//获取当前用户信息 填充name,employeeId,department,applicant
const getUserInfo = async () => {
  const data = await UserAPI.getProfile();
  form.applicantId = data.id;
  form.applicantName = data.nickname;
  form.employeeId = data.username;
  form.deptId = data.deptId;
  form.mobile = data.mobile || "";
};
  const handleQuery = async () => {
    if (props.ticketdata.id) {
      const statusRes:any = await businessAPI.getStepStatus(props.ticketdata.id);
      stepStatus.value = statusRes;
      for (const step in stepStatus.value) {
        if (stepStatus.value[step as keyof any] == 'process') {
          nowStep.value = step as string;
          break;
        }
      }
      await businessAPI.getFormData(props.ticketdata.id).then((data) => {
        Object.assign(form, data)
      })
     // 整改部门信息
   const foundItem = form.reviewProcessForms?.find(item => item.executeDeptType ==4);
   shstep.value=foundItem
      isCurrentStep();
      
      // 获取审核数据（前一步的结果）
      let filteredComments = form.comments ? form.comments.filter((item: any) => item.step == 2) : [];
      if (filteredComments.length > 0) {
        auditform.value = filteredComments[filteredComments.length - 1];
      }
      
      if (!showStep.value) {
        // 如果不是当前活动步骤，加载评估结果
        let assessComments = form.comments ? form.comments.filter((item: any) => item.step == 3) : [];
        if (assessComments.length > 0) {
          assessform.value = assessComments[assessComments.length - 1];
          assessFileList.value = assessform.value.fileList;

        }
        
      }
    }
  }
  
  // 设置默认评估时间
  watch(() => showStep.value, (val) => {
    if (val && !assessform.value.createTime) {
      assessform.value.createTime = formatLocalDateTime(new Date());
    }
  });
  
  onMounted(() => {
    handleQuery()
    SafetyEngineerConfig()
    getUserInfo()
  })
  
  </script>
  
  <style scoped>
  .security-assessment {
    padding: 20px;
  }
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .page-header h3 {
    color: var(--el-color-primary);
    font-size: 20px;
    margin: 0;
  }
  
  .form-container {
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .form-card {
    margin-bottom: 24px;
    border-radius: 8px;
  }
  
  .form-card :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .card-header span {
    font-size: 16px;
    font-weight: 500;
  }
  
  .asset-buttons {
    display: flex;
    gap: 12px;
  }
  
  .asset-buttons .el-button {
    min-width: 120px;
  }
  
  .form-actions {
    margin-top: 32px;
    text-align: center;
  }
  
  .form-actions .el-button {
    min-width: 120px;
    margin: 0 8px;
  }
  
  .file-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .file-item {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
  }
  
  .file-item:hover {
    color: var(--el-color-primary);
  }
  
  .upload-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
  
  /* 表格样式优化 */
  :deep(.el-table) {
    border-radius: 4px;
  }
  
  :deep(.el-table th) {
    background-color: var(--el-fill-color-light);
  }
  
  /* 输入框样式统一 */
  :deep(.el-input__inner) {
    border-radius: 4px;
  }
  
  /* 卡片内容区域padding */
  :deep(.el-card__body) {
    padding: 20px;
  }
  
  /* 资产弹窗样式 */
  .asset-dialog {
    :deep(.el-dialog__body) {
      padding: 20px;
    }
  }
  :deep(.el-form-item) {
  margin-right: 0;
}
  </style>
