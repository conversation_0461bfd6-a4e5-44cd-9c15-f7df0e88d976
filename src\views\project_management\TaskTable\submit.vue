<template>
  <div class="app-container">
    <el-card class="main-card">
      <div class="header-row">
        <!-- 搜索关键词 -->
        <div class="search-wrapper">
          <span class="input-label">任务关键词搜索：</span>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键词"
            clearable
            class="search-input"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </div>

        <!-- 项目切换 -->
        <div class="search-wrapper">
          <span class="input-label">项目切换搜索：</span>
          <el-select
            v-model="selectedProjectId"
            placeholder="请选择项目"
            filterable
            clearable
            @change="handleProjectChange"
            class="project-select"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="String(project.id)"
            />
          </el-select>
        </div>
      </div>

      <!-- 标签页 - 中文状态 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="全部" />
        <el-tab-pane label="未开始" name="未开始" />
        <el-tab-pane label="进行中" name="进行中" />
        <el-tab-pane label="已完成" name="已完成" />
        <el-tab-pane label="已逾期" name="已逾期" />
        <el-tab-pane label="已驳回" name="已驳回" />
      </el-tabs>

      <!-- 任务表格 -->
      <el-table
        :data="filteredTasks"
        row-key="id"
        :default-expand-all="true"
        :tree-props="{ children: 'children' }"
        style="width: 100%"
      >
        <el-table-column prop="title" label="任务名称" />
        <el-table-column prop="owner" label="负责人" width="120" />
        <el-table-column label="类型" width="100">
          <template #default="scope">
            <el-tag type="info" v-if="scope.row.type === 'milestone'">
              里程碑
            </el-tag>
            <el-tag type="success" v-else>任务</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '未开始'">未开始</el-tag>
            <el-tag type="warning" v-else-if="scope.row.status === '进行中'">
              进行中
            </el-tag>
            <el-tag type="success" v-else-if="scope.row.status === '已完成'">
              已完成
            </el-tag>
            <el-tag type="danger" v-else-if="scope.row.status === '已逾期'">
              已逾期
            </el-tag>
            <el-tag type="danger" v-else-if="scope.row.status === '已驳回'">
              已驳回
            </el-tag>
            <el-tag type="error" v-else>未知: {{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center">
          <template #default="scope">
            <!-- 查看按钮 -->
            <el-button
              size="small"
              type="default"
              v-if="scope.row.type !== 'milestone'"
              @click="openViewDialog(scope.row)"
              style="margin-left: 8px"
            >
              查看
            </el-button>

            <!-- 上传按钮 -->
            <el-button
              size="small"
              type="primary"
              v-if="scope.row.type !== 'milestone'"
              :disabled="
                (scope.row.canUpload && scope.row.status !== '已驳回') ||
                ['未开始', '已完成'].includes(scope.row.status)
              "
              @click="openUploadDialog(scope.row)"
              style="margin-left: 8px"
            >
              上传任务文件
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 查看文件弹窗 -->
    <el-dialog v-model="viewDialogVisible" title="查看任务文件" width="500px">
      <div v-if="currentViewTask">
        <p>
          <strong>任务名称：</strong>
          {{ currentViewTask.title }}
        </p>
        <p>
          <strong>负责人：</strong>
          {{ currentViewTask.owner || "未知" }}
        </p>
        <p>
          <strong>状态：</strong>
          {{ currentViewTask.status }}
        </p>
        <!-- 显示驳回原因 -->
        <p v-if="currentViewTask.rejectReason">
          <strong>驳回原因：</strong>
          {{ currentViewTask.rejectReason }}
        </p>

        <p><strong>已上传文件：</strong></p>
        <ul>
          <li v-for="file in currentViewFiles" :key="file.id">
            <a :href="file.url" target="_blank">{{ file.name }}</a>
            <el-button
              size="small"
              type="text"
              @click.stop="downloadFile(file)"
              style="margin-left: 8px"
            >
              下载
            </el-button>
          </li>
          <li v-if="currentViewFiles.length === 0 && !isLoadingFiles">
            无上传文件
          </li>
          <li v-if="isLoadingFiles">正在加载文件列表...</li>
        </ul>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 上传文件弹窗 -->
    <el-dialog v-model="uploadDialogVisible" title="上传任务文件" width="500px">
      <el-upload
        :auto-upload="false"
        drag
        class="upload-area"
        :http-request="customUploadRequest"
        :before-upload="beforeUpload"
        multiple
        :file-list="fileList"
        :disabled="uploadTarget?.canUpload && uploadTarget?.status !== '已驳回'"
        @change="handleFileChange"
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          拖拽文件到此处，或
          <em>点击上传</em>
        </div>
      </el-upload>
      <template #footer>
        <el-button @click="uploadDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="
            !fileList.length ||
            (uploadTarget?.canUpload && uploadTarget?.status !== '已驳回')
          "
          @click="handleUploadAll"
        >
          确认上传
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { ProjectAPI, TaskAPI } from "@/api/progress_management/createProject";
import { Project } from "@/types/project";
import type { UploadFile, UploadRequestOptions } from "element-plus";

interface TaskRow {
  id: number;
  title: string;
  type: string;
  status: "未开始" | "进行中" | "已完成" | "已逾期" | "已驳回" | string;
  owner: string;
  files: any[];
  children: TaskRow[];
  priority?: string;
  start?: string;
  end?: string;
  finish?: string;
  projectId?: number;
  members?: number[];
  canUpload?: boolean;
  rejectReason?: string;
  parentId?: number;
}

interface TaskFile {
  id: number;
  name: string;
  url: string;
  size?: number;
  createTime?: string;
}

const projects = ref<Project[]>([]);
const selectedProjectId = ref<string>("");
const activeTab = ref("全部");
const searchKeyword = ref("");

const uploadDialogVisible = ref(false);
const uploadTarget = ref<TaskRow | null>(null);
const fileList = ref<UploadFile[]>([]);

const viewDialogVisible = ref(false);
const currentViewTask = ref<TaskRow | null>(null);
const currentViewFiles = ref<TaskFile[]>([]);
const isLoadingFiles = ref(false);

const memberMap = new Map<number, string>([
  [1, "张三"],
  [2, "李四"],
  [3, "王五"],
]);

const handleProjectChange = async (projectId: string) => {
  if (!projectId) return;
  const numericId = Number(projectId);
  if (isNaN(numericId)) return;

  try {
    const rawTasks = await TaskAPI.getList(numericId);
    const tasks: TaskRow[] = [];
    const taskMap = new Map<number, TaskRow>();

    for (const task of rawTasks) {
      const taskStatus = task.status || "未知";
      const taskRow: TaskRow = {
        id: task.id,
        title: task.title,
        type: task.type,
        status: taskStatus,
        owner: task.members?.length
          ? memberMap.get(task.members[0]) || "未知"
          : "未知",
        files: [],
        children: [],
        priority: task.priority,
        start: task.start,
        end: task.end,
        finish: task.finish,
        projectId: task.projectId,
        members: task.members,
        canUpload: task.canUpload,
        rejectReason: task.rejectReason || "",
        parentId: task.parentId,
      };
      taskMap.set(task.id, taskRow);
      if (!task.parentId) {
        tasks.push(taskRow);
      }
    }

    for (const task of rawTasks) {
      if (task.parentId) {
        const parent = taskMap.get(task.parentId);
        const child = taskMap.get(task.id);
        if (parent && child) {
          parent.children = parent.children || [];
          parent.children.push(child);
        }
      }
    }

    const index = projects.value.findIndex((p) => String(p.id) === projectId);
    if (index > -1) {
      projects.value[index] = { ...projects.value[index], tasks };
    } else {
      const detail = await ProjectAPI.getDetail(numericId);
      projects.value.push({ ...detail, tasks });
    }
    searchKeyword.value = "";
    activeTab.value = "全部";
  } catch (e) {
    ElMessage.error("切换项目失败，请重试");
  }
};

onMounted(async () => {
  try {
    const list = await ProjectAPI.getList();
    projects.value = list;
    if (list.length > 0) {
      selectedProjectId.value = String(list[0].id);
      await handleProjectChange(selectedProjectId.value);
    }
  } catch (e) {
    ElMessage.error("初始化项目失败，请重试");
  }
});

const filteredTasks = computed<TaskRow[]>(() => {
  const project = projects.value.find(
    (p) => String(p.id) === selectedProjectId.value
  );
  if (!project || !project.tasks) return [];

  const filterFn = (task: TaskRow): TaskRow | null => {
    const matchKeyword = task.title.includes(searchKeyword.value);
    const matchStatus =
      activeTab.value === "全部" || task.status === activeTab.value;
    const children = task.children?.map(filterFn).filter(Boolean) as TaskRow[];

    if ((matchKeyword && matchStatus) || children?.length) {
      return { ...task, children };
    }
    return null;
  };

  return project.tasks.map(filterFn).filter(Boolean) as TaskRow[];
});

const handleSearch = () => {};
const handleTabChange = (tab: { name: string }) => {
  activeTab.value = tab.name;
};

const openViewDialog = async (row: TaskRow) => {
  currentViewTask.value = row;
  currentViewFiles.value = [];
  viewDialogVisible.value = true;
  isLoadingFiles.value = true;

  try {
    const taskId = Number(row.id);
    const files = await TaskAPI.getApprovalFiles(taskId);
    currentViewFiles.value = files;
  } catch (error) {
    ElMessage.error("获取文件列表失败，请重试");
  } finally {
    isLoadingFiles.value = false;
  }
};

const downloadFile = async (file: TaskFile) => {
  if (!file.url) {
    ElMessage.warning("文件地址不存在");
    return;
  }

  const loading = ElLoading.service({
    text: `正在下载 ${file.name}...`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // 使用fetch获取文件数据
    const response = await fetch(file.url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.setAttribute("download", file.name);
    link.style.display = "none";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success(`下载成功：${file.name}`);
  } catch (error) {
    console.error("文件下载失败:", error);
    ElMessage.error(`下载失败：${file.name}`);
  } finally {
    loading.close();
  }
};

const openUploadDialog = (row: TaskRow) => {
  uploadTarget.value = row;
  fileList.value = [];
  uploadDialogVisible.value = true;
};

const handleFileChange = (file: UploadFile, files: UploadFile[]) => {
  fileList.value = files;
};

const customUploadRequest = async (options: UploadRequestOptions) => {
  const { file, onSuccess, onError } = options;
  if (!uploadTarget.value) return;

  const loadingInstance = ElLoading.service({
    lock: true,
    text: `正在上传文件 ${file.name}...`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    await TaskAPI.uploadFiles({
      taskId: uploadTarget.value.id,
      files: [file as File],
    });
    ElMessage.success(`上传成功：${file.name}`);
    onSuccess?.({}, file);
  } catch (err) {
    ElMessage.error(`上传失败：${file.name}`);
    onError?.(err as any);
  } finally {
    loadingInstance.close();
  }
};

const handleUploadAll = async () => {
  if (!uploadTarget.value || !fileList.value.length) return;

  const loadingInstance = ElLoading.service({
    lock: true,
    text: "正在上传文件中...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const files = fileList.value.map((file) => file.raw as File);
    await TaskAPI.uploadFiles({
      taskId: uploadTarget.value.id,
      files,
    });
    ElMessage.success(`成功上传 ${files.length} 个文件`);
    uploadDialogVisible.value = false;
    fileList.value = [];

    if (selectedProjectId.value) {
      await handleProjectChange(selectedProjectId.value);
    }
  } catch {
    ElMessage.error("批量上传失败");
  } finally {
    loadingInstance.close();
  }
};

const beforeUpload = (file: File) => {
  if (!uploadTarget.value) {
    ElMessage.error("未找到任务信息");
    return false;
  }
  if (uploadTarget.value.type === "milestone") {
    ElMessage.error("里程碑任务不允许上传文件");
    return false;
  }
  if (uploadTarget.value.canUpload && uploadTarget.value.status !== "已驳回") {
    ElMessage.warning("该任务文件已上传，不能重复上传");
    return false;
  }
  if (["未开始", "已完成"].includes(uploadTarget.value.status)) {
    ElMessage.warning(
      `该任务处于${uploadTarget.value.status}状态，不能上传文件`
    );
    return false;
  }
  return true;
};
</script>

<style scoped>
.app-container {
  padding: 16px;
}
.main-card {
  padding: 16px;
  border-radius: 12px;
}
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.search-wrapper {
  display: flex;
  align-items: center;
}
.input-label {
  font-size: 14px;
  color: #606266;
  user-select: none;
  white-space: nowrap;
  margin-right: 8px;
}
.search-input {
  width: 300px;
}
.project-select {
  width: 200px;
}
.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 30px;
  text-align: center;
}
</style>
