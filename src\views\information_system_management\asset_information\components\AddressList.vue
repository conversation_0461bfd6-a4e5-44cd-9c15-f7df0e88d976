<template>
  <div class="form-subsection p-6 rounded shadow-md">
    <div class="header flex justify-between items-center mb-4">
      <div>访问地址列表</div>
      <div class="buttons flex space-x-3" v-if="!props.readonly">
        <el-button type="primary" @click="showDialog = true">新增访问地址</el-button>
        <el-button type="danger" @click="deleteSelected" :disabled="selected.length === 0">删除选中</el-button>
      </div>
    </div>

    <el-table :data="addressList" style="width: 100%" border :row-key="row => row.identifier"
      @selection-change="selected = $event" empty-text="暂无数据" size="small">
      <el-table-column v-if="!props.readonly" type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center" />

      <el-table-column label="录入类型" prop="inputType" align="center">
        <template #default="{ row }">{{ row.inputType || '—' }}</template>
      </el-table-column>

      <el-table-column label="录入时间" prop="inputTime" align="center">
        <template #default="{ row }">{{ row.inputTime || '—' }}</template>
      </el-table-column>

      <el-table-column label="访问地址标识" prop="identifier" align="center">
        <template #default="{ row }">{{ row.identifier || '—' }}</template>
      </el-table-column>

      <el-table-column label="访问地址" prop="address" align="center">
        <template #default="{ row }">{{ row.address || '—' }}</template>
      </el-table-column>

      <el-table-column label="录入部门" prop="dept" align="center">
        <template #default="{ row }">{{ row.dept || '—' }}</template>
      </el-table-column>

      <el-table-column label="录入人员" prop="user" align="center">
        <template #default="{ row }">{{ row.user || '—' }}</template>
      </el-table-column>

      <el-table-column label="联系方式" prop="contact" align="center">
        <template #default="{ row }">{{ row.contact || '—' }}</template>
      </el-table-column>

      <el-table-column label="探测时间" prop="detectTime" align="center">
        <template #default="{ row }">{{ row.detectTime || '—' }}</template>
      </el-table-column>

      <el-table-column label="探测状态" prop="status" align="center">
        <template #default="{ row }">{{ row.status || '—' }}</template>
      </el-table-column>

      <el-table-column v-if="!props.readonly" label="操作" width="100" align="center">
        <template #default="scope">
          <el-button size="small" type="danger" @click="deleteRow(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 弹窗（仅新增模式下显示） -->
    <el-dialog v-if="!props.readonly" title="新增访问地址" v-model="showDialog" width="1200px" :before-close="handleClose"
      custom-class="custom-dialog">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="140px" label-position="left" class="dialog-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="录入类型">
              <el-input v-model="form.inputType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="录入时间">
              <el-input v-model="form.inputTime" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="录入部门">
              <el-input v-model="form.dept" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="录入人员">
              <el-input v-model="form.user" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系方式">
              <el-input v-model="form.contact" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="探测时间">
              <el-input v-model="form.detectTime" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="访问地址标识" prop="identifier">
              <el-input v-model="form.identifier" autocomplete="off" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="访问地址" prop="address">
              <el-input v-model="form.address" autocomplete="off" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="探测状态">
              <el-input v-model="form.status" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmAdd">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue'])

const addressList = ref([...props.modelValue])
const selected = ref([])

watch(
  () => props.modelValue,
  (newVal) => {
    addressList.value = [...(newVal || [])]
  }
)

const showDialog = ref(false)
const formRef = ref(null)

const form = ref({
  identifier: '',
  address: '',
  inputType: '',
  inputTime: '',
  dept: '',
  user: '',
  contact: '',
  detectTime: '',
  status: ''
})

const rules = {
  identifier: [{ required: true, message: '请输入地址标识', trigger: 'blur' }],
  address: [{ required: true, message: '请输入访问地址', trigger: 'blur' }]
}

const confirmAdd = async () => {
  await formRef.value.validate()
  addressList.value.push({ ...form.value })
  emit('update:modelValue', addressList.value)
  showDialog.value = false
}

const deleteSelected = () => {
  const selectedIds = selected.value.map((item) => item.identifier)
  addressList.value = addressList.value.filter(
    (item) => !selectedIds.includes(item.identifier)
  )
  emit('update:modelValue', addressList.value)
  selected.value = []
}

const deleteRow = (index) => {
  addressList.value.splice(index, 1)
  emit('update:modelValue', addressList.value)
}

const getLocalDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const handleClose = () => {
  showDialog.value = false
}

watch(showDialog, (val) => {
  if (val) {
    const now = getLocalDateTime()
    form.value = {
      identifier: '',
      address: '',
      inputType: '手动录入',
      inputTime: now,
      dept: '技术部',
      user: '张三',
      contact: '13800138000',
      detectTime: now,
      status: '在线'
    }
  }
})
</script>

<style scoped></style>
