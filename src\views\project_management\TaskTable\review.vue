<template>
  <div class="app-container">
    <el-card class="main-card">
      <div class="header-row">
        <!-- 任务名称关键词搜索框及说明 -->
        <div class="search-wrapper">
          <span class="input-label">任务关键词搜索：</span>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入关键词"
            clearable
            class="search-input"
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </div>

        <!-- 项目切换下拉搜索框及说明 -->
        <div class="search-wrapper">
          <span class="input-label">项目切换搜索：</span>
          <el-select
            v-model="selectedProjectId"
            placeholder="请选择项目"
            filterable
            clearable
            @change="handleProjectChange"
            class="project-select"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="String(project.id)"
            />
          </el-select>
        </div>
      </div>

      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="all" />
        <el-tab-pane label="未开始" name="not_started" />
        <el-tab-pane label="进行中" name="in_progress" />
        <el-tab-pane label="已完成" name="completed" />
        <el-tab-pane label="已逾期" name="overdue" />
        <!-- 添加已驳回标签页 -->
        <el-tab-pane label="已驳回" name="rejected" />
      </el-tabs>

      <el-table
        :data="filteredTasks"
        row-key="id"
        :default-expand-all="true"
        :tree-props="{ children: 'children' }"
        style="width: 100%"
      >
        <el-table-column prop="title" label="任务名称" />
        <el-table-column prop="owner" label="负责人" width="120" />
        <el-table-column label="类型" width="100">
          <template #default="scope">
            <el-tag type="info" v-if="scope.row.type === 'milestone'">
              里程碑
            </el-tag>
            <el-tag type="success" v-else>任务</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'not_started'">未开始</el-tag>
            <el-tag
              type="warning"
              v-else-if="scope.row.status === 'in_progress'"
            >
              进行中
            </el-tag>
            <el-tag type="success" v-else-if="scope.row.status === 'completed'">
              已完成
            </el-tag>
            <el-tag type="danger" v-else-if="scope.row.status === 'overdue'">
              已逾期
            </el-tag>
            <!-- 添加已驳回状态显示 -->
            <el-tag type="danger" v-else-if="scope.row.status === 'rejected'">
              已驳回
            </el-tag>
            <el-tag type="info" v-else>未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center">
          <template #default="scope">
            <!-- 只有非里程碑任务显示查看和审批按钮 -->
            <el-button
              size="small"
              type="default"
              v-if="scope.row.type !== 'milestone'"
              @click="openViewDialog(scope.row)"
              style="margin-left: 8px"
            >
              查看
            </el-button>
            <el-button
              size="small"
              type="primary"
              v-if="scope.row.type !== 'milestone'"
              :disabled="!canApproveTask(scope.row)"
              @click="openApproveDialog(scope.row)"
              style="margin-left: 8px"
            >
              审批任务
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 查看/审批弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isViewMode ? '查看任务' : '审批任务'"
      width="500px"
    >
      <div v-if="currentTarget">
        <p>
          <strong>任务名称：</strong>
          {{ currentTarget.title }}
        </p>
        <p>
          <strong>负责人：</strong>
          {{ currentTarget.owner || "未知" }}
        </p>
        <p>
          <strong>状态：</strong>
          {{ statusText(currentTarget.status) }}
        </p>

        <p><strong>审批文件：</strong></p>
        <ul>
          <li v-for="file in approvalFiles" :key="file.id">
            <a :href="file.url" target="_blank">{{ file.name }}</a>
            <el-button
              size="mini"
              type="text"
              @click.stop="downloadSingleFile(file)"
              style="margin-left: 8px"
            >
              单独下载
            </el-button>
          </li>
          <li v-if="approvalFiles.length === 0">无审批文件</li>
        </ul>
      </div>
      <template #footer>
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <el-button
            type="default"
            @click="downloadAllFiles"
            :disabled="approvalFiles.length === 0"
          >
            下载全部文件
          </el-button>
          <div>
            <el-button @click="dialogVisible = false">取消</el-button>
            <!-- 驳回按钮，仅在审批模式显示 -->
            <el-button
              type="danger"
              @click="submitRejection"
              v-if="!isViewMode"
              style="margin-left: 8px"
            >
              驳回
            </el-button>
            <el-button
              type="primary"
              @click="submitApproval"
              v-if="!isViewMode"
              style="margin-left: 8px"
            >
              通过审批
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  ElMessage,
  ElLoading,
  ElNotification,
  ElMessageBox,
} from "element-plus";
import {
  ProjectAPI,
  TaskAPI,
} from "../../../api/progress_management/createProject";
import { Project, TaskRow, TaskStatus } from "@/types/project";

// 定义审批文件类型接口
interface ApprovalFile {
  id: number; // 文件ID
  name: string; // 文件名
  url: string; // 文件访问地址
  size?: number; // 文件大小（可选）
  createTime?: string; // 创建时间（可选）
}

// 路由实例
const route = useRoute();
const router = useRouter();

// 响应式数据
const projects = ref<Project[]>([]);
const selectedProjectId = ref<string>("");
const activeTab = ref("all");
const searchKeyword = ref("");
const dialogVisible = ref(false); // 合并查看和审批弹窗的显示状态
const currentTarget = ref<TaskRow | null>(null); // 当前查看或审批的目标任务
const isViewMode = ref(false); // 是否为查看模式
const approvalFiles = ref<ApprovalFile[]>([]); // 审批文件列表

// 模拟成员ID到名称的映射（实际中应从后端获取）
const memberMap = new Map<number, string>([
  [1, "张三"],
  [2, "李四"],
  [3, "王五"],
]);

// 中文状态到 TaskStatus 的映射（保持原有映射，仅添加已驳回）
const statusMap = new Map<string, TaskStatus>([
  ["未开始", "not_started"],
  ["进行中", "in_progress"],
  ["已完成", "completed"],
  ["已逾期", "overdue"],
  ["已驳回", "rejected"], // 仅添加这一行
]);

// 任务过滤，返回符合条件的树形任务列表
const filteredTasks = computed<TaskRow[]>(() => {
  const project = projects.value.find(
    (p) => String(p.id) === selectedProjectId.value
  );
  if (!project || !project.tasks) return [];

  const filterFn = (task: TaskRow): TaskRow | null => {
    const matchKeyword = task.title.includes(searchKeyword.value);
    const matchStatus =
      activeTab.value === "all" || task.status === activeTab.value;
    const children = task.children?.map(filterFn).filter(Boolean) as TaskRow[];

    if (task.status === "unknown") {
      console.warn(
        `任务 ${task.id} 状态为 unknown，可能 API 数据异常: ${task.status}`
      );
    }

    if ((matchKeyword && matchStatus) || children?.length) {
      return { ...task, children };
    }
    return null;
  };

  return project.tasks.map(filterFn).filter(Boolean) as TaskRow[];
});

// 选择项目后加载该项目任务列表
const handleProjectChange = async (projectId: string) => {
  console.log("[handleProjectChange] 传入 projectId:", projectId);

  if (!projectId) {
    console.warn("[handleProjectChange] projectId 为空，重置任务数据");
    selectedProjectId.value = "";
    const project = projects.value.find((p) => p.tasks);
    if (project) {
      project.tasks = [];
    }
    return;
  }

  const numericId = Number(projectId);
  if (isNaN(numericId)) {
    console.error(
      `[handleProjectChange] projectId 转换数字失败，值为: "${projectId}"`
    );
    ElMessage.error("无效的项目ID");
    return;
  }

  try {
    console.log(`[handleProjectChange] 请求任务列表，projectId=${numericId}`);
    const rawTasks = await TaskAPI.getList(numericId);
    console.log(
      `[handleProjectChange] 获取任务列表成功，任务数量：`,
      rawTasks.length
    );

    // 转换扁平任务列表为树形结构
    const tasks: TaskRow[] = [];
    const taskMap = new Map<number, TaskRow>();

    // 第一步：创建 TaskRow 对象，使用 API 的状态
    for (const task of rawTasks) {
      // 处理后端返回的中文状态，包括"已驳回"
      const validStatus: TaskStatus = statusMap.get(task.status) || "unknown";
      if (validStatus === "unknown") {
        console.warn(`任务 ${task.id} 的状态无效: ${task.status}`);
      }

      const taskRow: TaskRow = {
        id: task.id,
        title: task.title,
        type: task.type,
        status: validStatus,
        owner: task.members?.length
          ? memberMap.get(task.members[0]) || "未知"
          : "未知",
        children: [],
        priority: task.priority,
        start: task.start,
        end: task.end,
        finish: task.finish,
        projectId: task.projectId,
        members: task.members,
        parentId: task.parentId, // 保存父任务ID用于查找
      };
      taskMap.set(task.id, taskRow);
      if (!task.parentId) {
        tasks.push(taskRow);
      }
    }

    // 第二步：根据 parentId 分配子任务
    for (const task of rawTasks) {
      if (task.parentId) {
        const parent = taskMap.get(task.parentId);
        const child = taskMap.get(task.id);
        if (parent && child) {
          parent.children = parent.children || [];
          parent.children.push(child);
        }
      }
    }

    const index = projects.value.findIndex((p) => String(p.id) === projectId);
    if (index > -1) {
      projects.value[index] = { ...projects.value[index], tasks };
    } else {
      console.log(
        `[handleProjectChange] 项目列表中未找到 projectId=${projectId}，尝试请求项目详情`
      );
      const detail = await ProjectAPI.getDetail(numericId);
      projects.value.push({ ...detail, tasks });
    }
    searchKeyword.value = "";
    activeTab.value = "all";
  } catch (e) {
    console.error("[handleProjectChange] 切换项目失败：", e);
    ElMessage.error("加载项目任务失败");
  }
};

// 初始化加载项目列表并处理路由参数
const initData = async () => {
  try {
    // 获取项目列表
    const list = await ProjectAPI.getList();
    console.log("[initData] 初始化获取项目列表:", list);
    projects.value = list;

    // 检查路由参数中的 projectId
    const projectIdFromRoute = route.params.projectId
      ? String(route.params.projectId)
      : list.length > 0
        ? String(list[0].id)
        : "";

    if (projectIdFromRoute) {
      console.log(`[initData] 路由或默认 projectId: ${projectIdFromRoute}`);
      selectedProjectId.value = projectIdFromRoute;
      await handleProjectChange(projectIdFromRoute);
    } else {
      console.warn("[initData] 项目列表为空或无有效路由参数");
      ElMessage.warning("无可用项目，请先创建项目");
    }
  } catch (e) {
    console.error("[initData] 初始化项目失败：", e);
    ElMessage.error("初始化项目失败");
  }
};

// 监听路由参数变化
watch(
  () => route.params.projectId,
  async (newProjectId) => {
    if (newProjectId) {
      const projectId = String(newProjectId);
      console.log(
        "[watch route.params.projectId] 路由变化，新 projectId:",
        projectId
      );
      if (projects.value.some((p) => String(p.id) === projectId)) {
        selectedProjectId.value = projectId;
        await handleProjectChange(projectId);
      } else {
        console.warn(
          "[watch route.params.projectId] 无效的 projectId:",
          projectId
        );
        ElMessage.error("无效的项目ID");
        router.push({ name: "DefaultRoute" }); // 可选：重定向到默认路由
      }
    }
  },
  { immediate: true }
);

// 生命周期钩子
onMounted(async () => {
  await initData();
});

// 搜索框触发
const handleSearch = () => {
  console.log("[handleSearch] 搜索关键词:", searchKeyword.value);
};

// 标签切换事件
const handleTabChange = (tab: { name: string }) => {
  activeTab.value = tab.name;
  console.log("[handleTabChange] 切换标签:", activeTab.value);
};

// 递归获取指定任务的所有子任务
const getAllSubtasks = (task: TaskRow): TaskRow[] => {
  let subtasks: TaskRow[] = [...(task.children || [])];
  task.children?.forEach((child) => {
    subtasks = [...subtasks, ...getAllSubtasks(child)];
  });
  return subtasks;
};

// 递归检查所有子任务是否都已完成审批
const areAllSubtasksApproved = (milestone: TaskRow): boolean => {
  const allSubtasks = getAllSubtasks(milestone);

  // 如果没有子任务，不需要审批
  if (allSubtasks.length === 0) {
    return false;
  }

  // 检查所有子任务是否都已完成（排除已驳回状态）
  return allSubtasks.every((task) => task.status === "completed");
};

// 查找指定任务的父里程碑
const findParentMilestone = (taskId: number): TaskRow | null => {
  // 先找到当前任务
  let task: TaskRow | undefined;
  const findTask = (tasks: TaskRow[]): TaskRow | undefined => {
    for (const t of tasks) {
      if (t.id === taskId) {
        return t;
      }
      const found = findTask(t.children || []);
      if (found) return found;
    }
    return undefined;
  };

  // 遍历所有项目查找任务
  for (const project of projects.value) {
    if (project.tasks) {
      task = findTask(project.tasks);
      if (task) break;
    }
  }

  if (!task || !task.parentId) return null;

  // 查找父里程碑
  const findMilestone = (
    tasks: TaskRow[],
    parentId: number
  ): TaskRow | null => {
    for (const t of tasks) {
      if (t.id === parentId) {
        return t.type === "milestone" ? t : null;
      }
      const found = findMilestone(t.children || [], parentId);
      if (found) return found;
    }
    return null;
  };

  // 从项目中查找父里程碑
  for (const project of projects.value) {
    if (project.tasks) {
      const milestone = findMilestone(project.tasks, task.parentId);
      if (milestone) return milestone;
    }
  }

  return null;
};

// 自动审批里程碑
const autoApproveMilestone = async (milestone: TaskRow) => {
  try {
    console.log(
      `[autoApproveMilestone] 自动审批里程碑: ${milestone.id} - ${milestone.title}`
    );

    // 调用API审批里程碑
    await TaskAPI.updateApprovalStatus(milestone.id, "approved");

    // 显示通知
    ElNotification({
      title: "里程碑自动审批",
      message: `里程碑 "${milestone.title}" 下所有任务已完成，已自动通过审批`,
      type: "success",
    });

    // 重新加载任务列表
    if (selectedProjectId.value) {
      await handleProjectChange(selectedProjectId.value);
    }
  } catch (e) {
    console.error(`[autoApproveMilestone] 自动审批失败:`, e);
    ElNotification({
      title: "自动审批失败",
      message: `里程碑自动审批过程中发生错误`,
      type: "error",
    });
  }
};

// 判断任务是否可以审批（已驳回的任务也可以重新审批）
const canApproveTask = (row: TaskRow) => {
  // 非里程碑任务，状态为进行中、已逾期或已驳回可以审批
  return (
    row.type !== "milestone" &&
    ["in_progress", "overdue", "rejected"].includes(row.status)
  );
};

// 打开查看对话框
const openViewDialog = async (row: TaskRow) => {
  console.log("[openViewDialog] 打开查看弹窗，任务ID:", row.id);
  try {
    // 清空之前的文件列表
    approvalFiles.value = [];

    // 调用API获取审批文件
    const files = await TaskAPI.getApprovalFiles(row.id);
    console.log(`[openViewDialog] 获取到 ${files.length} 个审批文件`);

    // 存储查看目标和文件列表，设置为查看模式
    currentTarget.value = row;
    approvalFiles.value = files;
    isViewMode.value = true;

    // 显示弹窗
    dialogVisible.value = true;
  } catch (e) {
    console.error("[openViewDialog] 获取文件失败：", e);
    ElMessage.error("获取文件失败");
  }
};

// 打开审批对话框
const openApproveDialog = async (row: TaskRow) => {
  console.log("[openApproveDialog] 打开审批弹窗，任务ID:", row.id);
  try {
    // 清空之前的文件列表
    approvalFiles.value = [];

    // 调用API获取审批文件
    const files = await TaskAPI.getApprovalFiles(row.id);
    console.log(`[openApproveDialog] 获取到 ${files.length} 个审批文件`);

    // 存储审批目标和文件列表，设置为审批模式
    currentTarget.value = row;
    approvalFiles.value = files;
    isViewMode.value = false;

    // 显示弹窗
    dialogVisible.value = true;
  } catch (e) {
    console.error("[openApproveDialog] 获取审批文件失败：", e);
    ElMessage.error("获取审批文件失败");
  }
};

// 提交审批通过
const submitApproval = async () => {
  if (!currentTarget.value) return;

  const loading = ElLoading.service({
    lock: true,
    text: "正在提交审批...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    console.log("[submitApproval] 提交审批，任务ID:", currentTarget.value.id);
    await TaskAPI.updateApprovalStatus(currentTarget.value.id, "approved");
    console.log("[submitApproval] 审批提交成功");

    dialogVisible.value = false;
    ElMessage.success("审批已通过");

    // 重新加载任务列表
    if (selectedProjectId.value) {
      await handleProjectChange(selectedProjectId.value);

      // 检查其父里程碑是否所有子任务都已完成
      const parentMilestone = findParentMilestone(currentTarget.value.id);
      if (parentMilestone) {
        console.log(
          `[submitApproval] 检查父里程碑 ${parentMilestone.id} 子任务完成情况`
        );
        if (areAllSubtasksApproved(parentMilestone)) {
          // 所有子任务都已完成，自动审批里程碑
          await autoApproveMilestone(parentMilestone);
        }
      }
    }
  } catch (e) {
    console.error("[submitApproval] 提交审批失败：", e);
    ElMessage.error("提交审批失败");
  } finally {
    loading.close();
  }
};

// 提交驳回
const submitRejection = async () => {
  if (!currentTarget.value) return;

  // 二次确认
  try {
    await ElMessageBox.confirm("确定要驳回此任务吗？", "确认驳回", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
  } catch {
    // 用户取消操作
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: "正在提交驳回...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    console.log("[submitRejection] 提交驳回，任务ID:", currentTarget.value.id);
    // 调用驳回API（不传递驳回原因）
    await TaskAPI.rejectApproval(currentTarget.value.id);
    console.log("[submitRejection] 驳回提交成功");

    dialogVisible.value = false;
    ElMessage.success("已成功驳回");

    // 重新加载任务列表
    if (selectedProjectId.value) {
      await handleProjectChange(selectedProjectId.value);
    }
  } catch (e) {
    console.error("[submitRejection] 提交驳回失败：", e);
    ElMessage.error("提交驳回失败");
  } finally {
    loading.close();
  }
};

// 下载单个文件
const downloadSingleFile = async (file: ApprovalFile) => {
  try {
    console.log(
      `[downloadSingleFile] 开始下载文件: ${file.name} (ID: ${file.id})`
    );

    // 创建下载链接
    const link = document.createElement("a");
    link.id = `download-link-${Date.now()}-${file.id}`;
    link.href = file.url;
    link.download = file.name;
    document.body.appendChild(link);

    requestAnimationFrame(() => {
      link.click();
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);
    });

    ElMessage.success(`已开始下载 ${file.name}`);
  } catch (e) {
    console.error(`[downloadSingleFile] 文件 ${file.name} 下载失败：`, e);
    ElMessage.error(`文件 ${file.name} 下载失败`);
  }
};

// 下载所有文件
const downloadAllFiles = async () => {
  if (approvalFiles.value.length === 0) {
    console.warn("无审批文件可下载");
    ElMessage.warning("无审批文件可下载");
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: `正在准备下载 ${approvalFiles.value.length} 个文件...`,
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    for (let i = 0; i < approvalFiles.value.length; i++) {
      const file = approvalFiles.value[i];
      console.log(
        `[downloadAllFiles] 开始下载第 ${i + 1} 个文件: ${file.name}`
      );

      const link = document.createElement("a");
      link.id = `download-link-${Date.now()}-${i}-${file.id}`;
      link.href = file.url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      if (i < approvalFiles.value.length - 1) {
        const delay = 700 + Math.random() * 300;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    ElMessage.success(`已触发所有 ${approvalFiles.value.length} 个文件的下载`);
  } catch (e) {
    console.error("[downloadAllFiles] 批量下载失败：", e);
    ElMessage.error("批量下载失败，请尝试单独下载");
  } finally {
    loading.close();
  }
};

// 状态转文字显示（添加已驳回状态）
const statusText = (status: TaskStatus) => {
  switch (status) {
    case "not_started":
      return "未开始";
    case "in_progress":
      return "进行中";
    case "completed":
      return "已完成";
    case "overdue":
      return "已逾期";
    case "rejected": // 添加已驳回状态
      return "已驳回";
    default:
      return "未知";
  }
};
</script>

<style scoped>
.app-container {
  padding: 16px;
}
.main-card {
  padding: 16px;
  border-radius: 12px;
}
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.search-wrapper {
  display: flex;
  align-items: center;
}
.input-label {
  font-size: 14px;
  color: #606266;
  user-select: none;
  white-space: nowrap;
  margin-right: 8px;
}
.search-input {
  width: 300px;
}
.project-select {
  width: 200px;
}
</style>
