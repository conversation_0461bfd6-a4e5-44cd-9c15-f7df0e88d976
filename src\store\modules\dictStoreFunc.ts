import { defineS<PERSON> } from 'pinia';
import Dict<PERSON><PERSON> from "@/api/dict";
import Dept<PERSON><PERSON> from "@/api/dept";
import systemsEntityAPI from "@/api/assets_management/details/systems-entity";

export const useDictStore = defineStore('dict', {
  state: () => ({
    dictCache: {} as Record<string, { label: string, value: string | number, children?: any[] }[]>,
  }),
  actions: {
    async fetchOptions(code: string) {
      if (!this.dictCache[code]) {
        let options = [];
        try {
          if (code === "system") {
            options = await systemsEntityAPI.getOptions();
          } else if (code === "dept") {
            const response = await DeptAPI.getOptions();
            options = this.mapOptions(response);
          } else {
            options = await DictAPI.getOptions(code);
          }
          this.dictCache[code] = options;
          console.log(`Fetched options for code ${code}:`, options);
        } catch (error) {
          console.error(`Failed to fetch options for code ${code}:`, error);
        }
      } else {
        console.log(`Cache hit for code ${code}`);
      }
      return this.dictCache[code];
    },
    mapOptions(data: any): { label: string, value: string | number, children?: any[] }[] {
      return data.map((item: any) => ({
        label: item.label,
        value: item.value,
        children: item.children ? this.mapOptions(item.children) : undefined
      }));
    }
  }
});
