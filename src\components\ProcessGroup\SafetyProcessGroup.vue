<template>
  <base-process-group 
    ref="baseProcessGroupRef" 
    :business-type="businessType" 
    :departments="departments"
    :sms-templates="smsTemplates" 
    :dept-type-map="deptTypeMap"
    :step-configs="stepConfigs"
    :shstep="shstep"
    :safetype="safetype"
    @update:departments="handleDepartmentsUpdate" 
  />
</template>

<script setup lang="ts">
import { type } from 'os'
import { ref, computed } from 'vue'
import BaseProcessGroup from './BaseProcessGroup.vue'

// 定义接口
interface Department {
  id: number
  name: string
  active: boolean
  execution?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
  notification?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
}

interface UserInfo {
  id: number | string
  username: string
  nickname: string
  deptName: string
  mobile: string
}
// 定义props
const props = defineProps<{
shstep:Object,
}>()
// 业务类型
const businessType = 'safety'

// 部门类型映射
const deptTypeMap = {
  0: '2', // 工单审核
  1: '3', // 工单整改
  2: '4', // 工单复核
  3: '5'  // 工单评价
}
// 步骤配置 - 只对整改部门（索引1）进行特殊配置
const stepConfigs = computed(() => ({
  1: { // 工单整改部门（索引1）
    disableExecutionEdit: true,
    // disableNotificationEdit: true,
    executionButtonText: '自动配置',
    // notificationButtonText: '自动配置',
    enableAutoConfig: true
  }
  // 其他步骤不配置，使用默认行为
}))

// 流程步骤部门数据
const departments = ref<Department[]>([
  { id: 1, name: '工单审核部门', active: true },
  { id: 2, name: '工单整改部门', active: false },
  { id: 3, name: '工单复核部门', active: false },
  { id: 4, name: '工单评价部门', active: false }
])

watch(() => props.shstep, (newVal, oldVal) => {
  departments.value[1].execution=props.shstep

});
// 短信模板数据
const smsTemplates = ref([
  { id: 1, name: '安全检查通知模板', content: '尊敬的${name}，您有一项安全检查任务需要处理，请及时查看。' },
  { id: 2, name: '整改通知模板', content: '尊敬的${name}，您有一项安全整改任务需要完成，请及时处理。' }
])

// 基础组件引用
const baseProcessGroupRef = ref()

// 处理部门更新
const handleDepartmentsUpdate = (updatedDepartments: Department[]) => {
  console.log("处理部门更新",updatedDepartments)
  departments.value = updatedDepartments
}

// 获取流程数据
const getProcessData = () => {
  return baseProcessGroupRef.value?.getProcessData()
}

// 获取验证状态
const getValidationStatus = () => {
  return baseProcessGroupRef.value?.getValidationStatus()
}

// 初始化现有数据
const initFromExistingData = (data: any) => {
  baseProcessGroupRef.value?.initFromExistingData(data)
}

// 重置组件
const reset = () => {
  baseProcessGroupRef.value?.reset()
}

// 自动配置整改部门 - 调用通用方法
const autoConfigRemediationDept = (config: {
  deptId: number
  deptName: string
  managerName: string
  asset: any
}) => {
  // 调用基础组件的通用自动配置方法，配置索引1（整改部门）
  baseProcessGroupRef.value?.autoConfigStep(1, config)
}

const applyDefaultConfig = () => {
  return baseProcessGroupRef.value?.applyDefaultConfig()
}

// 暴露方法
defineExpose({
  getProcessData,
  getValidationStatus,
  initFromExistingData,
  reset,
  autoConfigRemediationDept, 
  applyDefaultConfig
})
</script>
