<template>
  <el-dialog 
    v-model="dialogVisible" 
    :title="title" 
    width="80%" 
    @close="handleClose"
  >
    <el-table :data="assetsList" border style="width: 100%">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="assetType" label="资产类型" align="center" width="120">
        <template #default="scope">
          <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="资产名称" show-overflow-tooltip />
      <el-table-column prop="address" label="资产地址" min-width="150" show-overflow-tooltip />
      <el-table-column prop="deptId" label="管理部门" width="180">
        <template #default="scope">
          <Dictmap v-model="scope.row.deptId" code="dept0x0" />
        </template>
      </el-table-column>
      <el-table-column prop="ownerName" label="管理人员" width="120" />
    </el-table>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'
import Dictmap from '@/components/Dictmap/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '关联资产'
  },
  assetsList: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = ref(props.visible)

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
})

// 监听dialog自身visible变化
watch(() => dialogVisible.value, (val) => {
  emit('update:visible', val)
})

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
/* 保持原有样式 */
:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
