import { defineMock } from "./base";

const systems: SystemInfo[] = [
  {
    id: 1,
    sysname: "系统1",
    IP: "***********,127.0.0.1",
    domain: "example.com,admin.com",
    manageUnit: "管理单位1",
    manager: "张三",
    deptId: "IT部门", // 注意我传过来的是部门ID，请把ID改成部门名称存数据库，给我返回名称就行
    contact: "123456789",
    otherManageUnit: "第三方运维单位1",
    otherManager: "李四",
    otherContact: "987654321",
    rank: "一级",
    status: 1, // 开放状态 0-未开放 1-已开放 2-半开放
    assetsNum: 10, //通过系统ID查询资产数量，给我返回数量就行
    createTime: "2023-01-01", // 注意实时生成一下创建资产的时间
    notes: "备注信息1",
  },
  {
    id: 2,
    sysname: "系统2",
    IP: "***********,127.0.0.1",
    domain: "example2.com",
    manageUnit: "管理单位2",
    manager: "王五",
    deptId: "运维部门", // 注意我传过来的是部门ID，请把ID改成部门名称存数据库，给我返回名称就行
    contact: "123456789",
    otherManageUnit: "第三方运维单位2",
    otherManager: "赵六",
    otherContact: "987654321",
    rank: "二级",
    status: 0, // 开放状态 0-未开放 1-已开放 2-半开放
    assetsNum: 20, //通过系统ID查询资产数量，给我返回数量就行
    createTime: "2023-02-01", // 注意实时生成一下创建系统的时间
    notes: "备注信息2",
  },
];

interface SystemInfo {
  id: number;
  sysname: string;
  keywords?: string;
  IP: string;
  domain: string;
  manageUnit: string;
  manager: string;
  deptId: string;
  contact: string;
  otherManageUnit: string;
  otherManager: string;
  otherContact: string;
  rank: string;
  status: number;
  assetsNum: number;
  notes?: string;
  createTime: string;
}

export default defineMock([
  {
    url: "systems/systemsAll",
    method: ["GET"],
    body: {
      code: "00000",
      data: systems,
      msg: "一切ok",
    },
  },
  {
    url: "systems/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: systems.map(system => ({ label: system.sysname, value: system.id })),
      msg: "一切ok",
    },
  },
  {
    url: "systems",
    method: ["POST"],
    body({ body }: { body: SystemInfo }) {
      const newSystem = { ...body, id: systems.length + 1 };
      systems.push(newSystem);
      return {
        code: "00000",
        data: newSystem,
        msg: "新增系统成功",
      };
    },
  },
  {
    url: "systems/:id",
    method: ["PUT"],
    body({ params, body }: { params: { id: number }; body: Partial<SystemInfo> }) {
      const { id } = params;
      const index = systems.findIndex((system) => system.id == id);
      if (index !== -1) {
        systems[index] = { ...systems[index], ...body };
        return {
          code: "00000",
          data: systems[index],
          msg: "修改系统成功",
        };
      } else {
        return {
          code: "404",
          msg: "系统未找到",
        };
      }
    },
  },
  {
    url: "systems/:id",
    method: ["DELETE"],
    body({ params }: { params: { id: number } }) {
      const { id } = params;
      const index = systems.findIndex((system) => system.id == id);
      if (index !== -1) {
        systems.splice(index, 1);
        return {
          code: "00000",
          data: null,
          msg: "删除系统成功",
        };
      } else {
        return {
          code: "404",
          msg: "系统未找到",
        };
      }
    },
  },
  {
    url: "systems/:id/form",
    method: ["GET"],
    body({ params }: { params: { id: number } }) {
      const { id } = params;
      const system = systems.find((system) => system.id == id);
      if (system) {
        return {
          code: "00000",
          data: system,
          msg: "一切ok",
        };
      } else {
        return {
          code: "404",
          msg: "系统未找到",
        };
      }
    },
  },
  {
    url: "systems/:ids",
    method: ["DELETE"],
    body({ params }: { params: { ids: string } }) {
      const { ids } = params;
      const idArray = ids.split(",").map((id) => parseInt(id.trim(), 10));
      idArray.forEach((id) => {
        const index = systems.findIndex((system) => system.id == id);
        if (index !== -1) {
          systems.splice(index, 1);
        }
      });
      return {
        code: "00000",
        data: null,
        msg: "批量删除成功",
      };
    },
  },
  {
    url: "systems/template",
    method: ["GET"],
    body: {
      code: "00000",
      data: "模板文件内容",
      msg: "一切ok",
    },
  },
  {
    url: "systems/import",
    method: ["POST"],
    body: {
      code: "00000",
      data: null,
      msg: "导入成功",
    },
  },
  {
    url: "systems/export",
    method: ["GET"],
    body: {
      code: "00000",
      data: "导出文件内容",
      msg: "一切ok",
    },
  },
  {
    url: "systems",
    method: ["GET"],
    body({ query }: { query: Partial<SystemInfo> }) {
      const { keywords, IP, rank, status, createTime, domain } = query;
      let filteredSystems = systems;
  
      if (keywords) {
        filteredSystems = filteredSystems.filter((system) =>
          system.sysname.includes(keywords) ||
          system.manager.includes(keywords) ||
          system.domain.includes(keywords) ||
          system.manageUnit.includes(keywords)
        );
      }
  
      if (IP) {
        filteredSystems = filteredSystems.filter((system) => system.IP.includes(IP));
      }
  
      if (rank) {
        filteredSystems = filteredSystems.filter((system) => system.rank.includes(rank));
      }
  
      if (status !== undefined) {
        filteredSystems = filteredSystems.filter((system) => system.status == status);
      }
  
      if (createTime) {
        const [start, end] = createTime.split(",");
        filteredSystems = filteredSystems.filter((system) => {
          const systemTime = new Date(system.createTime);
          return systemTime >= new Date(start) && systemTime <= new Date(end);
        });
      }
  
      return {
        code: "00000",
        data: {
          total: filteredSystems.length,
          list: filteredSystems,
        },
        msg: "一切ok",
      };
    },
  }
]);
