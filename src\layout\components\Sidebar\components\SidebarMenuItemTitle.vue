<template>
  <!-- 根据 icon 类型决定使用的不同类型的图标组件 -->
  <div class="menu-title-content">
    <el-icon v-if="icon && icon.startsWith('el-icon')" class="sub-el-icon">
      <component :is="icon.replace('el-icon-', '')" />
    </el-icon>
    <svg-icon v-else-if="icon" :icon-class="icon" />
    <svg-icon v-else icon-class="menu" />
    <span v-if="title" class="ml-2">{{ translateRouteTitle(title) }}</span>
  </div>
</template>

<script setup lang="ts">
import { translateRouteTitle } from "@/utils/i18n";

defineProps({
  icon: {
    type: String,
    default: "",
  },
  title: {
    type: String,
    default: "",
  },
});
</script>

<style lang="scss" scoped>
.sub-el-icon {
  width: 14px !important;
  margin-right: 0 !important;
  color: currentcolor;
}

.hideSidebar {
  .el-sub-menu,
  .el-menu-item {
    .svg-icon,
    .sub-el-icon {
      margin-left: 20px;
    }
  }
}
.menu-title-content {
  margin-left: 10px; // 控制图标和文字整体右移
  display: flex;
  align-items: center;
}
</style>
