<template>
  <el-dialog
    v-model="props.visible"
    :title="title"
    width="65%"
    :before-close="handleClose"
  >
    <el-tabs type="border-card">
      <!-- 服务器基本信息 -->
      <el-tab-pane label="基本信息">
        <el-form
          ref="formRef"
          :model="form.basic"
          :rules="rules.basic"
          label-width="150px"
          class="server-form"
        >
          <!-- 基础信息分组 -->
          <div class="form-section">
            <div class="section-title">基础信息</div>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="服务器名称" prop="serverName">
                  <el-input
                    v-model="form.basic.serverName"
                    placeholder="xxx系统web服务器"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作系统" prop="osVersion">
                  <el-input
                    v-model="form.basic.osVersion"
                    placeholder="例如Windows 2008"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <!-- todo --无接口待接入 -->
                <el-form-item label="操作系统（探测）" prop="autoSystem">
                  <el-input
                    v-model="form.basic.autoSystem"
                    placeholder="系统自动识别"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务器描述" prop="assetRemark">
                  <el-input
                    v-model="form.basic.assetRemark"
                    placeholder="请输入十字以内的描述"
                    maxlength="10"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 网络配置分组 -->
          <div class="form-section">
            <div class="section-title">IP地址</div>

            <!-- IP配置列表 -->
            <div
              v-for="(ipConfig, index) in form.basic.ipConfigs"
              :key="index"
              class="ip-config-section"
            >
              <div class="ip-header">
                <span class="ip-title">IP配置 #{{ index + 1 }}</span>
                <el-button
                  v-if="form.basic.ipConfigs.length > 1"
                  type="danger"
                  link
                  @click="removeIpConfig(index)"
                >
                  删除
                </el-button>
              </div>

              <el-row :gutter="20">
                <!-- todo --flag接口待接入 -->
                <el-col :span="12">
                  <el-form-item
                    :label="'IP地址标识'"
                    :prop="`basic.ipConfigs.${index}.flag`"
                  >
                    <el-input
                      v-model="ipConfig.flag"
                      placeholder="请输入五个字以内的标识"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    :label="'IP地址'"
                    :prop="`basic.ipConfigs.${index}.ip`"
                  >
                    <el-input
                      v-model="ipConfig.ip"
                      placeholder="请输入IP地址"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 端口配置列表 -->
              <div class="port-config-section">
                <div class="sub-section-title">端口配置</div>
                <el-table
                  :data="form.basic.ipConfigs[index].portConfigs"
                  style="width: 100%"
                >
                  <el-table-column prop="id" label="序号" width="80" />
                  <el-table-column prop="addway" label="录入方式" width="120" />
                  <el-table-column
                    prop="loginTime"
                    label="录入时间"
                    width="200px"
                  />
                  <el-table-column
                    prop="updateTime"
                    label="更新时间"
                    width="200px"
                  />
                  <el-table-column
                    prop="sortDescription"
                    label="端口描述"
                    width="200"
                  />
                  <el-table-column prop="port" label="端口" width="100" />
                  <el-table-column prop="protocol" label="协议" width="100" />
                  <el-table-column
                    prop="detectTime"
                    label="探测时间"
                    width="120"
                  />
                  <el-table-column prop="status" label="探测状态" width="100" />
                  <el-table-column fixed="right" label="操作" width="120">
                    <template #default="scope">
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="handleEdit(scope.row, scope.$index, index)"
                      >
                        查看
                      </el-button>
                      <el-button
                        link
                        type="danger"
                        size="small"
                        @click="removePort(index, scope.$index)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <el-button
                type="primary"
                plain
                icon="Plus"
                @click="addPortConfig(index)"
              >
                新增端口
              </el-button>
            </div>
            <!-- 添加IP配置按钮 -->
            <el-button type="primary" @click="addIpConfig" class="add-ip-btn">
              <el-icon>
                <Plus />
              </el-icon>
              添加IP配置
            </el-button>
          </div>
          <el-dialog
            v-model="dialogVisible"
            title="端口配置"
            width="500px"
            @close="resetPortForm"
            class="port-dialog"
          >
            <el-form
              :model="newPort"
              label-width="100px"
              :rules="sortRules"
              ref="dialogFormRef"
            >
              <el-form-item label="端口描述" prop="sortDescription">
                <el-input
                  v-model="newPort.sortDescription"
                  placeholder="请输入描述"
                  clearable
                />
              </el-form-item>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="协议" prop="protocol">
                    <el-input
                      v-model="newPort.protocol"
                      placeholder="请输入协议"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="端口" prop="port">
                    <el-input v-model="newPort.port" placeholder="请输入端口" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="录入方式">
                    <el-input v-model="newPort.addway" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="更新时间">
                    <el-input v-model="newPort.addtime" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <template #footer>
              <el-button @click="dialogVisible = false">取消</el-button>
              <el-button type="primary" @click="confirmAddPortConfig">
                确认
              </el-button>
            </template>
          </el-dialog>
          <!-- 新增端口弹窗 -->

          <div class="form-section">
            <div class="section-title">IP地址映射</div>
            <el-table
              :data="ipMappings"
              border
              style="width: 100%; margin-top: 16px"
            >
              <el-table-column prop="IpMapId" label="序号" width="80" />
              <el-table-column prop="description" label="映射描述" />
              <el-table-column prop="sourceIp" label="内网 IP" />
              <el-table-column prop="sourcePort" label="内网端口" />
              <el-table-column prop="targetIp" label="外网 IP" />
              <el-table-column prop="targetPort" label="外网端口" />

              <el-table-column label="操作" width="160" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="editMapping(scope.$index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="removeIpMapping(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <el-dialog
              v-model="ipDialogVisible"
              :title="dialogTitle"
              width="720px"
              @close="resetIpMappingForm"
            >
              <el-form
                :model="currentMapping"
                label-width="100px"
                label-position="left"
                class="p-2"
              >
                <el-row :gutter="20" class="mb-2">
                  <el-col>
                    <el-form-item label="映射描述">
                      <el-input
                        style
                        v-model="currentMapping.description"
                        placeholder="请输入映射描述"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- 第 1 行 -->
                <el-row :gutter="20" class="mb-2">
                  <el-col :span="12">
                    <el-form-item label="内网 IP">
                      <el-input
                        v-model="currentMapping.sourceIp"
                        placeholder="请输入内网 IP"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="内网端口">
                      <el-input
                        v-model="currentMapping.sourcePort"
                        placeholder="请输入端口"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 第 2 行 -->
                <el-row :gutter="20" class="mb-2">
                  <el-col :span="12">
                    <el-form-item label="外网 IP">
                      <el-input
                        v-model="currentMapping.targetIp"
                        placeholder="请输入外网 IP"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="外网端口">
                      <el-input
                        v-model="currentMapping.targetPort"
                        placeholder="请输入端口"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>

              <template #footer>
                <el-button @click="ipDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="confirmAddIpMapping">
                  确认
                </el-button>
              </template>
            </el-dialog>
            <el-button
              type="primary"
              @click="openIpMappingDialog"
              style="margin-top: 10px"
            >
              <el-icon><Plus /></el-icon>
              新增IP地址映射
            </el-button>
          </div>
          <!-- 服务器配置分组 -->
          <div class="form-section">
            <div class="section-title">其他信息</div>
            <!-- 接口todo -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间" prop="registerTime">
                  <el-date-picker
                    v-model="form.basic.registerTime"
                    type="datetime"
                    placeholder="系统自动识别"
                    class="w-full"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <!-- 接口todo -->
              <form-item label="更新时间" prop="updateTime">
                <el-date-picker
                  v-model="form.basic.updateTime"
                  type="datetime"
                  placeholder="系统自动识别"
                  class="w-full"
                  disabled
                />
              </form-item>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上线时间" prop="onlineTime">
                  <el-date-picker
                    v-model="form.basic.onlineTime"
                    type="datetime"
                    placeholder="请选择上线时间"
                    class="w-full"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上线负责人" prop="onlinePerson">
                  <el-input
                    v-model="form.basic.onlinePerson"
                    placeholder="请输入负责人姓名"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-col :span="12">
              <el-form-item label="联系方式" prop="onlineContact">
                <el-input
                  v-model="form.basic.onlineContact"
                  placeholder="请输入联系方式"
                />
              </el-form-item>
            </el-col>
          </div>

          <!-- 硬件配置分组 -->
          <div class="form-section">
            <div class="section-title">硬件配置</div>
            <div class="mb-2 ml-4">
              <el-radio-group v-model="machineChoose">
                <el-radio value="real" size="large">实体机</el-radio>
                <el-radio value="virtual" size="large">虚拟机</el-radio>
              </el-radio-group>
            </div>
            <div v-if="machineChoose === 'real'">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="服务器型号" prop="serverModel">
                    <el-input
                      v-model="form.basic.serverModel"
                      placeholder="戴尔R77515/未运行"
                    />
                  </el-form-item>
                </el-col>
                <!-- 接口todo -->
                <el-col :span="12">
                  <el-form-item label="机房名称" prop="machineroomName">
                    <el-input
                      v-model="form.basic.machineroomName"
                      placeholder="请输入机房名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <el-form-item label="机柜编号及机柜u位" prop="machineboxId">
                    <el-input
                      v-model="form.basic.machineboxId"
                      placeholder="请输入机柜编号及机柜u位"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row></el-row>
            </div>
            <div v-if="machineChoose === 'virtual'">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="虚拟化平台名称"
                    prop="virtualPlatformName"
                  >
                    <el-input
                      v-model="form.basic.serverModel"
                      placeholder="请输入虚拟化平台名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row></el-row>
            </div>
          </div>
          <!-- 关联系统分组 -->
          <div class="form-section">
            <div class="section-title">关联系统</div>
            <el-col :span="24">
              <el-form-item label="关联系统描述" prop="relatedSystem">
                <el-input
                  v-model="form.basic.relatedSystem"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入关联系统"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 管理单位基本信息 -->
      <el-tab-pane label="管理单位">
        <el-form
          ref="managementFormRef"
          :model="form.management"
          :rules="rules.management"
          label-width="150px"
          class="server-form"
        >
          <!-- 基本信息分组 -->
          <div class="form-section">
            <div class="section-title">基本信息</div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="管理单位" prop="deptId">
                  <el-tree-select
                    v-model="form.management.deptId"
                    placeholder="请选择所属部门"
                    :data="deptOptions"
                    filterable
                    check-strictly
                    :render-after-expand="false"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="办公地址" prop="officeAddress">
                  <el-input
                    v-model="form.management.officeAddress"
                    placeholder="填写办公地址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 领导信息部分 -->
          <div class="form-section">
            <div class="section-title">管理领导信息</div>

            <el-form-item label="选择管理领导" prop="manager">
              <el-button
                type="primary"
                @click="openSelectLeader"
                :disabled="!form.management.deptId"
              >
                <el-icon>
                  <User />
                </el-icon>
                选择部门领导
              </el-button>
            </el-form-item>

            <!-- 显示已选管理领导信息 -->
            <template v-if="peopleDialog.selectedLeaderId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="领导姓名" :span="2">
                    {{ form.management.manager }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.officePhone || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.mobile || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.email || "未填写" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>

          <!-- 系统管理员信息部分 -->
          <div class="form-section">
            <div class="section-title">系统管理员信息</div>

            <el-form-item label="选择系统管理员" prop="sysAdmin">
              <el-button
                type="primary"
                @click="openSelectAdmin"
                :disabled="!form.management.deptId"
              >
                <el-icon>
                  <User />
                </el-icon>
                选择系统管理员
              </el-button>
            </el-form-item>

            <!-- 显示已选系统管理员信息 -->
            <template v-if="peopleDialog.selectedAdminId">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="管理员姓名" :span="2">
                    {{ form.management.sysAdmin }}
                  </el-descriptions-item>
                  <el-descriptions-item label="办公电话">
                    {{ form.management.adminOfficePhone || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="手机号码">
                    {{ form.management.adminMobile || "未填写" }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.management.adminEmail || "未填写" }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 运维单位部分 -->
      <el-tab-pane label="运维单位">
        <el-form
          ref="operationsFormRef"
          :model="form.operations"
          :rules="rules.operations"
          label-width="150px"
          class="server-form"
        >
          <div class="form-section">
            <div class="section-title">
              <el-icon>
                <Office />
              </el-icon>
              运维服务商信息
            </div>

            <!-- 修改为直接使用选择组件，不在外层嵌套弹窗 -->
            <el-form-item label="选择服务商" prop="providerId">
              <div class="service-provider-select">
                <select-service-provider
                  v-model="form.operations.providerId"
                  @provider-selected="handleOperationsProviderSelected"
                />
              </div>
            </el-form-item>

            <!-- 显示已选服务商信息 -->
            <template v-if="form.operations.providerDetail">
              <div class="provider-info-card">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="公司名称" :span="2">
                    {{ form.operations.providerDetail.providerName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="项目负责人">
                    {{ form.operations.providerDetail.projectManager }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话">
                    {{ form.operations.providerDetail.managerMobile }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系邮箱" :span="2">
                    {{ form.operations.providerDetail.managerEmail }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术负责人">
                    {{ form.operations.providerDetail.techLeader }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术电话">
                    {{ form.operations.providerDetail.techMobile }}
                  </el-descriptions-item>
                  <el-descriptions-item label="技术邮箱" :span="2">
                    {{ form.operations.providerDetail.techEmail }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </div>
        </el-form>
      </el-tab-pane>
    </el-tabs>
    <!-- 添加人员选择组件 -->
    <select-people
      v-model:visible="peopleDialog.leaderVisible"
      :title="'选择管理领导'"
      :department-id="form.management.deptId"
      :selected-user-id="peopleDialog.selectedLeaderId"
      @selected="handleLeaderSelected"
    />

    <select-people
      v-model:visible="peopleDialog.adminVisible"
      :title="'选择系统管理员'"
      :department-id="form.management.deptId"
      :selected-user-id="peopleDialog.selectedAdminId"
      @selected="handleAdminSelected"
    />

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="submitForm">已填写完成</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { formatLocalDateTime } from "@/utils/dateUtils";
import DeptAPI from "@/api/dept";
import assetsAPI, {
  AssetsForm,
  IpConfig,
  Protocol,
  Middleware,
  Database,
} from "@/api/assets_management/details/assets";
import SelectServiceProvider from "@/components/AssetsManage/SelectServiceProvider.vue";
import ProviderAPI, {
  ProviderPageVO,
  ProviderForm,
  ProviderPageQuery,
} from "@/api/work_management/serviceProvider/index";
import { ref, reactive, watch, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import SelectPeople from "@/components/AssetsManage/SelectPeople.vue";
import UserAPI from "@/api/user";
import { Plus, User } from "@element-plus/icons-vue";

// 定义端口配置接口，确保包含sortDescription
interface PortConfig {
  id: number; // 唯一标识
  port: string; // 端口号
  protocol: Protocol; // 协议类型
  sortDescription?: string; // 端口描述
  addway?: string; // 录入方式
  addtime?: string; // 录入时间
  loginTime?: string; // 登录时间
  updateTime?: string; // 更新时间
  detectTime?: string; // 探测时间
  status?: string; // 探测状态
}

// 更新 IpConfig 接口以包含 portConfigs
interface IpConfig {
  id: number;
  ip: string;
  port: string;
  protocol: Protocol;
  usage: string;
  isPublic: boolean;
  mappedPort: string;
  mappedAddress: string;
  flag?: string; // 可选的 IP 地址标识
  portConfigs: PortConfig[]; // 端口配置列表
}
const machineChoose = ref("real"); // 选择实体机或虚拟机 默认为实体机
const dialogTitle = computed(() =>
  isEditMode.value ? "编辑IP映射" : "新增IP映射"
);
const editMapping = (index: number) => {
  const selected = ipMappings.value[index];
  Object.assign(currentMapping, selected); // 把数据拷贝进表单
  editingIndex.value = index;
  isEditMode.value = true;
  ipDialogVisible.value = true;
};
// 当前是否是编辑模式
const isEditMode = ref(false);

// 当前正在编辑的映射索引
const editingIndex = ref(-1);
const currentPortIndex = ref(-1);

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增服务器",
  },
  id: {
    type: [Number],
    default: "",
  },
});

const router = useRouter();
const emit = defineEmits(["update:visible", "submitted"]);

const ipMappings = ref([]);

const currentMapping = reactive({
  description: "",
  sourceIp: "",
  sourcePort: "",
  targetIp: "",
  targetPort: "",
});
const ipDialogVisible = ref(false);

const resetIpMappingForm = () => {
  currentMapping.description = "";
  currentMapping.sourceIp = "";
  currentMapping.sourcePort = "";
  currentMapping.targetIp = "";
  currentMapping.targetPort = "";
};

const confirmAddIpMapping = () => {
  if (
    currentMapping.description &&
    currentMapping.sourceIp &&
    currentMapping.sourcePort &&
    currentMapping.targetIp &&
    currentMapping.targetPort
  ) {
    ipMappings.value.push({ ...currentMapping });
    console.log("新增IP地址映射:", ipMappings.value);
    ipDialogVisible.value = false;
    resetIpMappingForm();
  } else {
    ElMessage.warning("请填写完整的映射信息！");
  }
};
const openIpMappingDialog = () => {
  resetIpMappingForm();
  ipDialogVisible.value = true;
};

const removeIpMapping = (mappingToRemove) => {
  ipMappings.value = ipMappings.value.filter(
    (mapping) => mapping !== mappingToRemove
  );
  console.log(
    "删除IP地址映射:",
    mappingToRemove,
    "剩余映射:",
    ipMappings.value
  );
};

// 表单引用
const formRef = ref();
const middlewareFormRef = ref();
const managementFormRef = ref();
const operationsFormRef = ref();
const deptOptions = ref<any>([]);
const inventoryFormRef = ref();
const detectionFormRef = ref();

// 人员选择相关响应式数据
const peopleDialog = reactive({
  leaderVisible: false, // 领导选择弹窗可见性
  adminVisible: false, // 管理员选择弹窗可见性
  selectedLeaderId: null, // 已选择领导 ID
  selectedAdminId: null, // 已选择管理员 ID
});
const portConfigs = ref<PortConfig[]>([]);
// 初始表单数据
const initialForm = {
  basic: {
    serverName: "",
    osVersion: "",
    ipConfigs: [
      {
        id: 1,
        ip: "",
        port: "默认数据",
        protocol: "",
        usage: "",
        isPublic: false,
        mappedPort: "",
        mappedAddress: "",
        flag: "",
        portConfigs: [
          {
            id: 1,
            port: "",
            protocol: "",
            sortDescription: "", // 初始化端口描述
          },
        ], // 默认包含一个端口配置
      },
    ],
    systemId: undefined,
    registerTime: new Date(),
    serverType: "",
    serverModel: "",
    networkArea: "",
    physicalLocation: "",
    assetStatus: "1", // 默认正常状态
    assetRemark: "",
    onlineTime: "",
    onlinePerson: "",
    onlineContact: "",
    mac: "", // 物理地址
    type: 1, // 默认为服务器类型
    version1: "", // 固件版本
    autoSystem: "", // 探测操作系统
  },
  middleware: {
    hasMiddleware: "0",
    middlewareList: [
      {
        id: 1,
        name: "",
        version: "",
        port: "",
      },
    ],
    hasDatabase: "0",
    databaseList: [
      {
        id: 1,
        name: "",
        version: "",
        port: "",
      },
    ],
    hasOtherComponents: "0",
    componentList: [
      {
        id: 1,
        name: "",
        version: "",
        port: "",
      },
    ],
  },
  management: {
    deptId: undefined,
    officeAddress: "", // 对应 depAddress
    manager: "", // 部门领导
    officePhone: "", // 对应 leaderPhone
    mobile: "", // 对应 leaderPhone1
    email: "", // 对应 leaderEmail
    sysAdmin: "", // 对应 ownerName
    adminOfficePhone: "", // 对应 ownerPhone1
    adminMobile: "", // 对应 ownerPhone
    adminEmail: "", // 对应 ownerEmail
  },
  operations: {
    providerId: undefined,
    providerDetail: null,
  },
  inventory: {
    enableInventory: "否",
    inventoryTaskName: "",
    enableScheduledInventory: "否",
    inventoryCycle: "",
    enableTemporaryInventory: "否",
    temporaryInventoryDate: "",
  },
  detection: {
    enableDetection: "否",
    detectionTaskName: "",
    enableScheduledDetection: "否",
    detectionCycle: "",
    enableTemporaryDetection: "否",
    temporaryDetectionDate: "",
  },
};
// 查看端口详情，参数是该行数据对象
const handleDetail = (row: any) => {
  // 这里你可以写查看逻辑，比如弹窗显示详情
  console.log("查看端口详情:", row);
  // 也可以打开详情弹窗，传入 row 数据
  openDetailDialog(row);
};

// 删除指定索引的端口配置
const removePort = (ipIndex: number, portIndex: number) => {
  const portConfigs = form.basic.ipConfigs[ipIndex].portConfigs;

  // 弹确认框，确认后删除
  ElMessageBox.confirm("确定删除该端口配置吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      portConfigs.splice(portIndex, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除
    });
};

// 处理运维服务商选择
const handleOperationsProviderSelected = (provider: any) => {
  console.log("选择的服务提供商:", provider);
  form.operations.providerId = provider.id;
  form.operations.providerDetail = provider;
};

// 响应式表单数据
const form = reactive<AssetsForm>(JSON.parse(JSON.stringify(initialForm)));

// 重置表单
const resetForm = () => {
  const forms = [
    formRef,
    middlewareFormRef,
    managementFormRef,
    operationsFormRef,
  ];
  forms.forEach((formRef) => {
    if (formRef.value) {
      formRef.value.resetFields();
      formRef.value.clearValidate();
    }
  });

  Object.assign(form, JSON.parse(JSON.stringify(initialForm)));
  peopleDialog.selectedLeaderId = null;
  peopleDialog.selectedAdminId = null;
  ipMappings.value = []; // 重置IP映射数据
};

// 打开选择领导弹窗
const openSelectLeader = () => {
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.leaderVisible = true;
};

// 打开选择管理员弹窗
const openSelectAdmin = () => {
  if (!form.management.deptId) {
    ElMessage.warning("请先选择管理部门");
    return;
  }
  peopleDialog.adminVisible = true;
};

// 处理领导选择
const handleLeaderSelected = (user) => {
  console.log("选择的领导:", user);
  peopleDialog.selectedLeaderId = user.id;
  form.management.manager = user.nickname || user.username;
  form.management.mobile = user.mobile || "";
  form.management.email = user.email || "";
  form.management.officePhone = user.officePhone || "";
};

// 处理管理员选择
const handleAdminSelected = (user) => {
  console.log("选择的管理员:", user);
  peopleDialog.selectedAdminId = user.id;
  form.management.sysAdmin = user.nickname || user.username;
  form.management.adminMobile = user.mobile || "";
  form.management.adminEmail = user.email || "";
  form.management.adminOfficePhone = user.officePhone || "";
};

const statusOptions = [
  { value: "1", label: "正常" },
  { value: "0", label: "异常" },
];

// 表单验证规则
const rules = reactive({
  basic: {
    serverName: [
      { required: true, message: "请输入服务器名称", trigger: "blur" },
      {
        min: 2,
        max: 50,
        message: "服务器名称长度应为2-50个字符",
        trigger: "blur",
      },
    ],
    registerTime: [
      { required: true, message: "请选择创建时间", trigger: "change" },
    ],
    onlinePerson: [
      { required: false, message: "请输入上线负责人", trigger: "blur" },
    ],
    onlineContact: [
      { required: false, message: "请输入联系方式", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/,
        message: "请输入正确的手机号或座机号",
        trigger: "blur",
      },
    ],
    assetStatus: [
      { required: true, message: "请选择资产状态", trigger: "change" },
    ],
    "ipConfigs.0.ip": [
      { required: true, message: "请输入IP地址", trigger: "blur" },
      {
        pattern:
          /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
        message: "请输入正确的IP地址",
        trigger: "blur",
      },
    ],
    "ipConfigs.0.port": [
      { required: false, message: "请输入端口号", trigger: "blur" },
      {
        pattern: /^[0-9]{1,5}$/,
        message: "请输入正确的端口号",
        trigger: "blur",
      },
    ],
    "ipConfigs.0.protocol": [
      { required: false, message: "请选择协议", trigger: "change" },
    ],
    "ipConfigs.0.flag": [
      {
        max: 5,
        message: "IP地址标识最多5个字符",
        trigger: "blur",
      },
    ],
  },
  middleware: {
    hasMiddleware: [
      { required: true, message: "请选择是否部署中间件", trigger: "change" },
    ],
    hasDatabase: [
      { required: true, message: "请选择是否部署数据库", trigger: "change" },
    ],
    hasOtherComponents: [
      { required: true, message: "请选择是否部署其它组件", trigger: "change" },
    ],
    "middlewareList.0.name": [
      {
        validator: (rule, value, callback) => {
          if (form.middleware.hasMiddleware === "1" && !value) {
            callback(new Error("请输入中间件名称"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
  },
  management: {
    deptId: [{ required: false, message: "请选择管理单位", trigger: "change" }],
    officeAddress: [
      { required: false, message: "请输入办公地址", trigger: "blur" },
    ],
    manager: [{ required: false, message: "请选择管理领导", trigger: "blur" }],
    mobile: [
      { required: false, message: "请输入手机号码", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    email: [
      { required: false, message: "请输入邮箱", trigger: "blur" },
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
    sysAdmin: [
      { required: false, message: "请选择系统管理员", trigger: "blur" },
    ],
    adminMobile: [
      { required: false, message: "请输入管理员手机号码", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    adminEmail: [
      { required: false, message: "请输入管理员邮箱", trigger: "blur" },
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" },
    ],
  },
  operations: {
    providerId: [
      { required: false, message: "请选择服务商", trigger: "change" },
    ],
  },
  inventory: {
    enableInventory: [
      { required: true, message: "请选择是否进行资产盘点", trigger: "change" },
    ],
    inventoryTaskName: [
      {
        validator: (rule, value, callback) => {
          if (form.inventory?.enableInventory === "是" && !value) {
            callback(new Error("请输入盘点任务名称"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
  },
  detection: {
    enableDetection: [
      { required: true, message: "请选择是否进行资产探测", trigger: "change" },
    ],
    detectionTaskName: [
      {
        validator: (rule, value, callback) => {
          if (form.detection?.enableDetection === "是" && !value) {
            callback(new Error("请输入探测任务名称"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
  },
});

// 将表单数据映射到 API 格式
function mapFormToApiData() {
  console.log("正在映射表单数据到API格式...");

  const apiData = {
    ...(props.id ? { id: props.id } : {}), // 编辑模式添加 ID
    // 基础信息
    name: form.basic.serverName,
    os: form.basic.osVersion,
    ip: form.basic.ipConfigs[0]?.ip?.replace(/[,:]/g, "_") || "",
    port: form.basic.ipConfigs[0]?.port?.replace(/[,:]/g, "_") || "",
    url: form.basic.ipConfigs[0]?.protocol
      ? `${form.basic.ipConfigs[0].protocol}://${form.basic.ipConfigs[0].ip?.replace(/[,:]/g, "_")}`
      : "",
    // 管理信息
    deptId: form.management.deptId,
    depAddress: form.management.officeAddress,
    // 管理员信息(资产管理者)
    ownerName: form.management.sysAdmin,
    ownerId: peopleDialog.selectedAdminId,
    ownerPhone: form.management.adminMobile,
    ownerPhone1: form.management.adminOfficePhone,
    ownerEmail: form.management.adminEmail,
    sysManagerId: peopleDialog.selectedAdminId, // 冗余字段
    // 部门领导信息
    leader: form.management.manager,
    managerId: peopleDialog.selectedLeaderId,
    leaderPhone: form.management.officePhone,
    leaderPhone1: form.management.mobile,
    leaderEmail: form.management.email,
    // 服务器信息
    systemId: form.basic.systemId,
    serverDep: form.basic.belongSystem,
    status: form.basic.assetStatus || "1",
    createTime: form.basic.registerTime
      ? formatLocalDateTime(form.basic.registerTime)
      : formatLocalDateTime(new Date()),
    onlineTime: form.basic.onlineTime
      ? formatLocalDateTime(form.basic.onlineTime)
      : null,
    onlineContact: form.basic.onlineContact,
    notes: form.basic.assetRemark,
    // 设备详情
    serverType: form.basic.serverType,
    model: form.basic.serverModel,
    netArea: form.basic.networkArea,
    local: form.basic.physicalLocation,
    mac: form.basic.mac,
    type: form.basic.type,
    version1: form.basic.version1,
    // 中间件信息
    isMiddleware: form.middleware.hasMiddleware,
    isDb: form.middleware.hasDatabase,
    isPlugin: form.middleware.hasOtherComponents,
    // 服务提供商信息
    providerId: form.operations.providerId,
    otherFactory: form.operations.providerDetail?.name,
    otherManager: form.operations.providerDetail?.projectManager,
    otherContact: form.operations.providerDetail?.managerMobile,
    // 添加IP地址映射数据
    ipMappings: ipMappings.value.map((mapping) => ({
      description: mapping.description || "",
      sourceIp: mapping.sourceIp || "",
      sourcePort: mapping.sourcePort || "",
      targetIp: mapping.targetIp || "",
      targetPort: mapping.targetPort || "",
    })),
  };

  // 处理 IP 配置
  if (form.basic.ipConfigs.length > 0) {
    apiData.address = JSON.stringify(
      form.basic.ipConfigs.map((cfg) => ({
        ip: cfg.ip || "",
        port: cfg.port || "",
        protocol: cfg.protocol || "",
        usage: cfg.usage || "",
        isPublic: cfg.isPublic ? 1 : 0,
        mappedPort: cfg.isPublic ? cfg.mappedPort || "" : "",
        mappedAddress: cfg.isPublic ? cfg.mappedAddress || "" : "",
        flag: cfg.flag || "",
        portConfigs: cfg.portConfigs.map((pc) => ({
          port: pc.port || "",
          protocol: pc.protocol || "",
          sortDescription: pc.sortDescription || "", // 确保包含端口描述
        })),
      }))
    );
  }

  // 处理中间件信息
  if (form.middleware.hasMiddleware === "1") {
    apiData.middleware = JSON.stringify(
      form.middleware.middlewareList.map((mw) => ({
        name: mw.name || "",
        version: mw.version || "",
        port: mw.port || "",
      }))
    );
  }

  // 处理数据库信息
  if (form.middleware.hasDatabase === "1") {
    apiData.db = JSON.stringify(
      form.middleware.databaseList.map((db) => ({
        name: db.name || "",
        version: db.version || "",
        port: db.port || "",
      }))
    );
  }

  // 处理组件信息
  if (form.middleware.hasOtherComponents === "1") {
    apiData.plugin = JSON.stringify(
      form.middleware.componentList.map((comp) => ({
        name: comp.name || "",
        version: comp.version || "",
        port: comp.port || "",
      }))
    );
  }

  console.log("映射后的API数据:", apiData);
  return apiData;
}

// 从 API 数据映射到表单
function mapApiDataToForm(apiData) {
  console.log("API返回的数据:", apiData);

  try {
    // 基础信息
    form.basic.serverName = apiData.name || "";
    form.basic.osVersion = apiData.os || "";

    // 处理 IP 配置
    form.basic.ipConfigs = [];
    if (Array.isArray(apiData.addressList) && apiData.addressList.length > 0) {
      form.basic.ipConfigs = apiData.addressList.map((addr, index) => ({
        id: index + 1,
        ip: addr.ip || "",
        port: addr.port ? String(addr.port) : "",
        protocol: addr.protocol || "",
        usage: addr.usage || "",
        isPublic: addr.isPublic === 1,
        mappedPort: addr.mappedPort || "",
        mappedAddress: addr.mappedAddress || "",
        flag: addr.flag || "",
        portConfigs: Array.isArray(addr.portConfigs)
          ? addr.portConfigs.map((pc, pIndex) => ({
              id: pIndex + 1,
              port: pc.port ? String(pc.port) : "",
              protocol: pc.protocol || "",
              sortDescription: pc.sortDescription || "", // 确保包含端口描述
              addway: pc.addway || "自动探测",
              loginTime: pc.loginTime || "",
              updateTime: pc.updateTime || "",
              detectTime: pc.detectTime || "",
              status: pc.status || "未检测",
            }))
          : [{ id: 1, port: "", protocol: "", sortDescription: "" }], // 确保至少有一个端口配置
      }));
    } else if (typeof apiData.address === "string" && apiData.address) {
      try {
        const parsedAddress = JSON.parse(apiData.address);
        if (Array.isArray(parsedAddress)) {
          form.basic.ipConfigs = parsedAddress.map((addr, index) => ({
            id: index + 1,
            ip: addr.ip || "",
            port: addr.port ? String(addr.port) : "",
            protocol: addr.protocol || "",
            usage: addr.usage || "",
            isPublic: addr.isPublic === 1,
            mappedPort: addr.mappedPort || "",
            mappedAddress: addr.mappedAddress || "",
            flag: addr.flag || "",
            portConfigs: Array.isArray(addr.portConfigs)
              ? addr.portConfigs.map((pc, pIndex) => ({
                  id: pIndex + 1,
                  port: pc.port ? String(pc.port) : "",
                  protocol: pc.protocol || "",
                  sortDescription: pc.sortDescription || "", // 确保包含端口描述
                  addway: pc.addway || "自动探测",
                  loginTime: pc.loginTime || "",
                  updateTime: pc.updateTime || "",
                  detectTime: pc.detectTime || "",
                  status: pc.status || "未检测",
                }))
              : [{ id: 1, port: "", protocol: "", sortDescription: "" }], // 确保至少有一个端口配置
          }));
        }
      } catch (e) {
        console.error("解析IP配置出错:", e);
      }
    }

    if (form.basic.ipConfigs.length === 0) {
      form.basic.ipConfigs.push({
        id: 1,
        ip: "",
        port: "",
        protocol: "",
        usage: "",
        isPublic: false,
        mappedPort: "",
        mappedAddress: "",
        flag: "",
        portConfigs: [{ id: 1, port: "", protocol: "", sortDescription: "" }], // 默认一个端口配置
      });
    } else {
      // 确保每个 ipConfig 都有 portConfigs 属性
      form.basic.ipConfigs.forEach((cfg, idx) => {
        if (!Array.isArray(cfg.portConfigs)) {
          cfg.portConfigs = [
            { id: 1, port: "", protocol: "", sortDescription: "" },
          ];
        } else {
          // 确保每个端口配置都有sortDescription属性
          cfg.portConfigs.forEach((portConfig) => {
            if (portConfig.sortDescription === undefined) {
              portConfig.sortDescription = "";
            }
          });
        }
      });
    }

    // 初始化IP映射数据
    if (apiData.ipMappings && Array.isArray(apiData.ipMappings)) {
      ipMappings.value = [...apiData.ipMappings];
    } else {
      ipMappings.value = [];
    }

    // 注册时间
    form.basic.registerTime = apiData.createTime
      ? new Date(apiData.createTime)
      : new Date();
    form.basic.onlineTime = apiData.onlineTime
      ? new Date(apiData.onlineTime)
      : undefined;
    form.basic.onlinePerson = apiData.onlinePerson || "";
    form.basic.onlineContact = apiData.onlineContact || "";
    form.basic.systemId = apiData.systemId || apiData.serverDep || "";
    form.basic.serverType = apiData.serverType || "";
    form.basic.serverModel = apiData.model || "";
    form.basic.networkArea = apiData.netArea || "";
    form.basic.physicalLocation = apiData.local || "";
    form.basic.assetStatus = apiData.status || "1";
    form.basic.assetRemark = apiData.notes || apiData.remark || "";
    form.basic.mac = apiData.mac || "";
    form.basic.type = apiData.type || 1;
    form.basic.version1 = apiData.version1 || "";

    // 管理单位信息
    form.management.deptId = apiData.deptId;
    form.management.officeAddress = apiData.depAddress || "";
    form.management.manager = apiData.leader || "";
    peopleDialog.selectedLeaderId = apiData.managerId;
    form.management.officePhone = apiData.leaderPhone || "";
    form.management.mobile = apiData.leaderPhone1 || "";
    form.management.email = apiData.leaderEmail || "";
    form.management.sysAdmin = apiData.ownerName || "";
    peopleDialog.selectedAdminId = apiData.ownerId || apiData.sysManagerId;
    form.management.adminOfficePhone = apiData.ownerPhone1 || "";
    form.management.adminMobile = apiData.ownerPhone || "";
    form.management.adminEmail = apiData.ownerEmail || "";

    // 异步加载用户信息
    Promise.all([
      loadUserIfNeeded(
        "leader",
        peopleDialog.selectedLeaderId,
        form.management.manager
      ),
      loadUserIfNeeded(
        "admin",
        peopleDialog.selectedAdminId,
        form.management.sysAdmin
      ),
    ]).catch((error) => {
      console.error("加载用户信息出错:", error);
    });

    // 处理中间件信息
    form.middleware.hasMiddleware = apiData.isMiddleware || "0";
    form.middleware.middlewareList = [];
    if (
      Array.isArray(apiData.middlewareList) &&
      apiData.middlewareList.length > 0
    ) {
      form.middleware.middlewareList = apiData.middlewareList.map(
        (mw, index) => ({
          id: index + 1,
          name: mw.name || "",
          version: mw.version || "",
          port: mw.port || "",
        })
      );
    }
    if (form.middleware.middlewareList.length === 0) {
      form.middleware.middlewareList.push({
        id: 1,
        name: "",
        version: "",
        port: "",
      });
    }

    // 处理数据库信息
    form.middleware.hasDatabase = apiData.isDb || "0";
    form.middleware.databaseList = [];
    if (Array.isArray(apiData.dbList) && apiData.dbList.length > 0) {
      form.middleware.databaseList = apiData.dbList.map((db, index) => ({
        id: index + 1,
        name: db.name || "",
        version: db.version || "",
        port: db.port || "",
      }));
    }
    if (form.middleware.databaseList.length === 0) {
      form.middleware.databaseList.push({
        id: 1,
        name: "",
        version: "",
        port: "",
      });
    }

    // 处理组件信息
    form.middleware.hasOtherComponents = apiData.isPlugin || "0";
    form.middleware.componentList = [];
    if (Array.isArray(apiData.pluginList) && apiData.pluginList.length > 0) {
      form.middleware.componentList = apiData.pluginList.map(
        (plugin, index) => ({
          id: index + 1,
          name: plugin.name || "",
          version: plugin.version || "",
          port: plugin.port || "",
        })
      );
    }
    if (form.middleware.componentList.length === 0) {
      form.middleware.componentList.push({
        id: 1,
        name: "",
        version: "",
        port: "",
      });
    }

    // 服务提供商信息
    if (apiData.provider) {
      form.operations.providerId = apiData.provider.id;
      form.operations.providerDetail = apiData.provider;
    } else {
      form.operations.providerId = apiData.providerId;
      if (apiData.providerId) {
        fetchProviderDetail(apiData.providerId);
      }
    }

    console.log("成功映射API数据到表单");
  } catch (error) {
    console.error("映射API数据到表单时出错:", error);
    ElMessage.error("表单数据加载异常");
  }
}

// 加载用户信息（如果需要）
async function loadUserIfNeeded(type, userId, existingName) {
  if (!userId || existingName) return;

  try {
    console.log(`加载${type}用户信息:`, userId);
    const userInfo = await UserAPI.getFormData(userId);
    if (type === "leader") {
      form.management.manager = userInfo.nickname || userInfo.username || "";
      form.management.officePhone =
        userInfo.mobile || form.management.officePhone;
      form.management.mobile = userInfo.mobile || form.management.mobile;
      form.management.email = userInfo.email || form.management.email;
    } else if (type === "admin") {
      form.management.sysAdmin = userInfo.nickname || userInfo.username || "";
      form.management.adminOfficePhone =
        userInfo.mobile || form.management.adminOfficePhone;
      form.management.adminMobile =
        userInfo.mobile || form.management.adminMobile;
      form.management.adminEmail = userInfo.email || form.management.adminEmail;
    }
  } catch (error) {
    console.error(`加载${type}用户信息失败:`, error);
  }
}

// 监听 visible 和 id 变化
watch(
  () => [props.visible, props.id],
  async ([visible, id]) => {
    if (visible && id) {
      try {
        const data = await assetsAPI.getServerDetail(id);
        mapApiDataToForm(data);
      } catch (error) {
        console.error("加载数据失败:", error);
        ElMessage.error("加载数据失败");
      }
    } else if (!visible) {
      resetForm();
    } else if (visible && !id) {
      resetForm();
    }
  },
  { immediate: true }
);

// 获取服务商详情
async function fetchProviderDetail(providerId) {
  try {
    const detail = await ProviderAPI.getFormData(providerId);
    form.operations.providerDetail = detail;
  } catch (error) {
    console.error("获取服务商详情失败:", error);
  }
}

// 添加 IP 配置
const addIpConfig = () => {
  const newId = Math.max(...form.basic.ipConfigs.map((ip) => ip.id)) + 1;
  form.basic.ipConfigs.push({
    id: newId,
    ip: "",
    port: "",
    protocol: "",
    usage: "",
    isPublic: false,
    mappedPort: "",
    mappedAddress: "",
    flag: "",
    portConfigs: [
      {
        id: 1,
        port: "",
        protocol: "",
        sortDescription: "", // 初始化端口描述
      },
    ], // 新 IP 配置默认包含一个端口配置
  });
};

// 删除 IP 配置
const removeIpConfig = (index: number) => {
  console.log(form.basic.ipConfigs);
  form.basic.ipConfigs.splice(index, 1);
};

// 弹窗控制
const dialogVisible = ref(false);

// 当前操作的 IP 配置索引
const currentIpIndex = ref(0);

// 弹窗表单引用
const dialogFormRef = ref();

// 弹窗表单数据
const newPort = reactive<PortConfig>({
  id: 0,
  port: "",
  protocol: "",
  sortDescription: "",
  addway: "",
  addtime: "",
});

// 表单校验规则
const sortRules = {
  port: [{ required: true, message: "请输入端口", trigger: "blur" }],
  protocol: [{ required: true, message: "请输入协议", trigger: "blur" }],
  sortDescription: [
    { required: false, message: "请输入端口描述", trigger: "blur" },
  ],
};
const editingPortIndex = ref(-1); // -1 代表新增，>=0 代表编辑的索引

const openPortDialog = (ipIndex: number, portIndex?: number) => {
  currentIpIndex.value = ipIndex;

  if (typeof portIndex === "number" && portIndex >= 0) {
    // 编辑模式
    editingPortIndex.value = portIndex;
    Object.assign(newPort, {
      ...form.basic.ipConfigs[ipIndex].portConfigs[portIndex],
    });
  } else {
    // 新增模式
    editingPortIndex.value = -1;
    Object.assign(newPort, {
      id: 0,
      port: "",
      protocol: "",
      sortDescription: "",
      addway: "手动录入",
      addtime: new Date().toLocaleString(),
      status: "未检测",
    });
  }

  dialogVisible.value = true;
};

// 打开弹窗
// 新增端口时调用
const addPortConfig = (ipIndex: number) => {
  openPortDialog(ipIndex);
};
const handleEdit = (row: any, index: number, ipIndex: number) => {
  openPortDialog(ipIndex, index);
};
// 编辑端口时调用
// 重置弹窗表单
const resetPortForm = () => {
  dialogFormRef.value?.resetFields();
  Object.assign(newPort, {
    id: 0,
    port: "",
    protocol: "",
    sortDescription: "",
    addway: "",
    addtime: "",
  });
};

// 确认新增端口配置
const confirmAddPortConfig = () => {
  dialogFormRef.value.validate((valid: boolean) => {
    if (!valid) return;

    // 获取当前选中的ipConfig的portConfigs数组
    // 获取当前选中的ipConfig的portConfigs数组
    const portConfigs = form.basic.ipConfigs[currentIpIndex.value].portConfigs;

    if (editingPortIndex.value >= 0) {
      // 编辑现有端口
      Object.assign(portConfigs[editingPortIndex.value], newPort);
    } else {
      // 生成新ID
      const newId = portConfigs.length
        ? Math.max(...portConfigs.map((p) => p.id || 0)) + 1
        : 1;

      // 新增端口配置对象
      portConfigs.push({
        id: newId,
        port: newPort.port,
        protocol: newPort.protocol,
        sortDescription: newPort.sortDescription,
        addway: "手动录入",
        addtime: new Date().toLocaleString(),
        status: "未检测",
      });
    }

    dialogVisible.value = false;
    resetPortForm();
  });
};

// 删除端口配置
const removePortConfig = (ipIndex: number, portIndex: number) => {
  form.basic.ipConfigs[ipIndex].portConfigs.splice(portIndex, 1);
};

// 添加中间件
const addMiddleware = () => {
  const newId =
    Math.max(...form.middleware.middlewareList.map((m) => m.id)) + 1;
  form.middleware.middlewareList.push({
    id: newId,
    name: "",
    version: "",
    port: "",
  });
};

// 删除中间件
const removeMiddleware = (index: number) => {
  form.middleware.middlewareList.splice(index, 1);
};

// 添加数据库
const addDatabase = () => {
  const newId = Math.max(...form.middleware.databaseList.map((d) => d.id)) + 1;
  form.middleware.databaseList.push({
    id: newId,
    name: "",
    version: "",
    port: "",
  });
};

// 删除数据库
const removeDatabase = (index: number) => {
  form.middleware.databaseList.splice(index, 1);
};

// 添加组件
const addComponent = () => {
  const newId = Math.max(...form.middleware.componentList.map((c) => c.id)) + 1;
  form.middleware.componentList.push({
    id: newId,
    name: "",
    version: "",
    port: "",
  });
};

// 删除组件
const removeComponent = (index: number) => {
  form.middleware.componentList.splice(index, 1);
};

// 关闭弹窗前确认
const handleClose = (done: any) => {
  ElMessageBox.confirm("确认关闭？")
    .then(() => {
      handleCancel();
      done();
    })
    .catch(() => {});
};

// 取消操作
const handleCancel = () => {
  emit("update:visible", false);
  resetForm();
};

// 提交表单
const submitForm = async () => {
  const validateForms = [
    formRef.value,
    middlewareFormRef.value,
    managementFormRef.value,
    operationsFormRef.value,
    form.inventory?.enableInventory === "是" ? inventoryFormRef.value : null,
    form.detection?.enableDetection === "是" ? detectionFormRef.value : null,
  ].filter(Boolean);

  try {
    // 验证所有表单
    await Promise.all(validateForms.map((form) => form?.validate()));

    // 检查人员 ID
    if (!peopleDialog.selectedLeaderId && form.management.manager) {
      ElMessage.warning('请通过"选择部门领导"按钮选择有效的管理领导');
      return false;
    }

    if (!peopleDialog.selectedAdminId && form.management.sysAdmin) {
      ElMessage.warning('请通过"选择系统管理员"按钮选择有效的系统管理员');
      return false;
    }

    // 映射到 API 数据
    const apiData = mapFormToApiData();

    // 新增或更新
    if (props.id) {
      await assetsAPI.update(props.id, apiData);
      ElMessage.success("修改成功");
    } else {
      await assetsAPI.add(apiData);
      ElMessage.success("新增成功");
    }

    emit("submitted");
    handleCancel();
  } catch (error) {
    console.error("提交表单出错:", error);
    ElMessage.error("请完善表单信息");
    return false;
  }
};

// 组件挂载时获取部门选项
onMounted(() => {
  DeptAPI.getOptions().then((data) => {
    deptOptions.value = data;
  });
});

// 未定义的函数实现
const openDetailDialog = (row: any) => {
  console.log("打开详情对话框:", row);
  // 实现详情对话框逻辑
};
</script>

<style scoped>
/* 服务商选择组件基础样式 */
.service-provider-select {
  display: inline-block;
  position: relative;
}

/* 选择按钮样式 */
.select-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.selected-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 弹窗层级控制 */
:deep(.el-dialog__wrapper) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  z-index: 2100 !important;
}

:deep(.el-overlay) {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2099 !important;
  height: 100%;
  background-color: var(--el-overlay-color-lighter);
}

.provider-dialog {
  :deep(.el-dialog) {
    margin-top: 15vh !important;
    z-index: 2101 !important;
  }
}

/* 弹窗内容样式 */
.dialog-content {
  display: flex;
  height: 600px;
  background-color: var(--el-bg-color-page);
  position: relative;
  z-index: 2102;
}

.dialog-footer {
  padding: 10px 20px;
  text-align: right;
  position: relative;
}

/* 左侧列表样式 */
.provider-list {
  width: 300px;
  border-right: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  flex-direction: column;
}

.list-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.header-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.list-content {
  padding: 12px;
}

/* 服务商列表项样式 */
.provider-item {
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
  border: 1px solid transparent;
}

.provider-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

.provider-item.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.item-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.provider-name {
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.item-sub {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 右侧内容区样式 */
.provider-detail {
  flex: 1;
  padding: 20px;
  background-color: var(--el-bg-color);
  overflow-y: auto;
}

/* 表单样式 */
.provider-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 20px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.section-title .el-icon {
  color: var(--el-color-primary);
}

/* 端口配置样式 */
.port-config-section {
  padding: 10px 0;
}

.sub-section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 10px;
}

.port-row {
  margin-bottom: 10px;
}

/* 详情展示样式 */
.detail-content {
  max-width: 800px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.detail-header h3 {
  margin: 0;
  font-size: 20px;
  color: var(--el-text-color-primary);
}

.info-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item .label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-item .value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.empty-tip {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 描述列表样式优化 */
:deep(.el-descriptions) {
  padding: 16px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-descriptions__label) {
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

:deep(.el-descriptions__content) {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

/* Dialog 样式优化 */
:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-dialog__footer) {
  margin: 0;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* 动画效果 */
.dialog-enter-active,
.dialog-leave-active {
  transition: opacity 0.3s ease;
}

.dialog-enter-from,
.dialog-leave-to {
  opacity: 0;
}
.form-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
}

.ip-config-section {
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin-bottom: 16px;
}

.ip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.ip-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.port-config-section {
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-top: 16px;
}

.sub-section-title {
  font-size: 14px;
  font-weight: bold;
  color: #606266;
  margin-bottom: 12px;
}

.port-row {
  padding: 12px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px; /* 减少行间距 */
}

.port-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* 减少标题与内容的间距 */
}

.port-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

/* 优化后的表单项样式 */
.el-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px; /* 减少行间距 */
}

.el-col {
  display: flex;
  flex-direction: column;
}

.el-form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.el-form-item__label {
  text-align: right;
  padding-right: 10px; /* 减少标签与输入框的间距 */
  width: 100px; /* 适中宽度 */
  font-size: 14px;
  color: #606266;
}

.el-input,
.el-select {
  flex: 1;
  height: 40px; /* 增加输入框高度 */
}

.el-input__inner,
.el-select .el-input__inner {
  padding: 0 15px; /* 增加内部padding */
  width: 100%; /* 充分利用列宽度 */
  box-sizing: border-box; /* 确保padding不超出宽度 */
}

.el-form-item__content {
  flex: 1;
  margin-left: 10px; /* 减少内容与标签的间距 */
}

.el-select .el-input__inner {
  padding-right: 30px; /* 确保下拉箭头有足够空间 */
}
.port-dialog .el-dialog__body {
  padding: 24px 20px;
}

.port-dialog .el-form-item {
  margin-bottom: 12px;
}

/* 统一加高 input 宽度和内边距 */
.port-dialog .el-input__wrapper {
  min-height: 40px;
  padding-top: 6px;
  padding-bottom: 6px;
  font-size: 14px;
}
</style>
