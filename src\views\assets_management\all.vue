<!-- 资产管理 -->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree v-model="queryParams.deptId" @node-click="handleDeptClick" class="mb-2" />
        <system-tree v-model="queryParams.systemId" @system-click="handleSystemClick" />
      </el-col>

      <!-- 资产列表 -->
      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="资产类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="资产类型" clearable class="!w-[130px]">
                <el-option label="信息系统" :value="10" />
                <el-option label="服务器" :value="1" />
                <el-option label="安全设备" :value="3" />
                <el-option label="网络设备" :value="2" />
                <el-option label="物联网设备" :value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="资产名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="资产名称" clearable class="!max-w-[130px]" />
            </el-form-item>
            <el-form-item label="资产管理人" prop="managerId">
              <el-select v-model="queryParams.managerId" filterable clearable placeholder="请选择资产管理人">
                <el-option v-for="user in userList" :key="user.userId" :label="user.nickname" :value="user.userId" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="资产ip" prop="ip">
              <el-input v-model="queryParams.ip" placeholder="资产ip" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="资产链接" prop="url">
              <el-input v-model="queryParams.url" placeholder="资产链接" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="资产端口" prop="port">
              <el-input v-model="queryParams.port" placeholder="资产端口" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="联系方式" prop="ownerPhone">
              <el-input v-model="queryParams.ownerPhone" placeholder="资产管理者手机号" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="操作系统" prop="os">
              <dictionary v-model="queryParams.os" code="os" class="!w-[150px]" />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="登记时间" prop="createTime">
              <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchClick"><i-ep-search />搜索</el-button>
              <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
            </el-form-item>
          </el-form>
          <el-button @click="toggleAdvancedFilters" class="mb-2">
            {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
          </el-button>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <!-- 仅显示当前页选中信息 -->
                <span v-if="ids.length > 0" class="selection-info">
                  已选择 <el-tag type="info">{{ ids.length }}</el-tag> 项
                  <el-button type="primary" link @click="clearSelection">清除选择</el-button>
                </span>
              </div>
              <div>
                <el-button class="ml-3" @click="handleExport" :loading="exportLoading">
                  <template #icon><i-ep-download /></template>
                  导出{{ ids.length > 0 ? '选中' : '全部' }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading"
            :data="pageData" highlight-current-row border @selection-change="handleSelectionChange"
            @row-click="handleRowClick">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="type" label="资产类型" width="120">
              <template #default="{ row }">
                <el-tag>{{ getAssetTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column key="name" label="资产名称" prop="name" min-width="120" />
            <el-table-column key="url" label="地址/ip" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ scope.row.type === 10 ? scope.row.url : scope.row.ip }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" key="deptName" label="管理部门" prop="deptName" min-width="150" />
            <el-table-column align="center" key="managerName" label="管理员" prop="managerName" min-width="100" />
            <el-table-column align="center" key="mobile" label="联系方式" prop="mobile" min-width="150" />
            <el-table-column align="center" key="sysName" label="资产所属系统名称" prop="sysName" min-width="150" />
            <el-table-column align="center" key="status" label="资产状态" prop="status" min-width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
                  {{ scope.row.status == '1' ? '正常' : (scope.row.status == '0' ? '异常' : '废弃') }}
                </el-tag>
              </template>
            </el-table-column>
            <!-- 新增探测状态列 -->
            <el-table-column align="center" label="探测状态" width="150">
              <template #default="scope">
                <asset-detection-status 
                  :survival-status-list="scope.row.survivalStatusList"
                  :asset-name="scope.row.name" 
                />
              </template>
            </el-table-column>
            <el-table-column align="center" key="createTime" label="创建时间" prop="createTime" min-width="200" />
            <!-- 新增操作列 -->
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="viewAssetDetail(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="handlePaginationChange()" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 资产详情弹窗 -->
    <!-- 服务器详情 -->
    <asset-detail-view 
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '1'"
      v-model:visible="assetDetailDialog.visible" 
      :asset-id="assetDetailDialog.assetId" 
    />

    <!-- 网络设备详情 -->
    <network-detail-view 
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '2'"
      v-model:visible="assetDetailDialog.visible" 
      :asset-id="assetDetailDialog.assetId" 
    />

    <!-- 安全设备详情 -->
    <safety-detail-view 
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '3'"
      v-model:visible="assetDetailDialog.visible" 
      :asset-id="assetDetailDialog.assetId" 
    />

    <!-- 物联网设备详情 -->
    <iot-detail-view 
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '4'"
      v-model:visible="assetDetailDialog.visible" 
      :asset-id="assetDetailDialog.assetId" 
    />

    <!-- 信息系统详情 -->
    <view-system-assets 
      v-if="assetDetailDialog.visible && assetDetailDialog.assetType === '10'"
      v-model:visible="assetDetailDialog.visible" 
      :system-id="assetDetailDialog.assetId" 
    />

  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";

defineOptions({
  name: "assets",
  inheritAttrs: false,
});

import assetsAPI, { assetsPageVO, assetsForm, assetsPageQuery } from "@/api/assets_management/details/assets";
import eventsAPI, { eventsPageVO, eventsForm, eventsPageQuery } from "@/api/work_management/critical";
import DeptAPI from "@/api/dept";
import UserAPI, { UserQuery } from "@/api/user";

// 引入资产详情组件
import AssetDetailView from './DetailOfAssets/details/components/AssetDetailView.vue';
import NetworkDetailView from './DetailOfAssets/networkFacility/components/NetworkDetailView.vue';
import SafetyDetailView from './DetailOfAssets/safetyFacility/components/SafetyDetailView.vue';
import ViewSystemAssets from './DetailOfAssets/system/components/ViewSystemAssets.vue';
import IotDetailView from './DetailOfAssets/iot/components/IotDetailView.vue';
// 引入探测状态组件
import AssetDetectionStatus from './DetailOfAssets/components/assetDetectionStatus.vue';

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const importDialogVisible = ref(false);
const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const userList = ref<any[]>([])
const dataTableRef = ref();
const exportLoading = ref(false);

// 资产详情弹窗配置
const assetDetailDialog = reactive({
  visible: false,
  assetId: 0,
  assetType: ''
});

const queryParams = reactive<assetsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  status: 1
});

const userParams = reactive<UserQuery>({});
const showAdvancedFilters = ref(false);
const pageData = ref<assetsPageVO[]>([]);
const deptOptions = ref<any>([]);

const dialog = reactive({
  title: "",
  visible: false,
  id: undefined,
});

const formData = reactive<assetsForm>({});

// 查看资产详情
const viewAssetDetail = (asset: any) => {
  if (!asset || !asset.id || !asset.type) {
    ElMessage.error('资产数据不完整，无法查看详情');
    return;
  }

  assetDetailDialog.assetId = asset.id;
  assetDetailDialog.assetType = String(asset.type);
  assetDetailDialog.visible = true;

  const supportedTypes = [1, 2, 3, 4, 10];
  if (!supportedTypes.includes(asset.type)) {
    ElMessage.warning('暂不支持该类型资产的详情查看');
    assetDetailDialog.visible = false;
  }
};

// 切换高级筛选
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}

const toggleDeptTree = () => {
  showDeptTree.value = !showDeptTree.value;
};

// 获取用户选项
const loadUserList = async () => {
  try {
    const data = await UserAPI.getList(userParams)
    userList.value = data
  } catch (error) {
    console.error('加载用户列表失败:', error);
  }
}

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '服务器',
    2: '网络设备',
    3: '安全设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

/** 重置资产管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  Object.keys(queryParams).forEach(key => {
    if (key !== 'pageNum' && key !== 'pageSize') {
      queryParams[key] = undefined;
    }
  });
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  queryParams.status = 1;
  selectedEvent.value = undefined;
  clearSelection();
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any[]) {
  ids.value = selection.map((item: any) => item.id);
}

/** 点击行时切换选择状态 */
function handleRowClick(row, column) {
  if (column.type !== 'selection') {
    dataTableRef.value.toggleRowSelection(row);
  }
}

/** 清除所有选择 */
function clearSelection() {
  ids.value = [];
}

/** 打开资产管理弹窗 */
async function handleOpenDialog(id?: number) {
  dialog.visible = true
  dialog.id = id || undefined
  dialog.title = id ? '修改资产' : '新增资产'
}

/** 点击部门树节点时清空选择并查询 */
function handleDeptClick() {
  clearSelection();
  handleQuery();
}

/** 点击系统树节点时清空选择并查询 */
function handleSystemClick() {
  clearSelection();
  handleQuery();
}

/** 点击搜索按钮时清空选择并查询 */
function handleSearchClick() {
  clearSelection();
  handleQuery();
}

/** 处理分页变化时清空选择并查询 */
function handlePaginationChange() {
  clearSelection();
  handleQuery();
}

/** 查询资产管理 */
function handleQuery() {
  loading.value = true;
  getEvents();
  assetsAPI.getPageAll(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .catch((error) => {
      console.error('查询资产失败:', error);
      ElMessage.error('查询资产失败');
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 删除资产管理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      assetsAPI.deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

const selectedEvent = ref(undefined);
const reinsuranceEvents = ref([
  { id: 1, name: '事件1', assets: [] }
]);

//获取重保事件
function getEvents() {
  eventsAPI.getPage(queryParams)
    .then((data) => {
      reinsuranceEvents.value = data.list.map((item: any) => {
        return {
          id: item.id,
          name: item.eventName,
          assets: item.assets?.split(',').map(Number) ?? []
        };
      });
    })
    .catch((error) => {
      console.error('获取事件失败:', error);
    });
}

const transferDialog = reactive({
  visible: false,
  allAssets: [] as { id: number; name: string }[],
  selectedAssets: [] as number[],
})

const filterMethod = (query: string, item: { id: number; name: string }) => {
  return item.name.toLowerCase().includes(query.toLowerCase())
}

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  const exportParams: any = {
    ...queryParams
  };

  if (ids.value.length > 0) {
    exportParams.assetIds = ids.value;
  }

  exportLoading.value = true;

  assetsAPI.exportAll(exportParams)
    .then((response: any) => {
      const fileData = response.data;
      const fileName = decodeURI(
        response.headers["content-disposition"].split(";")[1].split("=")[1]
      );
      const fileType =
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

      const blob = new Blob([fileData], { type: fileType });
      const downloadUrl = window.URL.createObjectURL(blob);

      const downloadLink = document.createElement("a");
      downloadLink.href = downloadUrl;
      downloadLink.download = fileName;

      document.body.appendChild(downloadLink);
      downloadLink.click();

      document.body.removeChild(downloadLink);
      window.URL.revokeObjectURL(downloadUrl);

      ElMessage.success(`成功导出${ids.value.length > 0 ? '选中' : '全部'}资产`);
    })
    .catch(error => {
      console.error('导出失败:', error);
      ElMessage.error('导出失败，请重试');
    })
    .finally(() => {
      exportLoading.value = false;
    });
}

onMounted(() => {
  handleQuery();
  loadUserList();
});
</script>

<style scoped>
.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
}

.el-table .el-table__cell {
  padding: 8px 0;
}

.el-button--small {
  padding: 5px 8px;
  font-size: 12px;
}
</style>
