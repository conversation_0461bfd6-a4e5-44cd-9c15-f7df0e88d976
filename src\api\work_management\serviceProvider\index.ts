import request from "@/utils/request";
import {systemsEntityPageQuery} from "@/api/assets_management/details/systems-entity";

const PROVIDER_BASE_URL = "/api/v1/providers";

class ProviderAPI {
    /** 获取服务商管理分页数据 */
    static getPage(queryParams?: ProviderPageQuery) {
        return request<any, PageResult<ProviderPageVO[]>>({
            url: `${PROVIDER_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 获取服务商管理表单数据
     *
     * @param id ProviderID
     * @returns Provider表单数据
     */
    static getFormData(id: bigint) {
        return request<any, ProviderForm>({
            url: `${PROVIDER_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加服务商管理*/
    static add(data: ProviderForm) {
        return request({
            url: `${PROVIDER_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新服务商管理
     *
     * @param id ProviderID
     * @param data Provider表单数据
     */
    static update(id: bigint, data: ProviderForm) {
        return request({
            url: `${PROVIDER_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除服务商管理，多个以英文逗号(,)分割
     *
     * @param ids 服务商管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${PROVIDER_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 下载系统模板
     * @returns {Promise} - 请求的Promise对象
     */
    static downloadTemplate() {
      return request({
        url: `${PROVIDER_BASE_URL}/template`,
        method: "get",
        responseType: "arraybuffer",
      });
    }

    /**
     * 导入服务商
     * @param {File} file - 导入文件
     * @returns {Promise} - 请求的Promise对象
     */
    static importServiceProvider(file: File) {
      const formData = new FormData();
      formData.append("file", file);
      return request<any, ExcelResult>({
        url: `${PROVIDER_BASE_URL}/import`,
        method: "post",
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
    }

    /**
     * 导出服务商
     * @param {ProviderPageQuery & {ids?: string}} queryParams - 查询参数，可包含ids参数
     * @returns {Promise} - 请求的Promise对象
     */
    static export(queryParams: ProviderPageQuery & {ids?: string}) {
      return request({
        url: `${PROVIDER_BASE_URL}/export`,
        method: "get",
        params: queryParams,
        responseType: "arraybuffer",
      });
    }
}

export default ProviderAPI;

/** 服务商管理分页查询参数 */
export interface ProviderPageQuery extends PageQuery {
    /** 主键ID */
    id?: bigint;
    /** 服务商名称 */
    providerName?: string;
    /** 项目负责人 */
    projectManager?: string;
    /** 项目负责人手机号 */
    managerMobile?: string;
    /** 项目负责人邮箱 */
    managerEmail?: string;
    /** 技术负责人 */
    techLeader?: string;
    /** 技术负责人手机号 */
    techMobile?: string;
    /** 技术负责人邮箱 */
    techEmail?: string;
    /** 服务商描述 */
    description?: string;
    /** 状态(0:禁用 1:启用) */
    status?: number;
    /** 合同开始时间 */
    contractStartTime?: string;
    /** 合同结束时间 */
    contractEndTime?: string;
}

/** 服务商管理表单对象 */
export interface ProviderForm {
    /** 主键ID */
    id?:  bigint;
    /** 服务商名称 */
    providerName?:  string;
    /** 项目负责人 */
    projectManager?:  string;
    /** 项目负责人手机号 */
    managerMobile?:  string;
    /** 项目负责人邮箱 */
    managerEmail?:  string;
    /** 技术负责人 */
    techLeader?:  string;
    /** 技术负责人手机号 */
    techMobile?:  string;
    /** 技术负责人邮箱 */
    techEmail?:  string;
    /** 服务商描述 */
    description?:  string;
    /** 状态(0:禁用 1:启用) */
    status?:  number;
    /** 合同开始时间 */
    contractStartTime?:  Date;
    /** 合同结束时间 */
    contractEndTime?:  Date;
    /** 合同文件路径 */
    contractFile?:  string;
    /** 创建时间 */
    createTime?:  Date;
    /** 更新时间 */
    updateTime?:  Date;
    /** 创建者 */
    createBy?:  string;
    /** 更新者 */
    updateBy?:  string;
}

/** 服务商管理分页对象 */
export interface ProviderPageVO {
    /** 主键ID */
    id?: bigint;
    /** 服务商名称 */
    providerName?: string;
    /** 项目负责人 */
    projectManager?: string;
    /** 项目负责人手机号 */
    managerMobile?: string;
    /** 项目负责人邮箱 */
    managerEmail?: string;
    /** 技术负责人 */
    techLeader?: string;
    /** 技术负责人手机号 */
    techMobile?: string;
    /** 技术负责人邮箱 */
    techEmail?: string;
    /** 服务商描述 */
    description?: string;
    /** 状态(0:禁用 1:启用) */
    status?: number;
    /** 合同开始时间 */
    contractStartTime?: Date;
    /** 合同结束时间 */
    contractEndTime?: Date;
    /** 合同文件路径 */
    contractFile?: string;
    /** 创建时间 */
    createTime?: Date;
    /** 更新时间 */
    updateTime?: Date;
    /** 创建者 */
    createBy?: string;
    /** 更新者 */
    updateBy?: string;
}
