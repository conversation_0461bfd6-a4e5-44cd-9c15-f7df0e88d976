<template>
  <el-dialog 
    v-model="visible" 
    :title="dialogTitle" 
    width="400px"
    @close="handleClose"
  >
    <el-form :model="formData" label-width="80px" ref="formRef">
      <el-form-item label="标题" required>
        <el-input v-model="formData.title" />
      </el-form-item>
      
      <el-form-item label="类型" required>
        <el-select 
          v-model="formData.type" 
          @change="handleTypeChange"
          :disabled="mode === 'view'"
        >
          <el-option 
            v-for="option in typeOptions"
            :key="option.value"
            :label="option.label" 
            :value="option.value" 
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="任务分配" required v-if="formData.type !== 'milestone'">
        <el-tree-select
          v-model="formData.parentId"
          :data="parentOptions"
          :props="{ label: 'title', children: 'children', value: 'id' }"
          placeholder="选择所属里程碑/任务文件夹"
          clearable
          check-strictly
          style="width: 100%"
          :disabled="mode === 'view'"
          @change="handleParentChange"
        />
      </el-form-item>
      
      <el-form-item label="人员分配" required>
        <el-select
          v-model="formData.members"
          multiple
          filterable
          placeholder="请选择成员"
          :disabled="mode === 'view'"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.username"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="优先级" required>
        <el-select v-model="formData.priority" :disabled="mode === 'view'">
          <el-option label="高" value="high" />
          <el-option label="普通" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="开始日期" required>
        <el-date-picker
          v-model="formData.start"
          type="date"
          value-format="YYYY-MM-DD"
          :disabled="mode === 'view'"
          @change="validateDateRange"
        />
      </el-form-item>
      
      <el-form-item label="截止日期" required :error="dateError">
        <el-date-picker
          v-model="formData.end"
          type="date"
          value-format="YYYY-MM-DD"
          :disabled="mode === 'view'"
          @change="validateDateRange"
        />
      </el-form-item>
      
      <el-form-item label="任务描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          rows="4"
          placeholder="请输入任务描述信息"
          :disabled="mode === 'view'"
        />
      </el-form-item>
    </el-form>
    
    <template #footer v-if="mode !== 'view'">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        {{ mode === 'create' ? '确定' : '保存' }}
      </el-button>
    </template>
    
    <template #footer v-else>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { TaskRow, TaskType, User } from "@/types/project";
import { TaskHierarchyManager } from "@/utils/taskHierarchy";
import dayjs from "dayjs";

interface Props {
  visible: boolean;
  mode: 'create' | 'edit' | 'view';
  task?: TaskRow | null;
  userList: User[];
  parentOptions: any[];
  initialType?: TaskType;
  initialParentId?: number | null;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'submit', data: TaskRow): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  task: null,
  initialType: 'milestone',
  initialParentId: null,
});

const emit = defineEmits<Emits>();

const formRef = ref();
const dateError = ref("");

const formData = ref<TaskRow>({
  id: 0,
  title: "",
  type: "milestone",
  priority: "medium",
  status: "not_started",
  start: dayjs().format("YYYY-MM-DD"),
  end: dayjs().add(1, "day").format("YYYY-MM-DD"),
  parentId: -1,
  members: [],
  children: [],
  description: "",
  projectId: 0,
  owner: "",
  canUpload: false,
});

const dialogTitle = computed(() => {
  switch (props.mode) {
    case 'create': return '新建工作项';
    case 'edit': return '编辑工作项';
    case 'view': return '工作项详情';
    default: return '';
  }
});

const typeOptions = computed(() => {
  if (props.mode === 'create' && props.initialParentId) {
    // 创建子项时，根据父项类型限制可选类型
    const parentTask = findTaskById(props.initialParentId);
    if (parentTask) {
      return TaskHierarchyManager.getCreateOptions(parentTask.type);
    }
  }
  
  // 默认返回所有类型
  return [
    { value: 'milestone', label: '里程碑' },
    { value: 'taskFolder', label: '任务文件夹' },
    { value: 'task', label: '任务' },
  ];
});

// 查找任务的辅助函数
function findTaskById(id: number): TaskRow | null {
  // 这里需要从父组件传入的数据中查找
  // 简化实现，实际应该从完整的任务列表中查找
  return null;
}

function handleTypeChange() {
  if (formData.value.type === 'milestone') {
    formData.value.parentId = -1;
  }
}

function handleParentChange(parentId: number) {
  // 验证父子关系的逻辑
  validateDateRange();
}

function validateDateRange() {
  dateError.value = "";
  
  if (!formData.value.start || !formData.value.end) {
    return;
  }
  
  if (dayjs(formData.value.start).isAfter(dayjs(formData.value.end))) {
    dateError.value = "开始日期不能晚于截止日期";
    return;
  }
  
  // 其他验证逻辑...
}

function handleSubmit() {
  if (!formData.value.title) {
    return;
  }
  
  validateDateRange();
  if (dateError.value) {
    return;
  }
  
  emit('submit', { ...formData.value });
}

function handleClose() {
  emit('update:visible', false);
  emit('close');
}

// 监听props变化，初始化表单数据
watch(() => [props.visible, props.task, props.mode], () => {
  if (props.visible) {
    if (props.mode === 'create') {
      formData.value = {
        id: 0,
        title: "",
        type: props.initialType || "milestone",
        priority: "medium",
        status: "not_started",
        start: dayjs().format("YYYY-MM-DD"),
        end: dayjs().add(1, "day").format("YYYY-MM-DD"),
        parentId: props.initialParentId || -1,
        members: [],
        children: [],
        description: "",
        projectId: 0,
        owner: "",
        canUpload: false,
      };
    } else if (props.task) {
      formData.value = { ...props.task };
    }
    dateError.value = "";
  }
}, { immediate: true });
</script>

<style scoped>
.el-form {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
