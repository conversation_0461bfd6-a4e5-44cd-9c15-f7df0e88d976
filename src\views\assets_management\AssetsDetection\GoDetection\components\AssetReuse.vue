<template>
    <el-dialog
      v-model="dialogVisible"
      title="复用探测任务资产"
      width="80%"
      destroy-on-close
    >
      <div class="asset-reuse-container">
        <div class="dialog-main">
          <!-- 左侧探测任务列表 -->
          <div class="left-panel">
            <div class="panel-header">
              <div class="panel-title">探测任务列表</div>
              <el-input
                v-model="taskSearchText"
                placeholder="搜索任务名称"
                clearable
                size="small"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <el-scrollbar height="350px">
              <div class="task-list">
                <el-skeleton :rows="6" animated v-if="loading" />
                <template v-else>
                  <div
                    v-for="task in filteredTasks"
                    :key="task.id"
                    class="task-item"
                    :class="{ active: selectedTask?.id === task.id }"
                    @click="selectTask(task)"
                  >
                    <div class="task-name">{{ task.detectionName || task.taskName }}</div>
                    <div class="task-info">资产数量: {{ getAssetCount(task) }}</div>
                  </div>
                  <div v-if="filteredTasks.length === 0" class="empty-data">
                    <el-empty description="暂无探测任务" :image-size="60" />
                  </div>
                </template>
              </div>
            </el-scrollbar>
          </div>
  
          <!-- 右侧资产列表 -->
          <div class="right-panel">
            <div class="panel-header">
              <div class="panel-title">资产列表</div>
              <div v-if="selectedTaskAssets.length > 0">
                <el-checkbox
                  v-model="selectAllAssets"
                  :indeterminate="isIndeterminate"
                  @change="handleCheckAllChange"
                >
                  全选
                </el-checkbox>
              </div>
            </div>
            <el-scrollbar height="350px">
              <el-empty v-if="!selectedTask" description="请选择一个探测任务" :image-size="60" />
              <div v-else-if="selectedTaskAssets.length === 0" class="empty-data">
                <el-empty description="该任务暂无资产" :image-size="60" />
              </div>
              <el-table
                v-else
                :data="selectedTaskAssets"
                border
                style="width: 100%"
                v-loading="assetLoading"
                @selection-change="handleSelectionChange"
                ref="assetTable"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
                <el-table-column prop="type" label="资产类型" min-width="100" align="center">
                  <template #default="scope">
                    <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="资产名称" min-width="120" align="center" show-overflow-tooltip />
                <el-table-column label="资产地址" min-width="150" align="center" show-overflow-tooltip>
                  <template #default="scope">
                    <div v-if="scope.row.ip">{{ scope.row.ip }}</div>
                    <div v-else-if="scope.row.url">{{ scope.row.url }}</div>
                    <div v-else-if="scope.row.address">{{ scope.row.address }}</div>
                    <div v-else>-</div>
                  </template>
                </el-table-column>
                <el-table-column prop="deptName" label="管理部门" min-width="120" align="center" show-overflow-tooltip>
                  <template #default="scope">
                    <dictmap v-model="scope.row.deptId" code="dept0x0" />
                  </template>
                </el-table-column>
                <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" show-overflow-tooltip />
              </el-table>
            </el-scrollbar>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirmSelection" :disabled="selectedAssets.length === 0">
            确认选择 ({{ selectedAssets.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Search } from '@element-plus/icons-vue'
  import DetectionAPI from '@/api/assets_management/assets_detection/index'
  
  // 定义props，接收已选资产ID列表和对话框可见性
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    currentSelectedIds: {
      type: Array as () => number[],
      default: () => []
    }
  })
  
  // 定义emit，用于触发资产选择事件和关闭弹窗
  const emit = defineEmits(['update:visible', 'select-assets'])
  
  // 界面控制变量
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })
  const loading = ref(false)
  const assetLoading = ref(false)
  const taskSearchText = ref('')
  const assetTable = ref(null)
  
  // 定义任务类型接口
  interface Task {
    id: number;
    detectionName?: string;
    taskName?: string;
    assetsList?: any[];
    assetId?: string | number[] | number;
    [key: string]: any;
  }
  
  // 数据变量
  const taskList = ref<Task[]>([])
  const selectedTask = ref<Task | null>(null)
  const selectedTaskAssets = ref<any[]>([])
  const selectedAssets = ref([])
  const selectAllAssets = ref(false)
  const isIndeterminate = ref(false)
  
  // 过滤后的任务列表
  const filteredTasks = computed(() => {
    if (!taskSearchText.value) return taskList.value
  
    return taskList.value.filter(task => 
      (task.detectionName || task.taskName || '').toLowerCase().includes(taskSearchText.value.toLowerCase())
    )
  })
  
  // 当对话框显示时加载任务列表
  watch(() => props.visible, (val) => {
    if (val) {
      loadTaskList()
    } else {
      // 重置选择状态
      selectedTask.value = null
      selectedTaskAssets.value = []
      selectedAssets.value = []
      selectAllAssets.value = false
      isIndeterminate.value = false
      taskSearchText.value = ''
    }
  })
  
  // 加载任务列表
  const loadTaskList = async () => {
    try {
      loading.value = true
      const params = {
        pageNum: 1,
        pageSize: 100,
        // 添加排序，按创建时间倒序
        orderByColumn: 'createTime',
        isAsc: 'desc'
      }
      
      const res = await DetectionAPI.getPage(params)
      if (res) {
        taskList.value = res.list
        console.log('加载到探测任务：', taskList.value.length)
      } else {
        taskList.value = []
      }
    } catch (error) {
      console.error('加载探测任务列表失败:', error)
      ElMessage.error('加载探测任务列表失败')
    } finally {
      loading.value = false
    }
  }
  
  // 获取资产数量
  const getAssetCount = (task) => {
    if (!task) return 0
    if (task.assetsList && Array.isArray(task.assetsList)) {
      return task.assetsList.length
    }
    
    // 尝试从assetId字段解析
    if (task.assetIds) {
      if (Array.isArray(task.assetIds)) {
        return task.assetId.length
      }
      if (typeof task.assetIds === 'string') {
        return task.assetIds.split(',').length
      }
      return 1 // 单个资产ID
    }
    
    return 0
  }
  
  // 资产类型转换
  const getAssetTypeName = (type) => {
    if (typeof type === 'string') {
      type = parseInt(type)
    }
  
    const typeMap = {
      1: '服务器',
      3: '安全设备',
      2: '网络设备',
      4: '物联网设备',
      10: '信息系统'
    }
    return typeMap[type] || '信息系统'
  }
  
  // 选择任务
  const selectTask = async (task) => {
    selectedTask.value = task
    selectedAssets.value = []
    selectAllAssets.value = false
    isIndeterminate.value = false
    
    try {
      assetLoading.value = true
      
      // 如果任务已经包含资产列表，直接使用
      if (task.assetsList && Array.isArray(task.assetsList) && task.assetsList.length > 0) {
        selectedTaskAssets.value = task.assetsList.map(asset => ({
          id: asset.id || asset.assetsId,
          name: asset.name || '',
          type: asset.type,
          ip: asset.ip || '',
          url: asset.url || '',
          address: asset.address || '',
          deptId: asset.deptId,
          deptName: asset.deptName || '',
          ownerName: asset.ownerName || asset.managerName || '',
        }))
      } else {
        // 需要通过API获取任务详情
        const detail = await DetectionAPI.getFormData(task.id)
        console.log('获取任务详情:', detail)
        
        if (detail && detail.assetsList && Array.isArray(detail.assetsList)) {
          selectedTaskAssets.value = detail.assetsList.map(asset => ({
            id: asset.id || asset.assetsId,
            name: asset.name || '',
            type: asset.type,
            ip: asset.ip || '',
            url: asset.url || '',
            address: asset.address || '',
            deptId: asset.deptId,
            deptName: asset.deptName || '',
            ownerName: asset.ownerName || asset.managerName || '',
          }))
        } else {
          selectedTaskAssets.value = []
        }
      }
    } catch (error) {
      console.error('加载任务资产失败:', error)
      ElMessage.error('加载任务资产失败')
      selectedTaskAssets.value = []
    } finally {
      assetLoading.value = false
    }
  }
  
  // 处理全选变化
  const handleCheckAllChange = (val) => {
    selectedAssets.value = val ? [...selectedTaskAssets.value] : []
    isIndeterminate.value = false
    
    // 更新表格选中状态
    if (assetTable.value) {
      selectedTaskAssets.value.forEach(row => {
        assetTable.value.toggleRowSelection(row, val)
      })
    }
  }
  
  // 处理多选变化
  const handleSelectionChange = (selection) => {
    selectedAssets.value = selection
    const checkedCount = selection.length
    selectAllAssets.value = checkedCount === selectedTaskAssets.value.length && selectedTaskAssets.value.length > 0
    isIndeterminate.value = checkedCount > 0 && checkedCount < selectedTaskAssets.value.length
  }
  
  // 确认选择
  const confirmSelection = () => {
    if (selectedAssets.value.length === 0) {
      ElMessage.warning('请至少选择一个资产')
      return
    }
  
    // 获取选择资产的ID和资产详情
    const selectedIds = selectedAssets.value.map(asset => asset.id)
    
    // 触发事件，将选定的资产ID和详情传递给父组件
    emit('select-assets', {
      selectedIds,
      selectedAssets: selectedAssets.value
    })
    
    ElMessage.success(`已选择${selectedAssets.value.length}个资产`)
    dialogVisible.value = false
  }
  
  // 取消按钮
  const cancel = () => {
    dialogVisible.value = false
  }
  </script>
  
  <style scoped>
  .asset-reuse-container {
    height: 100%;
  }
  
  .dialog-main {
    display: flex;
    gap: 20px;
    height: 450px;
  }
  
  .left-panel, .right-panel {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
  }
  
  .left-panel {
    width: 30%;
  }
  
  .right-panel {
    flex: 1;
  }
  
  .panel-header {
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--el-border-color-light);
    background-color: var(--el-fill-color-light);
  }
  
  .panel-title {
    font-size: 16px;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
  
  .task-list {
    padding: 10px;
  }
  
  .task-item {
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--el-border-color-lighter);
    background-color: var(--el-bg-color);
  }
  
  .task-item:hover {
    background-color: var(--el-fill-color-light);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }
  
  .task-item.active {
    background-color: var(--el-color-primary-light-9);
    border-color: var(--el-color-primary);
  }
  
  .task-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--el-text-color-primary);
  }
  
  .task-info {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
  
  .empty-data {
    padding: 60px 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .dialog-footer {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
  
  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }
  
  :deep(.el-table .cell) {
    word-break: break-word;
  }
  </style>
