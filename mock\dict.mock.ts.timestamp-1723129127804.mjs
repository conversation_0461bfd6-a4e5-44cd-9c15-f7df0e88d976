// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/dict.mock.ts
var dict_mock_default = defineMock([
  {
    url: "dict/:code/options",
    method: ["GET"],
    body: ({ params }) => {
      const code = params.code;
      let list = null;
      if (code == "gender") {
        list = [
          {
            value: "1",
            label: "\u7537"
          },
          {
            value: "2",
            label: "\u5973"
          },
          {
            value: "0",
            label: "\u4FDD\u5BC6"
          }
        ];
      }
      return {
        code: "00000",
        data: list,
        msg: "\u4E00\u5207ok"
      };
    }
  },
  {
    url: "dict/page",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        list: [
          {
            id: 1,
            name: "\u6027\u522B",
            code: "gender",
            status: 1,
            dictItems: [
              {
                id: 1,
                name: "\u7537",
                value: "1",
                sort: 1,
                status: 1
              },
              {
                id: 2,
                name: "\u5973",
                value: "2",
                sort: 2,
                status: 1
              },
              {
                id: 3,
                name: "\u4FDD\u5BC6",
                value: "0",
                sort: 3,
                status: 1
              }
            ]
          }
        ],
        total: 1
      },
      msg: "\u4E00\u5207ok"
    }
  },
  // 新增字典
  {
    url: "dict",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u65B0\u589E\u5B57\u5178" + body.name + "\u6210\u529F"
      };
    }
  },
  // 获取字典表单数据
  {
    url: "dict/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: dictMap[params.id],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 修改字典
  {
    url: "dict/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u4FEE\u6539\u5B57\u5178" + body.name + "\u6210\u529F"
      };
    }
  },
  // 删除字典
  {
    url: "dict/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "\u5220\u9664\u5B57\u5178" + params.id + "\u6210\u529F"
      };
    }
  }
]);
var dictMap = {
  1: {
    code: "00000",
    data: {
      id: 1,
      name: "\u6027\u522B",
      code: "gender",
      status: 1,
      dictItems: [
        {
          id: 1,
          name: "\u7537",
          value: "1",
          sort: 1,
          status: 1
        },
        {
          id: 2,
          name: "\u5973",
          value: "2",
          sort: 2,
          status: 1
        },
        {
          id: 3,
          name: "\u672A\u77E5",
          value: "0",
          sort: 3,
          status: 1
        }
      ]
    },
    msg: "\u4E00\u5207ok"
  }
};
export {
  dict_mock_default as default
};
