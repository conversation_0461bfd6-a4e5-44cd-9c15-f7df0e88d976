<template>
  <el-dialog
    v-model="dialogVisible"
    title="工单流转记录"
    width="85%"
    :close-on-click-modal="false"
    destroy-on-close
    class="transfer-record-dialog"
  >
    <div class="transfer-record-container">
      <!-- 流转记录卡片 -->
      <el-card class="flow-card mb-4">
        <template #header>
          <div class="card-header">
            <span class="header-title">工单流转记录</span>
            <div class="header-actions">
              <el-switch
                v-model="flowViewMode"
                active-text="时间线"
                inactive-text="表格"
                inline-prompt
              />
            </div>
          </div>
        </template>
        
        <!-- 表格视图 -->
        <el-table v-if="!flowViewMode" :data="flowRecords" style="width: 100%" stripe border v-loading="flowLoading">
          <el-table-column prop="step" label="环节" width="120">
            <template #default="{ row }">
              <el-tag :type="getFlowTimelineType(row.step)" effect="plain">{{ row.step }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="处理人" width="120">
            <template #default="{ row }">
              <div class="user-info">
                <el-avatar :size="24" :icon="UserFilled" class="user-avatar" />
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.startTime) }}
            </template>
          </el-table-column>
          <el-table-column label="执行时间" min-width="180">
            <template #default="{ row }">
              {{ formatDateTime(row.executeTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="result" label="处理结果" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.result" :type="getResultTagType(row.result)" size="small">
                {{ row.result }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="comment" label="处理意见" min-width="200" show-overflow-tooltip />
        </el-table>
        
        <!-- 时间线视图 -->
        <div v-else class="timeline-container" v-loading="flowLoading">
          <el-empty v-if="flowRecords.length === 0 && !flowLoading" description="暂无流转记录" />
          <el-timeline v-else>
            <el-timeline-item
              v-for="(flow, index) in flowRecords"
              :key="index"
              :type="getFlowTimelineType(flow.step)"
              :timestamp="formatDateTime(flow.executeTime || flow.startTime)"
              :hollow="index !== 0"
            >
              <h4>{{ flow.step }}</h4>
              <p class="timeline-content">
                <span class="timeline-user">处理人：
                  {{ flow.name }}
                </span>
                <span v-if="flow.result" class="timeline-result">
                  处理结果：
                  <el-tag :type="getResultTagType(flow.result)" size="small">{{ flow.result }}</el-tag>
                </span>
                <span v-if="flow.comment" class="timeline-msg">
                  处理意见：{{ flow.comment }}
                </span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
      
      <!-- 附件列表卡片 -->
      <el-card class="attachment-card" v-if="showAttachments">
        <template #header>
          <div class="card-header">
            <span class="header-title">附件列表</span>
            <div class="header-actions">
              <el-tag>共 {{ attachments.length }} 个附件</el-tag>
            </div>
          </div>
        </template>
        <div v-if="attachments.length === 0" class="attachment-empty">
          <el-empty description="暂无附件" />
        </div>
        <div v-else class="attachment-list">
          <el-table :data="attachments" style="width: 100%">
            <el-table-column prop="linkName" label="环节名称" />
            <el-table-column prop="name" label="文件名" />
            <el-table-column prop="createByName" label="创建人名称" />
            <el-table-column prop="createTime" label="上传时间" />
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { UserFilled } from '@element-plus/icons-vue';

// 定义流转记录类型
interface FlowRecord {
  step: string;
  name: string;
  startTime: string | null;
  executeTime: string | null;
  result: string;
  comment: string;
  index: number;
}

interface Attachment {
  id?: number;
  linkName?: string;
  name?: string;
  createByName?: string;
  createTime?: string;
  url?: string;
  [key: string]: any;
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // 工单ID
  ticketId: {
    type: [Number, String],
    required: true
  },
  // API 类型 - 安全问题、安全评估或资产下线
  apiType: {
    type: String,
    default: 'offline',  // offline, safety, business
    validator: (value: string) => ['offline', 'safety', 'business'].includes(value)
  },
  // 是否显示附件列表
  showAttachments: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:visible']);

// 组件状态
const dialogVisible = ref(false);
const flowViewMode = ref(false); // true=时间线, false=表格
const flowLoading = ref(false);
const attachmentLoading = ref(false);
const flowRecords = ref<FlowRecord[]>([]);
const attachments = ref<Attachment[]>([]);

// 监听props.visible变化
watch(() => props.visible, (newValue) => {
  dialogVisible.value = newValue;
  if (newValue && props.ticketId) {
    loadData();
  }
});

// 监听dialogVisible变化
watch(() => dialogVisible.value, (newValue) => {
  emit('update:visible', newValue);
});

// 获取对应API的函数
const getApiModule = () => {
  try {
    if (props.apiType === 'safety') {
      return import('@/api/work_management/safety/index').then(module => module.default);
    } else if (props.apiType === 'business') {
      return import('@/api/work_management/online_service').then(module => module.default);
    } else {
      return import('@/api/assets_management/details/offline').then(module => module.default); 
    }
  } catch (error) {
    console.error('获取API模块失败', error);
    return Promise.reject(error);
  }
};

// 加载流转记录
const loadFlowRecords = async () => {
  if (!props.ticketId) return;
  
  flowLoading.value = true;
  try {
    const api = await getApiModule();
    let res;
    
    // 根据不同的API类型调用不同的流转记录API
    if (props.apiType === 'safety') {
      res = await api.getFlowInfo(props.ticketId);
    } else if (props.apiType === 'business') {
      res = await api.getFlow(props.ticketId);
    } else {
      res = await api.getFlowInfo(props.ticketId);
    }
    
    console.log('流转记录响应:', res);
    
    if (res) {
      // 处理流转记录数据，统一格式
      flowRecords.value = res.map((item: any, index: number) => {
        return {
          step: item.name || item.step || '-',
          name: item.userName || item.operator || '-',
          startTime: item.executeStartTime || item.startTime || null,
          executeTime: item.executeTime || null,
          result: item.processingResults || item.result || '',
          comment: item.commentContent || item.comment || item.msg || '',
          index
        } as FlowRecord;
      });
    } else {
      flowRecords.value = [];
    }
  } catch (error) {
    console.error('加载流转记录失败:', error);
    ElMessage.error('加载流转记录失败');
  } finally {
    flowLoading.value = false;
  }
};

// 加载附件列表
const loadAttachments = async () => {
  if (!props.ticketId || !props.showAttachments) return;
  
  attachmentLoading.value = true;
  try {
    const api = await getApiModule();
    let res;
    
    if (props.apiType === 'safety') {
      // 安全问题使用getFileList
      res = await api.getFileList(props.ticketId);
    } else if (props.apiType === 'business') {
      // 安全评估使用getAttachments
      res = await api.getFileList(props.ticketId);
    } else {
      // 资产下线使用getAttachments
      res = await api.getFileList(props.ticketId);
    }
    
    console.log('附件列表响应:', res);
    
    attachments.value = Array.isArray(res) ? res : [];
  } catch (error) {
    console.error('加载附件列表失败:', error);
    ElMessage.warning('加载附件列表失败');
    attachments.value = [];
  } finally {
    attachmentLoading.value = false;
  }
};

// 下载文件
const downloadFile = (file: Attachment) => {
  if (!file || !file.url) {
    ElMessage.warning('文件链接不存在');
    return;
  }
  
  try {
    window.open(file.url, '_blank');
  } catch (error) {
    console.error('文件下载失败:', error);
    ElMessage.error('文件下载失败');
  }
};

// 格式化时间
const formatDateTime = (dateTimeStr: string | null | undefined) => {
  if (!dateTimeStr) return '-';
  
  try {
    if (dateTimeStr.includes('T')) {
      return dateTimeStr.replace('T', ' ').substring(0, 19);
    }
    return dateTimeStr;
  } catch (error) {
    return dateTimeStr || '-';
  }
};

// 获取流转记录时间线类型
const getFlowTimelineType = (stepName: string) => {
  const typeMap: Record<string, string> = {
    // 下线流程步骤
    '发起工单': 'primary',
    '下线申请': 'primary',
    '下线审核': 'success',
    '工单评价': 'warning',
    '完成工单': 'success',
    '关闭工单': 'info',
    
    // 安全问题流程步骤
    '漏洞审核': 'success',
    '漏洞整改': 'warning',
    '漏洞复核': 'info',
    '整改评价': 'danger',
    
    // 安全评估流程步骤
    '安全评估': 'warning',
    '工单整改': 'info',
    '工单复核': 'warning',
  };
  
  // 如果是数字类型的步骤ID
  if (!isNaN(Number(stepName))) {
    const stepMap: Record<string, string> = {
      '1': 'primary', // 发起工单
      '2': 'success', // 审核
      '3': 'warning', // 评估
      '4': 'info',    // 整改
      '5': 'warning', // 复核
      '6': 'danger',  // 评价
      '7': 'success'  // 关闭
    };
    return stepMap[stepName] || 'info';
  }
  
  return typeMap[stepName] || 'info';
};

// 获取处理结果标签类型
const getResultTagType = (result: string) => {
  const resultMap: Record<string, string> = {
    '通过': 'success',
    '不通过': 'danger',
    '已整改': 'success',
    '未整改': 'warning',
    '已修复': 'success',
    '未修复': 'danger',
    '已关闭': 'info',
    '满意': 'success',
    '不满意': 'danger'
  };
  
  return resultMap[result] || 'info';
};

// 加载所有数据
const loadData = async () => {
  await Promise.all([
    loadFlowRecords(),
    props.showAttachments ? loadAttachments() : Promise.resolve()
  ]);
};

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 暴露组件方法
defineExpose({
  loadData,
  loadFlowRecords,
  loadAttachments
});
</script>

<style scoped>
.transfer-record-dialog {
  --el-dialog-padding-primary: 20px;
}

.transfer-record-container {
  width: 100%;
}

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.header-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 16px;
  background-color: #409EFF;
  margin-right: 8px;
  border-radius: 2px;
  vertical-align: middle;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #ecf5ff;
  color: #409EFF;
}

.timeline-container {
  padding: 10px 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
}

.timeline-result {
  margin: 5px 0;
}

.timeline-msg {
  color: #606266;
  white-space: pre-wrap;
}

.attachment-empty {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}
</style>
