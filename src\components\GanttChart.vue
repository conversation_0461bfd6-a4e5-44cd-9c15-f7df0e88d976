<template>
  <div
    class="gantt-chart-pane"
    ref="chartPaneRef"
    @mousemove="handlePaneMouseMove"
    @mouseleave="handlePaneMouseLeave"
  >
    <div class="gantt-chart-header">
      <span v-for="week in ganttWeeks" :key="week" class="gantt-date">
        {{ week }}
      </span>
    </div>
    <div class="gantt-chart-body">
      <template v-for="row in visibleTaskListWithIndent" :key="row.id">
        <div class="gantt-row" :style="{ paddingLeft: row.indent * 20 + 'px' }">
          <div
            class="gantt-bar"
            :class="[
              {
                'gantt-bar-animate': barAnimated[row.id],
                'gantt-bar-hidden': !barAnimated[row.id],
              },
              getGanttBarClass(row),
            ]"
            :style="getGanttBarStyle(row)"
            @mouseenter="showTooltip($event, row)"
            @mouseleave="hideTooltip"
          ></div>
        </div>
      </template>
      <div
        v-if="tooltip.visible && tooltip.data && tooltip.data.title"
        class="gantt-tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div class="gantt-tooltip-title">{{ tooltip.data.title }}</div>
        <div>类型：{{ formatTypeLabel(tooltip.data.type) }}</div>
        <div>优先级：{{ tooltip.data.priority }}</div>
        <div>状态：{{ formatStatus(tooltip.data.status) }}</div>
        <div>开始日期：{{ formatIsoToDisplay(tooltip.data.start) }}</div>
        <div>截止日期：{{ formatIsoToDisplay(tooltip.data.end) }}</div>
        <div v-if="tooltip.data.status === 'completed'">
          完成时间：{{
            tooltip.data.finish ? formatIsoToDisplay(tooltip.data.finish) : "-"
          }}
        </div>
        <div>当前时间：{{ tooltip.now }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from "vue";
import { TaskRow, TASK_TYPE_CONFIGS } from "@/types/project";
import dayjs from "dayjs";

interface Props {
  taskList: TaskRow[];
  expandedRowKeys: number[];
}

const props = defineProps<Props>();

const chartPaneRef = ref<HTMLElement | null>(null);
const barAnimated = ref<{ [id: number]: boolean }>({});

// 提示框
const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  data: {} as TaskRow,
  now: "",
});

// 获取所有任务（递归遍历所有层级）
function getAllTasksRecursive(nodes: TaskRow[]): TaskRow[] {
  let allTasks: TaskRow[] = [];
  nodes.forEach((node) => {
    allTasks.push(node);
    if (node.children && node.children.length > 0) {
      allTasks = allTasks.concat(
        getAllTasksRecursive(node.children as TaskRow[])
      );
    }
  });
  return allTasks;
}

// 获取所有可见任务（包括子项）
function getVisibleTaskListWithIndent(
  nodes: TaskRow[],
  expandedKeys: number[],
  indent = 0
): any[] {
  let result: any[] = [];
  for (const node of nodes) {
    if (node.id == null) continue;
    result.push({ ...node, indent });
    // 如果有子项且已展开，则递归添加子项
    if (
      node.children &&
      node.children.length > 0 &&
      expandedKeys.includes(node.id)
    ) {
      result = result.concat(
        getVisibleTaskListWithIndent(
          node.children as TaskRow[],
          expandedKeys,
          indent + 1
        )
      );
    }
  }
  return result;
}

// 计算属性：所有可见任务（包括展开的子项）
const visibleTaskListWithIndent = computed(() => {
  return getVisibleTaskListWithIndent(props.taskList, props.expandedRowKeys);
});

// 计算甘特图周数
const ganttWeeks = computed(() => {
  // 获取所有任务（包括子项）
  const allTasks = getAllTasksRecursive(props.taskList);
  const validTasks = allTasks.filter(
    (t) =>
      t.start && t.end && dayjs(t.start).isValid() && dayjs(t.end).isValid()
  );

  // 如果没有有效任务，使用当前日期生成默认周数
  if (validTasks.length === 0) {
    const today = dayjs();
    const weeks = [];
    for (let i = 0; i < 8; i++) {
      const weekStart = today.add(i, "week").startOf("week");
      weeks.push(`第${i + 1}周 (${weekStart.format("YYYY-MM-DD")})`);
    }
    return weeks;
  }

  // 计算最小开始时间和最大结束时间
  const minStart = dayjs(
    Math.min(...validTasks.map((t) => dayjs(t.start).valueOf()))
  ).startOf("week");
  const maxEnd = dayjs(
    Math.max(...validTasks.map((t) => dayjs(t.end).valueOf()))
  ).endOf("week");
  const totalWeeks = maxEnd.diff(minStart, "week") + 1;

  // 生成周标签
  const weeks = [];
  for (let i = 0; i < totalWeeks; i++) {
    const weekStart = minStart.add(i, "week");
    weeks.push(`第${i + 1}周 (${weekStart.format("YYYY-MM-DD")})`);
  }
  return weeks;
});

// 获取甘特图条样式
function getGanttBarStyle(row: TaskRow) {
  // 处理没有时间的任务，给一个默认位置和宽度
  if (!row.start || !row.end) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  const start = dayjs(row.start);
  const end = dayjs(row.end);

  // 处理无效时间格式的任务
  if (!start.isValid() || !end.isValid()) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  // 获取所有任务用于计算时间范围
  const allTasks = getAllTasksRecursive(props.taskList);
  const validTasks = allTasks.filter(
    (t) =>
      t.start && t.end && dayjs(t.start).isValid() && dayjs(t.end).isValid()
  );

  // 如果没有有效任务，使用默认样式
  if (validTasks.length === 0) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  // 计算时间范围和位置
  const minStart = dayjs(
    Math.min(...validTasks.map((t) => dayjs(t.start).valueOf()))
  ).startOf("week");
  const maxEnd = dayjs(
    Math.max(...validTasks.map((t) => dayjs(t.end).valueOf()))
  ).endOf("week");
  const totalWeeks = maxEnd.diff(minStart, "week") + 1;
  if (totalWeeks <= 0) return { display: "block", width: "10%", left: "5%" };

  // 计算开始和结束周数
  const startWeek = start.diff(minStart, "week");
  const endWeek = end.diff(minStart, "week");
  const width = ((endWeek - startWeek + 1) / totalWeeks) * 100;
  const left = (startWeek / totalWeeks) * 100;

  // 确保甘特条有最小宽度
  return {
    left: `${left}%`,
    width: width < 5 ? "5%" : `${width}%`, // 最小宽度为5%
    display: "block",
    minWidth: "40px",
  };
}

// 获取甘特图条样式类
function getGanttBarClass(row: TaskRow) {
  // 对没有时间的任务特殊标记
  if (
    !row.start ||
    !row.end ||
    !dayjs(row.start).isValid() ||
    !dayjs(row.end).isValid()
  ) {
    return "gantt-bar-no-date";
  }

  // 使用接口返回的status确定甘特条样式
  if (row.status === "completed") return "gantt-bar-done";
  if (row.status === "in_progress") return "gantt-bar-doing";
  if (row.status === "not_started") return "gantt-bar-todo";
  if (row.status === "overdue") return "gantt-bar-overdue";
  return "";
}

// 格式化日期显示
const formatIsoToDisplay = (isoString: string) => {
  if (!isoString) return "";
  return dayjs(isoString).format("YYYY-MM-DD");
};

// 格式化类型标签
function formatTypeLabel(type: string): string {
  const config = TASK_TYPE_CONFIGS[type as keyof typeof TASK_TYPE_CONFIGS];
  return config?.label || type;
}

// 格式化状态
function formatStatus(status: string): string {
  const statusMap = {
    not_started: "未开始",
    in_progress: "进行中",
    completed: "已完成",
    overdue: "已逾期",
  };
  return statusMap[status as keyof typeof statusMap] || status;
}

// 触发甘特图条动画
function triggerGanttBarAnimation() {
  // 重置所有任务的动画状态
  barAnimated.value = {};
  // 为当前可见任务设置动画状态
  setTimeout(() => {
    visibleTaskListWithIndent.value.forEach((row: any) => {
      if (row.id != null) {
        barAnimated.value[row.id] = true;
      }
    });
  }, 300);
}

// 鼠标移动处理
function handlePaneMouseMove(e: MouseEvent) {
  if (!tooltip.value.data || !tooltip.value.data.title) {
    tooltip.value.visible = false;
  }
}

// 鼠标离开处理
function handlePaneMouseLeave() {
  tooltip.value.visible = false;
}

// 显示提示框
function showTooltip(e: MouseEvent, row: TaskRow) {
  tooltip.value.visible = true;
  tooltip.value.x = e.clientX + 16;
  tooltip.value.y = e.clientY + 8 + window.scrollY;
  tooltip.value.data = { ...row }; // 创建副本避免引用问题
  tooltip.value.now = dayjs().format("YYYY-MM-DD HH:mm:ss");
}

// 隐藏提示框
function hideTooltip() {
  tooltip.value.visible = false;
  tooltip.value.data = {} as TaskRow;
}

// 监听任务列表变化，触发动画
watch(
  () => props.taskList,
  () => {
    nextTick(() => {
      triggerGanttBarAnimation();
    });
  },
  { deep: true }
);

// 监听展开状态变化，触发动画
watch(
  () => props.expandedRowKeys,
  () => {
    nextTick(() => {
      triggerGanttBarAnimation();
    });
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  triggerGanttBarAnimation,
});
</script>

<style scoped>
.gantt-chart-pane {
  position: relative;
  flex: 0 0 40%;
  min-width: 500px;
  max-width: 1000px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 18px 32px 18px 0;
  margin-left: 0;
  margin-top: 40px;
  overflow-x: auto;
  overflow: visible;
}

.gantt-chart-header {
  height: 24px;
  position: relative;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.gantt-date {
  flex: 1;
  text-align: center;
  color: #888;
  font-size: 13px;
  padding: 2px 0;
}

.gantt-chart-body {
  display: flex;
  flex-direction: column;
}

.gantt-row {
  position: relative;
  height: 42px;
  display: flex;
  align-items: center;
}

.gantt-bar {
  position: absolute;
  height: 20px;
  border-radius: 10px;
  top: 6px;
  transition:
    width 0.7s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.7s,
    left 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  display: block !important;
  min-width: 20px;
  cursor: pointer;
}

.gantt-bar.gantt-bar-animate {
  opacity: 1;
}

.gantt-bar.gantt-bar-hidden {
  width: 0 !important;
  opacity: 0;
}

.gantt-bar-todo {
  background: #409eff;
}

.gantt-bar-doing {
  background: #ffb800;
}

.gantt-bar-done {
  background: #67c23a;
}

.gantt-bar-overdue {
  background: #f56c6c;
}

.gantt-bar-no-date {
  background: #ccc;
  opacity: 0.7;
}

.gantt-tooltip {
  position: fixed;
  z-index: 9999;
  min-width: 180px;
  background: #222;
  color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.18);
  padding: 12px 18px 10px 18px;
  font-size: 14px;
  pointer-events: none;
  transition: opacity 0.15s;
  opacity: 0.97;
  line-height: 1.7;
}

.gantt-tooltip-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
}
</style>
