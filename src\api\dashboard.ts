import request from "@/utils/request";

const DASHBOARD_BASE_URL = "/api/v1/dashboard";

class DashboardAPI {
  /**
   * 获取资产仪表盘数据
   *
   * @param timeRange 时间范围(可选): month/quarter/year
   * @returns 资产仪表盘数据
   */
  static getAssetDashboard(timeRange?: string) {
    return request<any, AssetDashboardVO>({
      url: `${DASHBOARD_BASE_URL}/assets`,
      method: "get",
      params: { timeRange },
    });
  }

  /**
   * 获取部门及人员仪表盘数据
   *
   * @param timeRange 时间范围(可选): month/quarter/year
   * @returns 部门及人员仪表盘数据
   */
  static getDeptAndPeopleDashboard(timeRange?: string) {
    return request<any, DeptPeopleDashboardVO>({
      url: `${DASHBOARD_BASE_URL}/deptPeople`,
      method: "get",
      params: { timeRange },
    });
  }

  /**
   * 获取安全漏洞仪表盘数据
   *
   * @param timeRange 时间范围(可选): month/quarter/year
   * @returns 安全漏洞仪表盘数据
   */
  static getVulnerabilityDashboard(timeRange?: string) {
    return request<any, VulnerabilityDashboardVO>({
      url: `${DASHBOARD_BASE_URL}/vulnerability`,
      method: "get",
      params: { timeRange },
    });
  }

  /**
   * 获取安全评估与问题仪表盘数据
   *
   * @param timeRange 时间范围(可选): month/quarter/year
   * @returns 安全评估与问题仪表盘数据
   */
  static getAssessmentDashboard(timeRange?: string) {
    return request<any, AssessmentDashboardVO>({
      url: `${DASHBOARD_BASE_URL}/assessment`,
      method: "get",
      params: { timeRange },
    });
  }
  /**
   * 获取主仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.timeRange - 时间范围: week/month/year
   * @param {string|null} params.startDate - 开始日期
   * @param {string|null} params.endDate - 结束日期
   * @returns {Promise}
   */
  static getMainDashboard(params: {
    timeRange: string;
    startDate?: string | null;
    endDate?: string | null;
  }) {
    return request<any, DashboardData>({
      url: `${DASHBOARD_BASE_URL}/main`,
      method: "get",
      params,
    });
  }
  
  
}

export default DashboardAPI;

/** 资产仪表盘数据类型 */
export interface AssetDashboardVO {
  /** 各类型资产总数 */
  assetCounts: Record<string, number>;
  /** 时间节点(月份/季度/年份) */
  trendMonths: string[];
  /** 资产增长趋势 */
  assetTrends: Record<string, Record<string, number>>;
  /** 资产状态分布 */
  assetStatuses: { status: string; count: number }[];
}

/** 部门及人员仪表盘数据类型 */
export interface DeptPeopleDashboardVO {
  /** 部门统计 */
  deptCounts: {
    /** 部门总数 */
    deptTotal: number;
    /** 用户总数 */
    userTotal: number;
    /** 本月新增人员 */
    newUserThisMonth: number;
  };
  /** 用户增长趋势 */
  userTrends: {
    /** 时间节点 */
    months: string[];
    /** 总用户数趋势 */
    total: Record<string, number>;
    /** 新增用户趋势 */
    new: Record<string, number>;
  };
  /** 部门分布 */
  deptDistribution: { name: string; userCount: number }[];
}

/** 安全漏洞仪表盘数据类型 */
export interface VulnerabilityDashboardVO {
  /** 漏洞统计数据 */
  vulnCounts: {
    /** 工单总数 */
    totalTickets: number;
    /** 未完成工单数 */
    pendingTickets: number;
    /** 已完成工单数 */
    completedTickets: number;
  };
  /** 漏洞工单趋势 */
  ticketTrends: {
    /** 时间节点 */
    months: string[];
    /** 新建工单趋势 */
    new: Record<string, number>;
    /** 已完成工单趋势 */
    completed: Record<string, number>;
  };
  /** 漏洞等级分布 */
  severityData: Record<string, number>;
  /** 漏洞类型分布 */
  typeData: Record<string, number>;
  /** 漏洞处理效率 */
  efficiencyData: {
    /** 时间节点 */
    months: string[];
    /** 发现漏洞数 */
    discovered: number[];
    /** 修复漏洞数 */
    fixed: number[];
    /** 平均修复时间 */
    avgTime: number[];
  };
}

/** 安全评估与问题仪表盘数据类型 */
export interface AssessmentDashboardVO {
  /** 工单统计 */
  ticketCounts: {
    /** 应急处置工单数 */
    emergency: number;
    /** 业务上线工单数 */
    business: number;
    /** 安全评估工单数 */
    security: number;
    /** 工单总数 */
    total: number;
  };
  /** 工单趋势 */
  ticketTrends: {
    /** 时间节点 */
    months: string[];
    /** 新建工单 */
    new: Record<string, number>;
    /** 已完成工单 */
    completed: Record<string, number>;
  };
  /** 工单状态数据 */
  statusData: {
    /** 未完成工单数 */
    pending: number;
    /** 已完成工单数 */
    completed: number;
  };
  /** 工单步骤数据 */
  stepData: Record<string, {
    emergency: number;
    business: number;
    security: number;
  }>;
}

// 定义仪表盘接口数据类型
export interface DashboardData {
  // 总览统计数据
  totalTickets: number;          // 总工单数
  pendingTickets: number;        // 待处理工单数
  completedTickets: number;      // 已完成工单数
  expectedPendingTickets: number; // 预期未处理工单数
  
  // 工作流程状态数据
  workflowStats: {
    assessment: { pending: number, reviewing: number, completed: number },
    vulnerability: { pending: number, reviewing: number, completed: number },
    asset: { pending: number, reviewing: number, completed: number }
  };
  
  // 趋势图数据
  trendData: {
    categories: string[];           // 时间类别 (如月份名称)
    assessment: number[];           // 安全评估工单数据
    vulnerability: number[];        // 安全漏洞工单数据
    asset: number[];                // 资产下线工单数据
  };
  
  // 最近活动数据
  recentActivities: Array<{
    content: string;                // 活动内容
    time: string;                   // 活动时间
    status: 'success' | 'warning' | 'danger' | 'primary' | 'info';  // 活动状态
    type: 'success' | 'warning' | 'danger' | 'primary' | 'info';    // 图标类型
  }>;
}
