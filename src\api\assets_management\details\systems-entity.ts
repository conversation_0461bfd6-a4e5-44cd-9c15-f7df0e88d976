import request from "@/utils/request";

const SYSTEMSENTITY_BASE_URL = "/api/v1/systemsEntitys";

class systemsEntityAPI {
    /** 获取系统管理分页数据 */
    static getPage(queryParams?: systemsEntityPageQuery) {
        return request<any, PageResult<systemsEntityPageVO[]>>({
            url: `${SYSTEMSENTITY_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 获取系统管理表单数据
     *
     * @param id systemsEntityID
     * @returns systemsEntity表单数据
     */
    static getFormData(id: number) {
        return request<any, systemsEntityForm>({
            url: `${SYSTEMSENTITY_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加系统管理*/
    static add(data: systemsEntityForm) {
        return request({
            url: `${SYSTEMSENTITY_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新系统管理
     *
     * @param id systemsEntityID
     * @param data systemsEntity表单数据
     */
    static update(id: number, data: systemsEntityForm) {
        return request({
            url: `${SYSTEMSENTITY_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除系统管理，多个以英文逗号(,)分割
     *
     * @param ids 系统管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${SYSTEMSENTITY_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 获取系统管理下拉列表
     */
    static getOptions() {
        return request<any, OptionType[]>({
            url: `${SYSTEMSENTITY_BASE_URL}/options`,
            method: "get",
        });
    }

  /**
   * 下载系统模板
   * @returns {Promise} - 请求的Promise对象
   */
  static downloadTemplate() {
    return request({
      url: `${SYSTEMSENTITY_BASE_URL}/template`,
      method: "get",
      responseType: "arraybuffer",
    });
  }

  /**
   * 导入系统
   * @param {File} file - 导入文件
   * @returns {Promise} - 请求的Promise对象
   */
  static importsystems(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, ExcelResult>({
      url: `${SYSTEMSENTITY_BASE_URL}/import`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /**
   * 导出系统
   * @param {systemsEntityPageQuery} queryParams - 查询参数
   * @returns {Promise} - 请求的Promise对象
   */
  static export(queryParams: systemsEntityPageQuery) {
    return request({
      url: `${SYSTEMSENTITY_BASE_URL}/export`,
      method: "get",
      params: queryParams,
      responseType: "arraybuffer",
    });
  }
}

export default systemsEntityAPI;

/** 系统管理分页查询参数 */
export interface systemsEntityPageQuery extends PageQuery {
    type?: number;
    /** 系统ID */
    id?: number;
    /** 系统名称 */
    sysname?: string;
    /** IP地址 */
    ip?: string;
    /** 域名 */
    domain?: string;
    /** 管理单位 */
    manageUnit?: string;
    /** 管理人Id */
    managerId?: number;
    /** 部门ID */
    deptId?: number;
    /** 联系方式 */
    contact?: string;
    /** 第三方运维单位 */
    otherManageUnit?: string;
    /** 运维人员 */
    otherManager?: string;
    /** 第三方运维联系方式 */
    otherContact?: string;
    /** 系统定级 */
    sysRank?: string;
    /** 开放状态 */
    status?: number;
    /** 资产数量 */
    assetsNum?: number;
    /** 创建时间 */
    createTime?: [string, string];
    /** 备注 */
    notes?: string;
    /** 更新时间 */
    updateTime?: [string, string];
    /** 系统ID*/
    systemId?: number;
}

/** 系统信息 */
export interface SystemInfo {
  id: number;
  sysname: string;
  IP: string;
  domain: string;
  manageUnit: string;
  manager: string;
  deptId: string;
  contact: string;
  otherManageUnit: string;
  otherManager: string;
  otherContact: string;
  rank: string;
  status: number;
  assetsNum: number;
  createTime: string;
  notes?: string;
}

/** 系统管理表单对象 */
export interface systemsEntityForm {
    assetsList: any;
    assetsList: any[];
    assetsList: any;
    assetsList: systemsEntityForm;
    id?:  number;
    /** 系统名称 */
    sysname?:  string;
    /** IP地址 */
    ip?:  string;
    /** 域名 */
    domain?:  string;
    /** 管理单位 */
    manageUnit?:  string;
    /** 管理人 */
    manager?:  string;

    managerId?:  number;
    /** 部门ID */
    deptId?:  string;
    /** 联系方式 */
    contact?:  string;
    /** 第三方运维单位 */
    otherManageUnit?:  string;
    /** 运维人员 */
    otherManager?:  string;
    /** 第三方运维联系方式 */
    otherContact?:  string;
    /** 系统定级 */
    sysRank?:  string;
    /** 开放状态 */
    status?:  number;
    /** 资产数量 */
    assetsNum?:  number;
    /** 创建时间 */
    createTime?:  Date;
    /** 备注 */
    notes?:  string;
    /** 更新时间 */
    updateTime?:  Date;
    /** 版本 */
    version?:  string;
    /** 公网IP */
    publicIp?:  string;
    /** 内网IP */
    privateIp?:  string;
    /** 开放框架 */
    frame?:  string;
}

/** 系统管理分页对象 */
export interface systemsEntityPageVO {
    /** 系统ID */
    id?: number;
    /** 系统名称 */
    sysname?: string;
    /** IP地址 */
    ip?: string;
    /** 域名 */
    domain?: string;
    /** 管理单位 */
    manageUnit?: string;
    /** 管理人 */
    manager?: string;
    /** 部门ID */
    deptId?: string;
    /** 联系方式 */
    contact?: string;
    /** 第三方运维单位 */
    otherManageUnit?: string;
    /** 运维人员 */
    otherManager?: string;
    /** 第三方运维联系方式 */
    otherContact?: string;
    /** 系统定级 */
    sysRank?: string;
    /** 开放状态 */
    status?: number;
    /** 资产数量 */
    assetsNum?: number;
    /** 创建时间 */
    createTime?: Date;
    /** 备注 */
    notes?: string;
    /** 更新时间 */
    updateTime?: Date;
}
