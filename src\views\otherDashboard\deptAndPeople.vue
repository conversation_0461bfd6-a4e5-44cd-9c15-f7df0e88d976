<template>
  <div class="dept-dashboard">
    <div class="dashboard-header">
      <h1 class="dashboard-title">部门及人员仪表盘</h1>
      <el-button type="primary" size="small" @click="loadDashboardData" :loading="refreshLoading">
        <el-icon><i-ep-refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 部门及人员总览卡片 -->
    <el-row :gutter="20" class="mb-4">
  <el-col :span="8">
    <el-card shadow="hover" class="data-card dept-card" :body-style="{padding: '0px'}">
      <div class="card-content dept-content">
        <div class="card-icon dept-icon" style="background-color: #1890FF;">
          <el-icon :size="40">
            <i-ep-office-building />
          </el-icon>
        </div>
        <div class="card-info dept-info">
          <div class="card-type dept-type">部门总数</div>
          <div class="card-count dept-count">{{ deptCounts.deptTotal }}</div>
          <!-- <div class="card-trend">
            <span class="trend-text trend-up">
              +2% <el-icon><i-ep-top /></el-icon>
            </span>
            <span class="trend-period">同比上季度</span>
          </div> -->
        </div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="8">
    <el-card shadow="hover" class="data-card dept-card" :body-style="{padding: '0px'}">
      <div class="card-content dept-content">
        <div class="card-icon dept-icon" style="background-color: #52C41A;">
          <el-icon :size="40">
            <i-ep-user />
          </el-icon>
        </div>
        <div class="card-info dept-info">
          <div class="card-type dept-type">人员总数</div>
          <div class="card-count dept-count">{{ deptCounts.userTotal }}</div>
          <!-- <div class="card-trend">
            <span class="trend-text trend-up">
              +5% <el-icon><i-ep-top /></el-icon>
            </span>
            <span class="trend-period">同比上季度</span>
          </div> -->
        </div>
      </div>
    </el-card>
  </el-col>
  <el-col :span="8">
    <el-card shadow="hover" class="data-card dept-card" :body-style="{padding: '0px'}">
      <div class="card-content dept-content">
        <div class="card-icon dept-icon" style="background-color: #F5222D;">
          <el-icon :size="40">
            <i-ep-user-filled />
          </el-icon>
        </div>
        <div class="card-info dept-info">
          <div class="card-type dept-type">本月新增人员</div>
          <div class="card-count dept-count">{{ deptCounts.newUserThisMonth }}</div>
          <!-- <div class="card-trend">
            <span class="trend-text trend-up">
              +33% <el-icon><i-ep-top /></el-icon>
            </span>
            <span class="trend-period">同比上月</span>
          </div> -->
        </div>
      </div>
    </el-card>
  </el-col>
</el-row>

    <!-- 用户增长趋势图表 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="chart-title">用户增长趋势</span>
              <el-radio-group v-model="trendTimeRange" size="small">
                <!-- <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="quarter">季度</el-radio-button> -->
                <el-radio-button label="year">年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" ref="trendChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 角色分布和部门人员分布饼图
    <el-row :gutter="20" class="mt-4">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="chart-title">角色类型分布</span>
            </div>
          </template>
          <div class="chart-container" ref="roleDistributionChartRef"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span class="chart-title">部门人员分布</span>
            </div>
          </template>
          <div class="chart-container" ref="deptDistributionChartRef"></div>
        </el-card>
      </el-col>
    </el-row> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import DashboardAPI from "@/api/dashboard";

// 添加主题检测
const isDark = ref(document.documentElement.classList.contains('dark'));

// 创建 MutationObserver 监听主题变化
let themeObserver: MutationObserver;

// 根据当前主题获取卡片颜色
const getCardColors = () => {
  return isDark.value 
    ? ['#177ddc', '#49aa19', '#d89614', '#a61d24'] // 暗色主题颜色
    : ['#1890FF', '#52C41A', '#FAAD14', '#F5222D']; // 亮色主题颜色
};

const refreshLoading = ref(false);

// 部门与人员统计数据
const deptCounts = reactive({
  deptTotal: 0,
  userTotal: 0,
  newUserThisMonth: 0
});

// 保存最后一次加载的数据，方便主题切换时重新渲染
const lastData = reactive({
  trendMonths: [] as string[],
  userTrends: {} as any
});

const trendTimeRange = ref('year');
const trendChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;

// 初始化用户增长趋势图表
const initTrendChart = (trendMonths: string[], userTrends: any) => {
  if (trendChartRef.value) {
    // 销毁旧图表
    if (trendChart) {
      trendChart.dispose();
    }
    
    // 使用当前主题初始化图表
    trendChart = echarts.init(trendChartRef.value, isDark.value ? 'dark' : undefined);
    
    // 获取主题颜色
    const textColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-text-color-primary').trim() || '#303133';
    const borderColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-border-color').trim() || '#E0E6F1';
    
    // 获取颜色变量
    const primaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-primary').trim() || '#1890FF';
    const successColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--el-color-success').trim() || '#52C41A';
    
    const option = {
      backgroundColor: 'transparent', // 透明背景，适配主题
      title: {
        text: '用户增长趋势',
        left: 'center',
        top: 0,
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
          color: textColor
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        backgroundColor: isDark.value ? '#1f2d3d' : '#fff',
        borderColor: isDark.value ? '#2d3b4d' : '#dcdfe6',
        textStyle: {
          color: textColor
        }
      },
      legend: {
        data: ['总用户数', '新增用户数'],
        top: '30px',
        textStyle: {
          color: textColor
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '80px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: trendMonths,
        axisLine: {
          lineStyle: {
            color: borderColor
          }
        },
        axisLabel: {
          color: textColor,
          formatter: (value: string) => {
            return value.length > 7 ? value.substring(0, 7) + '...' : value;
          }
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: textColor
        },
        splitLine: {
          lineStyle: {
            color: isDark.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '总用户数',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          itemStyle: {
            color: primaryColor
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: isDark.value ? `${primaryColor}50` : `${primaryColor}30`
              },
              {
                offset: 1,
                color: isDark.value ? `${primaryColor}10` : `${primaryColor}10`
              }
            ])
          },
          data: trendMonths.map(month => userTrends.total[month] || 0)
        },
        {
          name: '新增用户数',
          type: 'bar',
          barWidth: 20,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: successColor
              },
              {
                offset: 1,
                color: isDark.value ? `${successColor}80` : '#95DE64'
              }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          data: trendMonths.map(month => userTrends.new[month] || 0)
        }
      ]
    };
    trendChart.setOption(option);
  }
};

// 刷新图表
const refreshCharts = () => {
  if (lastData.trendMonths.length > 0) {
    initTrendChart(lastData.trendMonths, lastData.userTrends);
  }
};

// 加载仪表盘数据
function loadDashboardData() {
  refreshLoading.value = true;
  
  // 从API获取数据
  DashboardAPI.getDeptAndPeopleDashboard(trendTimeRange.value)
    .then((response) => {
      // 更新部门统计数据
      if (response.deptCounts) {
        Object.assign(deptCounts, response.deptCounts);
      }
      
      // 保存数据，用于主题切换时重新渲染
      if (response.userTrends) {
        lastData.trendMonths = response.userTrends.months;
        lastData.userTrends = response.userTrends;
        
        // 初始化用户增长趋势图表
        initTrendChart(response.userTrends.months, response.userTrends);
      }
    })
    .catch(error => {
      console.error('获取部门及人员仪表盘数据失败:', error);
      // 失败时使用模拟数据
      loadMockData();
    })
    .finally(() => {
      refreshLoading.value = false;
    });
}

function loadMockData() {
  setTimeout(() => {
    const mockDashboardData = {
      deptCounts: {
        deptTotal: 12,
        userTotal: 156,
        newUserThisMonth: 8
      },
      userTrends: {
        months: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08', '2025-09', '2025-10', '2025-11', '2025-12'],
        total: {
          '2025-01': 120,
          '2025-02': 128,
          '2025-03': 135,
          '2025-04': 142,
          '2025-05': 148,
          '2025-06': 156,
          '2025-07': 162,
          '2025-08': 168,
          '2025-09': 175,
          '2025-10': 180,
          '2025-11': 188,
          '2025-12': 196
        },
        new: {
          '2025-01': 5,
          '2025-02': 8,
          '2025-03': 7,
          '2025-04': 7,
          '2025-05': 6,
          '2025-06': 8,
          '2025-07': 6,
          '2025-08': 6,
          '2025-09': 7,
          '2025-10': 5,
          '2025-11': 8,
          '2025-12': 8
        }
      }
    };
    
    const { deptCounts: deptCountsData, userTrends } = mockDashboardData;
    Object.assign(deptCounts, deptCountsData);
    
    // 保存模拟数据
    lastData.trendMonths = userTrends.months;
    lastData.userTrends = userTrends;
    
    initTrendChart(userTrends.months, userTrends);
    refreshLoading.value = false;
  }, 800);
}

// 创建一个单独的函数用于处理窗口大小变化
const handleResize = () => {
  trendChart?.resize();
};

// 监听时间范围变化
watch(trendTimeRange, () => {
  loadDashboardData();
});

// 监听主题变化
watch(isDark, () => {
  refreshCharts();
});

onMounted(() => {
  // 设置主题观察器
  themeObserver = new MutationObserver(() => {
    const newIsDark = document.documentElement.classList.contains('dark');
    if (isDark.value !== newIsDark) {
      isDark.value = newIsDark;
    }
  });
  
  // 开始观察
  themeObserver.observe(document.documentElement, { 
    attributes: true, 
    attributeFilter: ['class'] 
  });
  
  // 加载数据
  loadDashboardData();
  
  // 监听窗口大小变化，重置图表大小
  window.addEventListener('resize', handleResize);
});

// 组件销毁时清理资源
onUnmounted(() => {
  // 停止主题观察
  themeObserver?.disconnect();
  
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
  
  // 销毁图表实例，避免内存泄漏
  trendChart?.dispose();
});
</script>

<style scoped>
.dept-dashboard {
  padding: 24px;
  background-color: var(--el-bg-color);  /* 使用 Element Plus 变量 */
  min-height: calc(100vh - 60px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量 */
  margin: 0;
}

.data-card {
  transition: all 0.3s;
  height: 140px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  background-color: var(--el-bg-color);  /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color-light);  /* 使用 Element Plus 变量 */
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px var(--el-box-shadow-lighter);  /* 使用 Element Plus 变量 */
}

.card-content {
  display: flex;
  padding: 20px;
  height: 100%;
}

.card-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 16px;
}

.card-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-type {
  font-size: 16px;
  color: var(--el-text-color-secondary);  /* 使用 Element Plus 变量 */
}

.card-count {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量 */
  margin: 5px 0;
}

.card-trend {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.trend-text {
  display: flex;
  align-items: center;
  font-weight: 500;
  margin-right: 8px;
}

.trend-up {
  color: var(--el-color-success);  /* 使用 Element Plus 变量 */
}

.trend-down {
  color: var(--el-color-danger);  /* 使用 Element Plus 变量 */
}

.trend-period {
  color: var(--el-text-color-secondary);  /* 使用 Element Plus 变量 */
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: var(--el-bg-color);  /* 使用 Element Plus 变量 */
  border-color: var(--el-border-color-light);  /* 使用 Element Plus 变量 */
}

.chart-card:hover {
  box-shadow: 0 6px 16px var(--el-box-shadow-lighter);  /* 使用 Element Plus 变量 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);  /* 使用 Element Plus 变量 */
}

.chart-container {
  height: 380px;
  padding: 10px;
  transition: background-color 0.3s;
}

/* 图标颜色 */
.card-icon :deep(svg) {
  color: #ffffff;  /* 图标颜色保持白色，在彩色背景上 */
}

.mt-4 {
  margin-top: 24px;
}

.mb-4 {
  margin-bottom: 24px;
}

:deep(.el-card) {
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);  /* 使用 Element Plus 变量 */
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 适配暗色主题下的卡片图标背景色 */
:deep(.el-col:nth-child(1) .card-icon) {
  background-color: var(--el-color-primary);
}

:deep(.el-col:nth-child(2) .card-icon) {
  background-color: var(--el-color-success);
}

:deep(.el-col:nth-child(3) .card-icon) {
  background-color: var(--el-color-danger);
}

:deep(.el-radio-button__inner) {
  color: var(--el-text-color-regular);
  border-color: var(--el-border-color);
  background-color: var(--el-fill-color-blank);
}

:deep(.el-radio-button__original) {
  opacity: 0;
}

:deep(.el-radio-button__inner:hover) {
  color: var(--el-color-primary);
}

:deep(.el-radio-button.is-active .el-radio-button__inner) {
  color: #ffffff;
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}
</style>
