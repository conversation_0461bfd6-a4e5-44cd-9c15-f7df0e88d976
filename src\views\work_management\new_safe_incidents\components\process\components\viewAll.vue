<template>
    <el-dialog
      v-model="dialogVisible"
      title="流程人员配置详情"
      width="900px"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="process-view-all">
        <!-- 顶部搜索过滤区 -->
        <div class="filter-area">
          <el-input
            v-model="searchText"
            placeholder="搜索部门、人员或联系方式"
            prefix-icon="Search"
            clearable
            class="search-input"
          />
          <el-select
            v-model="typeFilter"
            placeholder="类型"
            clearable
            class="type-filter"
          >
            <el-option label="全部" value="" />
            <el-option label="执行" value="执行" />
            <el-option label="通知" value="通知" />
          </el-select>
          <el-select
            v-model="stepFilter"
            placeholder="流程步骤"
            clearable
            class="step-filter"
          >
            <el-option label="全部" value="" />
            <el-option v-for="step in stepOptions" :key="step" :label="step" :value="step" />
          </el-select>
        </div>
  
        <!-- 表格展示区 -->
        <el-table :data="displayData" border stripe>
          <el-table-column prop="stepName" label="流程步骤" width="140" />
          <el-table-column prop="departmentName" label="所属部门" width="140" />
          <el-table-column prop="type" label="类型" width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.type === '执行' ? 'primary' : 'success'">
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="负责人员" min-width="200">
            <template #default="{ row }">
              <div v-if="!row.personDetails || row.personDetails.length === 0">
                <el-empty description="暂无人员" :image-size="30" />
              </div>
              <div v-else class="person-container">
                <div v-for="(person, index) in row.personDetails" :key="person.id" class="person-item">
                  <div class="person-info">
                    <span class="person-name">{{ person.nickname || person.username }}</span>
                    <span class="person-dept">({{ person.deptName }})</span>
                    <span class="person-mobile">{{ person.mobile || '暂无联系方式' }}</span>
                  </div>
                  <el-divider v-if="index < row.personDetails.length - 1" />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="通知设置" width="120" align="center">
            <template #default="{ row }">
              <div v-if="row.enableSms" class="notify-column">
                <el-tag type="success" effect="light">已启用短信</el-tag>
                <el-popover
                  placement="top-start"
                  title="通知设置详情"
                  :width="200"
                  trigger="hover"
                >
                  <template #default>
                    <div class="notify-details">
                      <div class="detail-item">
                        <span class="label">通知频率:</span>
                        <span class="value">{{ getNotifyTypeLabel(row.notifyType) }}</span>
                      </div>
                      <div v-if="row.notifyPeriod" class="detail-item">
                        <span class="label">通知周期:</span>
                        <span class="value">{{ getNotifyPeriodLabel(row.notifyPeriod) }}</span>
                      </div>
                    </div>
                  </template>
                  <template #reference>
                    <el-button type="info" size="small" plain class="details-btn">查看详情</el-button>
                  </template>
                </el-popover>
              </div>
              <el-tag v-else type="info" effect="light">未启用短信</el-tag>
            </template>
          </el-table-column>
        </el-table>
  
        <!-- 分页区域 -->
        <div class="pagination-area">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="filteredData.length"
            background
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  
  interface ProcessPerson {
    id: number | string
    nickname: string
    username: string
    deptName: string
    mobile: string
  }
  
  interface ProcessItem {
    stepName: string // 流程步骤名称
    departmentName: string
    originalDepartmentName: string
    type: '执行' | '通知'
    personNames: string
    personDetails?: ProcessPerson[]
    enableSms?: boolean
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    processData: {
      type: Array as () => ProcessItem[],
      default: () => []
    }
  })
  
  const emit = defineEmits(['update:visible'])
  
  const dialogVisible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
  })
  
  // 搜索和过滤
  const searchText = ref('')
  const typeFilter = ref('')
  const stepFilter = ref('')
  const currentPage = ref(1)
  const pageSize = ref(10)
  
  // 获取所有可用的流程步骤选项
  const stepOptions = computed(() => {
    const steps = new Set<string>()
    props.processData.forEach(item => {
      if (item.stepName) steps.add(item.stepName)
    })
    return Array.from(steps)
  })
  
  // 过滤后的数据
  const filteredData = computed(() => {
    let result = [...props.processData]
    
    // 搜索过滤
    if (searchText.value) {
      const searchLower = searchText.value.toLowerCase()
      result = result.filter(item => {
        return (item.departmentName && item.departmentName.toLowerCase().includes(searchLower)) || 
          (item.personNames && item.personNames.toLowerCase().includes(searchLower)) ||
          (item.stepName && item.stepName.toLowerCase().includes(searchLower)) ||
          (item.personDetails && item.personDetails.some(p => 
            (p.nickname && p.nickname.toLowerCase().includes(searchLower)) || 
            (p.username && p.username.toLowerCase().includes(searchLower)) ||
            (p.deptName && p.deptName.toLowerCase().includes(searchLower)) ||
            (p.mobile && p.mobile.toLowerCase().includes(searchLower))
          ))
      })
    }
    
    // 类型过滤
    if (typeFilter.value) {
      result = result.filter(item => item.type === typeFilter.value)
    }
    
    // 步骤过滤
    if (stepFilter.value) {
      result = result.filter(item => item.stepName === stepFilter.value)
    }
    
    return result
  })
  
  // 当前页显示的数据
  const displayData = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value
    return filteredData.value.slice(start, start + pageSize.value)
  })
  
  // 当搜索条件改变时，重置页码
  watch([searchText, typeFilter, stepFilter], () => {
    currentPage.value = 1
  })
  
  // 获取通知类型显示标签
  const getNotifyTypeLabel = (type?: string) => {
    if (!type) return '无'
    const labels: Record<string, string> = {
      once: '单次通知',
      periodic: '定期通知'
    }
    return labels[type] || type
  }
  
  // 获取通知周期显示标签
  const getNotifyPeriodLabel = (period?: string) => {
    if (!period) return '无'
    const labels: Record<string, string> = {
      daily: '每天',
      weekly: '每周',
      monthly: '每月',
      quarterly: '每季度'
    }
    return labels[period] || period
  }
  
  // 处理关闭
  const handleClose = () => {
    dialogVisible.value = false
  }
  </script>
  
  <style scoped>
  .process-view-all {
    padding: 0 10px;
  }
  
  .filter-area {
    display: flex;
    margin-bottom: 15px;
    gap: 15px;
  }
  
  .search-input {
    width: 250px;
  }
  
  .type-filter,
  .step-filter {
    width: 150px;
  }
  
  .person-container {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .person-item {
    padding: 5px 0;
  }
  
  .person-info {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
  }
  
  .person-name {
    font-weight: 500;
  }
  
  .person-dept {
    color: #606266;
    font-size: 13px;
  }
  
  .person-mobile {
    color: #409EFF;
    font-size: 13px;
  }
  
  .notify-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
  
  .details-btn {
    margin-top: 5px;
  }
  
  .pagination-area {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
  
  .notify-details {
    font-size: 14px;
  }
  
  .detail-item {
    margin-bottom: 8px;
    display: flex;
  }
  
  .detail-item .label {
    color: #606266;
    width: 80px;
  }
  
  .detail-item .value {
    color: #303133;
    flex: 1;
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
  
  .el-divider {
    margin: 8px 0;
  }
  </style>
