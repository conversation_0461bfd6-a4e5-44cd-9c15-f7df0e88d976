<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree v-model="queryParams.deptId" @node-click="handleQuery" />
      </el-col>
      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <!-- 顶部四个统计卡片 -->
        <el-row :gutter="20" class="mb-4">
          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">信息系统总数</div>
                  <div class="text-2xl font-bold mt-2 text-blue-600">120</div>
                </div>
                <el-icon style="font-size: 32px; color: #3b82f6;">
                  <InfoFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">在线系统</div>
                  <div class="text-2xl font-bold mt-2 text-green-500">85</div>
                </div>
                <el-icon style="font-size: 32px; color: #22c55e;">
                  <CircleCheckFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">部分在线</div>
                  <div class="text-2xl font-bold mt-2 text-yellow-500">20</div>
                </div>
                <el-icon style="font-size: 32px; color: #eab308;">
                  <WarningFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>

          <el-col :lg="6" :sm="12" :xs="24">
            <el-card shadow="hover" style="border-radius: 1rem;">
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-gray-500 text-sm">离线系统</div>
                  <div class="text-2xl font-bold mt-2 text-red-500">15</div>
                </div>
                <el-icon style="font-size: 32px; color: #ef4444;">
                  <CircleCloseFilled />
                </el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <div class="search-container p-4 rounded shadow-sm">
          <!-- 主搜索栏 -->
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="main-search-form" @submit.prevent>
            <!-- 主要搜索条件 -->
            <el-form-item style="flex-grow: 1;">
              <el-input v-model="queryParams.sysname" clearable class="input-fixed-width-main" placeholder="搜索信息系统名称">
                <template #prefix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>


            <!-- 搜索、重置按钮和高级筛选按钮 -->
            <el-form-item class="form-item buttons-group">
              <el-button type="primary" @click="handleQuery()">
                <i-ep-search /> 搜索
              </el-button>
              <el-button @click="handleResetQuery()" class="search-btn">
                <i-ep-refresh /> 重置
              </el-button>
              <el-button type="text" @click="toggleAdvancedFilters" class="advanced-toggle-btn">
                {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 高级筛选栏 -->
          <transition name="fade">
            <el-form v-if="showAdvancedFilters" :model="queryParams" :inline="true" class="advanced-search-form">
              <el-form-item label="信息系统名称" prop="sysname" class="form-item">
                <el-input v-model="queryParams.sysname" placeholder="信息系统名称" clearable class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="管理人" prop="managerId" class="form-item">
                <el-select v-model="queryParams.managerId" filterable clearable placeholder="请选择管理人"
                  class="input-fixed-width">
                  <el-option v-for="user in userList" :key="user.userId" :label="user.nickname" :value="user.userId" />
                </el-select>
              </el-form-item>

              <el-form-item label="联系方式" prop="contact" class="form-item">
                <el-input v-model="queryParams.contact" placeholder="联系方式" clearable class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="IP地址" prop="ip" class="form-item">
                <el-input v-model="queryParams.ip" placeholder="IP地址" clearable class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="域名" prop="domain" class="form-item">
                <el-input v-model="queryParams.domain" placeholder="域名" clearable class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="第三方运维单位" prop="otherManageUnit" class="form-item">
                <el-input v-model="queryParams.otherManageUnit" placeholder="第三方运维单位" clearable
                  class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="运维人员" prop="otherManager" class="form-item">
                <el-input v-model="queryParams.otherManager" placeholder="运维人员" clearable class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="第三方运维联系方式" prop="otherContact" class="form-item">
                <el-input v-model="queryParams.otherContact" placeholder="第三方运维联系方式" clearable
                  class="input-fixed-width" />
              </el-form-item>

              <el-form-item label="系统定级" prop="sysRank" class="form-item">
                <el-select v-model="queryParams.sysRank" placeholder="系统定级" clearable class="input-fixed-width">
                  <el-option v-for="item in dictCache.system_level" :key="item.value" :label="item.label"
                    :value="item.value" />
                </el-select>
              </el-form-item>

              <!-- 登记时间单独一排，宽度100% -->
              <el-form-item label="登记时间" prop="createTime" class="form-item full-width">
                <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~"
                  start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss"
                  class="input-fixed-width" />
              </el-form-item>
            </el-form>
          </transition>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>

                <el-button v-hasPerm="['system:systemsEntity:add']" type="success" class="ml-3" @click="openDialog()">
                  <i-ep-plus />
                  新增
                </el-button>
                <el-button v-hasPerm="['system:systemsEntity:delete']" type="danger" :disabled="ids.length === 0"
                  class="ml-3" @click="handleDelete()"><i-ep-delete />
                  删除
                </el-button>
              </div>
              <!-- 显示选择信息和操作按钮 -->
              <span v-if="ids.length > 0" class="selection-info">
                已选择 <el-tag type="info">{{ ids.length }}</el-tag> 项
                <el-button type="primary" link @click="clearSelection">清除选择</el-button>
              </span>
              <div>
                <el-button class="ml-3" @click="handleOpenImportDialog">
                  <template #icon><i-ep-upload /></template>
                  导入
                </el-button>

                <el-button class="ml-3" @click="handleExport" :loading="exportLoading">
                  <template #icon><i-ep-download /></template>
                  导出{{ ids.length > 0 ? '选中' : '全部' }}
                </el-button>
              </div>
            </div>
          </template>

          <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading"
            :data="pageData" highlight-current-row border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column align="center" sortable key="" label="序号" prop="" min-width="50" />
            <!-- <el-table-column align="center" sortable key="id" label="系统ID" prop="id" min-width="100" /> -->
            <el-table-column align="center" key="sysname" label="信息系统名称" prop="sysname" min-width="120" />
            <!-- <el-table-column align="center" key="sysVersion" label="版本" prop="sysVersion" min-width="130" /> -->
            <el-table-column align="center" key="url" label="域名/IP" prop="url" min-width="155" show-overflow-tooltip />
            <el-table-column align="center" key="url" label="访问地址" prop="url" min-width="155" show-overflow-tooltip />
            <el-table-column align="center" key="deptName" label="管理部门" prop="deptName" min-width="120" />
            <el-table-column align="center" key="managerName" label="管理人员" prop="managerName" min-width="100" />
            <el-table-column align="center" key="contact" label="联系方式" prop="contact" min-width="120" />
            <el-table-column align="center" key="status" label="资产状态" prop="status" min-width="80">
              <template #default="scope">
                <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '2' ? 'danger' : 'info')">
                  {{ getAssetStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" label="探测状态" width="150">
              <template #default="scope">
                <asset-detection-status :survival-status-list="scope.row.survivalStatusList"
                  :asset-name="scope.row.name" />
              </template>
            </el-table-column>
            <!-- <el-table-column align="center" key="isOpen" label="是否对公网开放" prop="isOpen" min-width="130">
              <template #default="scope">
                <span v-if="scope.row.isOpen">是</span>
                <span v-else-if="scope.row.isOpen">否</span>
              </template>
            </el-table-column>
            <el-table-column align="center" key="assets" label="关联资产" prop="assets" min-width="120">
              <template #default="scope">
                <el-button type="success" link @click="handleViewRelatedAssets(scope.row)">
                  <el-icon>
                    <Connection />
                  </el-icon>关联资产
                </el-button>
              </template>
            </el-table-column>
            <el-table-column align="center" key="frame" label="开发框架" prop="frame" min-width="100" />
            <el-table-column align="center" key="notes" label="备注" prop="notes" min-width="80">
              <template #default="scope">
                <div class="flex items-center">
                  <span class="truncate max-w-[80px]" :title="scope.row.notes">
                    {{ scope.row.notes ? (scope.row.notes.length > 0 ? '' :
                      scope.row.notes) : '-' }}
                  </span>
                  <ViewNotes v-if="scope.row.notes" :content="scope.row.notes" :title="`备注`" />
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" key="createTime" label="创建时间" prop="createTime" min-width="160" />
            <el-table-column align="center" key="updateTime" label="更新时间" prop="updateTime" min-width="160" /> -->
            <el-table-column align="center" fixed="right" label="操作" width="230">
              <template #default="scope">
                <el-button v-hasPerm="['system:systemsEntity:edit']" type="primary" size="small" link
                  @click="handleView(scope.row.id)">
                  <el-icon class="text-sm">
                    <View />
                  </el-icon>
                  查看详情
                </el-button>
                <el-button v-hasPerm="['system:systemsEntity:edit']" type="primary" size="small" link
                  @click="openDialog(scope.row.id)">
                  <i-ep-edit />
                  编辑
                </el-button>
                <el-button v-hasPerm="['system:systemsEntity:delete']" type="danger" size="small" link
                  @click="handleDelete(scope.row.id)">
                  <i-ep-delete />
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
        </el-card>
      </el-col>
    </el-row>
    <!-- 系统管理表单弹窗 -->
    <SystemDialog v-model:visible="dialog.visible" :title="dialog.title" :id="dialog.id" @submitted="handleQuery" />
    <!-- 关联资产弹窗 -->
    <ViewSystemAssets v-model:visible="assetsDialog.visible" :system-id="assetsDialog.systemId" />
    <ViewRelatedAssets v-model:visible="relatedAssetsDialog.visible" :system-id="relatedAssetsDialog.systemId" />
    <systemImport v-model:visible="importDialogVisible" @import-success="handleOpenImportDialogSuccess" />
  </div>
</template>

<script setup lang="ts">
import UserAPI, { UserQuery } from "@/api/user";
import { useDictStore } from '@/store/modules/dictStore'; // 导入字典store
import ViewSystemAssets from './components/ViewSystemAssets.vue'
import ViewRelatedAssets from "./components/ViewRelatedAssets.vue";
import AssetDetectionStatus from '../components/assetDetectionStatus.vue';

defineOptions({
  name: "systemsEntity",
  inheritAttrs: false,
});

import systemsEntityAPI, { systemsEntityPageVO, systemsEntityForm, systemsEntityPageQuery } from "@/api/assets_management/details/systems-entity";
import SystemDialog from './components/SystemDialog.vue'
import { reactive, ref, onMounted } from "vue";
import assetsAPI, { assetsPageVO, assetsForm, assetsPageQuery } from "@/api/assets_management/details/assets";

// 获取字典Store
const dictStore = useDictStore();

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const dataTableRef = ref(); // 添加表格引用
const importDialogVisible = ref(false);
const loading = ref(false);
const exportLoading = ref(false); // 添加导出加载状态
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const userList = ref<any[]>([])

const queryParams = reactive<systemsEntityPageQuery>({
  pageNum: 1,
  pageSize: 10,
  status: "1",
  type: 10
});


const relatedAssetsDialog = reactive({
  visible: false,
  systemId: undefined as number | undefined
})

const assetsDialog = reactive({
  visible: false,
  systemId: undefined as number | undefined
})
const userParams = reactive<UserQuery>({});

// 缓存字典数据
const dictCache = reactive<{
  dept0x0: any[],
  systemStatus: any[],
  system_level: any[],
  assetStatus: any[],
}>({
  dept0x0: [],
  systemStatus: [],
  system_level: [],
  assetStatus: []
});

// 部门映射缓存
const deptMap = ref<Record<string | number, string>>({});
// 系统状态映射缓存
const statusMap = ref<Record<string | number, string>>({});
// 系统定级映射缓存
const levelMap = ref<Record<string | number, string>>({});

const assetStatusMap = ref<Record<string | number, string>>({});

// 部门树显示状态
const toggleDeptTree = () => {
  showDeptTree.value = !showDeptTree.value;
};

// 系统管理表格数据
const pageData = ref<systemsEntityPageVO[]>([]);
//关联资产
const assets = ref<assetsPageVO[]>([]);

const showAdvancedFilters = ref(false);
// 切换高级筛选
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}
// 弹窗
const dialog = reactive({
  title: '',
  visible: false,
  id: undefined as number | undefined,
})

// 系统管理表单数据
const formData = reactive<systemsEntityForm>({
  assetsList: []
});

// 查看关联资产 - 修改为使用专门的关联资产弹窗
function handleViewRelatedAssets(row: any) {
  if (!row || !row.id) return;

  // 设置当前查看的系统ID并打开关联资产弹窗
  relatedAssetsDialog.systemId = row.id;
  relatedAssetsDialog.visible = true;
}

// 预加载字典数据
async function preloadDictData() {
  try {
    loading.value = true;

    // 并行加载所有需要的字典数据
    const [deptOptions, statusOptions, levelOptions, assetStatusOptions] = await Promise.all([
      dictStore.fetchOptions('dept0x0'),
      dictStore.fetchOptions('systemStatus'),
      dictStore.fetchOptions('system_level'),
      dictStore.fetchOptions('asset_status'),
    ]);

    // 更新缓存
    dictCache.dept0x0 = deptOptions || [];
    dictCache.systemStatus = statusOptions || [];
    dictCache.system_level = levelOptions || [];
    dictCache.assetStatus = assetStatusOptions || [];

    // 处理部门树数据
    processDeptTree(dictCache.dept0x0);

    // 处理系统状态选项
    processOptions(dictCache.systemStatus, statusMap.value);

    // 处理系统定级选项
    processOptions(dictCache.system_level, levelMap.value);

    // 处理资产状态选项
    processOptions(dictCache.assetStatus, assetStatusMap.value);

  } catch (error) {
    console.error('获取字典数据失败:', error);
  } finally {
    loading.value = false;
  }
}

// 处理部门树数据
function processDeptTree(depts: any[]) {
  if (!Array.isArray(depts)) return;

  depts.forEach(dept => {
    if (dept.value !== undefined && dept.label) {
      deptMap.value[dept.value] = dept.label;
    }

    if (dept.children && dept.children.length > 0) {
      processDeptTree(dept.children);
    }
  });
}

// 处理选项数据
function processOptions(options: any[], targetMap: Record<string | number, string>) {
  if (!Array.isArray(options)) return;

  options.forEach(option => {
    if (option.value !== undefined && option.label) {
      targetMap[option.value] = option.label;
    }
  });
}

// 获取部门名称
function getDeptName(deptId: string | number): string {
  if (!deptId) return '-';
  return deptMap.value[deptId] || `部门${deptId}`;
}

// 资产状态名称
function getAssetStatusName(status: string | number): string {
  const item = dictCache.assetStatus.find((item) => item.value == status);
  return item ? item.label : `未知状态${status}`;
}

function handleQuery() {
  loading.value = true
  systemsEntityAPI.getPage(queryParams).then((data) => {
    pageData.value = data.list
    total.value = data.total
  }).finally(() => {
    loading.value = false
  })
}

function handleResetQuery() {
  queryFormRef.value.resetFields()
  Object.keys(queryParams).forEach((key) => {
    (queryParams as any)[key] = undefined
  })
  queryParams.pageNum = 1
  queryParams.pageSize = 10
  handleQuery()
}

// 获取用户选项
const loadUserList = async () => {
  try {
    const data = await UserAPI.getList(userParams)
    userList.value = data
  } catch (error) {
  }
}

function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id)
}

/** 清除所有选择 */
function clearSelection() {
  ids.value = [];
  // 清除表格选择状态
  if (dataTableRef.value) {
    dataTableRef.value.clearSelection();
  }
}

async function openDialog(id?: number) {
  dialog.visible = true
  dialog.id = undefined
  dialog.title = id ? '修改信息系统' : '新增信息系统'
  nextTick(() => {
    dialog.id = id
  })
}

// 缓存资产数据
const assetsCache = ref<Map<number, assetsPageVO[]>>(new Map());

function handleView(id?: number) {
  if (!id) return;

  // 设置当前查看的系统ID并打开弹窗
  assetsDialog.systemId = id;
  assetsDialog.visible = true;
}

function handleDelete(id?: number) {
  let idsToDelete = id ? [id] : ids.value
  if (!idsToDelete.length) {
    ElMessage.warning('请勾选删除项')
    return
  }

  ElMessageBox.confirm('确认删除已选中的数据项?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    loading.value = true
    systemsEntityAPI.deleteByIds(idsToDelete.join(',')).then(() => {
      ElMessage.success('删除成功')
      // 删除后清除相关缓存
      idsToDelete.forEach(id => {
        assetsCache.value.delete(id);
      });
      handleResetQuery()
    }).finally(() => {
      loading.value = false
    })
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  // 构建导出参数
  const exportParams: any = {
    ...queryParams
  };

  // 如果当前页有选中项，则传入assetIds参数
  if (ids.value.length > 0) {
    exportParams.assetIds = ids.value;
  }

  exportLoading.value = true;

  assetsAPI.exportAll(exportParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });

  handleResetQuery();
}

onMounted(async () => {
  // 先加载字典数据
  await preloadDictData();
  // 再加载表格数据和用户列表
  handleQuery();
  loadUserList();
})
</script>

<style scoped>
/* 添加选择信息样式 */
.selection-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

search-container {
  max-width: 100%;
}

/* 主搜索栏样式 */
.main-search-form {
  display: flex;
  gap: 16px;
  align-items: center;
  width: 100%;
}

.input-fixed-width-main {
  flex-grow: 1;
  /* 占满剩余宽度 */
  min-width: 200px;
  /* 最小宽度，可调 */
  max-width: 100%;
  /* 最大不超出容器 */
  width: auto;
  /* 清除固定宽度 */
}


/* 高级筛选表单容器 */
.advanced-search-form {
  width: 1300px;
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: flex-start;
  align-items: flex-start;
}

/* 每个表单项固定宽度，防止拉伸 */
.form-item {
  width: 300px;
  flex: none;
}

/* 左对齐标签 */
.advanced-search-form .el-form-item__label {
  width: 80px !important;
  text-align: left !important;
}


.input-fixed-width {
  width: 180px;
}

.form-item.full-width {
  width: 100%;
}

.form-item.full-width .input-fixed-width {
  width: 100%;
}

.advanced-toggle-btn {
  background-color: rgb(34, 197, 94);
  width: 100px;
  color: #fff;
}

.search-btn {
  width: 100px;
  background-color: rgb(221, 151, 82);
  color: #fff;
  ;
}
</style>