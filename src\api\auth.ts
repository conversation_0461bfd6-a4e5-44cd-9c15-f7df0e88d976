import request from "@/utils/request";
import { a } from "vite-plugin-mock-dev-server/dist/types-BYspd62h";

const AUTH_BASE_URL = "/api/v1/auth";

class AuthAPI {
  /** 登录 接口*/
  static login(data: LoginData) {
    const formData = new FormData();
    formData.append("username", data.username);
    formData.append("password", data.password);
    formData.append("captchaKey", data.captchaKey);
    formData.append("captchaCode", data.captchaCode);
    return request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/login`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /** sso */
  static ssoLogin(data: SsoLoginData) {
    const formData = new FormData();
    formData.append("code", data.code);
    formData.append("redirectUri", data.redirectUri);
    return request<any, LoginResult>({
      url: `${AUTH_BASE_URL}/sso/login`,
      method: 'post',
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /** 注销 接口*/
  static logout() {
    return request({
      url: `${AUTH_BASE_URL}/logout`,
      method: "delete",
    });
  }

  /** 获取验证码 接口*/
  static getCaptcha() {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: "get",
    });
  }

  /** 上传新证书 */
  static uploadCertificate(data: FormData) {
    return request({
      url: `${AUTH_BASE_URL}/upload`,
      method: "post",
      data: data,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }

  /** 获取证书信息 */
  static getCertificate() {
    return request<any, CertificateInfo>({
      url: `${AUTH_BASE_URL}/certificate`,
      method: "get",
    });
  }

}

export default AuthAPI;

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码 */
  captchaCode: string;
}

/** 统一认证登录请求参数 */
export interface SsoLoginData {
  /** 授权码 */
  code: string;
  /** 应用回调地址 */
  redirectUri: string;
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string;
  /** 过期时间(单位：毫秒) */
  expires?: number;
  /** 刷新token */
  refreshToken?: string;
  /** token 类型 */
  tokenType?: string;
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码图片Base64字符串 */
  captchaBase64: string;
}

/** 证书信息对象 */
export interface CertificateInfo {
  /** 产品名称 */
  productName: string;
  /** 产品序列号 */
  serialNumber: string;
  /** 授权单位 */
  userName: string;
  /** 证书类型: official-正式证书, temporary-临时证书 */
  certType: 'official' | 'temporary';
  /** 有效期开始日期 */
  validFrom: string;
  /** 有效期结束日期 */
  validTo: string;
  /** 授权模块列表 */
  modules: string[];
  /** 备注信息 */
  remarks?: string;
}
