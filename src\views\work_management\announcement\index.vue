<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="公告类型" prop="type">
          <dictionary v-model="queryParams.type" code="ANN_TYPE" />
        </el-form-item>
        <el-form-item label="公告标题" prop="title">
          <el-input
            v-model="queryParams.title"
            placeholder="公告标题"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input
            v-model="queryParams.content"
            placeholder="公告内容"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="公告发布时间" prop="createTime">
          <el-date-picker
            :editable="false"
            class="!w-[240px]"
            v-model="queryParams.createTime"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="截止时间"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="已读未读" prop="isRead">
          <dictionary v-model="queryParams.isRead" code="IS_READ" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()"><i-ep-search />搜索</el-button>
          <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          v-hasPerm="['system:messages:add']"
          type="success"
          @click="handleOpenDialog()"
        >
          <i-ep-plus />
          新增
        </el-button>
        <el-button
          v-hasPerm="['system:messages:delete']"
          type="danger"
          :disabled="ids.length === 0"
          @click="handleDelete()"
        ><i-ep-delete />
          删除
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        :default-sort="{ prop: 'id', order: 'descending' }"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          sortable
          key="id"
          label="公告id"
          prop="id"
          min-width="80"
        />
        <el-table-column
          key="type"
          label="公告类型"
          prop="type"
          min-width="80"
        >
          <template #default="scope">
            <el-tag :type="scope.row.type == '1' ? 'success' : 'danger'">
              <!-- <dictmap code="ANN_TYPE" v-model="scope.row.type" /> -->
              {{ scope.row.type==1 ? '消息' : '通知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          key="title"
          label="公告标题"
          prop="title"
          min-width="100"
        />
        <el-table-column
          key="content"
          label="公告内容"
          prop="content"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              link
              @click="showDialog(scope.row.id)"
            >
              <i-ep-view />
              查看公告
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          key="createTime"
          label="公告发布时间"
          prop="createTime"
          min-width="130"
        />
        <el-table-column
          key="updateTime"
          label="公告更新时间"
          prop="updateTime"
          min-width="130"
        />
        <el-table-column
          key="isRead"
          label="查阅进度"
          prop="isRead"
          min-width="80"
        >
          <template #default="scope">
            <div>
              <!-- <el-tag :type="scope.row.isRead == '1' ? 'success' : 'danger'">
                {{ scope.row.isRead==1 ? '已读' : '未读' }}
              </el-tag> -->
              <div class="mt-1" v-if="scope.row.readStats">
                总数({{ scope.row.readStats.total }})：
                <el-text type="success">已读({{ scope.row.readStats.read }})</el-text> / 
                <el-text type="danger">未读({{ scope.row.readStats.unread }})</el-text>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column key="contractFile" label="公告文件" prop="contractFile" min-width="120" align="center" >
          <template #default="scope">
            <!-- 修改为点击事件 -->
            <el-button
              v-if="scope.row.fileUrl"
              type="primary"
              size="small"
              link
              @click="downloadFile(scope.row.fileUrl, scope.row.filename)"
            >
              公告附件
            </el-button>
          </template>
        </el-table-column>
        <!-- <el-table-column
          key="username"
          label="接收者"
          prop="username"
          min-width="100"
        >
          <template #default="scope">
            <Dictmap code="user0x0" v-model="scope.row.userId" />
          </template>
        </el-table-column> -->
        <!-- <el-table-column
          key="sendId"
          label="发送者"
          prop="sendId"
          min-width="100"
        /> -->
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button
              v-hasPerm="['system:messages:edit']"
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row.id)"
            >
              <i-ep-edit />
              编辑
            </el-button>
            <el-button
              v-hasPerm="['system:messages:delete']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 安全公告管理表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="60%"
      center
      append-to-body
      destroy-on-close
      @close="handleCloseDialog"
    >
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公告类型" prop="type">
              <dictionary v-model="formData.type" code="ANN_TYPE" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="公告发布时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                type="datetime"
                placeholder="公告发布时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="formData.type=='0'" label="优先级" prop="level">
              <dictionary v-model="formData.level" code="ANN_LEVEL" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="formData.type=='0'" label="通知类型" prop="noticeType">
              <dictionary v-model="formData.noticeType" code="ANN_MESSAGES_TYPE" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="公告标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="公告标题"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>

        <el-col v-if="formData.type=='0'" :span="12">
          <el-form-item label="接收类型" prop="receiverType">
            <el-radio-group v-model="formData.receiverType" @change="handleReceiverTypeChange">
              <el-radio :value="'0'">所有人</el-radio>
              <el-radio :value="'group'">用户组</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formData.receiverType === 'group'" label="用户组" prop="groupId">
            <Dictionary code="parameterSet0x0" v-model="formData.groupId" />
          </el-form-item>
        </el-col>

        <el-col v-if="formData.type=='1'" :span="12">
          <el-form-item label="接收类型" prop="receiverType">
            <el-radio-group v-model="formData.receiverType">
              <el-radio :value="'user'">接收用户</el-radio>
              <el-radio :value="'group'">用户组</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formData.receiverType === 'user'" label="接收用户" prop="userId">
            <LazyLoading v-model="formData.userId" code="user" />
          </el-form-item>
          <el-form-item v-if="formData.receiverType === 'group'" label="用户组" prop="groupId">
            <Dictionary code="parameterSet0x0" v-model="formData.groupId" />
          </el-form-item>
        </el-col>
        <el-form-item label="公告内容" prop="content">
          <editor v-model="formData.content" style="height: 330px;" class="dark-theme-compatible" />
        </el-form-item>
        <el-form-item label="公告文件" prop="fileUrl" style="margin-top: 0px;">
          <el-link v-if="formData.fileUrl" type="primary" :href="formData.fileUrl" target="_blank">
            查看公告文件
          </el-link>
          <el-upload
            class="upload-demo"
            :headers="headers"
            :action="action"
            :file-list="fileList"
            :accept="'.pdf,.doc,.docx'"
            :on-success="handleUploadSuccess"
          >
            <el-button type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible" append-to-body :width="dialogWidth">
      <div v-html="annvalue" style="height: 600px;overflow: auto;"></div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref} from "vue";

defineOptions({
  name: "messages",
  inheritAttrs: false,
});

import messagesAPI, { messagesPageVO, messagesForm, messagesPageQuery } from "@/enums/MessageTypeEnum";
import Editor from "@/components/WangEditor/index.vue";
import { hasAuth } from "@/plugins/permission";
import {TOKEN_KEY} from "@/enums/CacheEnum";
import {UploadUserFile} from "element-plus";
import FileAPI from "@/api/file";
import {formatLocalDateTime} from "@/utils/dateUtils";
const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<messagesPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 安全公告管理表格数据
const pageData = ref<messagesPageVO[]>([]);

// 在获取数据后处理查阅统计信息
function processReadStats() {
  pageData.value.forEach(item => {
    // 如果后端已经提供了统计数据，则直接使用
    if (!item.readStats) {
      // 否则创建模拟数据（实际项目中应从后端获取）
      item.readStats = {
        total: Math.floor(Math.random() * 50) + 10, // 模拟数据，实际应从后端获取
        read: 0,
        unread: 0
      };
      // 计算已读和未读数量
      item.readStats.read = Math.floor(item.readStats.total * (Math.random() * 0.8));
      item.readStats.unread = item.readStats.total - item.readStats.read;
    }
  });
}

const dialog = reactive({
  title: "",
  visible: false,
});

const fileList = ref([] as UploadUserFile[]);

const headers = ref({
  Authorization: localStorage.getItem(TOKEN_KEY)
});
const action = FileAPI.uploadUrl;

// 安全公告管理表单数据
const formData = reactive<messagesForm>({
  userId: 0,
});

const dialogVisible = ref(false);
const annvalue = ref("<p>这是初始内容</p>");
const dialogWidth = ref('60%');
const showDialog = (id:any) => {
  // handleReadChange(id);
  const selectedAnn = pageData.value.find((item) => item.id == id);
  console.log(selectedAnn);
  if (selectedAnn) {
    annvalue.value = selectedAnn.content ?? "";
  } else {
    annvalue.value = "";
  }
  dialogVisible.value = !dialogVisible.value;
};

const handleReceiverTypeChange = (value:any) => {
  if (value == "0") {
    formData.userId = -1;
  }
};
// 安全公告管理表单校验规则
const rules = reactive({
  type: [{ required: true, message: "请输入公告类型", trigger: "blur" }],
  title: [{ required: true, message: "请输入公告标题", trigger: "blur" }],
  content: [{ required: true, message: "请输入公告内容", trigger: "blur" }],
  createTime: [{ required: true, message: "请输入公告发布时间", trigger: "blur" }],
  receiverType: [{ required: true, message: "请选择接收类型", trigger: "blur" }],
  userId: [{ required: true, message: "请输入用户id", trigger: "blur" }],
  sendId: [{ required: true, message: "请输入发送者id", trigger: "blur" }],
});

function hasPermission(requiredPerms: string): boolean {
  // console.log(hasAuth(requiredPerms,'button'));
  return hasAuth(requiredPerms,'button');
}

/** 查看改变状态 */
function handleReadChange(id: number) {
  if (id) {
    pageData.value.forEach((item) => {
      if (item.id == id && item.isRead == 1) {
        return;
      }
    });
    messagesAPI.updateMessageRead(id).then(() => {
      handleQuery();
    });
  }
}

/** 查询安全公告管理 */
function handleQuery() {
  loading.value = true;
  messagesAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.list;
      // 修改附件下载文件名测试代码
      /* 
      // 打印 data 的数据结构用于调试
      console.log('data 的数据结构:', data);
      // 给 data.list 里的每一个字典对象都添加一个字段 "filename"
      data.list.forEach(item => {
        item.filename = "原文件名" + item.id.toString() + ".docx";
      });
      // 打印处理后的 pageData 的数据结构用于调试
      console.log('处理后的 pageData 的数据结构:', pageData.value);
      */
      total.value = data.total;
      // 处理查阅统计信息
      processReadStats();
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置安全公告管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开安全公告管理弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改安全公告管理";
    messagesAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增安全公告管理";
    formData.createTime = formatLocalDateTime(new Date()); // 新增时设置默认发布时间为当前时间
  }
}

/** 提交安全公告管理表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      formData.isRead = 0;
      formData.updateTime = formatLocalDateTime(new Date()); // 只更新修改时间，不更新创建时间
      formData.level = 0;
      formData.sendId = 0;
      if (id) {
        messagesAPI.update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        // 确保新增时有创建时间
        if (!formData.createTime) {
          formData.createTime = formatLocalDateTime(new Date());
        }
        messagesAPI.addMessage(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

// 文件上传成功回调
const handleUploadSuccess = (response: any) => {
  formData.fileUrl = response.data.url
}

/** 关闭安全公告管理弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
  formData.createTime = undefined; // 清空创建时间
  fileList.value = [];
}

/** 删除安全公告管理 */
function handleDelete(id?: number) {
  const messagesIds = [id || ids.value].join(",");
  if (!messagesIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      messagesAPI.deleteByIds(messagesIds)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});

// 新增下载文件方法
/**
 * 下载文件的异步函数
 * @param fileUrl - 文件的下载链接
 * @param filename - 可选参数，指定下载文件的文件名。如果未提供，则尝试从文件链接中提取文件名，若还是无法获取，则使用 'unknown_file'
 */
const downloadFile = async (fileUrl: string, filename?: string) => {
  try {
    // 发起网络请求，获取文件的响应对象
    const response = await fetch(fileUrl);
    // 从响应对象中提取文件的二进制数据
    const blob = await response.blob();

    // 创建一个指向二进制数据的临时 URL
    const url = window.URL.createObjectURL(blob);
    // 创建一个 <a> 元素，用于触发文件下载
    const a = document.createElement('a');
    // 将 <a> 元素的 href 属性设置为临时 URL
    a.href = url;
    // 设置下载文件的文件名，优先使用传入的 filename，若未传入则从文件链接中提取，若还是无法获取则使用 'unknown_file'
    a.download = filename || fileUrl.split('/').pop() || 'unknown_file';
    // 模拟点击 <a> 元素，触发文件下载
    a.click();

    // 释放临时 URL，避免内存泄漏
    window.URL.revokeObjectURL(url);
  } catch (error) {
    // 打印文件下载失败的错误信息
    console.error('文件下载失败:', error);
    // 显示错误提示消息
    ElMessage.error('文件下载失败');
  }
};

</script>

<style lang="scss" scoped>
:deep(.dark-theme-compatible) {
  /* 编辑器适应深色主题的样式 */
  .w-e-text-container {
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);
  }
  
  .w-e-toolbar {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
  
  .w-e-menu-item {
    color: var(--el-text-color-primary);
    
    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
  
  .w-e-text-container p,
  .w-e-text-container h1,
  .w-e-text-container h2,
  .w-e-text-container h3,
  .w-e-text-container h4,
  .w-e-text-container h5,
  .w-e-text-container h6,
  .w-e-text-container li {
    color: var(--el-text-color-primary);
  }
}

/* 查看公告对话框样式适配深色主题 */
:deep(.el-dialog__body) {
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
}

/* 新增和修改的弹窗样式 */
:deep(.el-dialog) {
  max-height: 90vh;
  overflow-y: auto;
  
  .el-dialog__body {
    padding: 20px;
    max-height: calc(90vh - 120px);
    overflow-y: auto;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 查阅进度统计样式 */
.mt-1 {
  margin-top: 4px;
  font-size: 12px;
  line-height: 1.2;
}
</style>