<template>
  <div class="search-filter-container">
    <!-- 主搜索区域 -->
    <div class="main-search-area">
      <!-- 模糊搜索框 -->
      <div class="search-input-wrapper">
        <el-input
          v-model="searchKeyword"
          :placeholder="placeholder"
          size="large"
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
          @clear="handleSearch"
        >
          <template #prefix>
            <el-icon class="search-icon">
              <i-ep-search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 操作按钮组 -->
      <div class="action-buttons">
        <el-button 
          type="primary" 
          size="large" 
          @click="handleSearch"
          class="search-btn"
        >
          <i-ep-search />
          搜索
        </el-button>
        <el-button 
          size="large" 
          @click="handleReset"
          class="reset-btn"
        >
          <i-ep-refresh />
          重置
        </el-button>
        <el-button 
          size="large" 
          @click="toggleAdvancedFilters"
          class="advanced-btn"
          :type="showAdvanced ? 'info' : 'default'"
        >
          <i-ep-setting />
          高级筛选
          <el-icon class="ml-1" :class="{ 'rotate-180': showAdvanced }">
            <i-ep-arrow-down />
          </el-icon>
        </el-button>
      </div>
    </div>

    <!-- 高级筛选区域 -->
    <el-collapse-transition>
      <div v-if="showAdvanced" class="advanced-filters">
        <el-card shadow="never" class="filter-card">
          <el-form 
            ref="formRef" 
            :model="formData" 
            :inline="true" 
            class="advanced-form"
          >
            <slot name="filters" :form="formData" :form-ref="formRef"></slot>
          </el-form>
        </el-card>
      </div>
    </el-collapse-transition>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElForm } from 'element-plus'

interface Props {
  modelValue?: Record<string, any>
  placeholder?: string
  searchField?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入搜索关键词',
  searchField: 'keyword'
})

const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  'search': [params: Record<string, any>]
  'reset': []
}>()

const formRef = ref<InstanceType<typeof ElForm>>()
const searchKeyword = ref('')
const showAdvanced = ref(false)

// 表单数据
const formData = reactive<Record<string, any>>({})

// 监听外部传入的查询参数
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    Object.assign(formData, newVal)
    searchKeyword.value = newVal[props.searchField] || ''
  }
}, { immediate: true, deep: true })

// 监听表单数据变化
watch(formData, (newVal) => {
  const params = { ...newVal }
  if (searchKeyword.value) {
    params[props.searchField] = searchKeyword.value
  }
  emit('update:modelValue', params)
}, { deep: true })

// 监听搜索关键词变化
watch(searchKeyword, (newVal) => {
  formData[props.searchField] = newVal
})

// 切换高级筛选
const toggleAdvancedFilters = () => {
  showAdvanced.value = !showAdvanced.value
}

// 搜索
const handleSearch = () => {
  const params = { ...formData }
  if (searchKeyword.value) {
    params[props.searchField] = searchKeyword.value
  }
  emit('search', params)
}

// 重置
const handleReset = () => {
  searchKeyword.value = ''
  Object.keys(formData).forEach(key => {
    formData[key] = undefined
  })
  
  nextTick(() => {
    formRef.value?.resetFields()
    emit('reset')
  })
}

// 暴露方法给父组件
defineExpose({
  search: handleSearch,
  reset: handleReset,
  toggleAdvanced: toggleAdvancedFilters
})
</script>

<style scoped lang="scss">
.search-filter-container {
  margin-bottom: 16px;
  width: 100%;
}

.main-search-area {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 8px;
  width: 100%;
  justify-content: center; // 居中对齐
}

.search-input-wrapper {
  flex: 1;
  
  .search-input {
    width: 100%; // 确保输入框撑满容器
    
    :deep(.el-input__wrapper) {
      /* 背景与边框全部使用 Element Plus 的主题变量 */
      background-color: var(--el-fill-color-blank);
      border: 1px solid var(--el-border-color);
      border-radius: 8px;

      /* 盒阴影也用变量，亮暗主题 Element Plus 已内置两套值 */
      box-shadow: var(--el-box-shadow-light);
      transition: all 0.3s;

      &:hover {
        box-shadow: var(--el-box-shadow-lighter, 0 2px 3px rgba(255, 255, 255, 0.12));
      }

      &.is-focus {
        box-shadow: var(--el-box-shadow-dark);
      }
    }
  }

  /* 图标颜色使用占位符文本颜色变量 */
  .search-icon {
    color: var(--el-text-color-placeholder);
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  
  .search-btn {
    border-radius: 8px;
    font-weight: 500;
  }
  
  .reset-btn {
    border-radius: 8px;
    color: #606266;
    border-color: #dcdfe6;
    
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
  
  .advanced-btn {
    border-radius: 8px;
    transition: all 0.3s;
    
    .el-icon {
      transition: transform 0.3s ease;
    }
    
    .rotate-180 {
      transform: rotate(180deg);
    }
    
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
}

.advanced-filters {
  margin-top: 12px;
  width: 100%; // 撑满宽度
  
  .filter-card {
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    background: #fafbfc;
    width: 100%;
    
    :deep(.el-card__body) {
      padding: 16px 20px;
    }
  }
  
  .advanced-form {
    width: 100%;
    
    :deep(.el-form-item) {
      margin-bottom: 16px;
      margin-right: 20px;
      
      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }
      
      .el-input,
      .el-select,
      .el-date-editor {
        border-radius: 6px;
        
        .el-input__wrapper {
          border-radius: 6px;
        }
      }
    }
    
    .el-form-item:last-child {
      margin-right: 0;
    }
  }
}

// 响应式断点
@media (max-width: 1200px) {
  .search-input-wrapper {
    max-width: 600px;
  }
}

@media (max-width: 768px) {
  .main-search-area {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .search-input-wrapper {
    max-width: none;
    min-width: auto;
  }
  
  .action-buttons {
    justify-content: center;
    width: 100%;
  }
  
  .advanced-filters {
    .advanced-form {
      :deep(.el-form-item) {
        margin-right: 0;
        margin-bottom: 12px;
        
        .el-input,
        .el-select,
        .el-date-editor {
          width: 100% !important;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .action-buttons {
    flex-direction: column;
    
    .el-button {
      width: 100%;
    }
  }
  
  .search-filter-container {
    margin-bottom: 12px;
  }
  
  .main-search-area {
    gap: 12px;
  }
}
</style>
