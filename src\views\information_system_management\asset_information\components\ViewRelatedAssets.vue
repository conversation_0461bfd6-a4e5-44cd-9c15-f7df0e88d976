<template>
    <el-dialog v-model="props.visible" title="系统关联资产" width="75%" :before-close="handleClose">
      <div class="header-info">
        <el-descriptions :column="3" border class="mb-4">
          <el-descriptions-item label="信息系统名称">{{ systemDetail.sysname || '-' }}</el-descriptions-item>
          <el-descriptions-item label="系统版本">{{ systemDetail.sysVersion || '-' }}</el-descriptions-item>
          <el-descriptions-item label="管理单位">{{ getDeptName(systemDetail.deptId) }}</el-descriptions-item>
        </el-descriptions>
      </div>
  
      <div class="filter-container mb-4">
        <el-form :model="queryParams" :inline="true" ref="queryForm">
          <el-form-item label="资产名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入资产名称" clearable />
          </el-form-item>
          <el-form-item label="资产类型" prop="type">
            <el-select v-model="queryParams.type" placeholder="请选择资产类型" clearable>
              <el-option 
                v-for="type in assetTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="IP地址" prop="ip">
            <el-input v-model="queryParams.ip" placeholder="请输入IP地址" clearable />
          </el-form-item>
          <el-form-item label="资产状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option 
                v-for="status in statusOptions" 
                :key="status.value" 
                :label="status.label" 
                :value="status.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon>查询
            </el-button>
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon>重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
  
      <div class="assets-table">
        <el-table 
          v-loading="loading" 
          :data="assetsList" 
          border 
          stripe 
          style="width: 100%"
          max-height="500"
        >
          <el-table-column prop="id" label="资产ID" min-width="80" align="center" />
          <el-table-column prop="name" label="资产名称" min-width="120" align="center" show-overflow-tooltip />
          <el-table-column prop="type" label="资产类型" min-width="100" align="center">
            <template #default="scope">
              <el-tag>{{ getAssetTypeName(scope.row.type) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="资产地址" min-width="180" align="center" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.ip">{{ scope.row.ip || '-'}}</div>
              <div v-else="scope.row.url">{{ scope.row.url }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="deptId" label="管理单位" min-width="120" align="center" show-overflow-tooltip>
            <template #default="scope">
              {{ getDeptName(scope.row.deptId) || scope.row.deptName || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="ownerName" label="管理人员" min-width="100" align="center" show-overflow-tooltip />
          <el-table-column prop="status" label="资产状态" min-width="100" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '0' ? 'danger' : 'info')">
                {{ getAssetStatusName(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" width="150" fixed="right" align="center">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="viewAssetDetail(scope.row)">
                <el-icon><View /></el-icon>详情
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
  
        <el-pagination
          v-if="total > 0"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          class="mt-4"
        />
  
        <el-empty v-if="!loading && (!assetsList || assetsList.length === 0)" description="暂无关联资产数据" />
      </div>
  
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { ref, reactive, watchEffect } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Search, Refresh, View } from '@element-plus/icons-vue';
  import systemAPI from '@/api/assets_management/details/systems-entity';
  import assetsAPI from '@/api/assets_management/details/assets';
  import { useDictStore } from '@/store/modules/dictStore';
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    systemId: {
      type: Number,
      default: undefined
    }
  });
  
  const emit = defineEmits(['update:visible']);
  
  // 获取字典Store
  const dictStore = useDictStore();
  
  // 系统详情
  const systemDetail = ref<any>({});
  const loading = ref(false);
  const assetsList = ref<any[]>([]);
  const total = ref(0);
  const assetDetailVisible = ref(false);
  const currentAssetId = ref<number>();
  
  // 查询参数
  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    systemId: undefined as number | undefined,
    name: '',
    type: '',
    status: '',
    ip: ''
  });
  
  // 资产类型选项 - 已更新为与系统一致
  const assetTypes = [
    { value: '1', label: '服务器' },
    { value: '2', label: '网络设备' },
    { value: '3', label: '安全设备' },
    { value: '10', label: '信息系统' }
  ];
  
  // 状态选项 - 已更新为与系统一致
  const statusOptions = [
    { value: '1', label: '正常' },
    { value: '2', label: '已下线' }
  ];
  
  // 字典映射
  const deptMap = ref<Record<string | number, string>>({});
  const deptCacheLoaded = ref(false);
  
  // 获取系统基本信息
  async function fetchSystemDetail(id: number) {
    if (!id) return;
    
    try {
      loading.value = true;
      const data = await systemAPI.getFormData(id);
      systemDetail.value = data;
      
      // 加载部门数据
      await loadDeptData();
      
    } catch (error) {
      console.error('获取系统详情失败:', error);
      ElMessage.error('获取系统详情失败');
    } finally {
      loading.value = false;
    }
  }
  
  // 获取系统关联的资产列表 - 确保API调用正确
  async function fetchRelatedAssets() {
    if (!props.systemId) return;
    
    try {
      loading.value = true;
      // 更新查询参数中的系统ID
      queryParams.systemId = props.systemId;
      
      // 确保使用正确的API调用方法
      const res = await systemAPI.getFormData(props.systemId);
      assetsList.value = res.assetsList || [];
      total.value = res.assetsList.length || 0;
    } catch (error) {
      console.error('获取关联资产失败:', error);
      ElMessage.error('获取关联资产列表失败');
    } finally {
      loading.value = false;
    }
  }
  
  // 加载部门数据
  async function loadDeptData() {
    if (deptCacheLoaded.value) return;
    
    try {
      // 获取部门选项
      const deptOptions = await dictStore.fetchOptions('dept0x0');
      
      // 处理部门树数据，构建ID到名称的映射
      const processDeptTree = (depts: any[]) => {
        if (!Array.isArray(depts)) return;
        
        depts.forEach(dept => {
          if (dept.value !== undefined && dept.label) {
            deptMap.value[dept.value] = dept.label;
            // 同时存储字符串形式的键
            if (typeof dept.value === 'number') {
              deptMap.value[String(dept.value)] = dept.label;
            }
          }
          
          if (dept.children && dept.children.length > 0) {
            processDeptTree(dept.children);
          }
        });
      };
      
      processDeptTree(deptOptions);
      deptCacheLoaded.value = true;
    } catch (error) {
      console.error('加载部门数据失败:', error);
    }
  }
  
  // 获取部门名称
  function getDeptName(deptId: string | number): string {
    if (!deptId) return '-';
    return deptMap.value[deptId] || `部门${deptId}`;
  }
  
  // 获取资产类型名称 - 已更新为与系统一致
  function getAssetTypeName(type: any): string {
    const typeMap = {
      1: '服务器',
      2: '网络设备',
      3: '安全设备',
      4: '物联网设备',
      10: '信息系统'
    };
    return typeMap[type] || '未知类型';
  }
  
  // 获取资产状态名称 - 已更新为与系统一致
  function getAssetStatusName(status: string | number): string {
    const statusMap = {
      '1': '正常',
      '2': '已下线'
    };
    return statusMap[status] || `未知状态(${status})`;
  }
  
  // 格式化日期
  function formatDate(dateStr: string | null | undefined): string {
    if (!dateStr) return '-';
    try {
      const date = new Date(dateStr);
      return date.toLocaleString();
    } catch (e) {
      return dateStr;
    }
  }
  
  // 查询按钮
  function handleQuery() {
    queryParams.pageNum = 1;
    fetchRelatedAssets();
  }
  
  // 重置查询
  function resetQuery() {
    queryParams.name = '';
    queryParams.type = '';
    queryParams.status = '';
    queryParams.ip = '';
    queryParams.pageNum = 1;
    fetchRelatedAssets();
  }
  
  // 查看资产详情
  function viewAssetDetail(row: any) {
    if (!row || !row.id) return;
    
    // 使用路由或其他方式跳转到资产详情页
    ElMessage.info(`查看资产ID: ${row.id} 的详情`);
    
    // 如果有单独的资产详情组件，可以在这里打开
    // currentAssetId.value = row.id;
    // assetDetailVisible.value = true;
  }
  
  // 每页条数改变
  function handleSizeChange(size: number) {
    queryParams.pageSize = size;
    fetchRelatedAssets();
  }
  
  // 页码改变
  function handleCurrentChange(page: number) {
    queryParams.pageNum = page;
    fetchRelatedAssets();
  }
  
  // 关闭弹窗
  function handleClose() {
    emit('update:visible', false);
  }
  
  // 监听ID变化和可见状态变化，加载系统详情和关联资产
  watchEffect(() => {
    if (props.visible && props.systemId) {
      fetchSystemDetail(props.systemId);
      fetchRelatedAssets();
    }
  });
  </script>
  
  <style scoped>
  .header-info {
    margin-bottom: 20px;
  }
  
  .filter-container {
    background-color: var(--el-fill-color-light);
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  
  .assets-table {
    margin-bottom: 20px;
  }
  
  :deep(.el-descriptions) {
    --el-descriptions-item-bordered-label-background: var(--el-fill-color-light);
  }
  
  :deep(.el-descriptions__label) {
    width: 120px;
    color: var(--el-text-color-regular);
  }
  
  :deep(.el-descriptions__content) {
    color: var(--el-text-color-primary);
  }
  
  :deep(.el-table) {
    --el-table-header-bg-color: var(--el-fill-color-light);
    --el-table-row-hover-bg-color: var(--el-fill-color);
    --el-table-border-color: var(--el-border-color-lighter);
  }
  
  :deep(.el-table th),
  :deep(.el-table td) {
    background-color: var(--el-bg-color);
  }
  
  :deep(.el-table--border) {
    border: 1px solid var(--el-table-border-color);
  }
  
  :deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
    background-color: var(--el-fill-color-lighter);
  }
  
  /* 标签样式 */
  :deep(.el-tag) {
    margin: 0 2px;
  }
  
  /* 分页器 */
  :deep(.el-pagination) {
    justify-content: center;
    margin-top: 16px;
  }
  
  .mt-4 {
    margin-top: 1rem;
  }
  
  .mb-4 {
    margin-bottom: 1rem;
  }
  
  .dialog-footer {
    padding: 10px 20px;
    text-align: right;
  }
  </style>
