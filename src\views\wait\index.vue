<template>
    <div class="wait-container">
      <el-card class="wait-card">
        <div class="content-wrapper">
          <div class="icon-container">
            <el-icon class="construction-icon" :size="64"><Tools /></el-icon>
          </div>
          <h1 class="title">更多功能开发中</h1>
          <div class="message">
            <p>该功能正在紧锣密鼓地开发中，敬请期待！</p>
            <p>预计完成时间：{{ expectedCompletionDate }}</p>
          </div>
          <div class="progress-container">
            <div class="progress-text">开发进度：{{ progressPercent }}%</div>
            <el-progress :percentage="progressPercent" :stroke-width="15" :show-text="false"></el-progress>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="goBack">返回上一页</el-button>
            <el-button @click="goHome">返回首页</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tools } from '@element-plus/icons-vue';
  
  defineOptions({
    name: "WaitingPage",
    inheritAttrs: false,
  });
  
  const router = useRouter();
  
  // 预计完成日期
  const expectedCompletionDate = ref('2025年6月30日');
  
  // 开发进度百分比
  const progressPercent = ref(88);
  
  // 返回上一页
  function goBack() {
    router.back();
  }
  
  // 返回首页
  function goHome() {
    router.push('/');
  }
  </script>
  
  <style scoped>
  .wait-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 20px;
  }
  
  .wait-card {
    width: 100%;
    max-width: 800px;
  }
  
  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }
  
  .icon-container {
    margin-bottom: 20px;
  }
  
  .construction-icon {
    color: #409EFF;
  }
  
  .title {
    font-size: 28px;
    color: #303133;
    margin-bottom: 15px;
  }
  
  .message {
    text-align: center;
    margin-bottom: 30px;
    color: #606266;
  }
  
  .progress-container {
    width: 100%;
    max-width: 600px;
    margin-bottom: 30px;
  }
  
  .progress-text {
    margin-bottom: 10px;
    font-size: 14px;
    color: #606266;
  }
  
  .action-buttons {
    margin-bottom: 30px;
  }
  </style>
