import { defineStore } from 'pinia';
import Dict<PERSON><PERSON> from "@/api/dict";
import Dept<PERSON><PERSON> from "@/api/dept";
import UserAPI from "@/api/user";
import systemsEntityAPI from "@/api/assets_management/details/systems-entity";

export const useDictStore = defineStore('dict', {
  state: () => ({
    dictCache: {} as Record<string, { label: string, value: string | number }[]>,
  }),
  actions: {
    async fetchOptions(code: string) {
      if (!this.dictCache[code]) {
        let options = [];
        if (code == "system0x0") {
          options = await systemsEntityAPI.getOptions();
        } else if (code == "dept0x0") {
          options = await DeptAPI.getOptions();
        } else if (code == "user0x0") {
          // 用户数据不缓存
          return [];
        } else {
          options = await DictAPI.getOptions(code);
        }
        this.dictCache[code] = options;
      }
      return this.dictCache[code];
    },
    async fetchUserData(id: string | number) {
      if (!id) {
        return [{
          label: '',
          value:-2,
        }];
      }
      const response = await UserAPI.getFormData(id);
      return [{
        label: response.nickname || '',
        value: response.id || -2,
      }];
    }
  }
});
