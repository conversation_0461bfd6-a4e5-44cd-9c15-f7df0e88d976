// 文件处理工具
export const fileUtils = {
    // 提取文件ID列表
    extractFileIds(fileList: any[]): number[] {
      if (!fileList || !Array.isArray(fileList)) return [];
      return fileList.map(file => file.id);
    },
    
    // 格式化API返回的文件列表
    formatFileList(fileList: any[]): any[] {
      if (!fileList || !Array.isArray(fileList)) return [];
      return fileList.map(file => ({
        ...file,
        url: file.url || ''
      }));
    },
    
    // 处理文件下载
    downloadFile(file: any) {
      if (!file) return false;
      
      const fileUrl = file.url;
      if (!fileUrl) {
        ElMessage.error('附件链接不存在');
        return false;
      }
      
      try {
        window.open(fileUrl, '_blank');
        return true;
      } catch (error) {
        console.error('文件下载失败:', error);
        ElMessage.error('文件下载失败，请重试');
        return false;
      }
    }
  };
