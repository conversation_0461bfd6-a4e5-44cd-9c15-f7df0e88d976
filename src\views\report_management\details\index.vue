<template>
  <div class="development-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>正在开发中</span>
        </div>
      </template>
      <div class="content">
        <el-alert
          title="此页面正在开发中"
          type="info"
          description="我们正在努力开发此页面，请稍后再试。"
          show-icon
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.development-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.card-header {
  font-size: 20px;
  font-weight: bold;
}

.content {
  text-align: center;
  margin-top: 20px;
}
</style>
