import request from "@/utils/request";

const CERTIFICATE_BASE_URL = "/api/v1/certificate";

class CertificateManageAPI {
  /**
   * 获取证书列表
   *
   * @param params 查询参数
   * @returns 证书列表和总数据量
   */
  static getCertificateList(params: CertificateQuery) {
    return request<any, { list: CertificateVO[], total: number }>({
      url: `${CERTIFICATE_BASE_URL}/page`,
      method: "get",
      params: params,
    });
  }

  /**
   * 获取证书详情
   *
   * @param id 证书ID
   * @returns 证书详细信息
   */
  static getCertificateDetail(id: string) {
    return request<any, CertificateVO>({
      url: `${CERTIFICATE_BASE_URL}/${id}/form`,
      method: "get",
    });
  }

  /**
   * 生成新证书
   *
   * @param data 证书信息
   * @returns 操作结果
   */
  static generateCertificate(data: CertificateForm) {
    return request({
      url: `${CERTIFICATE_BASE_URL}`,
      method: "post",
      data: data,
    });
  }

  /**
   * 更新证书信息
   *
   * @param id
   * @param data 证书更新信息
   * @returns 操作结果
   */
  static updateCertificate(id: number,data: CertificateForm) {
    return request({
      url: `${CERTIFICATE_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  }

  /**
   * 删除证书
   *
   * @param id 证书ID
   * @returns 操作结果
   */
  static deleteCertificate(id: string) {
    return request({
      url: `${CERTIFICATE_BASE_URL}/${id}`,
      method: "delete",
    });
  }

  /**
   * 批量删除证书
   *
   * @param ids 证书ID数组
   * @returns 操作结果
   */
  static batchDeleteCertificates(ids: string[]) {
    return request({
      url: `${CERTIFICATE_BASE_URL}/batch`,
      method: "delete",
      data: { ids },
    });
  }

  /**
   * 下载证书
   *
   * @param id 证书ID
   * @returns 证书文件流
   */
  static downloadCertificate(id: string) {
    return request({
      url: `${CERTIFICATE_BASE_URL}/download/${id}`,
      method: "get",
      responseType: "blob",
    });
  }

  /**
   * 验证证书
   *
   * @param id 证书ID
   * @returns 验证结果
   */
  static validateCertificate(id: string) {
    return request({
      url: `${CERTIFICATE_BASE_URL}/validate/${id}`,
      method: "get",
    });
  }
}

/** 证书查询参数 */
export interface CertificateQuery {
  /** 页码 */
  pageNum: number;
  /** 每页数量 */
  pageSize: number;
  /** 产品名称 */
  productName?: string;
  /** 序列号 */
  serialNumber?: string;
  /** 用户名称 */
  userName?: string;
  /** 证书类型 */
  certType?: string;
  /** 状态 */
  status?: string;
  /** 创建开始时间 */
  startCreateTime?: string;
  /** 创建结束时间 */
  endCreateTime?: string;
  /** 日期范围(界面使用) */
  dateRange?: string[];
}

/** 证书信息 */
export interface CertificateVO {
  /** 证书ID */
  id: string;
  /** 产品名称 */
  productName: string;
  /** 序列号 */
  serialNumber: string;
  /** 用户名称 */
  userName: string;
  /** 证书类型: official-正式证书, temporary-临时证书 */
  certType: 'official' | 'temporary';
  /** 有效期开始日期 */
  validFrom: string;
  /** 有效期结束日期 */
  validTo: string;
  /** 授权模块 */
  modules: string[];
  /** 创建时间 */
  createTime: string;
  /** 更新时间 */
  updateTime?: string;
  /** 备注 */
  remarks?: string;
}

/** 证书表单数据 */
export interface CertificateForm {
  /** 证书ID(编辑时必填) */
  id?: string;
  /** 产品名称 */
  productName: string;
  /** 序列号(编辑时显示) */
  serialNumber?: string;
  /** 用户名称 */
  userName: string;
  /** 证书类型 */
  certType: 'official' | 'temporary';
  /** 有效期开始日期 */
  validFrom: string;
  /** 有效期结束日期 */
  validTo: string;
  /** 授权模块 */
  modules: string[];
  /** 有效期日期范围(界面使用) */
  validDateRange?: string[];
  /** 备注 */
  remarks?: string;
}

export default CertificateManageAPI;
