<template>
  <div class="process-group">
    <el-card>
      <!-- 头部操作区 -->
      <div class="card-header">
        <div class="header-left">
          <!-- <el-button type="success" @click="handleLoadDefaultConfig">应用默认配置</el-button> -->
          <el-button type="warning" @click="handleDefaultConfigManage">默认配置管理</el-button>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleViewAll">查看全部</el-button>
        </div>
      </div>
      <!-- 表格布局 -->
      <el-table :data="tableData" border>
        <el-table-column prop="name" label="流程步骤" width="180">
          <template #default="scope">
            <div class="step-name">
              {{ scope.row.name }}
              <!-- 步骤完成状态指示器 -->
              <el-tag size="small" :type="hasExecutionConfig(scope.$index) ? 'success' : 'danger'">
                {{ hasExecutionConfig(scope.$index) ?'已配置': '未配置'}}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="执行列表">
          <template #default="scope">
            <div class="info-cell">
              <div class="info-group" :class="{ 'not-configured': !hasExecutionConfig(scope.$index) }">
                <div class="info-item">
                  <span class="label">所属部门:</span>
                  <span class="value">{{getExecutionData(scope.$index)?.deptName || '未设置' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">人员名称:</span>
                  <span class="value">
                    <template v-if="getExecutionPersons(scope.$index)?.length">
                      {{getExecutionPersons(scope.$index).map(p => p.nickname).join('、')}}
                    </template>
                    <template v-else>
                      {{ getExecutionData(scope.$index)?.personName || '未设置' }}
                    </template>
                  </span>
                </div>
              </div>
              <el-button type="primary" size="small" @click="handleEdit(scope.$index, true)"
                :disabled="isStepDisabled(scope.$index, true)">
                {{ getButtonText(scope.$index, true) }}
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="通知列表">
          <template #default="scope">
            <div class="info-cell">
              <div class="info-group">
                <div class="info-item">
                  <span class="label">所属部门:</span>
                  <span class="value">{{ getNotificationData(scope.$index)?.deptName || '未设置' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">人员名称:</span>
                  <span class="value">
                    <template v-if="getNotificationPersons(scope.$index)?.length">
                      {{getNotificationPersons(scope.$index).map(p => p.nickname).join('、')}}
                    </template>
                    <template v-else>
                      {{ getNotificationData(scope.$index)?.personName || '未设置' }}
                    </template>
                  </span>
                </div>
              </div>
              <el-button type="primary" size="small" @click="handleEdit(scope.$index, false)"
                :disabled="isStepDisabled(scope.$index, false)">
                {{ getButtonText(scope.$index, false) }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 编辑弹窗 -->
    <el-dialog v-model="dialogVisible" :title="isExecutionList ? '编辑执行人员' : '编辑通知人员'" width="550px">
      <el-form :model="editForm" label-width="120px">
        <!-- 部门选择 -->
        <el-form-item label="所属部门">
          <el-tree-select v-model="editForm.selectedDeptId" placeholder="请选择所属部门" :data="deptOptions" filterable
            check-strictly :render-after-expand="false" @change="handleDeptSelectChange"
            :props="{ value: 'value', label: 'label', children: 'children' }" />
          <el-text type="info" size="small" class="dept-info">
            {{ isExecutionList ? '执行部门' : '通知部门' }}
          </el-text>
        </el-form-item>

        <!-- 人员选择 -->
        <el-form-item label="选择人员" v-if="editForm.selectedDeptId > 0">
          <div class="person-select">
            <!-- 树形人员选择器 -->
            <el-tree-select v-model="selectedPersonIds" :data="personOptions" filterable :render-after-expand="false"
              :props="{
                value: 'id',
                label: 'nickname',
                children: 'children'
              }" :multiple="!isExecutionList" check-strictly placeholder="请选择人员" @change="handlePersonChange">
              <template #default="{ data }">
                {{ data.nickname }} - {{data.duty}} - ({{ data.deptName || '未知部门' }})
              </template>
            </el-tree-select>

            <!-- 已选人员标签展示 -->
            <div class="selected-tags">
              <el-tag v-for="person in selectedPersons" :key="person.id" closable @close="removePerson(person)">
                {{ person.nickname }} ({{ person.deptName }})
              </el-tag>
              <el-empty v-if="selectedPersons.length === 0" description="暂无选择人员" :image-size="10" />
            </div>

            <!-- 选择限制提示 -->
            <div class="person-limit-hint" v-if="selectedPersons.length > 0">
              <el-text type="info" class="person-count">
                已选择 {{ selectedPersons.length }} {{ isExecutionList ? '(执行只能单人)' : '' }}
              </el-text>
            </div>
          </div>
        </el-form-item>
        <el-empty v-else description="请先选择部门" :image-size="80" />

        <!-- 通知策略设置 -->
        <template v-if="editForm.selectedDeptId > 0 && selectedPersons.length > 0">
          <div class="divider">
            <el-divider content-position="left">通知策略设置</el-divider>
          </div>

          <el-form-item label="启用短信通知">
            <el-switch v-model="editForm.enableSms" />
          </el-form-item>

          <template v-if="editForm.enableSms">
            <el-form-item label="短信模板">
              <el-select v-model="editForm.smsTemplateId" @change="handleTemplateChange">
                <el-option v-for="item in smsTemplates" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>

            <el-form-item label="模板内容">
              <el-input v-model="editForm.smsContent" type="textarea" :rows="3" readonly />
            </el-form-item>

            <el-form-item label="通知频率">
              <el-radio-group v-model="editForm.notifyType">
                <el-radio label="once">单次通知</el-radio>
                <el-radio label="periodic">定期通知</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="通知周期" v-if="editForm.notifyType === 'periodic'">
              <el-select v-model="editForm.notifyPeriod">
                <el-option label="每天" value="daily" />
                <el-option label="每周" value="weekly" />
                <el-option label="每月" value="monthly" />
                <el-option label="每季度" value="quarterly" />
              </el-select>
            </el-form-item>
          </template>
        </template>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave"
            :disabled="!editForm.selectedDeptId || selectedPersons.length === 0">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看全部弹窗 -->
    <process-view-all v-model:visible="viewAllDialog.visible" :process-data="formattedProcessData" />

    <!-- 默认配置管理弹窗 -->
    <el-dialog v-model="defaultConfigDialog.visible" title="默认配置管理" width="900px" class="default-config-dialog">
      <el-table :data="defaultConfigList" border>
        <el-table-column prop="stepName" label="流程步骤" width="120" />
        <el-table-column label="执行配置" width="300">
          <template #default="scope">
            <div class="config-cell">
              <div v-if="scope.row.execution">
                <div>部门：{{ scope.row.execution.deptName || '未设置' }}</div>
                <div>人员：{{ scope.row.execution.userName || '未设置' }}</div>
                <div>短信：{{ scope.row.execution.enableSms === '1' ? '已启用' : '未启用' }}</div>
              </div>
              <div v-else class="not-configured">未配置</div>
              <!-- 修复：应用步骤配置的禁用状态 -->
              <el-button size="small" type="primary" @click="handleConfigEdit(scope.$index, 'execution')"
                :disabled="isStepDisabled(scope.$index, true)">
                {{ scope.row.execution ? '编辑' : '配置' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="通知配置" width="300">
          <template #default="scope">
            <div class="config-cell">
              <div v-if="scope.row.notification">
                <div>部门：{{ scope.row.notification.deptName || '未设置' }}</div>
                <div>人员：{{ scope.row.notification.userName || '未设置' }}</div>
                <div>短信：{{ scope.row.notification.enableSms === '1' ? '已启用' : '未启用' }}</div>
              </div>
              <div v-else class="not-configured">未配置</div>
              <!-- 修复：应用步骤配置的禁用状态 -->
              <el-button size="small" type="primary" @click="handleConfigEdit(scope.$index, 'notification')"
                :disabled="isStepDisabled(scope.$index, false)">
                {{ scope.row.notification ? '编辑' : '配置' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <!-- 修复：清空按钮也要考虑禁用状态 -->
            <el-button size="small" type="danger" @click="handleClearRowConfig(scope.$index)"
              :disabled="isStepDisabled(scope.$index, true) && isStepDisabled(scope.$index, false)">
              清空
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="defaultConfigDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveDefaultConfig">保存配置</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import ProcessViewAll from './viewAll.vue'
import ProcessConfigAPI, { type ReviewProcessConfigForm } from '@/api/work_management/processConfig'
import UserAPI, { UserForm, UserPageVO } from '@/api/user'
import DeptAPI from '@/api/dept'
import { useDictStore } from '@/store/modules/dictStore'
import { json } from 'stream/consumers'

// 配置接口
interface StepConfig {
  // 禁用执行编辑
  disableExecutionEdit?: boolean
  // 禁用通知编辑  
  disableNotificationEdit?: boolean
  // 自定义按钮文本
  executionButtonText?: string
  notificationButtonText?: string
  // 是否支持自动配置
  enableAutoConfig?: boolean
}

// 定义接口
interface ListItem {
  id: number
  deptName: string
  personName: string
  departmentId: number
  enableSms?: boolean
  smsTemplateId?: number
  smsContent?: string
  notifyType?: 'once' | 'periodic'
  notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  selectedDeptId?: number
  userIds?: (number | string)[]
}

interface Department {
  id: number
  name: string
  active: boolean
  execution?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
  notification?: {
    selectedDeptId?: number
    selectedDeptName?: string
    deptName?: string
    personName?: string
    persons?: UserInfo[]
    enableSms?: boolean
    smsTemplateId?: number
    smsContent?: string
    notifyType?: 'once' | 'periodic'
    notifyPeriod?: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  }
}

interface UserInfo {
  id: number | string
  username: string
  nickname: string
  deptName: string
  mobile: string
}

// 定义props
const props = defineProps<{
  shstep:Object,
  vulnerabilityForm:Object
  businessType: string // 业务类型
  departments: Department[] // 部门数据
  smsTemplates: Array<{ id: number; name: string; content: string }> // 短信模板
  deptTypeMap: Record<number, string> // 部门类型映射
  // 新增：步骤配置，key为步骤索引，value为配置
  stepConfigs?: Record<number, StepConfig>
}>()

// 定义emit
const emit = defineEmits<{
  (e: 'update:departments', value: Department[]): void
}>()

// 状态变量
const deptOptions = ref<any>([])
const personOptions = ref<any[]>([])
const selectedPersonIds = ref<(string | number)[]>([])
const loadingPersons = ref(false)
const executionList = ref<ListItem[]>([])
const notificationList = ref<ListItem[]>([])
const executionPersons = ref<Map<number, UserInfo[]>>(new Map())
const notificationPersons = ref<Map<number, UserInfo[]>>(new Map())
const selectedPersons = ref<UserInfo[]>([])
const dialogVisible = ref(false)
const viewAllDialog = reactive({ visible: false })
const defaultConfigDialog = reactive({ visible: false })
const isExecutionList = ref(true)
const currentEditIndex = ref(-1)
const defaultConfigList = ref<any[]>([])
const currentConfigType = ref<'execution' | 'notification'>('execution')
const currentConfigIndex = ref(-1)
const dictStore = useDictStore()
const dutyCache = ref<Record<string, string>>({}) 
// 部门信息
watch(() => props.shstep, (newVal, oldVal) => {
  currentConfigIndex.value=-1
  currentEditIndex.value=props.shstep.stepnum?2:1
  executionPersons.value.set(props.shstep.stepnum || 2,JSON.parse(JSON.stringify([props.shstep])) );
  selectedPersons.value=JSON.parse(JSON.stringify([props.shstep]))
  editForm.value=JSON.parse(JSON.stringify( {
      deptName:props.shstep.deptName,
      enableSms:false,
      notifyPeriod:"daily",
      notifyType:"once",
      personName:props.shstep.nickname,
      persons:[props.shstep],
      selectedDeptId:props.shstep.deptId,
      selectedDeptName:props.shstep.deptName,
      smsContent:"",
    }))
  handleSave()
});

// 编辑表单
const editForm = ref({
  id: 0,
  deptName: '',
  personName: '',
  departmentId: 0,
  selectedDeptId: 0,
  enableSms: false,
  smsTemplateId: undefined as number | undefined,
  smsContent: '',
  notifyType: 'once' as 'once' | 'periodic',
  notifyPeriod: 'daily' as 'daily' | 'weekly' | 'monthly' | 'quarterly',
  userIds: [] as (number | string)[]
})
// 计算属性
const tableData = computed(() => props.departments)
const formattedProcessData = computed(() => {
  const result: any[] = []

  props.departments.forEach((dept, index) => {
    // 添加执行配置数据
    if (dept.execution) {
      result.push({
        stepName: dept.name,
        departmentName: dept.execution.deptName || '未设置',
        originalDepartmentName: dept.execution.deptName || '未设置',
        type: '执行',
        personNames: dept.execution.personName || '未设置',
        personDetails: executionPersons.value.get(dept.id) || [],
        enableSms: dept.execution.enableSms || false,
        notifyType: dept.execution.notifyType || 'once',
        notifyPeriod: dept.execution.notifyPeriod || 'daily'
      })
    }

    // 添加通知配置数据
    if (dept.notification) {
      result.push({
        stepName: dept.name,
        departmentName: dept.notification.deptName || '未设置',
        originalDepartmentName: dept.notification.deptName || '未设置',
        type: '通知',
        personNames: dept.notification.personName || '未设置',
        personDetails: notificationPersons.value.get(dept.id) || [],
        enableSms: dept.notification.enableSms || false,
        notifyType: dept.notification.notifyType || 'once',
        notifyPeriod: dept.notification.notifyPeriod || 'daily'
      })
    }
  })

  return result
})

const loadDutyCache = async () => {
  if (Object.keys(dutyCache.value).length === 0) {
    try {
      // console.log('加载DUTY字典缓存')
      const options = await dictStore.fetchOptions('DUTY')
      // console.log('加载DUTY字典选项:', options)
      

      options.forEach(item => {
        dutyCache.value[item.value] = item.label
      })
      
      // console.log('构建的duty缓存:', dutyCache.value)
    } catch (error) {
      // console.error('加载DUTY字典失败:', error)
    }
  }
}

const getDutyText = (duty: string) => {
  if (!duty) return '' 
  
  
  if (duty.includes(',')) {
    return duty.split(',')
      .map(val => dutyCache.value[val.trim()] || val.trim())
      .join('、')
  }
  
  return dutyCache.value[duty] || duty
}

// 方法定义
const hasExecutionConfig = (index: number): boolean => {
  if (index < 0 || index >= props.departments.length) return false

  const dept = props.departments[index]
  const deptId = dept.id

  // 检查部门对象中的执行配置
  const hasExecConfig = !!(dept.execution?.selectedDeptId && dept.execution?.persons?.length > 0)

  // 检查人员映射中的配置
  const hasPersonsInMap = executionPersons.value.get(deptId)?.length > 0

  // 检查执行列表中的配置
  const execListItem = executionList.value.find(item => item.departmentId === deptId)
  const hasExecListItem = !!(execListItem?.selectedDeptId && execListItem?.userIds?.length > 0)

  // console.log(`步骤 ${index} 执行配置检查详情:`, {
  //   hasExecConfig,
  //   hasPersonsInMap,
  //   hasExecListItem,
  //   deptExecution: dept.execution,
  //   personsInMap: executionPersons.value.get(deptId),
  //   execListItem,
  //   deptId,
  //   allExecutionPersons: Object.fromEntries(executionPersons.value),
  //   allExecutionList: executionList.value
  // })

  // 只要任何一个地方有完整配置就认为已配置
  return hasExecConfig || (hasPersonsInMap && hasExecListItem)
}


const isAllExecutionConfigured = computed(() => {
  return props.departments.every((_, index) => hasExecutionConfig(index))
})

// 获取执行数据
const getExecutionData = (index: number) => {
  if (index < 0 || index >= props.departments.length) return null
  return props.departments[index].execution
}

// 获取通知数据
const getNotificationData = (index: number) => {
  if (index < 0 || index >= props.departments.length) return null
  return props.departments[index].notification
}

// 获取执行人员
const getExecutionPersons = (index: number) => {
  if (index < 0 || index >= props.departments.length) return []
  const deptId = props.departments[index].id
  return executionPersons.value.get(deptId) || []
}

// 获取通知人员
const getNotificationPersons = (index: number) => {
  if (index < 0 || index >= props.departments.length) return []
  const deptId = props.departments[index].id
  return notificationPersons.value.get(deptId) || []
}

// 初始化数据
const initData = () => {
  executionList.value = []
  notificationList.value = []

  // 确保 Map 对象正确初始化
  if (!executionPersons.value) {
    executionPersons.value = new Map()
  } else {
    executionPersons.value.clear()
  }

  if (!notificationPersons.value) {
    notificationPersons.value = new Map()
  } else {
    notificationPersons.value.clear()
  }
}

// 重置组件状态
const reset = () => {
  initData()
  selectedPersonIds.value = []
  selectedPersons.value = []
  dialogVisible.value = false
  viewAllDialog.visible = false
  defaultConfigDialog.visible = false
  currentEditIndex.value = -1
  currentConfigIndex.value = -1
  defaultConfigList.value = []

  // 重置编辑表单
  editForm.value = {
    id: 0,
    deptName: '',
    personName: '',
    departmentId: 0,
    selectedDeptId: 0,
    enableSms: false,
    smsTemplateId: undefined,
    smsContent: '',
    notifyType: 'once',
    notifyPeriod: 'daily',
    userIds: []
  }
}

// 从现有数据初始化
const initFromExistingData = (data: any) => {
  if (!data) return

  // console.log('从现有数据初始化:', data)
// 
  try {
    // 清空现有数据
    executionList.value = []
    notificationList.value = []
    executionPersons.value.clear()
    notificationPersons.value.clear()

    // 初始化执行列表
    if (data.executionList && Array.isArray(data.executionList)) {
      executionList.value = [...data.executionList]
    }

    // 初始化通知列表
    if (data.notificationList && Array.isArray(data.notificationList)) {
      notificationList.value = [...data.notificationList]
    }

    // 初始化人员映射 - 改进处理逻辑
    if (data.executionPersons) {
      Object.entries(data.executionPersons).forEach(([deptId, persons]) => {
        if (Array.isArray(persons)) {
          executionPersons.value.set(Number(deptId), persons as UserInfo[])
        }
      })
    }

    if (data.notificationPersons) {
      Object.entries(data.notificationPersons).forEach(([deptId, persons]) => {
        if (Array.isArray(persons)) {
          notificationPersons.value.set(Number(deptId), persons as UserInfo[])
        }
      })
    }

    // 同步到部门对象
    syncDataToDepartments()

    // console.log('数据初始化完成:', {
    //   executionList: executionList.value,
    //   notificationList: notificationList.value,
    //   executionPersons: Object.fromEntries(executionPersons.value),
    //   notificationPersons: Object.fromEntries(notificationPersons.value)
    // })
  } catch (error) {
    // console.error('初始化数据失败:', error)
  }
}

const syncDataToDepartments = () => {
  // console.log('开始强制同步数据到部门对象')

  props.departments.forEach((dept, index) => {
    // console.log(`处理部门 ${index}:`, dept.name)

    // 查找对应的执行配置
    const executionItem = executionList.value.find(item => item.departmentId === dept.id)
    const executionPersonsList = executionPersons.value.get(dept.id) || []

    // console.log(`部门 ${index} 执行配置同步:`, {
    //   executionItem,
    //   executionPersonsList,
    //   deptId: dept.id
    // })

    // 只要有人员配置就认为是有效的配置
    if (executionPersonsList.length > 0) {
      const config = {
        selectedDeptId: executionItem?.selectedDeptId || 0,
        selectedDeptName: executionItem?.deptName || '',
        deptName: executionItem?.deptName || '',
        personName: executionPersonsList.map(p => p.nickname).join('、'),
        persons: executionPersonsList,
        enableSms: executionItem?.enableSms || false,
        smsTemplateId: executionItem?.smsTemplateId,
        smsContent: executionItem?.smsContent || '',
        notifyType: executionItem?.notifyType || 'once',
        notifyPeriod: executionItem?.notifyPeriod || 'daily'
      }

      dept.execution = config
      // console.log(`部门 ${index} 执行配置已强制更新:`, dept.execution)
    }

    // 查找对应的通知配置
    const notificationItem = notificationList.value.find(item => item.departmentId === dept.id)
    const notificationPersonsList = notificationPersons.value.get(dept.id) || []

    if (notificationPersonsList.length > 0) {
      const config = {
        selectedDeptId: notificationItem?.selectedDeptId || 0,
        selectedDeptName: notificationItem?.deptName || '',
        deptName: notificationItem?.deptName || '',
        personName: notificationPersonsList.map(p => p.nickname).join('、'),
        persons: notificationPersonsList,
        enableSms: notificationItem?.enableSms || false,
        smsTemplateId: notificationItem?.smsTemplateId,
        smsContent: notificationItem?.smsContent || '',
        notifyType: notificationItem?.notifyType || 'once',
        notifyPeriod: notificationItem?.notifyPeriod || 'daily'
      }

      dept.notification = config
      // console.log(`部门 ${index} 通知配置已强制更新:`, dept.notification)
    }
  })

  // 触发更新
  emit('update:departments', [...props.departments])
  // console.log('强制数据同步完成，最终部门数据:', props.departments)
}


// 获取流程数据
const getProcessData = () => {
  // 构建完整的执行人员和通知人员数据
  const executionPersonsData = {}
  const notificationPersonsData = {}

  // 从人员映射中获取完整的用户信息
  executionPersons.value.forEach((persons, deptId) => {
    if (persons && persons.length > 0) {
      executionPersonsData[deptId] = persons.map(person => ({
        id: person.id,
        username: person.username || '',
        nickname: person.nickname || '',
        deptName: person.deptName || '',
        mobile: person.mobile || ''
      }))
    }
  })

  notificationPersons.value.forEach((persons, deptId) => {
    if (persons && persons.length > 0) {
      notificationPersonsData[deptId] = persons.map(person => ({
        id: person.id,
        username: person.username || '',
        nickname: person.nickname || '',
        deptName: person.deptName || '',
        mobile: person.mobile || ''
      }))
    }
  })

  return {
    executionList: executionList.value,
    notificationList: notificationList.value,
    executionPersons: executionPersonsData,
    notificationPersons: notificationPersonsData
  }
}

// 获取验证状态
const getValidationStatus = () => {
  const missingSteps: any[] = []

  props.departments.forEach((dept, index) => {
    if (!hasExecutionConfig(index)) {
      missingSteps.push({
        name: dept.name,
        type: '执行配置'
      })
    }
  })

  return {
    valid: missingSteps.length === 0,
    missingSteps
  }
}

// 保存默认配置项
const handleSaveDefaultConfigItem = () => {
  const configIndex = currentConfigIndex.value
  const configType = currentConfigType.value

  if (configIndex < 0 || configIndex >= defaultConfigList.value.length) {
    ElMessage.error('无效的配置索引')
    return
  }

  const configData = {
    deptId: editForm.value.selectedDeptId.toString(),
    deptName: editForm.value.deptName,
    userId: selectedPersons.value.map(p => p.id).join(','),
    userName: selectedPersons.value.map(p => p.nickname).join(','),
    enableSms: editForm.value.enableSms ? '1' : '0',
    smsTemplateId: editForm.value.smsTemplateId,
    notifyType: editForm.value.notifyType,
    notifyPeriod: editForm.value.notifyPeriod
  }

  defaultConfigList.value[configIndex][configType] = configData
}

// 初始化默认配置列表
const initDefaultConfigList = () => {
  defaultConfigList.value = props.departments.map((dept, index) => ({
    stepName: dept.name,
    deptType: props.deptTypeMap[index],
    execution: null,
    notification: null
  }))
}

// 加载默认配置
const loadDefaultConfig = async () => {
  try {
    const response = await ProcessConfigAPI.getConfig(props.businessType)
    // console.log('API响应:', response)

    if (response && Array.isArray(response)) {
      // console.log('开始处理配置数据:', response)

      // 将后端数据转换为前端格式
      const configMap = new Map()

      response.forEach((config: any) => {
        const key = `${config.executeDeptType}_${config.executeType}`
        configMap.set(key, config)
        // console.log(`添加配置映射: ${key}`, config)
      })

      // console.log('配置映射表:', configMap)

      // 更新默认配置列表
      defaultConfigList.value.forEach((item, index) => {
        const deptType = props.deptTypeMap[index]
        const executionKey = `${deptType}_1`
        const notificationKey = `${deptType}_2`

        // console.log(`处理部门 ${item.stepName}, 部门类型: ${deptType}`)
        // console.log(`查找执行配置键: ${executionKey}, 通知配置键: ${notificationKey}`)

        if (configMap.has(executionKey)) {
          const execConfig = configMap.get(executionKey)
          item.execution = {
            deptId: execConfig.deptId,
            deptName: execConfig.deptName,
            userId: execConfig.userId,
            userName: execConfig.userName,
            enableSms: execConfig.enableSms,
            smsTemplateId: execConfig.smsTemplateId,
            notifyType: execConfig.notifyType,
            notifyPeriod: execConfig.notifyPeriod
          }
          // console.log(`设置执行配置成功:`, item.execution)
        } else {
          // console.log(`未找到执行配置键: ${executionKey}`)
        }

        if (configMap.has(notificationKey)) {
          const notifyConfig = configMap.get(notificationKey)
          item.notification = {
            deptId: notifyConfig.deptId,
            deptName: notifyConfig.deptName,
            userId: notifyConfig.userId,
            userName: notifyConfig.userName,
            enableSms: notifyConfig.enableSms,
            smsTemplateId: notifyConfig.smsTemplateId,
            notifyType: notifyConfig.notifyType,
            notifyPeriod: notifyConfig.notifyPeriod
          }
          // console.log(`设置通知配置成功:`, item.notification)
        } else {
          // console.log(`未找到通知配置键: ${notificationKey}`)
        }
      })

      // console.log('最终配置列表:', defaultConfigList.value)

      // 强制触发视图更新
      defaultConfigList.value = [...defaultConfigList.value]
      console.log("66666666666666666666666666666",defaultConfigList.value)

    } else {
      // console.warn('API响应格式异常或无数据:', response)
      ElMessage.warning('暂无默认配置数据')
    }
  } catch (error) {
    // console.error('加载默认配置失败:', error)
    ElMessage.error('加载默认配置失败')
  }
}

// 应用默认配置
const handleLoadDefaultConfig = async () => {
  try {
    initDefaultConfigList()
    await loadDefaultConfig()

    // 应用配置到当前表单
    for (let i = 0; i < defaultConfigList.value.length; i++) {
      const configItem = defaultConfigList.value[i]

      // 检查执行配置
      if (configItem.execution) {
        // 如果该步骤禁用执行编辑且已有配置，则跳过
        if (isStepDisabled(i, true) && hasExecutionConfig(i)) {
          // console.log(`步骤 ${i} 禁用执行编辑且已配置，跳过应用默认配置`)
        } else {
          await applyConfigToForm(i, configItem.execution, true)
        }
      }

      // 检查通知配置
      if (configItem.notification) {
        // 如果该步骤禁用通知编辑且已有配置，则跳过
        if (isStepDisabled(i, false) && hasNotificationConfig(i)) {
          // console.log(`步骤 ${i} 禁用通知编辑且已配置，跳过应用默认配置`)
        } else {
          await applyConfigToForm(i, configItem.notification, false)
        }
      }
    }

    // 强制触发响应式更新
    emit('update:departments', [...props.departments])

    // console.log('默认配置应用完成，最终状态:', {
    //   departments: props.departments,
    //   executionList: executionList.value,
    //   notificationList: notificationList.value,
    //   executionPersons: Object.fromEntries(executionPersons.value),
    //   notificationPersons: Object.fromEntries(notificationPersons.value)
    // })

    ElMessage.success('已应用默认流程配置')
  } catch (error) {
    // console.error('应用默认配置失败:', error)
    ElMessage.error('应用默认配置失败')
  }
}

// 应用配置到表单
const applyConfigToForm = async (stepIndex: number, config: any, isExecution: boolean) => {
  try {
    // 检查配置是否为空或无效
    if (!config || !config.deptId || !config.userId) {
      // console.log(`步骤 ${stepIndex} 配置为空，跳过应用`)
      return
    }

    // console.log(`应用配置到步骤 ${stepIndex}, 执行类型: ${isExecution}`, config)

    const dept = props.departments[stepIndex]

    // 解析用户信息
    const userInfoArray: UserInfo[] = []
    if (config.userId && config.userName) {
      const userIds = config.userId.split(',').map((id: string) => Number(id.trim())).filter(Boolean)
      const userNames = config.userName.split(',').map((name: string) => name.trim()).filter(Boolean)

      userIds.forEach((userId: number, index: number) => {
        userInfoArray.push({
          id: userId,
          username: userNames[index] || `用户${userId}`,
          nickname: userNames[index] || `用户${userId}`,
          deptName: config.deptName || '未知部门',
          mobile: ''
        })
      })
    }

    // 如果解析后没有有效用户，跳过
    if (userInfoArray.length === 0) {
      // console.log(`步骤 ${stepIndex} 没有有效用户信息，跳过应用`)
      return
    }

    const baseConfig = {
      selectedDeptId: Number(config.deptId),
      selectedDeptName: config.deptName,
      deptName: config.deptName,
      personName: config.userName || '未设置',
      persons: userInfoArray,
      enableSms: config.enableSms === '1',
      smsTemplateId: config.smsTemplateId,
      smsContent: '',
      notifyType: config.notifyType || 'once',
      notifyPeriod: config.notifyPeriod || 'daily'
    }

    // 1. 更新部门对象
    if (isExecution) {
      dept.execution = { ...baseConfig }
    } else {
      dept.notification = { ...baseConfig }
    }

    // 2. 更新人员映射
    if (isExecution) {
      executionPersons.value.set(dept.id, [...userInfoArray])
    } else {
      notificationPersons.value.set(dept.id, [...userInfoArray])
    }

    // 3. 重要：同步更新列表
    if (isExecution) {
      syncExecutionList(stepIndex, baseConfig, userInfoArray)
    } else {
      syncNotificationList(stepIndex, baseConfig, userInfoArray)
    }

    // console.log(`步骤 ${stepIndex} 配置应用完成，部门信息:`, dept)
  } catch (error) {
    // console.error('应用配置到表单失败:', error)
  }
}

// 处理默认配置管理
const handleDefaultConfigManage = async () => {
  initDefaultConfigList()
  await loadDefaultConfig()
  defaultConfigDialog.visible = true
}

// 处理配置编辑
const handleConfigEdit = (index: number, type: 'execution' | 'notification') => {
  // 检查是否禁用编辑
  if (type === 'execution' && isStepDisabled(index, true)) {
    ElMessage.warning('该步骤的执行配置已禁用编辑')
    return
  }

  if (type === 'notification' && isStepDisabled(index, false)) {
    ElMessage.warning('该步骤的通知配置已禁用编辑')
    return
  }

  currentConfigIndex.value = index
  currentEditIndex.value = -1 // 重置普通编辑索引
  currentConfigType.value = type
  isExecutionList.value = type === 'execution'

  // 预填配置数据
  const configItem = defaultConfigList.value[index]
  const config = configItem[type]

  editForm.value = {
    id: 0,
    deptName: config?.deptName || '',
    personName: '',
    departmentId: 0,
    selectedDeptId: config?.deptId ? Number(config.deptId) : 0,
    enableSms: config?.enableSms === '1',
    smsTemplateId: config?.smsTemplateId || undefined,
    smsContent: '',
    notifyType: config?.notifyType || 'once',
    notifyPeriod: config?.notifyPeriod || 'daily',
    userIds: config?.userId ? config.userId.split(',') : []
  }

  // 设置已选人员
  if (config?.userId) {
    selectedPersonIds.value = config.userId.split(',')
    // 这里需要加载用户详情来显示已选人员
    loadSelectedPersonsForConfig(config.userId.split(','))
  } else {
    selectedPersonIds.value = []
    selectedPersons.value = []
  }

  // 如果选择了部门，加载部门人员
  if (config?.deptId) {
    loadDeptPersons(Number(config.deptId))
  }

  dialogVisible.value = true
}

// 为配置加载已选人员
const loadSelectedPersonsForConfig = async (userIds: string[]) => {
  try {
    const userDetails: UserInfo[] = []
    for (const userId of userIds) {
      try {
        const response = await UserAPI.getFormData(Number(userId))
        if (response) {
          userDetails.push({
            id: response.id || 0,
            username: response.username || '',
            nickname: response.nickname || '',
            deptName: '', // 从部门 API 获取部门名称
            mobile: response.mobile || ''
          })
        }
      } catch (error) {
        // console.warn(`加载用户详情失败 ID:${userId}`, error)
      }
    }
    selectedPersons.value = userDetails
  } catch (error) {
    // console.error('加载已选人员失败:', error)
  }
}

// 清空行配置
const handleClearRowConfig = (index: number) => {
  // 检查是否可以清空
  const canClearExecution = !isStepDisabled(index, true)
  const canClearNotification = !isStepDisabled(index, false)

  if (!canClearExecution && !canClearNotification) {
    ElMessage.warning('该步骤的配置已禁用编辑，无法清空')
    return
  }

  // 只清空允许编辑的配置
  if (canClearExecution) {
    defaultConfigList.value[index].execution = null
  }

  if (canClearNotification) {
    defaultConfigList.value[index].notification = null
  }

  const clearedTypes = []
  if (canClearExecution) clearedTypes.push('执行配置')
  if (canClearNotification) clearedTypes.push('通知配置')

  ElMessage.success(`${clearedTypes.join('和')}已清空`)
}

// 保存默认配置
const handleSaveDefaultConfig = async () => {
  try {
    const configForms: ReviewProcessConfigForm[] = []

    defaultConfigList.value.forEach((item, index) => {
      const deptType = props.deptTypeMap[index]

      // 添加执行配置
      if (item.execution) {
        configForms.push({
          executeDeptType: deptType,
          executeType: '1',
          deptId: item.execution.deptId,
          userId: item.execution.userId,
          enableSms: item.execution.enableSms,
          smsTemplateId: item.execution.smsTemplateId,
          notifyType: item.execution.notifyType,
          notifyPeriod: item.execution.notifyPeriod
        })
      }

      // 添加通知配置
      if (item.notification) {
        configForms.push({
          executeDeptType: deptType,
          executeType: '2',
          deptId: item.notification.deptId,
          userId: item.notification.userId,
          enableSms: item.notification.enableSms,
          smsTemplateId: item.notification.smsTemplateId,
          notifyType: item.notification.notifyType,
          notifyPeriod: item.notification.notifyPeriod
        })
      }
    })

    await ProcessConfigAPI.saveConfig(props.businessType, configForms)
    ElMessage.success('默认配置保存成功')
    defaultConfigDialog.visible = false
  } catch (error) {
    // console.error('保存默认配置失败:', error)
    ElMessage.error('保存默认配置失败')
  }
}

// 处理查看全部
const handleViewAll = () => {
  viewAllDialog.visible = true
}

// 处理编辑
const handleEdit = (index: number, isExecution: boolean) => {
  // 检查是否禁用编辑
  if (isExecution && isStepDisabled(index, true)) {
    ElMessage.warning('该步骤的执行配置已禁用编辑')
    return
  }

  if (!isExecution && isStepDisabled(index, false)) {
    ElMessage.warning('该步骤的通知配置已禁用编辑')
    return
  }

  currentEditIndex.value = index
  currentConfigIndex.value = -1 // 重置默认配置编辑索引
  isExecutionList.value = isExecution

  // 获取当前部门配置
  const dept = props.departments[index]
  const config = isExecution ? dept.execution : dept.notification
  const deptId = dept.id

  // 获取已选人员信息
  const selectedPersonsList = isExecution
    ? executionPersons.value.get(deptId) || []
    : notificationPersons.value.get(deptId) || []

  // console.log('编辑配置:', {
  //   index,
  //   isExecution,
  //   dept,
  //   config,
  //   selectedPersonsList
  // })

  // 预填表单数据
  editForm.value = {
    id: 0,
    deptName: config?.deptName || '',
    personName: config?.personName || '',
    departmentId: deptId,
    selectedDeptId: config?.selectedDeptId || 0,
    enableSms: config?.enableSms || false,
    smsTemplateId: config?.smsTemplateId || undefined,
    smsContent: config?.smsContent || '',
    notifyType: config?.notifyType || 'once',
    notifyPeriod: config?.notifyPeriod || 'daily',
    userIds: selectedPersonsList.map(p => p.id)
  }

  // 设置已选人员
  selectedPersons.value = [...selectedPersonsList]
  selectedPersonIds.value = selectedPersonsList.map(p => p.id)

  // 如果有选择的部门，加载部门人员选项
  if (config?.selectedDeptId) {
    loadDeptPersons(config.selectedDeptId)
  } else {
    personOptions.value = []
  }

  // 如果启用了短信且有模板ID，设置模板内容
  if (config?.enableSms && config?.smsTemplateId) {
    const template = props.smsTemplates.find(t => t.id === config.smsTemplateId)
    if (template) {
      editForm.value.smsContent = template.content
    }
  }

  dialogVisible.value = true
}

// 处理部门选择变更
const handleDeptSelectChange = async (value: number) => {
  // 找到选中的部门详情
  const findDeptName = (options: any[], id: number): string => {
    for (const option of options) {
      if (option.value === id) {
        return option.label
      }
      if (option.children?.length) {
        const found = findDeptName(option.children, id)
        if (found) return found
      }
    }
    return ''
  }

  const deptName = findDeptName(deptOptions.value, value)

  // 更新表单数据
  editForm.value.selectedDeptId = value
  editForm.value.deptName = deptName

  // 如果是编辑模式且部门没有变化，保持原有人员选择
  const isEditMode = currentEditIndex.value >= 0
  const deptChanged = value !== editForm.value.selectedDeptId

  // 只有在新建或部门确实变化时才清空人员选择
  if (!isEditMode || deptChanged) {
    selectedPersons.value = []
    selectedPersonIds.value = []
  }

  // 加载新部门的人员
  if (value) {
    await loadDeptPersons(value)

    // 编辑模式下，重新设置已选人员的选中状态
    if (isEditMode && !deptChanged && selectedPersons.value.length > 0) {
      selectedPersonIds.value = selectedPersons.value.map(p => p.id)
    }
  } else {
    personOptions.value = []
  }
}

// 加载部门下的人员
const loadDeptPersons = async (deptId: number) => {
  if (!deptId) return

  try {
    loadingPersons.value = true

    await loadDutyCache()

    const response = await UserAPI.getPage({
      deptId,
      pageNum: 1,
      pageSize: 100
    })

    if (response && response.list) {
      // console.log(response)
      const persons = response.list.map((user: UserPageVO) => ({
        id: user.id || 0,
        nickname: user.nickname || '',
        username: user.username || '',
        deptName: user.deptName || '',
        mobile: user.mobile || '',
        leaf: true,
        duty: getDutyText(user.duty || ''),
      }))
      personOptions.value = persons

      // console.log('加载部门人员完成:', {
      //   deptId,
      //   personCount: persons.length,
      //   currentSelectedPersons: selectedPersons.value
      // })
    } else {
      personOptions.value = []
    }
  } catch (error) {
    // console.error('加载部门人员失败:', error)
    ElMessage.error('加载部门人员失败')
    personOptions.value = []
  } finally {
    loadingPersons.value = false
  }
}

// 新增：重置编辑状态的方法
const resetEditState = () => {
  currentEditIndex.value = -1
  currentConfigIndex.value = -1
  selectedPersonIds.value = []
  selectedPersons.value = []
  editForm.value = {
    id: 0,
    deptName: '',
    personName: '',
    departmentId: 0,
    selectedDeptId: 0,
    enableSms: false,
    smsTemplateId: undefined,
    smsContent: '',
    notifyType: 'once',
    notifyPeriod: 'daily',
    userIds: []
  }
}

// 处理人员变更
const handlePersonChange = (value: (string | number)[]) => {
  // 确保是数组格式
  const ids = Array.isArray(value) ? value : [value].filter(Boolean)

  // console.log('人员选择变更:', {
  //   selectedIds: ids,
  //   isExecution: isExecutionList.value,
  //   currentPersonOptions: personOptions.value
  // })

  // 限制执行人员只能选一个
  if (isExecutionList.value && ids.length > 1) {
    // 只保留最后选择的一个
    const lastId = ids[ids.length - 1]
    selectedPersonIds.value = [lastId]
    ElMessage.warning('执行人员只能选择一人')
    return
  }

  // 更新选中ID列表
  selectedPersonIds.value = ids

  // 根据ID更新已选人员列表
  const newSelectedPersons: UserInfo[] = []
  ids.forEach(id => {
    // 首先在当前人员选项中查找
    let foundPerson = findPersonById(personOptions.value, id)

    // 如果在当前选项中找不到，尝试在已选人员中查找（编辑模式可能出现这种情况）
    if (!foundPerson && selectedPersons.value.length > 0) {
      foundPerson = selectedPersons.value.find(p => p.id === id) || null
    }

    if (foundPerson) {
      newSelectedPersons.push(foundPerson)
    } else {
      // console.warn(`未找到ID为 ${id} 的人员信息`)
    }
  })

  // 更新已选人员列表
  selectedPersons.value = newSelectedPersons

  // 更新表单中的userIds
  editForm.value.userIds = newSelectedPersons.map(p => p.id)

  // console.log('人员选择结果:', {
  //   selectedPersonIds: selectedPersonIds.value,
  //   selectedPersons: selectedPersons.value
  // })
}

// 递归查找人员
const findPersonById = (list: any[], id: string | number): UserInfo | null => {
  for (const item of list) {
    if (item.id === id) {
      return {
        id: item.id,
        username: item.username,
        nickname: item.nickname,
        deptName: item.deptName,
        mobile: item.mobile || ''
      }
    }
    if (item.children) {
      const found = findPersonById(item.children, id)
      if (found) return found
    }
  }
  return null
}

// 移除人员
const removePerson = (person: UserInfo) => {
  selectedPersonIds.value = selectedPersonIds.value.filter(id => id !== person.id)
  selectedPersons.value = selectedPersons.value.filter(p => p.id !== person.id)
}

// 处理模板变更
const handleTemplateChange = (templateId: number) => {
  const template = props.smsTemplates.find(t => t.id === templateId)
  if (template) {
    editForm.value.smsContent = template.content
  }
}

// 处理保存
const handleSave = () => {
  try {
    // 验证必填项
    if (!editForm.value.selectedDeptId) {
      ElMessage.warning('请选择部门')
      return
    }

    if (selectedPersons.value.length === 0) {
      ElMessage.warning('请选择人员')
      return
    }

    if (isExecutionList.value && selectedPersons.value.length > 1) {
      ElMessage.warning('执行人员只能选择一人')
      return
    }
    // 判断是否为默认配置编辑还是普通编辑
    if (currentConfigIndex.value >= 0) {
      // 默认配置编辑
      handleSaveDefaultConfigItem()
    } else if (currentEditIndex.value >= 0) {
      // 普通编辑
      handleSaveNormalConfig()
    } else {
      ElMessage.error('无效的编辑状态')
      return
    }

    // 关闭弹窗并重置状态
    dialogVisible.value = false
    resetEditState()
    ElMessage.success('保存成功')
  } catch (error) {
    // console.error('保存时出错:', error)
    ElMessage.error('保存失败')
  }
}

// 保存普通配置
const handleSaveNormalConfig = () => {
  const deptIndex = currentEditIndex.value
  if (deptIndex < 0 || deptIndex >= props.departments.length) {
    ElMessage.error('无效的部门索引')
    return
  }

  const dept = props.departments[deptIndex]
  const originalDeptId = dept.id
  // 确保 Map 对象存在
  if (!executionPersons.value) {
    executionPersons.value = new Map()
  }
  if (!notificationPersons.value) {
    notificationPersons.value = new Map()
  }
  const baseConfig = {
    selectedDeptId: editForm.value.selectedDeptId,
    selectedDeptName: editForm.value.deptName,
    deptName: editForm.value.deptName,
    personName: selectedPersons.value.map(p => p.nickname).join('、') || editForm.value.personName,
    persons: [...selectedPersons.value] || editForm.value.persons,
    enableSms: editForm.value.enableSms,
    smsTemplateId: editForm.value.smsTemplateId,
    smsContent: editForm.value.smsContent,
    notifyType: editForm.value.notifyType,
    notifyPeriod: editForm.value.notifyPeriod

  }

  console.log(baseConfig)

  // console.log('保存配置:', {
  //   deptIndex,
  //   originalDeptId,
  //   isExecutionList: isExecutionList.value,
  //   baseConfig,
  //   selectedPersons: selectedPersons.value
  // })

  // 根据是执行列表还是通知列表更新数据
  if (isExecutionList.value) {
    // 执行配置只能选一个人 - 修复：重命名局部变量避免冲突
    const finalExecutionPersons = selectedPersons.value.length > 0 ? [selectedPersons.value[0]] : []

    // 1. 更新部门对象
    dept.execution = {
      ...baseConfig,
      persons: finalExecutionPersons,
      personName: finalExecutionPersons.map(p => p.nickname).join('、')
    }

    // 2. 更新人员映射 - 使用正确的全局变量
    executionPersons.value.set(originalDeptId, finalExecutionPersons)

    // 3. 同步更新执行列表
    syncExecutionList(deptIndex, {
      ...baseConfig,
      personName: finalExecutionPersons.map(p => p.nickname).join('、')
    }, finalExecutionPersons)

    // console.log('执行配置已保存:', {
    //   dept: dept.execution,
    //   personsMap: executionPersons.value.get(originalDeptId),
    //   executionList: executionList.value
    // })

  } else {
    // 1. 更新部门对象
    dept.notification = { ...baseConfig }

    // 2. 更新人员映射 - 使用正确的全局变量
    notificationPersons.value.set(originalDeptId, [...selectedPersons.value])

    // 3. 同步更新通知列表
    syncNotificationList(deptIndex, baseConfig, selectedPersons.value)

    // console.log('通知配置已保存:', {
    //   dept: dept.notification,
    //   personsMap: notificationPersons.value.get(originalDeptId),
    //   notificationList: notificationList.value
    // })
  }

  // 触发更新
  emit('update:departments', [...props.departments])
  console.log('手动配置保存完成:', {
    department: dept,
    executionList: executionList.value,
    notificationList: notificationList.value,
    executionPersons: Object.fromEntries(executionPersons.value),
    notificationPersons: Object.fromEntries(notificationPersons.value)
  })
}

// 检查步骤是否禁用编辑
const isStepDisabled = (stepIndex: number, isExecution: boolean): boolean => {
  const config = props.stepConfigs?.[stepIndex]
  if (!config) return false

  return isExecution ? !!config.disableExecutionEdit : !!config.disableNotificationEdit
}

// 获取按钮文本
const getButtonText = (stepIndex: number, isExecution: boolean): string => {
  const config = props.stepConfigs?.[stepIndex]
  if (!config) return '编辑'

  if (isExecution && config.executionButtonText) {
    return config.executionButtonText
  }

  if (!isExecution && config.notificationButtonText) {
    return config.notificationButtonText
  }

  return '编辑'
}

// 检查步骤是否支持自动配置
const isAutoConfigEnabled = (stepIndex: number): boolean => {
  const config = props.stepConfigs?.[stepIndex]
  return !!config?.enableAutoConfig
}

// 修复：检查通知配置是否完成的逻辑
const hasNotificationConfig = (index: number): boolean => {
  if (index < 0 || index >= props.departments.length) return false

  const dept = props.departments[index]
  const deptId = dept.id

  // 检查是否有人员配置
  const hasPersonsInMap = notificationPersons.value.get(deptId)?.length > 0
  const hasPersonsInDept = dept.notification?.persons?.length > 0
  const hasDeptId = dept.notification?.selectedDeptId

  return !!(hasDeptId && (hasPersonsInMap || hasPersonsInDept))
}

// 修复：改进自动配置方法，确保数据同步
const autoConfigStep = async (stepIndex: number, config: {
  deptId: number
  deptName: string
  managerName: string
  managerInfo?: any
  asset?: any
}) => {
  try {
    // console.log(`开始自动配置步骤 ${stepIndex}:`, config)

    if (stepIndex < 0 || stepIndex >= props.departments.length) {
      // console.error('无效的步骤索引')
      return
    }

    const dept = props.departments[stepIndex]
    if (!dept) {
      // console.error('未找到对应步骤的部门配置')
      return
    }

    // 检查该步骤是否支持自动配置
    if (!isAutoConfigEnabled(stepIndex)) {
      // console.warn(`步骤 ${stepIndex} 不支持自动配置`)
      return
    }

    // 根据管理员姓名查找用户信息（如果没有提供managerInfo）
    let managerInfo = config.managerInfo
    if (!managerInfo && config.managerName && config.deptId) {
      // console.log(`查找用户: ${config.managerName} 在部门: ${config.deptId}`)
      managerInfo = await findUserByName(config.managerName, config.deptId)
      // console.log('查找到的用户信息:', managerInfo)
    }

    // 如果没有找到管理员信息，创建一个基本的用户信息
    if (!managerInfo && config.managerName) {
      // console.log('未找到用户详情，创建基本用户信息')
      managerInfo = {
        id: `auto_${Date.now()}`, // 生成一个临时ID
        username: config.managerName,
        nickname: config.managerName,
        deptName: config.deptName,
        mobile: ''
      }
    }

    // 准备用户信息数组
    const userInfoArray = managerInfo ? [managerInfo] : []

    // 确保我们有有效的用户信息
    if (userInfoArray.length === 0) {
      // console.warn(`步骤 ${stepIndex} 没有有效的用户信息，跳过自动配置`)
      ElMessage.warning(`无法为 ${config.deptName} 找到管理员 ${config.managerName}，请手动配置`)
      return
    }

    const baseConfig = {
      selectedDeptId: config.deptId,
      selectedDeptName: config.deptName,
      deptName: config.deptName,
      personName: config.managerName || '未设置',
      persons: userInfoArray,
      enableSms: false,
      smsTemplateId: undefined,
      smsContent: '',
      notifyType: 'once' as const,
      notifyPeriod: 'daily' as const
    }

    // console.log(`配置对象:`, baseConfig)
    // console.log(`用户信息数组:`, userInfoArray)

    // 1. 更新部门对象配置
    dept.execution = { ...baseConfig }
    dept.notification = { ...baseConfig }

    // console.log(`步骤 ${stepIndex} 部门对象已更新:`, dept)

    // 2. 更新人员映射
    executionPersons.value.set(dept.id, [...userInfoArray])
    notificationPersons.value.set(dept.id, [...userInfoArray])

    // console.log(`步骤 ${stepIndex} 人员映射已更新:`, {
    //   execution: executionPersons.value.get(dept.id),
    //   notification: notificationPersons.value.get(dept.id)
    // })

    // 3. 重要：同步更新执行和通知列表
    syncExecutionList(stepIndex, baseConfig, userInfoArray)
    syncNotificationList(stepIndex, baseConfig, userInfoArray)

    // 4. 强制触发响应式更新
    emit('update:departments', [...props.departments])

    // 5. 等待DOM更新后再次验证数据同步
    await nextTick()

    // 验证数据是否正确同步
    const hasExecConfig = hasExecutionConfig(stepIndex)
    // console.log(`步骤 ${stepIndex} 自动配置完成，执行配置状态:`, hasExecConfig)
    // console.log('部门对象:', dept)
    // console.log('执行人员映射:', executionPersons.value.get(dept.id))
    // console.log('执行列表项:', executionList.value.find(item => item.departmentId === dept.id))

    if (!hasExecConfig) {
      // console.warn(`步骤 ${stepIndex} 自动配置后仍显示未配置，强制重新同步`)
      // 强制重新同步
      await nextTick()
      syncDataToDepartments()

      // 再次验证
      setTimeout(() => {
        const finalCheck = hasExecutionConfig(stepIndex)
        // console.log(`最终验证结果:`, finalCheck)
        if (finalCheck) {
          ElMessage.success(`${config.deptName} 自动配置成功`)
        }
      }, 100)
    } else {
      ElMessage.success(`${config.deptName} 自动配置成功`)
    }

  } catch (error) {
    // console.error(`自动配置步骤 ${stepIndex} 失败:`, error)
    ElMessage.error(`自动配置 ${config.deptName} 失败`)
  }
}

const syncExecutionList = (stepIndex: number, config: any, userInfoArray: any[]) => {
  const dept = props.departments[stepIndex]
  const existingIndex = executionList.value.findIndex(item => item.departmentId === dept.id)

  const listItem = {
    id: dept.id,
    deptName: config.deptName,
    personName: config.personName,
    departmentId: dept.id,
    selectedDeptId: config.selectedDeptId,
    enableSms: config.enableSms || false,
    smsTemplateId: config.smsTemplateId,
    smsContent: config.smsContent || '',
    notifyType: config.notifyType || 'once',
    notifyPeriod: config.notifyPeriod || 'daily',
    userIds: userInfoArray.map(u => u.id)
  }

  if (existingIndex !== -1) {
    // 更新现有项
    executionList.value[existingIndex] = listItem
    // console.log(`更新执行列表项 - 步骤${stepIndex}:`, listItem)
  } else {
    // 添加新项
    executionList.value.push(listItem)
    // console.log(`添加执行列表项 - 步骤${stepIndex}:`, listItem)
  }

  // console.log('当前完整执行列表:', executionList.value)
}

const syncNotificationList = (stepIndex: number, config: any, userInfoArray: any[]) => {
  const dept = props.departments[stepIndex]
  const existingIndex = notificationList.value.findIndex(item => item.departmentId === dept.id)

  const listItem = {
    id: dept.id + 100, // 通知列表的ID规则
    deptName: config.deptName,
    personName: config.personName,
    departmentId: dept.id,
    selectedDeptId: config.selectedDeptId,
    enableSms: config.enableSms || false,
    smsTemplateId: config.smsTemplateId,
    smsContent: config.smsContent || '',
    notifyType: config.notifyType || 'once',
    notifyPeriod: config.notifyPeriod || 'daily',
    userIds: userInfoArray.map(u => u.id)
  }

  if (existingIndex !== -1) {
    // 更新现有项
    notificationList.value[existingIndex] = listItem
    // console.log(`更新通知列表项 - 步骤${stepIndex}:`, listItem)
  } else {
    // 添加新项
    notificationList.value.push(listItem)
    // console.log(`添加通知列表项 - 步骤${stepIndex}:`, listItem)
  }

  // console.log('当前完整通知列表:', notificationList.value)
}


// 新增：更新执行列表的辅助方法
const updateExecutionList = (stepIndex: number, config: any, userInfoArray: any[]) => {
  const dept = props.departments[stepIndex]
  const existingIndex = executionList.value.findIndex(item => item.departmentId === dept.id)

  const listItem = {
    id: dept.id,
    deptName: config.deptName,
    personName: config.personName,
    departmentId: dept.id,
    selectedDeptId: config.selectedDeptId,
    enableSms: config.enableSms,
    smsTemplateId: config.smsTemplateId,
    smsContent: config.smsContent,
    notifyType: config.notifyType,
    notifyPeriod: config.notifyPeriod,
    userIds: userInfoArray.map(u => u.id)
  }

  if (existingIndex !== -1) {
    executionList.value[existingIndex] = listItem
  } else {
    executionList.value.push(listItem)
  }
}

// 新增：更新通知列表的辅助方法
const updateNotificationList = (stepIndex: number, config: any, userInfoArray: any[]) => {
  const dept = props.departments[stepIndex]
  const existingIndex = notificationList.value.findIndex(item => item.departmentId === dept.id)

  const listItem = {
    id: dept.id + 100, // 通知列表的ID规则
    deptName: config.deptName,
    personName: config.personName,
    departmentId: dept.id,
    selectedDeptId: config.selectedDeptId,
    enableSms: config.enableSms,
    smsTemplateId: config.smsTemplateId,
    smsContent: config.smsContent,
    notifyType: config.notifyType,
    notifyPeriod: config.notifyPeriod,
    userIds: userInfoArray.map(u => u.id)
  }

  if (existingIndex !== -1) {
    notificationList.value[existingIndex] = listItem
  } else {
    notificationList.value.push(listItem)
  }
}

// 根据姓名和部门ID查找用户 - 保持不变
const findUserByName = async (userName: string, deptId: number): Promise<UserInfo | null> => {
  if (!userName || !deptId) return null

  try {
    // console.log(`尝试在部门 ${deptId} 中查找用户: ${userName}`)

    // 方法1: 直接在指定部门查找
    const response = await UserAPI.getPage({
      deptId,
      pageNum: 1,
      pageSize: 100
    })

    if (response && response.list) {
      // console.log(`部门 ${deptId} 中的用户列表:`, response.list.map(u => ({ id: u.id, nickname: u.nickname, username: u.username })))

      // 精确匹配用户名或昵称
      let matchedUser = response.list.find((user: UserPageVO) =>
        user.nickname === userName || user.username === userName
      )

      // 如果精确匹配没找到，尝试模糊匹配
      if (!matchedUser) {
        matchedUser = response.list.find((user: UserPageVO) =>
          user.nickname?.includes(userName) || user.username?.includes(userName) ||
          userName.includes(user.nickname || '') || userName.includes(user.username || '')
        )
      }

      if (matchedUser) {
        // console.log('找到匹配用户:', matchedUser)
        return {
          id: matchedUser.id || 0,
          username: matchedUser.username || '',
          nickname: matchedUser.nickname || '',
          deptName: matchedUser.deptName || '',
          mobile: matchedUser.mobile || ''
        }
      }
    }

    // 方法2: 如果在指定部门没找到，尝试全局搜索
    // console.log(`在部门 ${deptId} 中未找到用户 ${userName}，尝试全局搜索`)
    const globalResponse = await UserAPI.getPage({
      pageNum: 1,
      pageSize: 100,
      keywords: userName
    })

    if (globalResponse && globalResponse.list) {
      const matchedUser = globalResponse.list.find((user: UserPageVO) =>
        user.nickname === userName || user.username === userName
      )

      if (matchedUser) {
        // console.log('全局搜索找到用户:', matchedUser)
        return {
          id: matchedUser.id || 0,
          username: matchedUser.username || '',
          nickname: matchedUser.nickname || '',
          deptName: matchedUser.deptName || '',
          mobile: matchedUser.mobile || ''
        }
      }
    }

    // console.log(`未找到用户: ${userName}`)
    return null
  } catch (error) {
    // console.error('查找用户失败:', error)
    return null
  }
}

// 供外部调用的应用默认配置方法
const applyDefaultConfig = async () => {
  // console.log('开始应用默认配置（外部调用）')
  await handleLoadDefaultConfig()
}

// 暴露方法
defineExpose({
  getProcessData,
  getValidationStatus,
  initFromExistingData,
  reset,
  autoConfigStep,
  applyDefaultConfig
})

// 生命周期钩子
onMounted(() => {
  initData()
  DeptAPI.getOptions().then((data) => {
    deptOptions.value = data
  })
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    // 弹窗关闭时重置编辑状态
    setTimeout(() => {
      resetEditState()
    }, 200)
  }
})
</script>

<style scoped>
.process-group {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.el-button:disabled {
  color: var(--el-text-color-placeholder) !important;
  cursor: not-allowed !important;
  background-color: var(--el-fill-color-light) !important;
  border-color: var(--el-border-color-light) !important;
}

.el-button:disabled:hover {
  color: var(--el-text-color-placeholder) !important;
  background-color: var(--el-fill-color-light) !important;
  border-color: var(--el-border-color-light) !important;
}

.header-left {
  display: flex;
  gap: 10px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.info-cell {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 10px;
}

.info-group {
  flex: 1;
}

.info-item {
  margin-bottom: 8px;
  line-height: 20px;
}

.label {
  color: var(--el-text-color-regular);
  margin-right: 8px;
  font-weight: 500;
}

.value {
  color: var(--el-text-color-primary);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
}

.el-table {
  margin-top: 10px;
}

.person-select {
  display: flex;
  flex-direction: column;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 60px;
  margin-bottom: 10px;
  border-radius: 4px;
  border: 1px dashed var(--el-border-color);
  padding: 8px;
  background-color: var(--el-fill-color-light);
}

.selected-tags .el-empty {
  width: 100%;
  padding: 10px 0;
}

.person-limit-hint {
  margin-top: 5px;
  display: flex;
  justify-content: flex-end;
}

.person-count {
  color: var(--el-text-color-secondary);
  font-size: 13px;
}

.dept-select {
  display: flex;
  gap: 10px;
  align-items: center;
}

.dept-select .el-input {
  width: calc(100% - 100px);
}

.divider {
  margin: 16px 0;
}

.dept-info {
  margin-left: 10px;
}

.not-configured {
  opacity: 0.7;
}

.step-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.default-config-dialog .config-cell {
  padding: 8px;
  line-height: 1.6;
}

.default-config-dialog .config-cell>div {
  margin-bottom: 4px;
  font-size: 13px;
}

.default-config-dialog .config-cell .el-button {
  margin-top: 8px;
}
</style>
