<!-- 资产管理 -->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]" v-if="showDeptTree">
        <dept-tree v-model="queryParams.deptId" @node-click="handleQuery" class="mb-2" />
        <system-tree v-model="queryParams.systemId" @system-click="handleQuery" />
      </el-col>

      <!-- 资产列表 -->
      <el-col :lg="showDeptTree ? 20 : 24" :xs="24">
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="资产类型" prop="type">
              <el-select v-model="queryParams.type" placeholder="资产类型" clearable class="!w-[130px]">
                <el-option label="信息系统" :value="10" />
                <el-option label="服务器" :value="1" />
                <el-option label="安全设备" :value="3" />
                <el-option label="网络设备" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item label="资产名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="资产名称" clearable class="!max-w-[130px]" />
            </el-form-item>
            <el-form-item label="资产管理人" prop="managerId">
              <el-select v-model="queryParams.managerId" filterable clearable placeholder="请选择资产管理人">
                <el-option v-for="user in userList" :key="user.userId" :label="user.nickname" :value="user.userId" />
              </el-select>
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="资产ip" prop="ip">
              <el-input v-model="queryParams.ip" placeholder="资产ip" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="资产链接" prop="url">
              <el-input v-model="queryParams.url" placeholder="资产链接" clearable />
            </el-form-item>
            <!-- <el-form-item v-if="showAdvancedFilters" label="资产端口" prop="port">
              <el-input v-model="queryParams.port" placeholder="资产端口" clearable />
            </el-form-item> -->

            <el-form-item v-if="showAdvancedFilters" label="联系方式" prop="ownerPhone">
              <el-input v-model="queryParams.ownerPhone" placeholder="资产管理者手机号" clearable />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="操作系统" prop="os">
              <dictionary v-model="queryParams.os" code="os" class="!w-[150px]" />
            </el-form-item>
            <el-form-item v-if="showAdvancedFilters" label="登记时间" prop="createTime">
              <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleQuery()"><i-ep-search />搜索</el-button>
              <el-button @click="handleResetQuery()"><i-ep-refresh />重置</el-button>
            </el-form-item>
          </el-form>
          <!-- <el-button @click="toggleDeptTree" class="mb-2">
            {{ showDeptTree ? '隐藏部门树' : '显示部门树' }}
          </el-button> -->
          <el-button @click="toggleAdvancedFilters" class="mb-2">
            {{ showAdvancedFilters ? '隐藏高级筛选' : '显示高级筛选' }}
          </el-button>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <el-button v-hasPerm="['system:assets:delete']" type="success" :disabled="ids.length === 0"
                  @click="handleBatchEnable()">
                  <i-ep-check />批量启用
                </el-button>
                <el-button v-hasPerm="['system:assets:delete']" type="danger" :disabled="ids.length === 0"
                  @click="handleDelete()">
                  <i-ep-delete />批量删除
                </el-button>
              </div>
              <div>
                <el-button class="ml-3" @click="handleExport">
                  <template #icon><i-ep-download /></template>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading"
            :data="pageData" highlight-current-row border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <!-- <el-table-column align="center" sortable key="id" label="资产id" prop="id" min-width="100" /> -->
            <el-table-column prop="type" label="资产类型" width="120">
              <template #default="{ row }">
                <el-tag>{{ getAssetTypeName(row.type) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" key="name" label="资产名称" prop="name" min-width="120" />
            <!-- <el-table-column  align="center" key="ip" label="资产ip" prop="ip" min-width="150" /> -->
            <el-table-column align="center" key="url" label="地址/ip" min-width="150" show-overflow-tooltip>
              <template #default="scope">
                <span>{{ scope.row.type === 10 ? scope.row.url : scope.row.ip }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column align="center" key="port" label="资产端口" prop="port" min-width="80" /> -->
            <el-table-column align="center" key="deptName" label="管理部门" prop="deptName" min-width="150" />
            <el-table-column align="center" key="ownerName" label="管理员" prop="ownerName" min-width="100" />
            <el-table-column align="center" key="mobile" label="联系方式" prop="mobile" min-width="150" />
            <!-- <el-table-column  align="center" key="otherFactory" label="第三方运维单位" prop="otherFactory" min-width="150" /> -->
            <!-- <el-table-column  align="center" key="otherManager" label="运维人员" prop="otherManager" min-width="100" /> -->
            <!-- <el-table-column  align="center" key="otherContact" label="运维人员联系方式" prop="otherContact" min-width="150" /> -->
            <el-table-column align="center" key="sysName" label="资产所属系统名称" prop="sysName" min-width="150" />
            <el-table-column align="center" key="status" label="资产状态" prop="status" min-width="100">
              <template #default="scope">
                <el-tag :type="scope.row.status == '1' ? 'success' : (scope.row.status == '2' ? 'danger' : 'info')">
                  {{ scope.row.status == '1' ? '正常' : (scope.row.status == '2' ? '已下线' : '已下线') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" key="offlineTime" label="下线时间" prop="offlineTime" min-width="150" />
            <el-table-column align="center" key="createTime" label="登记时间" prop="createTime" min-width="150" />
            <!-- <el-table-column  align="center" key="os" label="系统名称及版本" prop="os" min-width="150"/> -->
            <!-- <el-table-column  align="center" key="notes" label="描述" prop="notes" min-width="200" /> -->
          </el-table>

          <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
        </el-card>
      </el-col>
    </el-row>
    <!-- 资产管理表单弹窗 -->
    <AssetsDialog v-model:visible="dialog.visible" :title="dialog.title" :id="dialog.id" @submitted="handleQuery" />

    <!-- 用户导入弹窗 -->
    <assetsImport v-model:visible="importDialogVisible" @import-success="handleOpenImportDialogSuccess" />

  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";

defineOptions({
  name: "assets",
  inheritAttrs: false,
});

// import assetsImport from "./components/assets-import.vue";
import assetsAPI, { assetsPageVO, assetsForm, assetsPageQuery } from "@/api/assets_management/details/assets";
import eventsAPI, { eventsPageVO, eventsForm, eventsPageQuery } from "@/api/work_management/critical";
import DeptAPI from "@/api/dept";
import UserAPI, { UserQuery } from "@/api/user";
// import AssetsDialog from "./components/assetsDialog.vue";
const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const importDialogVisible = ref(false);
const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);
const showDeptTree = ref(false);
const userList = ref<any[]>([])

const queryParams = reactive<assetsPageQuery>({
  pageNum: 1,
  pageSize: 10,
  status: "2",
});

const userParams = reactive<UserQuery>({});

const showAdvancedFilters = ref(false);
// 切换高级筛选
function toggleAdvancedFilters() {
  showAdvancedFilters.value = !showAdvancedFilters.value;
}
// 资产管理表格数据
const pageData = ref<assetsPageVO[]>([]);
const deptOptions = ref<any>([]);
// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
  id: undefined,
});
// 部门树显示状态
const toggleDeptTree = () => {
  showDeptTree.value = !showDeptTree.value;
};
// 资产管理表单数据
const formData = reactive<assetsForm>({});

/** 查询资产管理 */
function handleQuery() {
  loading.value = true;
  getEvents();
  assetsAPI.getPageAll(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 获取用户选项
const loadUserList = async () => {
  try {
    const data = await UserAPI.getList(userParams)
    userList.value = data
  } catch (error) {
  }
}

const getAssetTypeName = (type: number) => {
  const typeMap: Record<number, string> = {
    '-1': '未知类型',
    1: '服务器',
    3: '安全设备',
    2: '网络设备',
    4: '物联网设备',
    10: '信息系统'
  }
  return typeMap[type] || '未知类型'
}

/** 重置资产管理查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  Object.keys(queryParams).forEach(key => {
    queryParams[key] = undefined;
  });
  queryParams.pageNum = 1;
  queryParams.pageSize = 10;
  selectedEvent.value = undefined;

  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开资产管理弹窗 */
async function handleOpenDialog(id?: number) {
  dialog.visible = true
  dialog.id = id || undefined
  dialog.title = id ? '修改资产' : '新增资产'
}


/** 删除资产管理 */
function handleDelete(id?: number) {
  const removeId = [id || ids.value].join(",");
  if (!removeId) {
    ElMessage.warning("请勾选项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      assetsAPI.deleteByIds(removeId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

const selectedEvent = ref(undefined);
const reinsuranceEvents = ref([
  { id: 1, name: '事件1', assets: [] }
]);

//获取重保事件
function getEvents() {
  loading.value = true;
  eventsAPI.getPage(queryParams)
    .then((data) => {
      reinsuranceEvents.value = data.list.map((item: any) => {
        return {
          id: item.id,
          name: item.eventName,
          assets: item.assets?.split(',').map(Number) ?? []
        };
      });
    })
    .finally(() => {
      loading.value = false;
    });
}
// 添加新的响应式变量
const transferDialog = reactive({
  visible: false,
  allAssets: [] as { id: number; name: string }[],
  selectedAssets: [] as number[],
})

// 添加筛选方法
const filterMethod = (query: string, item: { id: number; name: string }) => {
  return item.name.toLowerCase().includes(query.toLowerCase())
}


function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleOpenImportDialogSuccess() {
  handleQuery();
}

function handleExport() {
  assetsAPI.exportAll(queryParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}

function handleBatchEnable() {
  if (ids.value.length === 0) {
    ElMessage.warning("请勾选要启用的资产");
    return;
  }

  ElMessageBox.confirm(
    `确认启用已选中的 ${ids.value.length} 项资产？启用后资产状态将变为"正常"`,
    "批量启用确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(
    () => {
      loading.value = true;
      assetsAPI.batchUpdateStatus(ids.value.join(","))
        .then(() => {
          ElMessage.success("批量启用成功");
          handleResetQuery();
        })
        .catch(() => {
          ElMessage.error("批量启用失败");
        })
        .finally(() => {
          loading.value = false;
        });
    },
    () => {
      ElMessage.info("已取消批量启用");
    }
  );
}


onMounted(() => {
  handleQuery();
  loadUserList();
});
</script>
