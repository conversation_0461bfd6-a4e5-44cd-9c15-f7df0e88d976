import request from "@/utils/request";

const SAFETY_BASE_URL = "/api/v1/safetys";

class safetyAPI {
    /** 获取安全问题管理分页数据 */
    static getPage(queryParams?: safetyPageQuery) {
        return request<any, PageResult<safetyPageVO[]>>({
            url: `${SAFETY_BASE_URL}/page`,
            method: "get",
            params: queryParams,
        });
    }
    /**
     * 获取安全问题管理表单数据
     *
     * @param id safetyID
     * @returns safety表单数据
     */
    static getFormData(id: number) {
        return request<any, safetyForm>({
            url: `${SAFETY_BASE_URL}/${id}/form`,
            method: "get",
        });
    }

    /** 添加安全问题管理*/
    static add(data: safetyForm) {
        return request({
            url: `${SAFETY_BASE_URL}`,
            method: "post",
            data: data,
        });
    }

    /**
     * 更新安全问题管理
     *
     * @param id safetyID
     * @param data safety表单数据
     */
    static update(id: number, data: safetyForm) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}`,
            method: "put",
            data: data,
        });
    }

    /**
     * 批量删除安全问题管理，多个以英文逗号(,)分割
     *
     * @param ids 安全问题管理ID字符串，多个以英文逗号(,)分割
     */
    static deleteByIds(ids: string) {
        return request({
            url: `${SAFETY_BASE_URL}/${ids}`,
            method: "delete",
        });
    }

    /**
     * 获取安全步骤状态
     * @param id
     * @returns
     * @constructor
     * */
    static getStepStatus(id: number) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/satus`,
            method: "get",
        });
    }

    /**
     * 提交审核
     */
    static auditTicket(id: number, data: object) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/audit1`,
            method: "put",
            data,
        });
    }

    /**
     * 获取文件列表
     */
    static getFileList(id: number) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/fileList`,
            method: "get",
        });
    }

    /**
     * 漏洞整改提交
     */
    static submitFix(id: number, data: object) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/fix`,
            method: "post",
            data,
        });
      }

          /**
     * 获取漏洞整改信息
     */
    static getVulns(id: number) {
      return request({
          url: `${SAFETY_BASE_URL}/${id}/selectVulns`,
          method: "get",
      });
  }

    /**
     * 漏洞复核提交
     */
    static submitVulns(id: number, data: object) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/submitVulns`,
            method: "post",
            data,
        });
    }

    /**
     * 漏洞整改评价
     */
    static evaluateVulns(id: number, data: object) {
        return request({
            url: `${SAFETY_BASE_URL}/${id}/commentsVulns`,
            method: "post",
            data,
        });
    }

    /**
     * 安全流转信息
     * /api/v1/safetys/reviewProcess/{id}
     */
    static getFlowInfo(id: number) {
        return request({
            url: `${SAFETY_BASE_URL}/reviewProcess/${id}`,
            method: "get",
        });
    }


    /**
     * 切换漏洞修复状态
    */
    static switchFixStatus(id: string,isFix: number) {
        return request({
            url: `/api/v1/vulnss/${id}/switch`,
            method: "post",
            data: { isFix }
        });
    }




        /** 
   * 导出
   * @param {PageQuery} queryParams - 查询参数
  */
        static export(queryParams: PageQuery) {
          return request({
            url: `${SAFETY_BASE_URL}/export`,
            method: "get",
            params: queryParams,
            responseType: "arraybuffer",
          });
        }
     /**
     * 获取安全工程师配置数据
     * /api/v1/safetys/getSafetyEngineerConfig
     */
    static getSafetyEngineerConfig(queryParams:{}) {
        return request({
            url: `${SAFETY_BASE_URL}/getSafetyEngineerConfig`,
            method: "get",
            params:queryParams
        });
    }
    /**
     * 新增安全工程师配置
     * /api/v1/safetys/saveSafetyEngineerConfig
     */
    static saveSafetyEngineerConfig(data: any) {
        return request({
            url: `${SAFETY_BASE_URL}/saveSafetyEngineerConfig`,
            method: "post",
            data:data
        });
    }
    /**
     * 修改安全工程师配置
     * /api/v1/safetys/updateSafetyEngineerConfig/{id}
     */
    static updateSafetyEngineerConfig(id: any,data:any) {
        return request({
            url: `${SAFETY_BASE_URL}/updateSafetyEngineerConfig/${id}`,
            method: "post",
            data:data,
        });
    }
    /**
     * 安全问题管理列表页面数据统计
     * /api/v1/safetys/pageCount
     */
    static getpageCount(queryParams:{}) {
        return request({
            url: `${SAFETY_BASE_URL}/pageCount`,
            method: "get",
            params:queryParams
        });
    }
}

export default safetyAPI;

/** 安全问题管理分页查询参数 */
export interface safetyPageQuery extends PageQuery {
    /** 安全问题工单id */
    id?: string;
    /** 标题 */
    title?: string;
    /** 申请人 */
    applicant?: number;
    /** 工单概述内容 */
    content?: string;
    /** 工单提交时间 */
    createTime?: [string, string];
    /** 要求整改时间 */
    deadline?: [string, string];
    /** 申请人部门id */
    deptId?: number;
    /** 未通过步骤 */
    errStep?: number;
    /** 文件列表 */
    fileList?: string;
    /** 手机号 */
    mobile?: string;
    /** 漏洞来源 */
    loopholeSource?: string;
    /** 状态 */
    status?: any;
    /** 当前步骤 */
    step?: number;
    /** 系统id */
    systemId?: number;
    /** 工单更新时间 */
    updateTime?: Date;
}

/** 安全问题管理表单对象 */
export interface safetyForm {
    /** 安全问题工单id */
    id?:  string;
    /** 标题 */
    title?:  string;
    /** 申请人 */
    applicant?:  number;
    /** 工单概述内容 */
    content?:  string;
    /** 工单提交时间 */
    createTime?:  Date;
    /** 要求整改时间 */
    deadline?:  Date;
    /** 申请人部门id */
    deptId?:  number;
    /** 未通过步骤 */
    errStep?:  number;
    /** 文件列表 */
    fileIds?:  any;
    /** 手机号 */
    mobile?:  string;
    /** 漏洞来源 */
    loopholeSource?:  string;
    assetIds? :number[]
    /** 状态 */
    status?:  number;
    /** 当前步骤 */
    step?:  number;
    /** 系统id */
    systemId?:  number;
    /** 工单更新时间 */
    updateTime?:  string;
    /** 漏洞表单信息 */
    vulns?: VulnerabilityForm[];
    /** 部门执行表单信息 */
    reviewProcessForms?: ReviewProcessForm[];
    /**  */
    remarks?: string;
    /** 申请人 */
    employeeId?: string;
    comments?: [];
    executionList?: any;
    notificationList?: any;
    executionPersons?: any;
    notificationPersons?: any;
    fileList?: any;
    assetsList?: any;
    safetyResult?: any;
    engineerId:any;
    engineerName:string;
    engineerMobile:any;
    engineerWechat:any;
    engineerQq:any;
    engineerEmail:string;
}

/** 安全问题管理分页对象 */
export interface safetyPageVO {
    /** 安全问题工单id */
    id?: string;
    /** 标题 */
    title?: string;
    /** 申请人 */
    applicant?: number;
    /** 工单概述内容 */
    content?: string;
    /** 工单提交时间 */
    createTime?: Date;
    /** 要求整改时间 */
    deadline?: Date;
    /** 申请人部门id */
    deptId?: number;
    /** 未通过步骤 */
    errStep?: number;
    /** 文件列表 */
    fileList?: string;
    /** 手机号 */
    mobile?: string;
    /** 漏洞来源 */
    loopholeSource?: string;
    /** 状态 */
    status?: number;
    /** 当前步骤 */
    step?: number;
    /** 系统id */
    systemId?: number;
    /** 工单更新时间 */
    updateTime?: Date;
}


/** 
 * 漏洞表单信息
*/
export interface VulnerabilityForm {
  id: string;
  name?: string;
  level?: string;
  ip?: string;
  url?: string;
  remark?: string;
  fix?: string;
  createTime?: string;
  updateTime?: string;
}

/** 审核流程表单对象 */
export interface ReviewProcessForm {
  /** Id */
  id?:  bigint;
  /** 业务类型（safety安全漏洞） */
  businessType?:  string;
  /** 业务Id */
  businessId?:  bigint;
  /** 执行部门类型（安全漏洞：（2工单审核 3工单整改 4工单复核 5工单评价）安全评估：（2工单审核 3安全评估 4工单整改 5工单复核 6工单评价）） */
  executeDeptType?:  string;
  /** 执行类型（1执行 2通知） */
  executeType?:  string;
  /** 部门Id */
  deptId?:  bigint;
  /** 用户Id */
  userId?:  bigint;
  /** 启用短信通知(0不启用 1启用) */
  enableSms?:  string;
  /** 短信模板Id */
  smsTemplateId?: number;
  /** 启用短信类型(once单次通知 periodic定期通知) */
  notifyType?:  string;
  /** 通知周期(daily每天 weekly每周 monthly每月 quarterly每季度) */
  notifyPeriod?:  string;
}
