// mock/base.ts
import path from "path";
import { createDefineMock } from "vite-plugin-mock-dev-server";
var defineMock = createDefineMock((mock) => {
  mock.url = path.join(
    "/dev-api/api/v1/",
    mock.url
  );
});

// mock/user.mock.ts
var user_mock_default = defineMock([
  {
    url: "users/me",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        userId: 2,
        nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
        username: "admin",
        avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
        roles: ["ROOT"],
        perms: [
          "sys:menu:delete",
          "sys:dept:edit",
          "sys:dict_type:add",
          "sys:dict:edit",
          "sys:dict:delete",
          "sys:dict_type:edit",
          "sys:menu:add",
          "sys:user:add",
          "sys:role:edit",
          "sys:dept:delete",
          "sys:user:edit",
          "sys:user:delete",
          "sys:user:password:reset",
          "sys:dept:add",
          "sys:role:delete",
          "sys:dict_type:delete",
          "sys:menu:edit",
          "sys:dict:add",
          "sys:role:add",
          "sys:user:query",
          "sys:user:export"
        ]
      },
      msg: "\u4E00\u5207ok"
    }
  },
  {
    url: "users/page",
    method: ["GET"],
    body: {
      code: "00000",
      data: {
        list: [
          {
            id: 2,
            username: "admin",
            nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
            mobile: "17621210366",
            gender: 1,
            avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
            email: "",
            status: 1,
            deptId: 1,
            roleIds: [2]
          },
          {
            id: 3,
            username: "test",
            nickname: "\u6D4B\u8BD5\u5C0F\u7528\u6237",
            mobile: "17621210366",
            gender: 1,
            avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
            email: "<EMAIL>",
            status: 1,
            deptId: 3,
            roleIds: [3]
          }
        ],
        total: 2
      },
      msg: "\u4E00\u5207ok"
    }
  },
  // 新增用户
  {
    url: "users",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u65B0\u589E\u7528\u6237" + body.nickname + "\u6210\u529F"
      };
    }
  },
  // 获取用户表单数据
  {
    url: "users/:userId/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: userMap[params.userId],
        msg: "\u4E00\u5207ok"
      };
    }
  },
  // 修改用户
  {
    url: "users/:userId",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "\u4FEE\u6539\u7528\u6237" + body.nickname + "\u6210\u529F"
      };
    }
  },
  // 删除用户
  {
    url: "users/:userId",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "\u5220\u9664\u7528\u6237" + params.id + "\u6210\u529F"
      };
    }
  },
  // 重置密码
  {
    url: "users/:userId/password",
    method: ["PATCH"],
    body({ query }) {
      return {
        code: "00000",
        data: null,
        msg: "\u91CD\u7F6E\u5BC6\u7801\u6210\u529F\uFF0C\u65B0\u5BC6\u7801\u4E3A\uFF1A" + query.password
      };
    }
  },
  // 导出Excel
  {
    url: "users/_export",
    method: ["GET"],
    headers: {
      "Content-Disposition": "attachment; filename=%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8.xlsx",
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    }
  }
]);
var userMap = {
  2: {
    id: 2,
    username: "admin",
    nickname: "\u7CFB\u7EDF\u7BA1\u7406\u5458",
    mobile: "17621210366",
    gender: 1,
    avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
    email: "",
    status: 1,
    deptId: 1,
    roleIds: [2]
  },
  3: {
    id: 3,
    username: "test",
    nickname: "\u6D4B\u8BD5\u5C0F\u7528\u6237",
    mobile: "17621210366",
    gender: 1,
    avatar: "https://oss.youlai.tech/youlai-boot/2023/05/16/811270ef31f548af9cffc026dfc3777b.gif",
    email: "<EMAIL>",
    status: 1,
    deptId: 3,
    roleIds: [3]
  }
};
export {
  user_mock_default as default
};
