<template>
  <div class="my-inventory-tasks-container">
<!--    <div class="page-header">-->
<!--      <h2>我的盘点任务</h2>-->
<!--      <div class="header-actions">-->
<!--        <el-button type="success" @click="refreshTasks">-->
<!--          <el-icon>-->
<!--            <Refresh />-->
<!--          </el-icon>-->
<!--          刷新任务-->
<!--        </el-button>-->
<!--        <el-button type="primary" @click="handleAddTask">-->
<!--          <el-icon>-->
<!--            <Plus />-->
<!--          </el-icon>-->
<!--          新建任务-->
<!--        </el-button>-->
<!--      </div>-->
<!--    </div>-->
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">任务数据</div>
            <div class="stat-value text-2xl font-bold text-blue-600">
              {{ totalCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-blue-500"><Calendar /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">进行中</div>
            <div class="stat-value text-2xl font-bold text-orange-600">
              {{ inProgressCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-orange-500"><Clock /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">逾期未完成</div>
            <div class="stat-value text-2xl font-bold text-red-600">
              {{ completedCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-green-500"><Clock /></el-icon>
        </div>
      </el-card>

      <el-card class="stat-card" shadow="hover">
        <div class="flex items-center justify-between">
          <div>
            <div class="stat-title text-gray-600">已完成</div>
            <div class="stat-value text-2xl font-bold text-green-600">
              {{ overdueCount }}
            </div>
          </div>
          <el-icon class="text-4xl text-red-500"><Check /></el-icon>
        </div>
      </el-card>
    </div>

    <!-- 搜索过滤部分 -->
<!--    <el-card class="filter-card">-->
<!--      <div class="filter-container">-->
<!--        <el-form :model="queryParams" ref="queryRef" :inline="true">-->
<!--          <el-form-item label="任务名称" prop="inventoryName">-->
<!--            <el-input-->
<!--              v-model="queryParams.inventoryName"-->
<!--              placeholder="请输入任务名称"-->
<!--              clearable-->
<!--            />-->
<!--          </el-form-item>-->
<!--          <el-form-item label="盘点状态" prop="status">-->
<!--            <el-select-->
<!--              v-model="queryParams.status"-->
<!--              style="width: 180px"-->
<!--              placeholder="请选择盘点状态"-->
<!--              clearable-->
<!--            >-->
<!--              <el-option label="待进行" value="0" />-->
<!--              <el-option label="进行中" value="1" />-->
<!--              <el-option label="已完成" value="2" />-->
<!--              <el-option label="逾期完成" value="3" />-->
<!--              <el-option label="逾期未完成" value="4" />-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item>-->
<!--            <el-button type="primary" @click="handleQuery">搜索</el-button>-->
<!--            <el-button @click="resetQuery">重置</el-button>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--      </div>-->
<!--    </el-card>-->
    <el-card class="mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex flex-1 gap-2">
          <!-- Search input for multiple fields -->
          <el-input
            v-model="queryParams.keyword"
            placeholder="搜索任务名称、发起部门、发起人员..."
            class="flex-1"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <!-- Search button -->
          <el-button
            type="primary"
            class="flex items-center gap-2"
            @click="handleQuery"
          >
            <el-icon><Search /></el-icon>
            <span>搜索</span>
          </el-button>

          <!-- Reset button -->
          <el-button class="flex items-center gap-2" @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            <span>重置</span>
          </el-button>

          <!-- Filter toggle button -->
          <el-button
            class="flex items-center gap-2 ml-auto"
            @click="toggleAdvancedFilters"
          >
            <el-icon><Filter /></el-icon>
            <span>筛选</span>
            <el-icon
              class="transition-transform duration-300"
              :class="{ 'rotate-180': showAdvancedFilters }"
            >
              <ArrowDown />
            </el-icon>
          </el-button>
        </div>
      </div>
    </el-card>
    <!-- 高级筛选 -->
    <el-card v-if="showAdvancedFilters" class="mt-4" shadow="never">
      <div class="advanced-filters">
        <el-form :model="queryParams" ref="queryRef" :inline="true">

          <el-form-item label="发起人员" prop="createUserName">
            <el-input
              v-model="queryParams.createUserName"
              placeholder="请输入发起人员姓名"
              clearable
            />
          </el-form-item>

          <el-form-item label="发起部门" prop="createDeptName">
            <el-input
              v-model="queryParams.createDeptName"
              placeholder="请输入发起部门名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="queryParams.startDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <!-- 应用筛选按钮 -->
          <el-form-item class="apply-filters">
            <el-button
              type="primary"
              @click="applyAdvancedFilters"
              :icon="Search"
            >
              应用筛选
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <!-- 任务列表 - 使用表格形式展示 -->
    <el-card v-loading="loading" class="tasks-list-card" shadow="never">
      <template #header>
        <div class="flex-x-between">
          <div>
          </div>
          <div>
            <el-button type="primary" size="large" @click="refreshTasks">
              <el-icon><Refresh /></el-icon>
              刷新任务
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="tasksList.length === 0" class="empty-tasks">
        <el-empty description="暂无盘点任务" />
      </div>

      <el-table v-else :data="tasksList" stripe border style="width: 100%">
        <el-table-column
          key="id"
          label="序号"
          prop="taskId"
          min-width="80"
          align="center"
        />
        <!-- 状态 -->
        <el-table-column
          prop="inventoryStatus"
          label="状态"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.inventoryStatus)"
              size="small"
            >
              {{ getStatusText(scope.row.inventoryStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- 任务名称 -->
        <el-table-column
          prop="taskName"
          label="任务名称"
          min-width="160"
          show-overflow-tooltip
          align="center"
        >
          <template #default="scope">
            <div class="task-name-cell">
              <span class="task-name-text font-bold">{{
                  scope.row.taskName
                }}</span>
            </div>
          </template>
        </el-table-column>

        <!-- 创建时间 -->
        <el-table-column
          prop="createTime"
          label="开始时间"
          width="160"
          align="center"
        >
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>

        <!-- 截止时间 -->
        <el-table-column
          prop="deadline"
          label="截止时间"
          width="100"
          align="center"
        />

        <!-- 发起部门 -->
        <el-table-column
          prop="deptName"
          label="发起部门"
          width="140"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          key="username"
          label="发起人员"
          min-width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div style="line-height: 1.5;">
              <div>{{ scope.row.username }}</div>
              <div>{{ scope.row.phone || '15639000055' }}</div>
            </div>
          </template>
        </el-table-column>
        <!-- 资产数量 -->
<!--        <el-table-column-->
<!--          prop="assetCount"-->
<!--          label="待盘点资产数量"-->
<!--          width="120"-->
<!--          align="center"-->
<!--        >-->
<!--          <template #default="scope">-->
<!--            <span>{{ scope.row.assetCount || 0 }} 个</span>-->
<!--          </template>-->
<!--        </el-table-column>-->

        <!-- 完成进度 -->
        <!-- 修改盘点进度列 -->
        <el-table-column
          key="completionRate"
          label="盘点进度"
          min-width="300"
          align="center"
        >
          <template #default="scope">
            <div class="inventory-progress">
              <!-- 进度统计 -->
              <div class="progress-stats mb-2">
                <div class="stats-item">
                  <span class="label">总资产:</span>
                  <span class="value text-blue-600"
                  >{{ scope.row.assetCount }}个</span
                  >
                </div>
                <div class="stats-item">
                  <span class="label">已盘点:</span>
                  <span class="value text-green-600"
                  >{{ scope.row.inventoryAssetsNum }}个</span
                  >
                </div>
                <div class="stats-item">
                  <span class="label">待盘点:</span>
                  <span class="value text-orange-600"
                  >{{
                      scope.row.assetCount - scope.row.inventoryAssetsNum
                    }}个</span
                  >
                </div>
              </div>

              <!-- 进度条 -->
              <div class="progress-bar-wrapper">
                <div class="progress-bar bg-gray-100 rounded-full">
                  <div
                    class="progress-inner rounded-full transition-all duration-300"
                    :class="{
                      'bg-red-500': scope.row.completionRate < 30,
                      'bg-yellow-500':
                        scope.row.completionRate >= 30 &&
                        scope.row.completionRate < 70,
                      'bg-green-500': scope.row.completionRate >= 70,
                    }"
                    :style="{
                      width: `${scope.row.completionRate}%`,
                    }"
                  ></div>
                </div>
                <span class="progress-text"
                >{{ scope.row.completionRate }}%</span
                >
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 操作栏 -->
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="success"
              size="small"
              @click="showTaskInfoDialog(scope.row)"
            >
              <i-ep-view class="mr-1" />
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="showInventoryDialog(scope.row)"
              :disabled="scope.row.inventoryStatus === '2'"
            >
              <i-ep-edit class="mr-1" />
              {{ getButtonText(scope.row.inventoryStatus) }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <inventory-task-info
        v-model:visible="taskInfoDialogVisible"
        :taskId="currentTaskId"
        :taskData="currentTaskData"
      />
      <!-- 盘点任务详情弹窗 -->
      <inventory-task-dialog
        v-model:visible="inventoryDialogVisible"
        :taskId="currentTaskId"
        @submit-success="handleInventorySubmitSuccess"
      />

      <!-- 分页组件 -->
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getInventoryTasks"
        class="pagination-container"
      />
    </el-card>

    <!--    新建盘点任务组件-->
    <inventory-add-dialog
      v-model:visible="addDialogVisible"
      :title="'新增盘点任务'"
      @submitted="handleAddTaskSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// 日期格式化函数
const formatDate = (dateStr: string) => {
  if (!dateStr) return "";
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return dateStr;
  const y = d.getFullYear();
  const m = String(d.getMonth() + 1).padStart(2, "0");
  const day = String(d.getDate()).padStart(2, "0");
  return `${y}-${m}-${day}`;
};
import { ref, reactive, onMounted, computed } from "vue"; // 添加 computed
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import {
  Document,
  Clock,
  Warning,
  Check,
  Refresh,
  Search, Filter, ArrowDown
} from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import Pagination from "@/components/Pagination/index.vue";
import InventoryTaskDialog from "./components/InventoryTaskDialog.vue";
import InventoryAddDialog from './components/InventoryAddDialog.vue';
import InventoryAPI, {
  InventoryPageQuery,
} from "@/api/assets_management/assets_inventory/index";
import InventoryTaskInfo from "./components/InventoryTaskInfo.vue";

const router = useRouter();

// 页面状态
const loading = ref(false);
const queryRef = ref<FormInstance>();
const total = ref(0);
const taskInfoDialogVisible = ref(false);
// 新增任务弹窗状态
const addDialogVisible = ref(false)

// 查询参数与API一致
const queryParams = reactive<InventoryPageQuery>({
  pageNum: 1,
  pageSize: 10,
  inventoryName: "",
  status: "",
});

// 定义任务类型接口
interface TaskItem {
  taskId: number | string;
  taskName: string;
  inventoryStatus: string;
  createTime: string;
  deadline: string;
  deptName: string;
  assetCount: number;
  completionRate: number;
  username: string;
}

// 引入弹窗相关状态
const inventoryDialogVisible = ref(false);
const currentTaskId = ref("");
const currentTaskData = ref<TaskItem | null>(null);

const showAdvancedFilters = ref(false);
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
};
// 显示盘点弹窗
const showInventoryDialog = (task: TaskItem) => {
  currentTaskId.value = task.taskId as string;
  currentTaskData.value = task;
  inventoryDialogVisible.value = true;
};
// 处理新增任务成功回调
const handleAddTaskSuccess = () => {
  ElMessage.success('新增盘点任务成功')
  getInventoryTasks()
  getInventoryTasksNoPage()
}
// 显示任务详情弹窗
const showTaskInfoDialog = (task) => {
  currentTaskId.value = task.taskId as string;
  currentTaskData.value = task;
  taskInfoDialogVisible.value = true;
};
// 添加一个存储所有数据的响应式变量
const allTasksList = ref<TaskItem[]>([]);

// 修改统计相关的计算属性，使用 allTasksList 而不是 tasksList
const totalCount = computed(() => {
  return allTasksList.value.length;
});

const inProgressCount = computed(() => {
  return allTasksList.value.filter((task) => task.inventoryStatus === "1")
    .length;
});

const overdueCount = computed(() => {
  return allTasksList.value.filter((task) => {
    const now = new Date();
    const deadline = task.deadline ? new Date(task.deadline) : null;
    return task.inventoryStatus === "1" && deadline && now > deadline;
  }).length;
});

const completedCount = computed(() => {
  return allTasksList.value.filter((task) => task.inventoryStatus === "2")
    .length;
});

// 处理盘点提交成功
const handleInventorySubmitSuccess = ({
                                        taskId,
                                        completionRate,
                                        inventoryStatus,
                                      }) => {
  // 更新任务列表中相应任务的完成率和状态
  const taskIndex = tasksList.value.findIndex((t) => t.taskId === taskId);
  if (taskIndex !== -1) {
    tasksList.value[taskIndex].completionRate = completionRate;
    tasksList.value[taskIndex].inventoryStatus = inventoryStatus;

    // 如果完成率100%且状态变为已完成，显示完成提示
    if (completionRate === 100 && inventoryStatus === "2") {
      ElMessage.success(
        `任务「${tasksList.value[taskIndex].taskName}」已完成盘点!`
      );
    }
  }
};

// 获取盘点任务列表数据
const tasksList = ref<TaskItem[]>([]);

// 获取盘点状态类型 - 根据API实际返回状态值映射
const getStatusType = (status: string | number) => {
  const statusMap: Record<string, string> = {
    "0": "info", // 待进行
    "1": "primary", // 进行中
    "2": "success", // 已完成
    "3": "warning", // 逾期完成
    "4": "danger", // 逾期未完成
  };
  return statusMap[String(status)] || "info";
};

// 获取盘点状态文本 - 根据API实际返回状态值映射
const getStatusText = (status: string | number) => {
  const statusMap: Record<string, string> = {
    "0": "待进行",
    "1": "进行中",
    "2": "已完成",
    "3": "逾期完成",
    "4": "逾期未完成",
  };
  return statusMap[String(status)] || "未知状态";
};

// 获取进度条状态
const getProgressStatus = (percentage: number) => {
  if (percentage >= 100) return "success";
  if (percentage > 0) return "";
  return "exception";
};

// 获取按钮文本
const getButtonText = (status: string | number) => {
  const textMap: Record<string, string> = {
    "0": "开始盘点",
    "1": "继续",
    "2": "已完成",
  };
  return textMap[String(status)] || "开始盘点";
};

// 获取用户的盘点任务列表 - 使用API调用
const getInventoryTasks = async () => {
  loading.value = true;

  try {
    const response = await InventoryAPI.getMyInventoryTasks(queryParams);
    console.log("获取盘点任务列表:", response);
    if (response && response.list) {
      // 将API返回的数据映射到页面需要的格式
      tasksList.value = response.list.map((task) => ({
        taskId: task.id,
        taskName: task.inventoryName || "",
        inventoryStatus: task.status || "0",
        createTime: task.createTime || "",
        deadline: task.deadline || "",
        deptName: task.createDeptName || "",
        username: task.createUserName || "",
        assetCount: task.treatInventoryNum || 0,
        completionRate: task.inventoryProgress || 0,
        inventoryAssetsNum: task.inventoryAssetsNum || 0,
        assetsNum: task.assetsNum || 0,
      }));

      total.value = response.total || 0;
    } else {
      tasksList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("获取任务列表失败:", error);
    ElMessage.error("获取盘点任务列表失败");
    tasksList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};
// 修改获取任务列表的方法
const getInventoryTasksNoPage = async () => {
  loading.value = true;

  try {
    // 同时获取分页数据和所有数据
    const [pageResponse, allResponse] = await Promise.all([
      // 获取分页数据
      InventoryAPI.getMyInventoryTasks(queryParams),
      // 获取所有数据
      InventoryAPI.getMyInventoryTasks({
        pageNum: 1,
        pageSize: 99999, // 设置一个足够大的数以获取所有数据
        inventoryName: queryParams.inventoryName,
        status: queryParams.status,
      }),
    ]);

    // 处理分页数据
    if (pageResponse && pageResponse.list) {
      tasksList.value = pageResponse.list.map(mapTaskData);
      total.value = pageResponse.total || 0;
    }

    // 处理所有数据用于统计
    if (allResponse && allResponse.list) {
      allTasksList.value = allResponse.list.map(mapTaskData);
    }
  } catch (error) {
    console.error("获取任务列表失败:", error);
    ElMessage.error("获取盘点任务列表失败");
    tasksList.value = [];
    allTasksList.value = [];
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 抽取数据映射逻辑为独立函数
const mapTaskData = (task: any): TaskItem => ({
  taskId: task.id,
  taskName: task.inventoryName || "",
  inventoryStatus: task.status || "0",
  createTime: task.createTime || "",
  deadline: task.deadline || "",
  deptName: task.createDeptName || "",
  username: task.createUserName || "",
  assetCount: task.treatInventoryNum || 0,
  completionRate: task.inventoryProgress || 0,
  inventoryAssetsNum: task.inventoryAssetsNum || 0,
  assetsNum: task.assetsNum || 0,
});
// 处理查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  getInventoryTasks();
  getInventoryTasksNoPage();
};

// 重置查询
const resetQuery = () => {
  queryRef.value?.resetFields();
  queryParams.pageNum = 1;
  getInventoryTasks();
  getInventoryTasksNoPage();
};

// 刷新任务列表
const refreshTasks = () => {
  getInventoryTasks();
  getInventoryTasksNoPage();
  ElMessage.success("任务列表已刷新");
};

// 组件挂载后执行
onMounted(() => {
  console.log("任务数据：", tasksList.value);
  getInventoryTasks();
  getInventoryTasksNoPage();
});
</script>

<style scoped>
/* 搜索框相关样式 */
.el-input {
  --el-input-height: 40px;
}
.my-inventory-tasks-container {
  padding: 16px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.tasks-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-tasks {
  padding: 40px 0;
  display: flex;
  justify-content: center;
}

/* 任务名称单元格样式 */
.task-name-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 8px;
}

.task-name-text {
  font-weight: 500;
  margin-right: 10px;
}

/* 表格行样式 */
:deep(.el-table__row) {
  height: 60px;
}

/* 表格内进度条样式 */
:deep(.el-progress) {
  margin: 0;
  width: 95%;
}

/* 分页容器 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: left;
}

/* 链接按钮样式 */
:deep(.el-button--primary.is-link) {
  font-weight: 500;
}

:deep(.el-button--primary.is-link.is-disabled) {
  color: var(--el-text-color-disabled);
}
/* 进度条相关样式 */
.inventory-progress {
  padding: 8px 12px;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stats-item .label {
  color: #909399;
}

.stats-item .value {
  font-weight: 500;
}

.progress-bar-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 10px;
  background: #f0f2f5;
  border-radius: 5px;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  transition: all 0.3s ease;
}

.progress-text {
  min-width: 45px;
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-yellow-500 {
  background-color: #f59e0b;
}

.bg-green-500 {
  background-color: #22c55e;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #059669;
}

.text-orange-600 {
  color: #ea580c;
}

/*任务名称样式*/
.task-name-text {
  font-weight: 700; /* 或者使用 bold */
  margin-right: 10px;
}
</style>
