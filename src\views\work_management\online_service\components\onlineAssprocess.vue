<template>
  <div class="asset-online-process">
    <div class="process-header">
      <h2>安全评估流程</h2>
      <el-button 
        type="primary" 
        link
        @click="showTransferRecord"
      >
        <el-icon><Document /></el-icon>
        查看流转记录
      </el-button>
    </div>
    
    <el-steps :active="getStepIndex" finish-status="success" align-center>
      <el-step 
        v-for="(step, index) in steps" 
        :key="index" 
        :title="step.title" 
        :icon="step.icon" 
        :description="step.description" 
        @click="handleStepClick(getStepKey(index))"
      />
    </el-steps>
    
    <!-- 使用弹窗式流转记录组件 -->
    <transfer-record-dialog
      v-model:visible="showRecordsDialog"
      :ticket-id="ticketdata.id"
      :api-type="'business'"
      :show-attachments="true"
      ref="transferRecordRef"
    />
    
    <div class="step-content">
      <component 
        :is="steps[clickIndex].component" 
        v-model:ticketdata="ticketdata"
        @next="nextStep"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { Edit, Upload, Connection, Check, CloseBold, UserFilled } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import InitiateTicket from './process/assInitiateTicket.vue'
import AssetsAudit from './process/AssetsAudit.vue'
import AssetsAssessment from './process/AssetsAssessment.vue' // 新增安全评估组件
import AssetsFix from './process/AssetsFix.vue'
import FixVerification from './process/assFixVerification.vue'
import fixEvaluation from './process/assfixEvaluation.vue'
import CloseTicket from './process/assCloseTicket.vue'
import { hasAuth } from "@/plugins/permission"
import businessAPI ,{businessPageQuery,businessForm,StepStatus,businessPageVO } from '@/api/work_management/online_service'
import TransferRecordDialog from '@/components/GeneralModel/TransferRecord.vue'

const props = defineProps({
  ticketId: Number,
  currentStatus: Number,
})

// 监听 ticketId 的变化
watch(() => props.ticketId, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    handleQuery();
  }
})

const emit = defineEmits(['init'])


const transferRecordRef = ref();
const showRecordsDialog = ref(false);

// 显示流转记录
const showTransferRecord = () => {
  if (ticketdata.value.id) {
    showRecordsDialog.value = true;
  } else {
    ElMessage.warning('请先保存工单');
  }
};

// 获取流转记录时间线类型
function getFlowTimelineType(stepName) {
  const typeMap = {
    '发起工单': 'primary',
    '工单审核': 'success',
    '安全评估': 'warning', 
    '工单整改': 'info',
    '工单复核': 'warning',
    '工单评价': 'danger',
    '完成工单': 'success'
  };
  
  return typeMap[stepName] || 'info';
}

// 获取处理结果标签类型
function getResultTagType(result) {
  const resultMap = {
    '通过': 'success',
    '不通过': 'danger',
    '已整改': 'success',
    '未整改': 'warning',
    '已关闭': 'info',
    '满意': 'success',
    '不满意': 'danger'
  };
  
  return resultMap[result] || 'info';
}

// 更新步骤数组，加入新的安全评估步骤
const steps = [
  { title: '发起工单', icon: Edit, description: '发起工单申请', component: InitiateTicket },
  { title: '安全评估', icon: Connection, description: '进行安全评估', component: AssetsAssessment },
  { title: '工单审核', icon: Upload, description: '审核工单信息', component: AssetsAudit },
  { title: '工单整改', icon: Connection, description: '根据评估意见进行整改', component: AssetsFix },
  { title: '工单复核', icon: Check, description: '验证整改结果', component: FixVerification },
  { title: '工单评价', icon: Check, description: '评价整改结果', component: fixEvaluation },
  { title: '完成工单', icon: CloseBold, description: '完成工单流程', component: CloseTicket }
]

// 更新步骤状态对象
const stepStatus = ref({
  initiateTicket: 'process',
  assetsAudit: 'wait',
  assetsAssessment: 'wait', // 新增安全评估步骤
  assetsFix: 'wait',
  fixVerification: 'wait',
  fixEvaluation: 'wait',
  closeTicket: 'wait'
})

// 更新步骤权限
const stepPermission = ref({
  initiateTicket: "system:business:add",
  assetsAudit: "system:business:audit",
  assetsAssessment: "system:business:assess", // 新增安全评估权限
  assetsFix: "system:business:fix",
  fixVerification: "system:business:verify",
  fixEvaluation: "system:business:evaluate",
  closeTicket: "system:business:close"
})

const currentStep = ref<keyof typeof stepStatus.value>('initiateTicket')
const ticketdata = ref({
  id: props.ticketId,
  currentStep: currentStep.value,
  isClick: false
})
const nowStatus = ref<keyof StepStatus>('initiateTicket') // 当前步骤步骤条状态
const isClick = ref<boolean>(false) // 是否是点击事件

const getStepKey = (index: number): keyof typeof stepStatus.value => {
  return Object.keys(stepStatus.value)[index] as keyof typeof stepStatus.value
}

const getStepIndex = computed(() => {
  return Object.keys(stepStatus.value).indexOf(nowStatus.value)
})

const clickIndex = computed(() => {
  return Object.keys(stepStatus.value).indexOf(currentStep.value)
})

const hasPermission = (requiredPerms: string): boolean => {
  return hasAuth(requiredPerms, 'button')
}

const handleStepClick = async (step: keyof typeof stepStatus.value) => {
  if (step === 'closeTicket' && nowStatus.value !== 'closeTicket') {
      emit('init', props.ticketId)
  } else if (stepStatus.value[step] === 'wait') {
    ElMessage.warning('当前步骤未开始')
  } else {
    ticketdata.value.isClick = true
    currentStep.value = step
    ticketdata.value.currentStep = step
  }
}

const nextStep = async (id: number) => {
  console.log('id', id)
  emit('init', id)
  
  // 如果流转记录对话框已打开，则刷新数据
  if (showRecordsDialog.value && transferRecordRef.value) {
    transferRecordRef.value.loadData();
  }
}

const handleQuery = async () => {
  console.log('props.ticketId', props.ticketId)

  if (props.ticketId) {
    try {
      const statusRes:any = await businessAPI.getStepStatus(props.ticketId);
      stepStatus.value = statusRes;
      for (const step in stepStatus.value) {
        if (stepStatus.value[step as keyof typeof stepStatus.value] === 'process') {
          currentStep.value = step as keyof typeof stepStatus.value
          nowStatus.value = step as keyof StepStatus;
          break
        }
      }
      ticketdata.value.currentStep = currentStep.value
      
    } catch (error) {
      console.error('Error fetching step status:', error)
    }
  } else {
    currentStep.value = 'initiateTicket'
    nowStatus.value = 'initiateTicket'
  }
}

onMounted(() => {
  handleQuery()
})
</script>

<style scoped>

.mb-4 {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  background-color: #ecf5ff;
  color: #409EFF;
}

.timeline-container {
  padding: 10px 0;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-user {
  font-weight: 500;
}

.timeline-result {
  margin: 5px 0;
}

.timeline-msg {
  color: #606266;
  white-space: pre-wrap;
}

.flow-card {
  margin: 20px 0;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.asset-online-process {
  padding: 20px;
}

.step-content {
  margin-top: 30px;
}

:deep(.el-step) {
  cursor: pointer;
}

:deep(.el-step__title) {
  font-size: 14px;
}

:deep(.el-step__description) {
  font-size: 12px;
}

:deep(.el-step.is-process .el-step__icon) {
  background-color: #409EFF;
  border-color: #409EFF;
}

:deep(.el-step.is-finish .el-step__icon) {
  background-color: #67C23A;
  border-color: #67C23A;
}

:deep(.el-step.is-wait .el-step__icon) {
  background-color: #909399;
  border-color: #909399;
}

.asset-online-process {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
}

.step-content {
  width: 100%;
  margin-top: 30px;
}
</style>
