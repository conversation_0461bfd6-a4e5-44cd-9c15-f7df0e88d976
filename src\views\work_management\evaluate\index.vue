<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="评价人id" prop="commentBy">
          <el-input v-model="queryParams.commentBy" placeholder="评价人id" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="评价内容" prop="commentContent">
          <el-input v-model="queryParams.commentContent" placeholder="评价内容" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="评价类型" prop="commentType">
          <dictionary v-model="formData.commentType" code="VLUNS_E" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item label="评价id" prop="id">
          <el-input v-model="queryParams.id" placeholder="评价id" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="评价目标id" prop="parentCode">
          <el-input v-model="queryParams.parentCode" placeholder="评价目标id" clearable @keyup.enter="handleQuery()" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker v-model="queryParams.updateTime" type="daterange" range-separator="~" start-placeholder="开始时间"
            end-placeholder="结束时间" value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery()">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="handleResetQuery()">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <!-- <el-button
            v-hasPerm="['system:comments:add']"
            type="success"
            @click="handleOpenDialog()"
        >
          <i-ep-plus />
          新增
        </el-button> -->
        <el-button v-hasPerm="['system:comments:delete']" type="danger" :disabled="ids.length === 0"
          @click="handleDelete()">
          <i-ep-delete />
          删除
        </el-button>
      </template>

      <el-table ref="dataTableRef" :default-sort="{ prop: 'id', order: 'descending' }" v-loading="loading"
        :data="pageData" highlight-current-row border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column sortable key="id" label="评价id" prop="id" min-width="100" align="center"/>
        <el-table-column key="titleContent" label="任务名称" prop="titleContent" min-width="150" align="center" />
        <el-table-column key="commentByName" label="评价人" prop="commentByName" min-width="100" align="center"/>
        <el-table-column key="commentContent" label="评价内容" prop="commentContent" min-width="100" />
        <el-table-column key="rate" label="评分(总分5)" prop="rate" min-width="100" align="center"/>

        <el-table-column key="type" label="工单类型" prop="type" min-width="100" />
        <!-- <el-table-column
                    key="parentCode"
                    label="评价目标"
                    prop="parentCode"
                    min-width="100"
                /> -->
        <el-table-column key="createTime" label="创建时间" prop="createTime" min-width="200" />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button v-hasPerm="['system:safety:commentsVulns']" type="primary" size="small" link
              @click="handleJump(scope.row.parentCode, scope.row.type)">
              <i-ep-edit />
              查看工单
            </el-button>
            <el-button v-hasPerm="['system:comments:edit']" type="primary" size="small" link
              @click="handleOpenDialog(scope.row.id)">
              <i-ep-edit />
              编辑
            </el-button>
            <el-button v-hasPerm="['system:comments:delete']" type="danger" size="small" link
              @click="handleDelete(scope.row.id)">
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-if="total > 0" v-model:total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="handleQuery()" />
    </el-card>

    <!-- 评论表单弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" @close="handleCloseDialog">
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px">
        <!-- <el-form-item label="评价人id" prop="commentBy">
                      <el-input
                          v-model="formData.commentBy"
                          disabled
                          placeholder="评价人id"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item> -->
        <el-form-item label="评价内容" prop="commentContent">
          <el-input type="textarea" v-model="formData.commentContent" placeholder="评价内容" clearable
            @keyup.enter="handleQuery()" />
        </el-form-item>
        <!-- <el-form-item label="评价类型" prop="commentType" >
                          <dictionary v-model="formData.commentType" code="VLUNS_E" />
                </el-form-item> -->
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker disabled v-model="formData.createTime" type="datetime" placeholder="创建时间"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
        <!-- <el-form-item label="评价id" prop="id">
                      <el-input
                          v-model="formData.id"
                          placeholder="评价id"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item> -->
        <!-- <el-form-item label="评价目标id" prop="parentCode">
                      <el-input
                          v-model="formData.parentCode"
                          placeholder="评价目标id"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item> -->
        <el-form-item label="更新时间" prop="updateTime">
          <el-date-picker v-model="formData.updateTime" type="datetime" placeholder="更新时间"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "comments",
  inheritAttrs: false,
});

import commentsAPI, {
  commentsPageVO,
  commentsForm,
  commentsPageQuery,
} from "@/api/comments";
import router from "@/router";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);

const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

const queryParams = reactive<commentsPageQuery>({
  pageNum: 1,
  pageSize: 10,
});

// 评论表格数据
const pageData = ref<commentsPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 评论表单数据
const formData = reactive<commentsForm>({});

// 评论表单校验规则
const rules = reactive({
  id: [{ required: true, message: "请输入评价id", trigger: "blur" }],
});

/** 查询评论 */
function handleQuery() {
  loading.value = true;
  commentsAPI
    // .getPage(queryParams)
    .getPage_step5(queryParams)
    .then((data) => {
      pageData.value = data.list;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
const handleJump = (id: number, type: string) => {
  if (type == "安全问题管理") {
    router.push({ path: "/work_management/safety/vulns", query: { id: id } });
  }
  if (type == "业务上线管理") {
    router.push({ path: "/work_management/online_service", query: { id: id } });
  }
};
/** 重置评论查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  ids.value = selection.map((item: any) => item.id);
}

/** 打开评论弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改评论";
    commentsAPI.getFormData(id).then((data) => {
      console.log(data);
      Object.assign(formData, data);
      console.log(formData);
    });
  } else {
    dialog.title = "新增评论";
  }
}

/** 提交评论表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true;
      const id = formData.id;
      if (id) {
        commentsAPI
          .update(id, formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      } else {
        commentsAPI
          .add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleResetQuery();
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 关闭评论弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
}

/** 删除评论 */
function handleDelete(id?: number) {
  const rmsId = [id || ids.value].join(",");
  if (!rmsId) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      commentsAPI
        .deleteByIds(rmsId)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}

onMounted(() => {
  handleQuery();
});
</script>
