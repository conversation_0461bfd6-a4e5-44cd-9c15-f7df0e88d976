
  // 格式化为日期部分
// const dateOnly = formatLocalDateTime(new Date(), 'date'); // '2025-04-07'

// // 格式化为时间部分
// const timeOnly = formatLocalDateTime(new Date(), 'time'); // '10:30:45'

// // 完整日期时间
// const fullDateTime = formatLocalDateTime(new Date()); // '2025-04-07 10:30:45'

// // 如果后端需要ISO格式但保留时区信息
// const isoWithTz = formatLocalISOString(new Date()); // '2025-04-07T10:30:45.000+08:00'


// 格式化日期，只保留年月日
export function formatDateOnly(dateTimeStr: string) {
  if (!dateTimeStr) return '-';
  
  // 如果包含时间部分，则只截取日期部分
  if (dateTimeStr.includes(' ')) {
    return dateTimeStr.split(' ')[0];
  }
  
  // 如果是ISO格式的日期（包含T），也只取日期部分
  if (dateTimeStr.includes('T')) {
    return dateTimeStr.split('T')[0];
  }
  
  return dateTimeStr;
}



/**
 * 格式化日期为本地时间字符串，避免时区转换问题
 * @param date 日期对象、字符串或时间戳
 * @param format 格式 'datetime'='YYYY-MM-DD HH:mm:ss', 'date'='YYYY-MM-DD', 'time'='HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatLocalDateTime(date: Date | string | number | null | undefined, format: 'datetime' | 'date' | 'time' = 'datetime'): string | null {
  if (!date) return null;
  
  const d = new Date(date);
  
  // 检查日期是否有效
  if (isNaN(d.getTime())) return null;
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  if (format === 'date') {
    return `${year}-${month}-${day}`;
  } else if (format === 'time') {
    return `${hours}:${minutes}:${seconds}`;
  } else {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }
}

/**
* 格式化为带Z的ISO字符串，但保持本地时间值不变
* 即将本地时间值以UTC时区表示，解决时区转换问题
* @param date 日期对象、字符串或时间戳
* @returns 标准ISO格式字符串，带Z后缀
*/
export function formatToUTCString(date: Date | string | number | null | undefined): string | null {
if (!date) return null;

const d = new Date(date);

// 检查日期是否有效
if (isNaN(d.getTime())) return null;

// 使用本地日期时间组件，创建一个等效的UTC时间
const year = d.getFullYear();
const month = d.getMonth(); // 0-11
const day = d.getDate();
const hours = d.getHours();
const minutes = d.getMinutes();
const seconds = d.getSeconds();
const ms = d.getMilliseconds();

// 创建UTC时间
const utcTime = Date.UTC(year, month, day, hours, minutes, seconds, ms);
return new Date(utcTime).toISOString();
}


/**
* 格式化日期为指定格式
* 支持的格式化标记:
* - yyyy: 年份，四位数
* - MM: 月份，两位数（01-12）
* - dd: 日，两位数（01-31）
* 
* @param date 要格式化的日期
* @param formatStr 格式化模式，例如 'yyyy-MM-dd'
* @returns 格式化后的日期字符串
*/
export function format(date: Date | string | number, formatStr: string = 'yyyy-MM-dd'): string {
const d = new Date(date);

// 检查日期是否有效
if (isNaN(d.getTime())) {
  return 'Invalid Date';
}

const tokens: Record<string, string> = {
  'yyyy': d.getFullYear().toString(),
  'MM': String(d.getMonth() + 1).padStart(2, '0'),
  'dd': String(d.getDate()).padStart(2, '0'),
  'HH': String(d.getHours()).padStart(2, '0'),
  'mm': String(d.getMinutes()).padStart(2, '0'),
  'ss': String(d.getSeconds()).padStart(2, '0')
};

return formatStr.replace(/yyyy|MM|dd|HH|mm|ss/g, match => tokens[match]);
}

/**
* 计算两个日期之间的天数差
* @param dateLeft 较晚的日期
* @param dateRight 较早的日期
* @returns 天数差（正数表示dateLeft比dateRight晚）
*/
export function differenceInDays(dateLeft: Date | number | string, dateRight: Date | number | string): number {
// 创建日期对象，并设置为当天的起始时间（0时0分0秒）
const startLeft = new Date(dateLeft);
startLeft.setHours(0, 0, 0, 0);

const startRight = new Date(dateRight);
startRight.setHours(0, 0, 0, 0);

// 转换为时间戳并计算天数差
const diffTime = startLeft.getTime() - startRight.getTime();
return Math.floor(diffTime / (1000 * 60 * 60 * 24));
}

/**
* 检查日期是否有效
* @param date 要检查的日期
* @returns 日期是否有效
*/
export function isValidDate(date: any): boolean {
if (date === null || date === undefined) return false;

const d = new Date(date);
return !isNaN(d.getTime());
}

/**
* 判断证书是否即将过期（30天内）
* @param validTo 到期日期
* @returns 是否即将过期
*/
export function isExpiring(validTo: Date | string | number): boolean {
const today = new Date();
today.setHours(0, 0, 0, 0);
const endDate = new Date(validTo);
endDate.setHours(0, 0, 0, 0);

const diffDays = differenceInDays(endDate, today);
return diffDays >= 0 && diffDays <= 30;
}

/**
* 判断证书是否已过期
* @param validTo 到期日期
* @returns 是否已过期
*/
export function isExpired(validTo: Date | string | number): boolean {
const today = new Date();
today.setHours(0, 0, 0, 0);
const endDate = new Date(validTo);
endDate.setHours(0, 0, 0, 0);

return differenceInDays(endDate, today) < 0;
}

/**
* 获取证书状态信息
* @param validTo 到期日期
* @returns 状态信息对象 {text, type}
*/
export function getCertificateStatus(validTo: Date | string | number): {text: string, type: string} {
const remainingDays = differenceInDays(new Date(validTo), new Date());

if (remainingDays < 0) {
  return { text: '已过期', type: 'danger' };
} else if (remainingDays <= 30) {
  return { text: '即将过期', type: 'warning' };
} else {
  return { text: '有效', type: 'success' };
}
}
