<template>
  <div class="app-container">
    <el-card class="box-card">
      <el-button
        type="primary"
        @click="openCreateDialog"
        style="margin-bottom: 16px"
      >
        新建项目
      </el-button>

      <el-table :data="projects" style="width: 100%" v-loading="isLoading">
        <el-table-column prop="name" label="项目名称" />
        <el-table-column prop="owner" label="负责人" />
        <el-table-column prop="startDate" label="开始日期">
          <template #default="{ row }">
            {{ formatDate(row.startDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束日期">
          <template #default="{ row }">
            {{
              formatDate(new Date(new Date(row.endDate).getTime() - 86400000))
            }}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" disable-transitions>
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template #default="{ row }">
            <el-button type="default" size="small" @click="handleView(row)">
              详情
            </el-button>
            <el-button type="primary" size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="closeDialog"
    >
      <el-form ref="formRef" :model="form" :rules="formRules" :key="dialogKey">
        <el-form-item label="项目名称" label-width="90px" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入项目名称"
            :disabled="dialogType === 'view'"
          />
        </el-form-item>
        <el-form-item label="项目时间" label-width="90px" prop="dateRange">
          <el-date-picker
            v-model="form.dateRange"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled="dialogType === 'view'"
            style="width: 100%"
            unlink-panels
          />
        </el-form-item>
        <el-form-item label="负责人" label-width="90px" prop="owner">
          <el-select
            v-model="form.owner"
            placeholder="请选择负责人"
            style="width: 100%"
            :disabled="dialogType === 'view'"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.username"
              :value="user.username"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          v-if="dialogType !== 'view'"
          type="primary"
          @click="handleConfirm"
          :loading="isSubmitting"
        >
          {{ dialogType === "create" ? "确定" : "更新" }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessageBox, ElMessage } from "element-plus";
import { ProjectAPI, UserAPI } from "@/api/progress_management/createProject";
import type { Project, User, ProjectForm } from "@/types/project.ts";

const dialogVisible = ref(false);
const dialogType = ref<"create" | "edit" | "view">("create");
const dialogTitle = ref("新建项目");
const currentProject = ref<Project | null>(null);
const dialogKey = ref(0);

const projects = ref<Project[]>([]);
const users = ref<User[]>([]);
const isLoading = ref(false);
const isSubmitting = ref(false); // 单独的提交加载状态

const formRef = ref<FormInstance | null>(null);

const form = ref<{
  name: string;
  owner: string;
  dateRange: string[];
  status: string;
}>({
  name: "",
  owner: "",
  dateRange: [],
  status: "",
});

const formRules: FormRules = {
  name: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  owner: [{ required: true, message: "请选择负责人", trigger: "change" }],
  dateRange: [
    {
      required: true,
      type: "array",
      message: "请选择时间范围",
      trigger: "change",
    },
  ],
};

// 格式化日期显示
const formatDate = (date: string | Date | null): string => {
  if (!date) return "未设置";
  try {
    const d = new Date(date);
    return d.toLocaleDateString("zh-CN");
  } catch (error) {
    console.error("日期格式化错误:", error);
    return "格式错误";
  }
};

// 根据状态获取标签类型
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    未开始: "info",
    已开始: "warning",
    已完成: "success",
    已逾期: "danger",
  };
  return statusMap[status] || "default";
};

// 获取项目列表
const fetchProjects = async () => {
  const oldProjects = [...projects.value];
  try {
    isLoading.value = true;
    const response = await ProjectAPI.getList();
    projects.value = Array.isArray(response) ? response : [];
  } catch (error) {
    console.error("获取项目列表失败:", error);
    ElMessage.error("获取项目列表失败");
    projects.value = oldProjects;
  } finally {
    isLoading.value = false;
  }
};

// 获取用户列表
const fetchUsers = async () => {
  try {
    const response = await UserAPI.getList();
    users.value = Array.isArray(response) ? response : [];
  } catch (error) {
    console.error("获取用户列表失败:", error);
    ElMessage.error("获取用户列表失败");
  }
};

// 清理表单数据
const clearFormData = () => {
  form.value = {
    name: "",
    owner: "",
    dateRange: [],
    status: "",
  };

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 打开弹窗
const openDialog = async (
  type: "create" | "edit" | "view",
  title: string,
  project: Project | null = null
) => {
  dialogVisible.value = false;
  await new Promise((resolve) => setTimeout(resolve, 300));

  dialogKey.value++;
  clearFormData();

  dialogType.value = type;
  dialogTitle.value = title;
  currentProject.value = project;

  if (project) {
    form.value.name = project.name || "";
    form.value.owner = project.owner || "";
    form.value.status = project.status || "未开始";

    if (project.startDate) {
      try {
        const startDate = new Date(project.startDate);
        form.value.dateRange[0] = startDate.toISOString().slice(0, 10);
      } catch (error) {
        form.value.dateRange[0] = "";
      }
    }

    if (project.endDate) {
      try {
        const endDate = new Date(
          new Date(project.endDate).getTime() - 86400000
        );
        form.value.dateRange[1] = endDate.toISOString().slice(0, 10);
      } catch (error) {
        form.value.dateRange[1] = "";
      }
    }
  } else if (type === "create") {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10);
    form.value.dateRange = [dateStr, dateStr];
    form.value.status = "未开始";
  }

  dialogVisible.value = true;
};

const openCreateDialog = () => openDialog("create", "新建项目");
const handleEdit = (row: Project) => openDialog("edit", "编辑项目", row);
const handleView = (row: Project) => openDialog("view", "查看项目", row);

const closeDialog = () => {
  dialogVisible.value = false;
  currentProject.value = null;
  clearFormData();
};

// 格式化时间工具函数
const toStartOfDayISOString = (dateStr: string): string => {
  try {
    return new Date(`${dateStr}T00:00:00Z`).toISOString();
  } catch (error) {
    return new Date().toISOString();
  }
};

const toNextDayStartISOString = (dateStr: string): string => {
  try {
    const d = new Date(`${dateStr}T00:00:00Z`);
    d.setDate(d.getDate() + 1);
    return d.toISOString();
  } catch (error) {
    return new Date().toISOString();
  }
};

const handleDelete = async (row: Project) => {
  if (!row.id) {
    ElMessage.warning("项目ID不存在");
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除项目"${row.name}"吗？`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    isLoading.value = true;
    // 调用删除API，不验证返回内容，只看是否成功
    await ProjectAPI.delete(row.id);
    // 从本地列表中移除
    projects.value = projects.value.filter((project) => project.id !== row.id);
    ElMessage.success("项目删除成功");
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除项目失败:", error);
      ElMessage.error("删除项目失败");
    }
  } finally {
    isLoading.value = false;
  }
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (!form.value.dateRange || form.value.dateRange.length !== 2) {
      ElMessage.warning("请选择有效的时间范围");
      return;
    }

    const payload: ProjectForm = {
      name: form.value.name,
      owner: form.value.owner,
      startDate: toStartOfDayISOString(form.value.dateRange[0]),
      endDate: toNextDayStartISOString(form.value.dateRange[1]),
      status: form.value.status || "未开始",
    };

    isSubmitting.value = true;

    if (dialogType.value === "create") {
      // 不验证返回值，只要请求成功就认为创建成功
      await ProjectAPI.create(payload);
      // 重新获取列表确保数据最新
      await fetchProjects();
      ElMessage.success("项目创建成功");
    } else if (dialogType.value === "edit" && currentProject.value?.id) {
      // 不验证返回值，只要请求成功就认为更新成功
      await ProjectAPI.update(currentProject.value.id, payload);
      // 重新获取列表确保数据最新
      await fetchProjects();
      ElMessage.success("项目更新成功");
    }

    closeDialog();
  } catch (error) {
    // 只有当不是表单验证错误且不是用户取消时才提示
    if (typeof error !== "string" && error !== "cancel") {
      console.error("操作失败:", error);
      ElMessage.error("操作失败");
    }
  } finally {
    isSubmitting.value = false;
  }
};

onMounted(() => {
  fetchProjects();
  fetchUsers();
});
</script>

<style scoped>
.app-container {
  padding: 24px;
}
.box-card {
  border-radius: 12px;
}
</style>
