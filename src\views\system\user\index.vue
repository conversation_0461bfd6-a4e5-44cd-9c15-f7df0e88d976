<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" class="mb-[12px]">
        <dept-tree v-model="queryParams.deptId" @node-click="handleQuery" />
      </el-col>

      <!-- 用户列表 -->
      <el-col :lg="20" :xs="24">
        <div class="search-container">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="关键字" prop="keywords">
              <el-input
                v-model="queryParams.keywords"
                placeholder="用户名/昵称/手机号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="全部"
                clearable
                class="!w-[100px]"
              >
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleQuery">
                <i-ep-search />
                搜索
              </el-button>
              <el-button @click="handleResetQuery">
                <i-ep-refresh />
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-card shadow="never" class="table-container">
          <template #header>
            <div class="flex-x-between">
              <div>
                <el-button
                  v-hasPerm="['sys:user:add']"
                  type="success"
                  @click="openUserDialog()"
                >
                  <i-ep-plus />
                  新增
                </el-button>
                <el-button
                  v-hasPerm="['sys:user:delete']"
                  type="danger"
                  :disabled="removeIds.length === 0"
                  @click="handleDelete()"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </div>
              <div>
                <el-button class="ml-3" @click="handleOpenImportDialog">
                  <template #icon><i-ep-upload /></template>
                  导入
                </el-button>

                <el-button class="ml-3" @click="handleExport">
                  <template #icon><i-ep-download /></template>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <el-table
            v-loading="loading"
            :data="pageData"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column
              key="id"
              label="编号"
              align="center"
              prop="id"
              width="80"
            />
            <el-table-column
              key="username"
              label="用户名"
              align="center"
              prop="username"
            />
            <el-table-column label="用户姓名" align="center" prop="nickname" />

            <!-- 修改：使用字典映射组件显示职责分类 -->
            <el-table-column
              label="职责分类"
              width="150"
              align="center"
              prop="duty"
              show-overflow-tooltip
            >
              <template #default="scope">
                <dictmap 
                  :model-value="scope.row.duty" 
                  code="DUTY" 
                  multiple 
                  placeholder="未设置"
                />
              </template>
            </el-table-column>

            <el-table-column
              label="性别"
              width="80"
              align="center"
              prop="genderLabel"
            />

            <el-table-column
              label="部门"
              width="150"
              align="center"
              prop="deptName"
            />
            <el-table-column
              label="手机号码"
              align="center"
              prop="mobile"
              width="120"
            />

            <el-table-column
              label="状态"
              align="center"
              prop="status"
              width="100"
            >
              <template #default="scope">
                <el-tag :type="scope.row.status == 1 ? 'success' : 'info'">
                  {{ scope.row.status == 1 ? "正常" : "禁用" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              width="180"
            />
            <el-table-column label="操作" fixed="right" width="220">
              <template #default="scope">
                <el-button
                  v-hasPerm="['sys:user:password:reset']"
                  type="primary"
                  size="small"
                  link
                  @click="hancleResetPassword(scope.row)"
                >
                  <i-ep-refresh-left />
                  重置密码
                </el-button>
                <el-button
                  v-hasPerm="['sys:user:edit']"
                  type="primary"
                  link
                  size="small"
                  @click="openUserDialog(scope.row.id)"
                >
                  <i-ep-edit />
                  编辑
                </el-button>
                <el-button
                  v-hasPerm="['sys:user:delete']"
                  type="danger"
                  link
                  size="small"
                  @click="handleDelete(scope.row.id)"
                >
                  <i-ep-delete />
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handleQuery"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 用户表单弹窗 -->
    <UserDialog
      v-model:visible="dialogVisible"
      :userId="selectedUserId"
      @refresh="handleQuery"
    />

    <!-- 用户导入 -->
    <UserImport v-model="importDialogVisible" @import-success="handleQuery()" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "User",
  inheritAttrs: false,
});

import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import UserAPI, { UserPageQuery, UserPageVO } from "@/api/user";
import UserDialog from './components/UserDialog.vue';
import UserImport from "./components/UserImport.vue";
import { useDictStore } from '@/store/modules/dictStore';

const queryFormRef = ref();
const loading = ref(false);
const removeIds = ref([]);
const total = ref(0);
const pageData = ref<UserPageVO[]>();
const queryParams = reactive<UserPageQuery>({
  pageNum: 1,
  pageSize: 10,
});
const dialogVisible = ref(false);
const selectedUserId = ref<number | null>(null);
const importDialogVisible = ref(false);

// 获取字典存储
const dictStore = useDictStore();

// 需要预加载的字典类型
const requiredDictTypes = ref([
  'DUTY',      // 职责分类字典
]);

// 记录字典是否已加载
const dictLoaded = ref(false);

/** 
 * 预加载字典数据 
 * 在表格数据加载前，预先加载所有可能用到的字典数据
 */
async function preloadDictData() {
  // 如果已加载，则跳过
  if (dictLoaded.value || requiredDictTypes.value.length === 0) {
    return;
  }

  try {
    console.log('正在预加载字典数据...');
    const dictPromises = requiredDictTypes.value.map(type => dictStore.fetchOptions(type));
    await Promise.all(dictPromises);
    console.log('字典数据预加载完成');

    // 标记字典已加载
    dictLoaded.value = true;
  } catch (error) {
    console.error('字典数据预加载失败:', error);
  }
}

async function handleQuery() {
  loading.value = true;
  
  try {
    // 首先预加载字典数据（只在第一次加载时执行）
    if (!dictLoaded.value) {
      await preloadDictData();
    }

    // 获取表格数据
    const data = await UserAPI.getPage(queryParams);
    pageData.value = data.list;
    total.value = data.total;
  } catch (error) {
    console.error('查询数据失败:', error);
    ElMessage.error('获取数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
}

function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  queryParams.deptId = undefined;
  queryParams.duty = undefined; // 重置职责分类筛选
  handleQuery();
}

function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

function hancleResetPassword(row: { [key: string]: any }) {
  ElMessageBox.prompt(
    "请输入用户「" + row.username + "」的新密码\n" +
    "密码要求：至少10位，必须包含大写字母、小写字母、数字和特殊字符",
    "重置密码",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
    }
  ).then(
    ({ value }) => {
      // 密码复杂度验证
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumbers = /\d/.test(value);
      const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
      
      if (!value || value.length < 10) {
        ElMessage.warning("密码长度至少需要10位字符，请重新输入");
        return false;
      } else if (!(hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar)) {
        ElMessage.warning("密码必须包含大写字母、小写字母、数字和特殊字符，请重新输入");
        return false;
      }
      
      UserAPI.resetPassword(row.id, value).then(() => {
        ElMessage.success("密码重置成功，新密码是：" + value);
      });
    },
    () => {
      ElMessage.info("已取消重置密码");
    }
  );
}

function openUserDialog(id?: number) {
  selectedUserId.value = id || null;
  dialogVisible.value = true;
}

function handleDelete(id?: number) {
  const userIds = [id || removeIds.value].join(",");
  if (!userIds) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除用户?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    function () {
      loading.value = true;
      UserAPI.deleteByIds(userIds)
        .then(() => {
          ElMessage.success("删除成功");
          handleResetQuery();
        })
        .finally(() => (loading.value = false));
    },
    function () {
      ElMessage.info("已取消删除");
    }
  );
}

function handleOpenImportDialog() {
  importDialogVisible.value = true;
}

function handleExport() {
  UserAPI.export(queryParams).then((response: any) => {
    const fileData = response.data;
    const fileName = decodeURI(
      response.headers["content-disposition"].split(";")[1].split("=")[1]
    );
    const fileType =
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8";

    const blob = new Blob([fileData], { type: fileType });
    const downloadUrl = window.URL.createObjectURL(blob);

    const downloadLink = document.createElement("a");
    downloadLink.href = downloadUrl;
    downloadLink.download = fileName;

    document.body.appendChild(downloadLink);
    downloadLink.click();

    document.body.removeChild(downloadLink);
    window.URL.revokeObjectURL(downloadUrl);
  });
}

onMounted(async () => {
  await handleQuery();
});
</script>

<style scoped>
.mr-1 {
  margin-right: 4px;
}

.mb-1 {
  margin-bottom: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
