<template>
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="650px"
      class="provider-detail-dialog"
    >
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><information-filled /></el-icon>
            基本信息
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="服务商名称">{{ data.providerName }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="data.status === 1 ? 'success' : 'danger'">
                {{ data.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="服务商描述" :span="2">{{ data.description }}</el-descriptions-item>
          </el-descriptions>
        </div>
  
        <!-- 项目负责人信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><user /></el-icon>
            项目负责人信息
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目负责人">{{ data.projectManager }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ data.managerMobile }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱" :span="2">{{ data.managerEmail }}</el-descriptions-item>
          </el-descriptions>
        </div>
  
        <!-- 技术负责人信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><tools /></el-icon>
            技术负责人信息
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="技术负责人">{{ data.techLeader }}</el-descriptions-item>
            <el-descriptions-item label="手机号码">{{ data.techMobile }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱" :span="2">{{ data.techEmail }}</el-descriptions-item>
          </el-descriptions>
        </div>
  
        <!-- 合同信息 -->
        <div class="detail-section">
          <div class="section-title">
            <el-icon><document /></el-icon>
            合同信息
          </div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="合同开始时间">{{ data.contractStartTime }}</el-descriptions-item>
            <el-descriptions-item label="合同结束时间">{{ data.contractEndTime }}</el-descriptions-item>
            <el-descriptions-item label="合同文件" :span="2">
              <el-link v-if="data.contractFile" type="primary" :href="data.contractFile" target="_blank">
                查看合同文件
              </el-link>
              <span v-else>暂无合同文件</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
  
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script setup lang="ts">
  import { computed } from 'vue'
  
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '服务商详情'
    },
    data: {
      type: Object,
      default: () => ({})
    }
  })
  
  const emit = defineEmits(['update:visible'])
  
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val)
  })
  
  const handleClose = () => {
    dialogVisible.value = false
  }
  </script>
  
  <style lang="scss" scoped>
  .provider-detail-dialog {
    :deep(.el-dialog__body) {
      padding: 20px 24px;
    }
  }
  
  .detail-section {
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--el-fill-color-blank);
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 15px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  
    .el-icon {
      margin-right: 8px;
      font-size: 18px;
      color: var(--el-color-primary);
    }
  }
  
  .dialog-footer {
    text-align: right;
    padding-top: 16px;
  }
  </style>
