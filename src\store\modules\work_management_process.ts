import { defineStore } from 'pinia'

// 定义步骤键的类型
type StepKey = 'initiateTicket' | 'vulnerabilityAudit' | 'vulnerabilityFix' | 'fixVerification' | 'fixEvaluation' | 'closeTicket';

// 定义一个名为 useProcessStore 的 store
export const useProcessStore = defineStore('process', {
  // 定义 store 的状态
  state: () => ({
    currentStep: 'initiateTicket', // 当前步骤
    stepStatus: {
      initiateTicket: 'process', // 当前步骤正在进行
      vulnerabilityAudit: 'wait', // 等待进行
      vulnerabilityFix: 'wait',
      fixVerification: 'wait',
      fixEvaluation: 'wait',
      closeTicket: 'wait',
    } as Record<StepKey, string>,
    ticketData: { // 工单数据
      id: '',
      title: '',
      description: '',
      status: '',
      createdAt: '',
      updatedAt: '',
    },
    auditResult: null as boolean | null, // 审核结果
    fixResult: null as boolean | null, // 修复结果
    evaluationResult: null as boolean | null, // 评估结果
    history: [] as Array<{ step: string, timestamp: string }>, // 历史记录
  }),
  // 定义 store 的 actions
  actions: {
    // 设置当前步骤并添加到历史记录
    setCurrentStep(step: string) {
      this.currentStep = step
      this.addHistory(step)
    },
    // 更新步骤状态
    updateStepStatus(step: StepKey, status: string) {
      this.stepStatus[step] = status
    },
    // 获取特定步骤的状态
    getStatusForStep(step: StepKey) {
      return this.stepStatus[step]
    },
    // 更新工单数据
    updateTicketData(data: Partial<typeof this.ticketData>) {
      this.ticketData = { ...this.ticketData, ...data }
    },
    // 设置审核结果
    setAuditResult(result: boolean) {
      this.auditResult = result
      if (result) {
        this.updateStepStatus('vulnerabilityAudit', 'finish')
        this.updateStepStatus('vulnerabilityFix', 'process')
      } else {
        this.updateStepStatus('vulnerabilityAudit', 'error')
      }
    },
    // 设置修复结果
    setFixResult(result: boolean) {
      this.fixResult = result
      if (result) {
        this.updateStepStatus('vulnerabilityFix', 'finish')
        this.updateStepStatus('fixVerification', 'process')
      } else {
        this.updateStepStatus('vulnerabilityFix', 'error')
      }
    },
    // 设置评估结果
    setEvaluationResult(result: boolean) {
      this.evaluationResult = result
      if (result) {
        this.updateStepStatus('fixEvaluation', 'finish')
        this.updateStepStatus('closeTicket', 'process')
      } else {
        this.updateStepStatus('fixEvaluation', 'error')
      }
    },
    // 重置流程
    resetProcess() {
      this.currentStep = 'initiateTicket'
      this.stepStatus = {
        initiateTicket: 'process',
        vulnerabilityAudit: 'wait',
        vulnerabilityFix: 'wait',
        fixVerification: 'wait',
        fixEvaluation: 'wait',
        closeTicket: 'wait',
      }
      this.ticketData = {
        id: '',
        title: '',
        description: '',
        status: '',
        createdAt: '',
        updatedAt: '',
      }
      this.auditResult = null
      this.fixResult = null
      this.evaluationResult = null
      this.history = []
    },
    // 添加历史记录
    addHistory(step: string) {
      const timestamp = new Date().toISOString()
      this.history.push({ step, timestamp })
    },
    // 获取工单状态
    getTicketStatus() {
      if (this.evaluationResult !== null) {
        return 'Evaluated'
      } else if (this.fixResult !== null) {
        return 'Fixed'
      } else if (this.auditResult !== null) {
        return 'Audited'
      } else {
        return 'Initiated'
      }
    },
  },
  // 定义 store 的 getters
  getters: {
    // 判断审核是否完成
    isAuditCompleted: (state) => state.auditResult !== null,
    // 判断修复是否完成
    isFixCompleted: (state) => state.fixResult !== null,
    // 判断评估是否完成
    isEvaluationCompleted: (state) => state.evaluationResult !== null,
    // 获取工单状态
    ticketStatus: (state) => {
      if (state.evaluationResult !== null) {
        return 'Evaluated'
      } else if (state.fixResult !== null) {
        return 'Fixed'
      } else if (state.auditResult !== null) {
        return 'Audited'
      } else {
        return 'Initiated'
      }
    },
  },
})
