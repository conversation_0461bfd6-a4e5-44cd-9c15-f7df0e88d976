<template>
  <div class="task-actions">
    <!-- 查看按钮 -->
    <el-button
      link
      type="primary"
      size="small"
      style="padding: 0 4px; min-width: unset"
      @click="$emit('view', task)"
    >
      查看
    </el-button>

    <!-- 编辑按钮 -->
    <el-button
      link
      type="warning"
      size="small"
      style="padding: 0 4px; min-width: unset"
      @click="$emit('edit', task)"
    >
      编辑
    </el-button>

    <!-- 新建子项按钮 - 只有可以包含子项的类型才显示 -->
    <el-dropdown
      v-if="canCreateChildren"
      trigger="click"
      @command="handleCreateChild"
    >
      <el-button
        link
        type="success"
        size="small"
        style="padding: 0 4px; min-width: unset"
      >
        新建子项
        <el-icon class="el-icon--right">
          <ArrowDown />
        </el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="option in createOptions"
            :key="option.value"
            :command="option.value"
          >
            <TaskIcon :type="option.value" size="14px" />
            {{ option.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 删除按钮 -->
    <el-button
      link
      type="danger"
      size="small"
      style="padding: 0 4px; min-width: unset"
      @click="$emit('delete', task)"
    >
      删除
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { TaskRow, TaskType } from "@/types/project";
import { TaskHierarchyManager } from "@/utils/taskHierarchy";
import TaskIcon from "./TaskIcon.vue";

interface Props {
  task: TaskRow;
}

interface Emits {
  (e: "view", task: TaskRow): void;
  (e: "edit", task: TaskRow): void;
  (e: "delete", task: TaskRow): void;
  (e: "createChild", parentTask: TaskRow, childType: TaskType): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 检查是否可以创建子项
const canCreateChildren = computed(() => {
  return TaskHierarchyManager.canHaveChildren(props.task.type);
});

// 获取可创建的子项类型选项
const createOptions = computed(() => {
  if (!canCreateChildren.value) return [];
  return TaskHierarchyManager.getCreateOptions(props.task.type);
});

// 处理创建子项
const handleCreateChild = (childType: TaskType) => {
  emit("createChild", props.task, childType);
};
</script>

<style scoped>
.task-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.el-icon--right {
  margin-left: 2px;
}
</style>
