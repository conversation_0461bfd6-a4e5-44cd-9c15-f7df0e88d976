<template>
  <div class="gantt-table-wrapper">
    <div class="gantt-table-pane">
      <el-button
        type="primary"
        link
        :icon="Plus"
        class="gantt-create-btn"
        @click="openCreateDialog"
      >
        新建工作项
      </el-button>
      <el-table
        :data="treeTableData"
        border
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        class="gantt-el-table"
        :expand-row-keys="expandedRowKeys"
        @expand-change="handleExpandChange"
        :row-class-name="getRowClass"
        :fit="false"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column
          prop="title"
          label="标题"
          min-width="375px"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <el-icon
              v-if="scope.row.children && scope.row.children.length > 0"
              style="margin-right: 6px; color: #409eff"
            >
              <Folder />
            </el-icon>
            <el-icon v-else style="margin-right: 6px; color: #409eff">
              <Document />
            </el-icon>
            <span
              :class="{
                'folder-title':
                  scope.row.children && scope.row.children.length > 0,
              }"
            >
              {{ scope.row.title }}
            </span>
          </template>
        </el-table-column>
        <!-- 直接使用接口返回的status字段 -->
        <el-table-column prop="status" label="状态" width="90">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="90">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="172" align="center">
          <template #default="scope">
            <el-button
              link
              type="primary"
              size="small"
              style="padding: 0 4px; min-width: unset"
              @click="openViewDialog(scope.row)"
            >
              查看
            </el-button>
            <el-button
              link
              type="warning"
              size="small"
              style="padding: 0 4px; min-width: unset"
              @click="openEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              style="padding: 0 4px; min-width: unset"
              @click="openDeleteDialog(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-model="createDialogVisible" title="新建工作项" width="400px">
        <el-form :model="createForm" label-width="80px">
          <el-form-item label="标题" required>
            <el-input v-model="createForm.title" />
          </el-form-item>
          <el-form-item label="类型" required>
            <el-select v-model="createForm.type" @change="handleTypeChange">
              <el-option label="里程碑" value="milestone" />
              <el-option label="任务" value="task" />
              <el-option label="任务文件夹" value="taskFolder" />
            </el-select>
          </el-form-item>
          <el-form-item label="任务分配" required>
            <el-tree-select
              v-model="createForm.parentId"
              :data="milestoneTreeData"
              :props="{ label: 'title', children: 'children', value: 'id' }"
              placeholder="选择所属里程碑/任务文件夹"
              clearable
              check-strictly
              style="width: 100%"
              :default-value="-1"
              :disabled="createForm.type === 'milestone'"
              @change="handleParentChange"
            />
          </el-form-item>
          <el-form-item label="人员分配" required>
            <el-select
              v-model="createForm.members"
              multiple
              filterable
              placeholder="请选择成员"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级" required>
            <el-select v-model="createForm.priority">
              <el-option label="高" value="高" />
              <el-option label="普通" value="普通" />
              <el-option label="低" value="低" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期" required>
            <el-date-picker
              v-model="createForm.start"
              type="date"
              value-format="YYYY-MM-DD"
              @change="validateDateRange('create')"
            />
          </el-form-item>
          <el-form-item label="截止日期" required :error="createDateError">
            <el-date-picker
              v-model="createForm.end"
              type="date"
              value-format="YYYY-MM-DD"
              @change="
                () => {
                  validateDateRange('create');
                  autoUpdateStatusByDate(createForm);
                }
              "
            />
          </el-form-item>
          <!-- 新增任务描述字段 -->
          <el-form-item label="任务描述">
            <el-input
              v-model="createForm.description"
              type="textarea"
              rows="4"
              placeholder="请输入任务描述信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreate">确定</el-button>
        </template>
      </el-dialog>

      <el-dialog v-model="editDialogVisible" title="编辑工作项" width="400px">
        <el-form :model="editForm" label-width="80px">
          <el-form-item label="标题" required>
            <el-input v-model="editForm.title" />
          </el-form-item>
          <el-form-item label="类型" required>
            <el-select v-model="editForm.type" @change="handleEditTypeChange">
              <el-option label="里程碑" value="milestone" />
              <el-option label="任务" value="task" />
              <el-option label="任务文件夹" value="taskFolder" />
            </el-select>
          </el-form-item>
          <el-form-item label="任务分配" required>
            <el-tree-select
              v-model="editForm.parentId"
              :data="milestoneTreeData"
              :props="{ label: 'title', children: 'children', value: 'id' }"
              placeholder="选择所属里程碑/任务文件夹"
              clearable
              check-strictly
              style="width: 100%"
              :disabled="editForm.type === 'milestone'"
              @change="handleEditParentChange"
            />
          </el-form-item>
          <el-form-item label="人员分配" required>
            <el-select
              v-model="editForm.members"
              multiple
              filterable
              placeholder="请选择成员"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级" required>
            <el-select v-model="editForm.priority">
              <el-option label="高" value="高" />
              <el-option label="普通" value="普通" />
              <el-option label="低" value="低" />
            </el-select>
          </el-form-item>
          <el-form-item label="开始日期" required>
            <el-date-picker
              v-model="editForm.start"
              type="date"
              value-format="YYYY-MM-DD"
              @change="validateDateRange('edit')"
            />
          </el-form-item>
          <el-form-item label="截止日期" required :error="editDateError">
            <el-date-picker
              v-model="editForm.end"
              type="date"
              value-format="YYYY-MM-DD"
              @change="
                () => {
                  validateDateRange('edit');
                  autoUpdateStatusByDate(editForm);
                }
              "
            />
          </el-form-item>
          <!-- 编辑对话框也添加任务描述 -->
          <el-form-item label="任务描述">
            <el-input
              v-model="editForm.description"
              type="textarea"
              rows="4"
              placeholder="请输入任务描述信息"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEdit">保存</el-button>
        </template>
      </el-dialog>

      <el-dialog v-model="viewDialogVisible" title="工作项详情" width="400px">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="标题">
            {{ viewRow.title }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            {{ formatTypeLabel(viewRow.type) }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            {{ viewRow.priority }}
          </el-descriptions-item>
          <!-- 直接使用接口返回的status字段 -->
          <el-descriptions-item label="状态">
            {{ viewRow.status }}
          </el-descriptions-item>
          <el-descriptions-item label="开始日期">
            {{ formatIsoToDisplay(viewRow.start) }}
          </el-descriptions-item>
          <el-descriptions-item label="截止日期">
            {{ formatIsoToDisplay(viewRow.end) }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ viewRow.finish ? formatIsoToDisplay(viewRow.finish) : "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="人员分配">
            {{
              viewRow.members && viewRow.members.length
                ? viewRow.members
                    .map(
                      (id) =>
                        userList.find((u) => u.id === id)?.name || "未知用户"
                    )
                    .join(", ")
                : "-"
            }}
          </el-descriptions-item>
          <!-- 查看对话框添加任务描述 -->
          <el-descriptions-item label="任务描述">
            {{ viewRow.description || "-" }}
          </el-descriptions-item>
        </el-descriptions>
        <template #footer>
          <el-button @click="viewDialogVisible = false">关闭</el-button>
        </template>
      </el-dialog>

      <el-dialog v-model="deleteDialogVisible" title="确认删除" width="340px">
        <div style="margin: 20px 0">
          确定要删除该工作项吗？
          <template v-if="deleteRow.value && hasChildren(deleteRow.value)">
            <br />
            该工作项包含子项，删除后所有子项也将被一并删除。
          </template>
        </div>
        <template #footer>
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="handleDelete">删除</el-button>
        </template>
      </el-dialog>
    </div>

    <div class="gantt-divider"></div>

    <div
      class="gantt-chart-pane"
      ref="chartPaneRef"
      @mousemove="handlePaneMouseMove"
      @mouseleave="handlePaneMouseLeave"
    >
      <el-select
        v-model="currentProjectId"
        filterable
        placeholder="选择项目"
        style="
          position: fixed;
          top: 95px;
          right: 40px;
          z-index: 1000;
          width: 200px;
        "
        @change="handleSwitchProject"
      >
        <el-option
          v-for="proj in projectList"
          :key="proj.id"
          :label="proj.name"
          :value="proj.id"
        />
      </el-select>
      <div class="gantt-chart-header">
        <span v-for="week in ganttWeeks" :key="week" class="gantt-date">
          {{ week }}
        </span>
      </div>
      <div class="gantt-chart-body">
        <template v-for="row in visibleTaskListWithIndent" :key="row.id">
          <div
            class="gantt-row"
            :style="{ paddingLeft: row.indent * 20 + 'px' }"
          >
            <div
              class="gantt-bar"
              :class="[
                {
                  'gantt-bar-animate': barAnimated[row.id],
                  'gantt-bar-hidden': !barAnimated[row.id],
                },
                getGanttBarClass(row),
              ]"
              :style="getGanttBarStyle(row)"
              @mouseenter="showTooltip($event, row)"
              @mouseleave="hideTooltip"
            ></div>
          </div>
        </template>
        <div
          v-if="tooltip.visible && tooltip.data && tooltip.data.title"
          class="gantt-tooltip"
          :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
        >
          <div class="gantt-tooltip-title">{{ tooltip.data.title }}</div>
          <div>类型：{{ formatTypeLabel(tooltip.data.type) }}</div>
          <div>优先级：{{ tooltip.data.priority }}</div>
          <!-- 直接使用接口返回的status字段 -->
          <div>状态：{{ tooltip.data.status }}</div>
          <div>开始日期：{{ formatIsoToDisplay(tooltip.data.start) }}</div>
          <div>截止日期：{{ formatIsoToDisplay(tooltip.data.end) }}</div>
          <div v-if="tooltip.data.status === '已完成'">
            完成时间：{{
              tooltip.data.finish
                ? formatIsoToDisplay(tooltip.data.finish)
                : "-"
            }}
          </div>
          <div>当前时间：{{ tooltip.now }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Document, Folder, Plus } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import {
  TaskAPI,
  ProjectAPI,
  UserAPI,
} from "../../../api/progress_management/createProject";
import { TaskRow, Project, User } from "../../../types/project";

// 扩展TaskRow接口，添加description字段
interface TaskRowWithDescription extends TaskRow {
  description?: string;
}

// 格式化日期工具函数
const formatToIsoTime = (dateString: string) => {
  if (!dateString) return "";
  return dayjs(dateString).toISOString();
};

const formatIsoToDisplay = (isoString: string) => {
  if (!isoString) return "";
  return dayjs(isoString).format("YYYY-MM-DD");
};

// 判断日期是否逾期（截止日期 < 当前时间）
const isOverdue = (endDate: string) => {
  return dayjs(endDate).isBefore(dayjs(), "day");
};

// 根据日期自动更新状态
const autoUpdateStatusByDate = (form: TaskRowWithDescription) => {
  // 仅处理当前状态为“已逾期”的任务
  if (form.status !== "已逾期") return;

  // 若修改后的截止日期 >= 当前时间，自动更新状态为“未开始”
  if (!isOverdue(form.end)) {
    form.status = "未开始";
    ElMessage.info('任务已不再逾期，状态已更新为"未开始"');
  }
};

// 数据存储
const treeTableData = ref<TaskRowWithDescription[]>([]);
const flatData = ref<TaskRowWithDescription[]>([]);
const projectList = ref<Project[]>([]);
const userList = ref<User[]>([]);
const currentProjectId = ref<number | null>(null);

// 路由实例
const route = useRoute();
const router = useRouter();

// 用于判断和验证子项与里程碑时间关系的变量
const parentMilestone = ref<TaskRowWithDescription | null>(null);
const editParentMilestone = ref<TaskRowWithDescription | null>(null);
const createDateError = ref("");
const editDateError = ref("");

// 里程碑树形数据构建逻辑
const milestoneTreeData = computed(() => {
  // 创建原始数据的深拷贝，避免修改源数据
  const treeCopy = JSON.parse(
    JSON.stringify(treeTableData.value)
  ) as TaskRowWithDescription[];

  // 构建树形结构的递归函数
  const buildTree = (
    items: TaskRowWithDescription[]
  ): TaskRowWithDescription[] => {
    return (
      items
        // 只过滤掉当前正在编辑的项，避免自引用
        .filter((item) => item.id !== editForm.value.id && item.id != null)
        .map((item) => {
          // 递归处理子项
          const children =
            item.children && item.children.length
              ? buildTree(item.children as TaskRowWithDescription[])
              : [];

          return {
            id: item.id,
            title: item.title,
            description: item.description,
            // 保留所有类型的节点作为可选父项（里程碑、任务文件夹等）
            children: children,
          };
        })
    );
  };

  // 构建完整树形结构，包含所有可用的父项选择
  const filteredTree = buildTree(treeCopy);

  // 返回包含顶级选项的树形数据
  return [
    {
      id: -1,
      title: "顶级任务",
      children: filteredTree,
    },
  ];
});

// 构建树形结构
function buildTree(
  flatData: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  const map = new Map<number, TaskRowWithDescription>();
  const tree: TaskRowWithDescription[] = [];

  // 首先创建所有节点的映射
  flatData.forEach((item) => {
    if (item.id != null) {
      // 强制创建新对象，确保响应式
      map.set(item.id, { ...item, children: [...(item.children || [])] });
    }
  });

  // 构建树形结构
  flatData.forEach((item) => {
    if (item.id == null) return;
    const currentItem = map.get(item.id)!;
    if (item.parentId === -1 || item.parentId === null) {
      tree.push(currentItem);
    } else {
      const parent = map.get(item.parentId);
      if (parent) {
        parent.children = [...(parent.children || []), currentItem];
      } else {
        console.warn("buildTree: 找不到 parentId =", item.parentId, "的父节点");
        tree.push(currentItem);
      }
    }
  });

  return tree;
}

// 更新父任务时间
function updateParentTaskTime(
  nodes: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  const nodesCopy = JSON.parse(
    JSON.stringify(nodes)
  ) as TaskRowWithDescription[];
  function processNodes(list: TaskRowWithDescription[], depth = 0) {
    if (depth > 100) return; // 防止递归过深
    for (const node of list) {
      if (node.type === "milestone") continue;

      if (node.children && node.children.length > 0) {
        processNodes(node.children as TaskRowWithDescription[], depth + 1);
        let minStart: string | null = null;
        let maxEnd: string | null = null;

        function collectTimes(items: TaskRowWithDescription[]) {
          for (const item of items) {
            if (item.children && item.children.length > 0) {
              collectTimes(item.children as TaskRowWithDescription[]);
            }
            if (item.start && dayjs(item.start).isValid()) {
              if (!minStart || dayjs(item.start).isBefore(minStart)) {
                minStart = item.start;
              }
            }
            if (item.end && dayjs(item.end).isValid()) {
              if (!maxEnd || dayjs(item.end).isAfter(maxEnd)) {
                maxEnd = item.end;
              }
            }
          }
        }
        collectTimes(node.children as TaskRowWithDescription[]);
        if (minStart) node.start = minStart;
        if (maxEnd) node.end = maxEnd;
      }
    }
  }
  processNodes(nodesCopy);
  return nodesCopy;
}

// 获取所有任务（递归遍历所有层级）
function getAllTasksRecursive(
  nodes: TaskRowWithDescription[]
): TaskRowWithDescription[] {
  let allTasks: TaskRowWithDescription[] = [];
  nodes.forEach((node) => {
    allTasks.push(node);
    if (node.children && node.children.length > 0) {
      allTasks = allTasks.concat(
        getAllTasksRecursive(node.children as TaskRowWithDescription[])
      );
    }
  });
  return allTasks;
}

// 查找祖先里程碑
function findAncestorMilestone(
  taskId: number,
  allTasks: TaskRowWithDescription[]
): TaskRowWithDescription | null {
  const task = allTasks.find((t) => t.id === taskId);
  if (!task) return null;

  // 如果当前任务就是里程碑，返回自身
  if (task.type === "milestone") return task;

  // 如果没有父节点，返回null
  if (task.parentId === null || task.parentId === -1) return null;

  // 递归查找父节点
  return findAncestorMilestone(task.parentId, allTasks);
}

// 递归获取所有子项ID（包括当前项）
function getAllChildIds(
  item: TaskRowWithDescription,
  allItems: TaskRowWithDescription[]
): number[] {
  const ids: number[] = [];
  if (item.id != null) {
    ids.push(item.id);
  }

  // 查找当前项的直接子项
  const children = allItems.filter((child) => child.parentId === item.id);

  // 递归收集子项的子项
  children.forEach((child) => {
    ids.push(...getAllChildIds(child, allItems));
  });

  return ids;
}

// 判断是否有子项
function hasChildren(row: TaskRowWithDescription): boolean {
  const allItems = getAllTasksRecursive(treeTableData.value);
  return allItems.some((item) => item.parentId === row.id);
}

// 获取数据
async function fetchData(projectId: number) {
  try {
    if (!projectId) {
      console.error("fetchData: 无效的 projectId:", projectId);
      ElMessage.error("无效的项目ID");
      flatData.value = [];
      treeTableData.value = [];
      return;
    }

    // 1. 强制清空旧数据
    flatData.value = [];
    treeTableData.value = [];
    await nextTick();

    // 2. 重新拉取数据
    const tasksResponse = await TaskAPI.getList(projectId);
    console.log("后端返回原始数据:", tasksResponse);

    // 标准化数据格式
    const tasks = Array.isArray(tasksResponse)
      ? tasksResponse
      : tasksResponse?.data && Array.isArray(tasksResponse.data)
        ? tasksResponse.data
        : [tasksResponse];

    if (!Array.isArray(tasks) || !tasks.length) {
      console.error("fetchData: 任务数据为空或格式错误:", tasksResponse);
      ElMessage.error("任务数据为空或格式错误");
      return;
    }

    // 3. 格式化数据，包含description字段
    const formattedTasks = tasks
      .filter((item) => item.id != null)
      .map((item: any) => ({
        ...item,
        id: Number(item.id),
        parentId:
          item.parentId === null || item.parentId === undefined
            ? -1
            : Number(item.parentId),
        members: Array.isArray(item.members) ? item.members.map(Number) : [],
        type: item.type || "task",
        start: item.start
          ? formatIsoToDisplay(item.start)
          : dayjs().format("YYYY-MM-DD"),
        end: item.end
          ? formatIsoToDisplay(item.end)
          : dayjs().add(1, "day").format("YYYY-MM-DD"),
        finish: item.finish ? formatIsoToDisplay(item.finish) : null,
        children: item.children || [],
        description: item.description || "", // 处理描述字段
      }));

    // 4. 强制替换数据，触发响应式更新
    flatData.value = [...formattedTasks];
    const tree = buildTree(flatData.value);
    treeTableData.value = updateParentTaskTime(tree);

    // 5. 等待DOM更新后再触发动画
    await nextTick();
    triggerGanttBarAnimation();
  } catch (error) {
    console.error("fetchData: 错误:", error);
    ElMessage.error("获取任务数据失败");
    flatData.value = [];
    treeTableData.value = [];
  }
}

// 独立的获取项目列表方法
async function fetchProjectList() {
  try {
    const projectsResponse = await ProjectAPI.getList();
    const projects = Array.isArray(projectsResponse)
      ? projectsResponse
      : projectsResponse?.data && Array.isArray(projectsResponse.data)
        ? projectsResponse.data
        : [];

    projectList.value = Array.isArray(projects)
      ? projects
          .filter((item) => item.id != null)
          .map((item: Project) => ({ ...item, id: Number(item.id) }))
      : [];
  } catch (error) {
    console.error("获取项目列表失败:", error);
    ElMessage.error("获取项目列表失败");
  }
}

// 初始化数据
async function initData() {
  try {
    // 并行获取项目列表和用户列表
    await Promise.all([
      fetchProjectList(), // 使用独立的项目列表获取方法
      (async () => {
        const usersResponse = await UserAPI.getList();
        // 处理用户数据
        const users = Array.isArray(usersResponse)
          ? usersResponse
          : usersResponse?.data && Array.isArray(usersResponse.data)
            ? usersResponse.data
            : [];
        userList.value = Array.isArray(users)
          ? users
              .filter((item) => item.id != null && !isNaN(Number(item.id)))
              .map((item: User) => ({
                ...item,
                id: Number(item.id),
                name: item.username || "未知用户",
              }))
          : [];
      })(),
    ]);

    // 处理项目ID
    const projectIdFromRoute = route.params.projectId
      ? Number(route.params.projectId)
      : null;

    if (
      projectIdFromRoute &&
      projectList.value.some((p) => p.id === projectIdFromRoute)
    ) {
      currentProjectId.value = projectIdFromRoute;
    } else if (projectList.value.length > 0) {
      currentProjectId.value = projectList.value[0].id;
    }

    if (currentProjectId.value) {
      await fetchData(currentProjectId.value);
    } else {
      console.error("initData: 无有效的 projectId");
      ElMessage.error("无可用项目，请先创建项目");
      router.push({ name: "DefaultRoute" });
    }
  } catch (error) {
    console.error("initData: 错误:", error);
    ElMessage.error("初始化数据失败");
  }
}

// 切换项目
async function handleSwitchProject(id: number) {
  currentProjectId.value = id;
  expandedRowKeys.value = [];
  await fetchData(id);
}

// 展开行处理
const expandedRowKeys = ref<number[]>([]);
function handleExpandChange(row: TaskRowWithDescription, expanded: boolean) {
  const id = row.id;
  if (expanded && id != null) {
    if (!expandedRowKeys.value.includes(id)) {
      expandedRowKeys.value.push(id);
      // 展开时触发子任务动画
      setTimeout(() => {
        const childTasks = getAllTasksRecursive(
          (row.children as TaskRowWithDescription[]) || []
        );
        childTasks.forEach((child) => {
          if (child.id != null) {
            barAnimated.value[child.id] = false;
            setTimeout(() => {
              barAnimated.value[child.id] = true;
            }, 50);
          }
        });
      }, 0);
    }
  } else {
    expandedRowKeys.value = expandedRowKeys.value.filter((k) => k !== id);
    // 收起时重置子任务动画状态
    const childTasks = getAllTasksRecursive(
      (row.children as TaskRowWithDescription[]) || []
    );
    childTasks.forEach((child) => {
      if (child.id != null) {
        barAnimated.value[child.id] = false;
      }
    });
  }
}

// 获取所有可见任务（包括子项）
function getVisibleTaskListWithIndent(
  nodes: TaskRowWithDescription[],
  expandedKeys: number[],
  indent = 0
): any[] {
  let result: any[] = [];
  for (const node of nodes) {
    if (node.id == null) continue;
    result.push({ ...node, indent });
    // 如果有子项且已展开，则递归添加子项
    if (
      node.children &&
      node.children.length > 0 &&
      expandedKeys.includes(node.id)
    ) {
      result = result.concat(
        getVisibleTaskListWithIndent(
          node.children as TaskRowWithDescription[],
          expandedKeys,
          indent + 1
        )
      );
    }
  }
  return result;
}

// 计算属性：所有可见任务（包括展开的子项）
const visibleTaskListWithIndent = computed(() => {
  return getVisibleTaskListWithIndent(
    treeTableData.value,
    expandedRowKeys.value
  );
});

// 计算甘特图周数
const ganttWeeks = computed(() => {
  // 获取所有任务（包括子项）
  const allTasks = getAllTasksRecursive(treeTableData.value);
  const validTasks = allTasks.filter(
    (t) =>
      t.start && t.end && dayjs(t.start).isValid() && dayjs(t.end).isValid()
  );

  // 如果没有有效任务，使用当前日期生成默认周数
  if (validTasks.length === 0) {
    const today = dayjs();
    const weeks = [];
    for (let i = 0; i < 8; i++) {
      const weekStart = today.add(i, "week").startOf("week");
      weeks.push(`第${i + 1}周 (${weekStart.format("YYYY-MM-DD")})`);
    }
    return weeks;
  }

  // 计算最小开始时间和最大结束时间
  const minStart = dayjs(
    Math.min(...validTasks.map((t) => dayjs(t.start).valueOf()))
  ).startOf("week");
  const maxEnd = dayjs(
    Math.max(...validTasks.map((t) => dayjs(t.end).valueOf()))
  ).endOf("week");
  const totalWeeks = maxEnd.diff(minStart, "week") + 1;

  // 生成周标签
  const weeks = [];
  for (let i = 0; i < totalWeeks; i++) {
    const weekStart = minStart.add(i, "week");
    weeks.push(`第${i + 1}周 (${weekStart.format("YYYY-MM-DD")})`);
  }
  return weeks;
});

// 获取甘特图条样式
function getGanttBarStyle(row: TaskRowWithDescription) {
  // 处理没有时间的任务，给一个默认位置和宽度
  if (!row.start || !row.end) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  const start = dayjs(row.start);
  const end = dayjs(row.end);

  // 处理无效时间格式的任务
  if (!start.isValid() || !end.isValid()) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  // 获取所有任务用于计算时间范围
  const allTasks = getAllTasksRecursive(treeTableData.value);
  const validTasks = allTasks.filter(
    (t) =>
      t.start && t.end && dayjs(t.start).isValid() && dayjs(t.end).isValid()
  );

  // 如果没有有效任务，使用默认样式
  if (validTasks.length === 0) {
    return {
      display: "block",
      left: "5%",
      width: "10%",
      minWidth: "40px",
    };
  }

  // 计算时间范围和位置
  const minStart = dayjs(
    Math.min(...validTasks.map((t) => dayjs(t.start).valueOf()))
  ).startOf("week");
  const maxEnd = dayjs(
    Math.max(...validTasks.map((t) => dayjs(t.end).valueOf()))
  ).endOf("week");
  const totalWeeks = maxEnd.diff(minStart, "week") + 1;
  if (totalWeeks <= 0) return { display: "block", width: "10%", left: "5%" };

  // 计算开始和结束周数
  const startWeek = start.diff(minStart, "week");
  const endWeek = end.diff(minStart, "week");
  const width = ((endWeek - startWeek + 1) / totalWeeks) * 100;
  const left = (startWeek / totalWeeks) * 100;

  // 确保甘特条有最小宽度
  return {
    left: `${left}%`,
    width: width < 5 ? "5%" : `${width}%`, // 最小宽度为5%
    display: "block",
    minWidth: "40px",
  };
}

// 获取甘特图条样式类
function getGanttBarClass(row: TaskRowWithDescription) {
  // 对没有时间的任务特殊标记
  if (
    !row.start ||
    !row.end ||
    !dayjs(row.start).isValid() ||
    !dayjs(row.end).isValid()
  ) {
    return "gantt-bar-no-date";
  }

  // 使用接口返回的status确定甘特条样式
  if (row.status === "已完成") return "gantt-bar-done";
  if (row.status === "进行中") return "gantt-bar-doing";
  if (row.status === "未开始") return "gantt-bar-todo";
  if (row.status === "已逾期") return "gantt-bar-overdue";
  return "";
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  if (status === "已完成") return "success";
  if (status === "进行中") return "warning";
  if (status === "未开始") return "info";
  if (status === "已逾期") return "danger";
  return "info";
}

// 获取优先级标签类型
function getPriorityTagType(priority: string) {
  if (priority === "高") return "danger";
  if (priority === "普通") return "primary";
  if (priority === "低") return "info";
  return "info";
}

// 获取行样式类
function getRowClass({ row }: { row: TaskRowWithDescription }) {
  return row.children && row.children.length > 0 ? "gantt-folder-row" : "";
}

// 表格样式
const headerCellStyle = {
  padding: "8px 0",
  background: "#fafbfc",
  color: "#888",
  fontWeight: "bold",
  border: "1px solid #ebeef5",
};

const cellStyle = { padding: "8px 0", border: "1px solid #ebeef5" };

// 对话框状态
const createDialogVisible = ref(false);
const createForm = ref<TaskRowWithDescription>({
  id: 0,
  title: "",
  type: "milestone",
  priority: "普通",
  status: "未开始", // 默认状态
  start: dayjs().format("YYYY-MM-DD"),
  end: dayjs().add(1, "day").format("YYYY-MM-DD"),
  parentId: -1,
  members: [],
  children: [],
  description: "", // 新增描述字段
});

const viewDialogVisible = ref(false);
const viewRow = ref<TaskRowWithDescription>({} as TaskRowWithDescription);
const deleteDialogVisible = ref(false);
const deleteRow = ref<TaskRowWithDescription | null>(null);
const editDialogVisible = ref(false);
const editForm = ref<TaskRowWithDescription>({
  id: 0,
  title: "",
  type: "",
  priority: "普通",
  status: "",
  start: "",
  end: "",
  parentId: -1,
  members: [],
  children: [],
  description: "", // 新增描述字段
});

// 判断是否为里程碑的子项
const isSubItemOfMilestone = computed(() => {
  return parentMilestone.value !== null;
});

const isCreatingMilestone = computed(() => {
  return createForm.value.type === "milestone";
});

const isEditingMilestone = computed(() => {
  return editForm.value.type === "milestone";
});

const isEditingSubItemOfMilestone = computed(() => {
  return editParentMilestone.value !== null;
});

// 提示框
const tooltip = ref({
  visible: false,
  x: 0,
  y: 0,
  data: {} as TaskRowWithDescription,
  now: "",
});

// 甘特图引用和动画状态
const chartPaneRef = ref<HTMLElement | null>(null);
const barAnimated = ref<{ [id: number]: boolean }>({});

// 触发甘特图条动画
function triggerGanttBarAnimation() {
  // 重置所有任务的动画状态
  barAnimated.value = {};
  // 为当前可见任务设置动画状态
  setTimeout(() => {
    visibleTaskListWithIndent.value.forEach((row: any) => {
      if (row.id != null) {
        barAnimated.value[row.id] = true;
      }
    });
  }, 300);
}

// 鼠标移动处理
function handlePaneMouseMove(e: MouseEvent) {
  if (!tooltip.value.data || !tooltip.value.data.title) {
    tooltip.value.visible = false;
  }
}

// 鼠标离开处理
function handlePaneMouseLeave() {
  tooltip.value.visible = false;
}

// 显示提示框
function showTooltip(e: MouseEvent, row: TaskRowWithDescription) {
  tooltip.value.visible = true;
  tooltip.value.x = e.clientX + 16;
  tooltip.value.y = e.clientY + 8 + window.scrollY;
  tooltip.value.data = { ...row }; // 创建副本避免引用问题
  tooltip.value.now = dayjs().format("YYYY-MM-DD HH:mm:ss");
}

// 隐藏提示框
function hideTooltip() {
  tooltip.value.visible = false;
  tooltip.value.data = {} as TaskRowWithDescription;
}

// 格式化类型标签
function formatTypeLabel(type: string): string {
  switch (type) {
    case "milestone":
      return "里程碑";
    case "task":
      return "任务";
    case "taskFolder":
      return "任务文件夹";
    default:
      return type || "未知类型";
  }
}

// 类型变更处理
function handleTypeChange() {
  if (createForm.value.type === "milestone") {
    createForm.value.parentId = -1;
    parentMilestone.value = null;
  } else {
    handleParentChange(createForm.value.parentId);
  }
}

// 父项变更处理
function handleParentChange(parentId: number) {
  const allTasks = getAllTasksRecursive(treeTableData.value);
  parentMilestone.value =
    parentId === -1 ? null : findAncestorMilestone(parentId, allTasks);

  // 如果是里程碑的子项，自动设置时间为里程碑的时间
  if (parentMilestone.value) {
    createForm.value.start =
      parentMilestone.value.start || dayjs().format("YYYY-MM-DD");
    createForm.value.end =
      parentMilestone.value.end || dayjs().add(1, "day").format("YYYY-MM-DD");
  }

  validateDateRange("create");
}

// 编辑类型变更处理
function handleEditTypeChange() {
  if (editForm.value.type === "milestone") {
    editForm.value.parentId = -1;
    editParentMilestone.value = null;
  } else {
    handleEditParentChange(editForm.value.parentId);
  }
}

// 编辑父项变更处理
function handleEditParentChange(parentId: number) {
  const allTasks = getAllTasksRecursive(treeTableData.value);
  editParentMilestone.value =
    parentId === -1 ? null : findAncestorMilestone(parentId, allTasks);

  // 如果是里程碑的子项，自动设置时间为里程碑的时间
  if (editParentMilestone.value) {
    editForm.value.start =
      editParentMilestone.value.start || dayjs().format("YYYY-MM-DD");
    editForm.value.end =
      editParentMilestone.value.end ||
      dayjs().add(1, "day").format("YYYY-MM-DD");
  }

  validateDateRange("edit");
}

// 验证日期范围是否在里程碑范围内
function validateDateRange(formType: "create" | "edit") {
  const form = formType === "create" ? createForm.value : editForm.value;
  const milestone =
    formType === "create" ? parentMilestone.value : editParentMilestone.value;
  const errorRef = formType === "create" ? createDateError : editDateError;

  errorRef.value = "";

  if (!milestone) return true;

  const start = dayjs(form.start);
  const end = dayjs(form.end);
  const milestoneStart = dayjs(milestone.start);
  const milestoneEnd = dayjs(milestone.end);

  if (!start.isValid() || !end.isValid()) {
    errorRef.value = "请选择有效的日期";
    return false;
  }

  if (
    start.isBefore(milestoneStart, "day") ||
    end.isAfter(milestoneEnd, "day")
  ) {
    errorRef.value = `子项时间不能超过里程碑时间范围(${formatIsoToDisplay(milestone.start)} - ${formatIsoToDisplay(milestone.end)})，请先修改里程碑时间`;
    return false;
  }

  return true;
}

// 打开创建对话框
function openCreateDialog() {
  createForm.value = {
    id: 0,
    title: "",
    type: "milestone",
    priority: "普通",
    status: "未开始", // 默认状态
    start: dayjs().format("YYYY-MM-DD"),
    end: dayjs().add(1, "day").format("YYYY-MM-DD"),
    parentId: -1,
    members: [],
    children: [],
    description: "", // 初始化描述字段
  };
  parentMilestone.value = null;
  createDateError.value = "";
  createDialogVisible.value = true;
}

// 处理创建
async function handleCreate() {
  if (!createForm.value.title) {
    ElMessage.error("标题不能为空");
    return;
  }

  // 提交时进行日期范围校验
  if (!validateDateRange("create")) {
    return;
  }

  if (dayjs(createForm.value.start).isAfter(dayjs(createForm.value.end))) {
    ElMessage.error("开始日期不能晚于截止日期");
    return;
  }

  if (!Array.isArray(createForm.value.members)) {
    console.error("handleCreate: 人员分配数据无效");
    ElMessage.error("人员分配数据无效");
    return;
  }
  // 确保里程碑只能是顶级任务
  if (
    createForm.value.type === "milestone" &&
    createForm.value.parentId !== -1
  ) {
    ElMessage.error("里程碑只能添加到顶级任务");
    return;
  }
  try {
    const isoStart = formatToIsoTime(createForm.value.start);
    const isoEnd = formatToIsoTime(createForm.value.end);

    const newTask: TaskRowWithDescription = {
      id: Date.now(),
      projectId: currentProjectId.value || undefined,
      ...createForm.value,
      parentId:
        createForm.value.parentId === -1 ? null : createForm.value.parentId,
      members: createForm.value.members,
      finish: null,
      children: [],
      start: isoStart,
      end: isoEnd,
      description: createForm.value.description, // 包含描述字段
    };

    await TaskAPI.create(currentProjectId.value!, newTask);
    // 强制刷新数据
    await fetchData(currentProjectId.value!);
    createDialogVisible.value = false;
    ElMessage.success("工作项创建成功");
  } catch (error) {
    console.error("handleCreate: 错误:", error);
    ElMessage.error("创建工作项失败");
  }
}

// 打开查看对话框
function openViewDialog(row: TaskRowWithDescription) {
  viewRow.value = { ...row }; // 创建副本避免引用问题
  viewDialogVisible.value = true;
}

// 打开删除对话框
function openDeleteDialog(row: TaskRowWithDescription) {
  deleteRow.value = { ...row }; // 创建副本避免引用问题
  deleteDialogVisible.value = true;
}

// 处理删除（包括所有子项）
async function handleDelete() {
  if (!deleteRow.value || !currentProjectId.value) return;

  try {
    // 获取所有需要删除的项ID（当前项 + 所有子项）
    const allItems = getAllTasksRecursive(treeTableData.value);
    const idsToDelete = getAllChildIds(deleteRow.value, allItems);

    if (idsToDelete.length === 0) {
      ElMessage.warning("没有可删除的项目");
      return;
    }

    // 批量删除所有相关项
    // 这里假设后端有批量删除接口，如果没有则需要循环调用单个删除接口
    if (TaskAPI.batchDelete) {
      await TaskAPI.batchDelete(currentProjectId.value, idsToDelete);
    } else {
      // 后端不支持批量删除时，循环删除
      for (const id of idsToDelete) {
        await TaskAPI.delete(currentProjectId.value, id);
      }
    }

    // 强制刷新数据
    await fetchData(currentProjectId.value);
    deleteDialogVisible.value = false;
    ElMessage.success(`已成功删除 ${idsToDelete.length} 个工作项（包括子项）`);
  } catch (error) {
    console.error("handleDelete: 错误:", error);
    ElMessage.error("删除工作项失败");
  }
}

// 打开编辑对话框
function openEditDialog(row: TaskRowWithDescription) {
  // 确保时间正确初始化
  editForm.value = {
    ...row,
    parentId: row.parentId === null ? -1 : row.parentId,
    children: row.children || [],
    members: Array.isArray(row.members) ? [...row.members] : [],
    start: row.start
      ? formatIsoToDisplay(row.start)
      : dayjs().format("YYYY-MM-DD"),
    end: row.end
      ? formatIsoToDisplay(row.end)
      : dayjs().add(1, "day").format("YYYY-MM-DD"),
    description: row.description || "", // 初始化描述字段
  };

  // 查找父里程碑
  const allTasks = getAllTasksRecursive(treeTableData.value);
  editParentMilestone.value =
    editForm.value.parentId === -1
      ? null
      : findAncestorMilestone(editForm.value.parentId, allTasks);

  editDateError.value = "";
  editDialogVisible.value = true;
}

// 处理编辑
async function handleEdit() {
  if (!editForm.value.title) {
    ElMessage.error("标题不能为空");
    return;
  }

  // 提交时进行日期范围校验
  if (!validateDateRange("edit")) {
    return;
  }

  if (dayjs(editForm.value.start).isAfter(dayjs(editForm.value.end))) {
    ElMessage.error("开始日期不能晚于截止日期");
    return;
  }

  if (!Array.isArray(editForm.value.members)) {
    console.error("handleEdit: 人员分配数据无效");
    ElMessage.error("人员分配数据无效");
    return;
  }
  // 确保里程碑只能是顶级任务
  if (editForm.value.type === "milestone" && editForm.value.parentId !== -1) {
    ElMessage.error("里程碑只能设置为顶级任务");
    return;
  }
  try {
    const isoStart = formatToIsoTime(editForm.value.start);
    const isoEnd = formatToIsoTime(editForm.value.end);
    const isoFinish = editForm.value.finish
      ? formatToIsoTime(editForm.value.finish)
      : null;

    await TaskAPI.update(currentProjectId.value!, editForm.value.id, {
      ...editForm.value,
      parentId: editForm.value.parentId === -1 ? null : editForm.value.parentId,
      members: editForm.value.members,
      start: isoStart,
      end: isoEnd,
      finish: editForm.value.status === "已完成" ? isoEnd : isoFinish,
      description: editForm.value.description, // 包含描述字段
    });
    // 强制刷新数据
    await fetchData(currentProjectId.value!);
    editDialogVisible.value = false;
    ElMessage.success("工作项更新成功");
  } catch (error) {
    console.error("handleEdit: 错误:", error);
    ElMessage.error("更新工作项失败");
  }
}

// 监听 createForm.type，确保里程碑的 parentId 为 -1
watch(
  () => createForm.value.type,
  (newType) => {
    if (newType === "milestone") {
      createForm.value.parentId = -1;
    }
  }
);

// 监听 editForm.type，确保里程碑的 parentId 为 -1
watch(
  () => editForm.value.type,
  (newType) => {
    if (newType === "milestone") {
      editForm.value.parentId = -1;
    }
  }
);

// 监听路由参数变化以刷新数据
watch(
  () => route.params.projectId,
  async (newProjectId) => {
    if (newProjectId) {
      const projectId = Number(newProjectId);
      if (projectId && projectList.value.some((p) => p.id === projectId)) {
        currentProjectId.value = projectId;
        expandedRowKeys.value = [];
        await fetchData(projectId);
      } else {
        ElMessage.error("无效的项目ID");
        router.push({ name: "DefaultRoute" });
      }
    }
  },
  { immediate: true }
);

// 生命周期钩子
onMounted(async () => {
  await initData();
});

onUnmounted(() => {
  tooltip.value.visible = false;
});
</script>

<style scoped>
.gantt-table-wrapper {
  display: flex;
  width: 100%;
  align-items: flex-start;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  margin: 0;
  padding: 0;
}
.gantt-table-pane {
  flex: 0 0 60%;
  min-width: 420px;
  max-width: 900px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 18px 0 0 18px;
}
.gantt-create-btn {
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  margin-right: 10px;
}
.gantt-el-table {
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  --el-table-border-color: #f0f0f0;
  --el-table-bg-color: transparent;
}
.gantt-el-table .el-table__header-wrapper th,
.gantt-el-table .el-table__body-wrapper td {
  border: 1px solid #ebeef5 !important;
  background: #fff !important;
  box-sizing: border-box;
}
.gantt-el-table .el-table__header-wrapper th {
  background: #fafbfc !important;
  color: #888 !important;
  font-weight: bold;
}
.gantt-el-table .el-table__body-wrapper td {
  color: #333;
}
.gantt-el-table .gantt-folder-row td {
  font-weight: bold;
  background: #f8fafc !important;
}
.folder-title {
  font-weight: bold;
}
.gantt-divider {
  width: 1px;
  min-width: 1px;
  max-width: 1px;
  height: 100%;
  background: repeating-linear-gradient(
    to bottom,
    #e0e0e0,
    #e0e0e0 4px,
    transparent 4px,
    transparent 8px
  );
  margin: 0;
  align-self: stretch;
}
.gantt-chart-pane {
  position: relative;
  flex: 0 0 40%;
  min-width: 500px;
  max-width: 1000px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 18px 32px 18px 0;
  margin-left: 0;
  margin-top: 40px;
  overflow-x: auto;
  overflow: visible;
}
.gantt-chart-header {
  height: 24px;
  position: relative;
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}
.gantt-date {
  flex: 1;
  text-align: center;
  color: #888;
  font-size: 13px;
  padding: 2px 0;
}
.gantt-chart-body {
  display: flex;
  flex-direction: column;
}
.gantt-row {
  position: relative;
  height: 42px;
  display: flex;
  align-items: center;
}
.gantt-bar {
  position: absolute;
  height: 20px;
  border-radius: 10px;
  top: 6px;
  transition:
    width 0.7s cubic-bezier(0.4, 0, 0.2, 1),
    opacity 0.7s,
    left 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
  display: block !important;
  min-width: 20px;
  cursor: pointer;
}
.gantt-bar.gantt-bar-animate {
  opacity: 1;
}
.gantt-bar.gantt-bar-hidden {
  width: 0 !important;
  opacity: 0;
}
.gantt-bar-todo {
  background: #409eff;
}
.gantt-bar-doing {
  background: #ffb800;
}
.gantt-bar-done {
  background: #67c23a;
}
.gantt-bar-overdue {
  background: #f56c6c;
}
.gantt-bar-no-date {
  background: #ccc;
  opacity: 0.7;
}
.gantt-tooltip {
  position: fixed;
  z-index: 9999;
  min-width: 180px;
  background: #222;
  color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.18);
  padding: 12px 18px 10px 18px;
  font-size: 14px;
  pointer-events: none;
  transition: opacity 0.15s;
  opacity: 0.97;
  line-height: 1.7;
}
.gantt-tooltip-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
}
</style>
