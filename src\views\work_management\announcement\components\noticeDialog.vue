<template>
  <el-dialog
    v-model="dialogVisible"
    :title="notice.title"
    width="70%"
    :before-close="handleClose"
  >
    <div v-if="isEditMode">
      <quill-editor v-model:content="editedContent" contentType="html" />
    </div>
    <div v-else v-html="notice.content"></div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="!isEditMode" type="primary" @click="startEdit">
          编辑
        </el-button>
        <el-button v-else type="primary" @click="saveEdit">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import  Quill  from 'quill';
import 'quill/dist/quill.snow.css';
import { Notice } from '@/enums/MessageTypeEnum';

const props = defineProps<{
  modelValue: boolean;
  notice: Notice;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'save', notice: Notice): void;
}>();

const dialogVisible = ref(props.modelValue);
const isEditMode = ref(false);
const editedContent = ref('');

watch(() => props.modelValue, (newValue) => {
  dialogVisible.value = newValue;
});

watch(dialogVisible, (newValue) => {
  emit('update:modelValue', newValue);
});

const handleClose = () => {
  isEditMode.value = false;
  dialogVisible.value = false;
};

const startEdit = () => {
  editedContent.value = props.notice.content || '';
  isEditMode.value = true;
};

const saveEdit = () => {
  emit('save', { ...props.notice, content: editedContent.value });
  isEditMode.value = false;
};
</script>
