<template>
  <div class="task-table-container">
    <el-table
      :data="taskList"
      border
      row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      class="task-el-table"
      :expand-row-keys="expandedRowKeys"
      @expand-change="handleExpandChange"
      :row-class-name="getRowClass"
      :fit="false"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
      <el-table-column type="selection" width="40" />
      
      <el-table-column
        prop="title"
        label="标题"
        min-width="375px"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <TaskIcon :type="scope.row.type" />
          <span
            :class="{
              'milestone-title': scope.row.type === 'milestone',
              'folder-title': scope.row.type === 'taskFolder',
              'task-title': scope.row.type === 'task',
            }"
          >
            {{ scope.row.title }}
          </span>
        </template>
      </el-table-column>
      
      <el-table-column prop="status" label="状态" width="90">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ formatStatus(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="priority" label="优先级" width="90">
        <template #default="{ row }">
          <el-tag :type="getPriorityTagType(row.priority)">
            {{ formatPriority(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column fixed="right" label="操作" width="220" align="center">
        <template #default="scope">
          <TaskActions
            :task="scope.row"
            @view="$emit('view', scope.row)"
            @edit="$emit('edit', scope.row)"
            @delete="$emit('delete', scope.row)"
            @create-child="$emit('createChild', scope.row, $event)"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { TaskRow, TaskType } from "@/types/project";
import TaskIcon from "./TaskIcon.vue";
import TaskActions from "./TaskActions.vue";

interface Props {
  taskList: TaskRow[];
  expandedRowKeys: number[];
}

interface Emits {
  (e: 'expandChange', row: TaskRow, expanded: boolean): void;
  (e: 'view', task: TaskRow): void;
  (e: 'edit', task: TaskRow): void;
  (e: 'delete', task: TaskRow): void;
  (e: 'createChild', parentTask: TaskRow, childType: TaskType): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 处理展开变化
function handleExpandChange(row: TaskRow, expanded: boolean) {
  emit('expandChange', row, expanded);
}

// 获取行样式类
function getRowClass({ row }: { row: TaskRow }) {
  return row.children && row.children.length > 0 ? "task-folder-row" : "";
}

// 获取状态标签类型
function getStatusTagType(status: string) {
  if (status === "completed") return "success";
  if (status === "in_progress") return "warning";
  if (status === "not_started") return "info";
  if (status === "overdue") return "danger";
  return "info";
}

// 获取优先级标签类型
function getPriorityTagType(priority: string) {
  if (priority === "high") return "danger";
  if (priority === "medium") return "primary";
  if (priority === "low") return "info";
  return "info";
}

// 格式化状态显示
function formatStatus(status: string): string {
  const statusMap = {
    'not_started': '未开始',
    'in_progress': '进行中',
    'completed': '已完成',
    'overdue': '已逾期',
  };
  return statusMap[status as keyof typeof statusMap] || status;
}

// 格式化优先级显示
function formatPriority(priority: string): string {
  const priorityMap = {
    'high': '高',
    'medium': '普通',
    'low': '低',
  };
  return priorityMap[priority as keyof typeof priorityMap] || priority;
}

// 表格样式
const headerCellStyle = {
  padding: "8px 0",
  background: "#fafbfc",
  color: "#888",
  fontWeight: "bold",
  border: "1px solid #ebeef5",
};

const cellStyle = { padding: "8px 0", border: "1px solid #ebeef5" };
</script>

<style scoped>
.task-table-container {
  flex: 0 0 60%;
  min-width: 420px;
  max-width: 900px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 18px 0 0 18px;
}

.task-el-table {
  background: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  --el-table-border-color: #f0f0f0;
  --el-table-bg-color: transparent;
}

.task-el-table .el-table__header-wrapper th,
.task-el-table .el-table__body-wrapper td {
  border: 1px solid #ebeef5 !important;
  background: #fff !important;
  box-sizing: border-box;
}

.task-el-table .el-table__header-wrapper th {
  background: #fafbfc !important;
  color: #888 !important;
  font-weight: bold;
}

.task-el-table .el-table__body-wrapper td {
  color: #333;
}

.task-el-table .task-folder-row td {
  font-weight: bold;
  background: #f8fafc !important;
}

.milestone-title {
  font-weight: bold;
  color: #e6a23c;
}

.folder-title {
  font-weight: bold;
  color: #409eff;
}

.task-title {
  color: #606266;
}
</style>
