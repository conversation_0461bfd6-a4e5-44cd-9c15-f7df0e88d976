import axios from 'axios';
import { Notice, Message } from '@/enums/MessageTypeEnum';

const API_URL = '/api/notices'; // 替换为实际的API URL

/**
 * 获取所有公告
 * @returns 包含公告数组的Promise对象
 */
export const getNotices = () => axios.get<Notice[]>(API_URL);

/**
 * 获取单个公告
 * @param id - 公告ID
 * @returns 包含公告对象的Promise对象
 */
export const getNotice = (id: number) => axios.get<Notice>(`${API_URL}/${id}`);

/**
 * 创建新公告
 * @param notice - 新公告的数据（不包含ID）
 * @returns 包含创建的公告对象的Promise对象
 */
export const createNotice = (notice: Omit<Notice, 'id'>) => axios.post<Notice>(API_URL, notice);

/**
 * 更新公告
 * @param id - 公告ID
 * @param notice - 更新后的公告数据（部分字段）
 * @returns 包含更新后的公告对象的Promise对象
 */
export const updateNotice = (id: number, notice: Partial<Notice>) => axios.put<Notice>(`${API_URL}/${id}`, notice);

/**
 * 删除公告
 * @param id - 公告ID
 * @returns 包含删除操作结果的Promise对象
 */
export const deleteNotice = (id: number) => axios.delete(`${API_URL}/${id}`);

/**
 * 获取所有消息
 * @returns 包含消息数组的Promise对象
 */
export const getMessages = () => axios.get<Message[]>('/api/messages');

/**
 * 获取所有待办事项
 * @returns 包含待办事项数组的Promise对象
 */
export const getTodos = () => axios.get<Message[]>('/api/todos');

/**
 * 获取所有通知
 * @returns 包含通知数组的Promise对象
 */
export const getNotifications = () => axios.get<Message[]>('/api/notifications');
