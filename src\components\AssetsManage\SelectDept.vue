<template>
    <el-dialog
      v-model="props.visible"
      :title="title"
      width="600px"
    >
      <el-tree
        ref="treeRef"
        :data="deptList"
        node-key="value"
        :props="{
          label: 'label',
          children: 'children'
        }"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <span>{{ data.label }}</span>
        </template>
      </el-tree>
      <template #footer>
        <div class="dialog-footer">
          <span v-if="selectedDept" class="selected-dept">
            已选择: {{ selectedDept.label }}
          </span>
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { ElMessage } from 'element-plus'
  import DeptAPI from '@/api/dept'
  
  const props = defineProps({
    visible: {
      type: Boolean,
      required: true
    },
    title: {
      type: String,
      default: '选择部门'
    }
  })
  
  const emit = defineEmits(['update:visible', 'selected'])
  
  const deptList = ref<any[]>([])
  const selectedDept = ref<any>(null)
  
  // 加载部门数据
  const loadDeptData = async () => {
    try {
      const response = await DeptAPI.getOptions()
      if (Array.isArray(response)) {
        deptList.value = response
      }
    } catch (error) {
      console.error('获取部门列表失败:', error)
      ElMessage.error('获取部门列表失败')
    }
  }
  
  // 处理节点点击
  const handleNodeClick = (data: any) => {
    selectedDept.value = data
  }
  
  // 处理取消
  const handleCancel = () => {
    selectedDept.value = null
    emit('update:visible', false)
  }
  
  // 处理确认
  const handleConfirm = () => {
    if (selectedDept.value) {
      emit('selected', selectedDept.value)
      emit('update:visible', false)
    } else {
      ElMessage.warning('请选择部门')
    }
  }
  
  // 监听弹窗显示
  watch(() => props.visible, (val) => {
    if (val) {
      loadDeptData()
    }
  })
  </script>
  
  <style scoped>
  .selected-dept {
    margin-right: 20px;
    color: var(--el-color-primary);
  }
  
  :deep(.el-tree) {
    background: transparent;
    margin: 10px 0;
  }
  
  :deep(.el-tree-node) {
    white-space: normal;
    width: 100%;
  }
  
  :deep(.el-tree-node__content) {
    height: auto;
    padding: 8px 0;
  }
  
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
  
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
  }
  </style>